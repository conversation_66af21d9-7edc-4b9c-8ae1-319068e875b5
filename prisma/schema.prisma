generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Permission {
  id              Int              @id @default(autoincrement())
  permission      String           @unique
  description     String?
  rolePermissions RolePermission[]
}

model Role {
  id              Int              @id @default(autoincrement())
  name            String           @unique
  description     String?
  rolePermissions RolePermission[]
  users           User[]
}

model RolePermission {
  id           Int        @id @default(autoincrement())
  roleId       Int
  permissionId Int
  permission   Permission @relation(fields: [permissionId], references: [id])
  role         Role       @relation(fields: [roleId], references: [id])

  @@unique([roleId, permissionId])
}

model User {
  id           Int           @id @default(autoincrement())
  email        String        @unique
  name         String
  password     String
  isActive     Boolean       @default(true)
  roleId       Int
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  securityLogs SecurityLog[]
  role         Role          @relation(fields: [roleId], references: [id])

  @@index([email])
  @@index([isActive])
  @@index([roleId])
  @@index([createdAt])
}

model Company {
  id          Int       @id @default(autoincrement())
  name        String
  nameEn      String?
  logo        String?
  address     String?
  city        String?
  country     String?   @default("الإمارات العربية المتحدة")
  phone       String?
  email       String?
  taxNumber   String?
  website     String?
  bankDetails String?
  currency    String?   @default("AED")
  taxRate     Float?    @default(5)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  expenses    Expense[]
  invoices    Invoice[]
  products    Product[]
}

model Customer {
  id            Int       @id @default(autoincrement())
  name          String
  nameEn        String?
  email         String?
  phone         String?
  address       String?
  city          String?
  country       String?   @default("الإمارات العربية المتحدة")
  taxNumber     String?
  contactPerson String?
  contactPhone  String?
  contactEmail  String?
  notes         String?
  customerType  String?   @default("company")
  status        String    @default("active")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  invoices      Invoice[]

  @@index([email])
  @@index([phone])
  @@index([status])
  @@index([customerType])
  @@index([name])
  @@index([createdAt])
  @@index([status, customerType])
}

model ProductCategory {
  id          Int       @id @default(autoincrement())
  name        String
  nameEn      String?
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Product {
  id              Int                    @id @default(autoincrement())
  name            String
  nameEn          String?
  description     String?
  descriptionEn   String?
  sku             String?
  barcode         String?
  price           Float
  cost            Float?                 @default(0)
  wholesalePrice  Float?
  category        String?
  categoryEn      String?
  unit            String?                @default("قطعة")
  unitEn          String?                @default("Unit")
  taxRate         Float                  @default(5)
  status          String                 @default("active")
  trackInventory  Boolean                @default(false)
  currentStock    Int                    @default(0)
  minStock        Int                    @default(0)
  maxStock        Int                    @default(0)
  isComposite     Boolean                @default(false)
  imageUrl        String?
  categoryId      Int?
  companyId       Int?
  createdAt       DateTime               @default(now())
  updatedAt       DateTime               @updatedAt
  invoiceItems    InvoiceItem[]
  company         Company?               @relation(fields: [companyId], references: [id])
  productCategory ProductCategory?       @relation(fields: [categoryId], references: [id])
  productionPlans ProductionPlan[]
  planDetails     ProductionPlanDetail[]
  wastage         ProductionWastage[]
  purchaseItems   PurchaseRequestItem[]
  ingredientIn    RecipeItem[]           @relation("IngredientProduct")
  recipeItems     RecipeItem[]

  @@index([sku])
  @@index([barcode])
  @@index([status])
  @@index([category])
  @@index([trackInventory])
  @@index([currentStock])
  @@index([name])
  @@index([price])
  @@index([createdAt])
  @@index([status, category])
  @@index([trackInventory, currentStock])
  @@index([currentStock, minStock])
}

model Invoice {
  id             Int           @id @default(autoincrement())
  number         String        @unique
  customerId     Int
  companyId      Int?
  issueDate      String
  dueDate        String
  status         String        @default("draft")
  subtotal       Float         @default(0)
  taxAmount      Float         @default(0)
  discountAmount Float         @default(0)
  total          Float         @default(0)
  currency       String        @default("AED")
  notes          String?
  terms          String?
  paymentStatus  String        @default("pending")
  paymentMethod  String?
  paidAt         DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  company        Company?      @relation(fields: [companyId], references: [id])
  customer       Customer      @relation(fields: [customerId], references: [id])
  items          InvoiceItem[]
  payments       Payment[]

  @@index([number])
  @@index([status])
  @@index([paymentStatus])
  @@index([customerId])
  @@index([issueDate])
  @@index([dueDate])
  @@index([createdAt])
  @@index([total])
  @@index([status, paymentStatus])
  @@index([customerId, status])
  @@index([issueDate, status])
  @@index([paymentStatus, dueDate])
}

model InvoiceItem {
  id          Int      @id @default(autoincrement())
  invoiceId   Int
  productId   Int?
  name        String
  nameEn      String?
  description String?
  quantity    Float
  unitPrice   Float
  taxRate     Float    @default(5)
  taxAmount   Float    @default(0)
  total       Float    @default(0)
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  product     Product? @relation(fields: [productId], references: [id])
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
  @@index([productId])
}

model Payment {
  id            Int      @id @default(autoincrement())
  invoiceId     Int
  amount        Float
  paymentMethod String
  paymentDate   DateTime @default(now())
  reference     String?
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  invoice       Invoice  @relation(fields: [invoiceId], references: [id])

  @@index([invoiceId])
}

model ExpenseCategory {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  icon        String?
  color       String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  expenses    Expense[]

  @@index([name])
}

model Expense {
  id               Int              @id @default(autoincrement())
  title            String
  description      String
  amount           Float
  taxAmount        Float?
  totalAmount      Float
  receiptUrl       String?
  expenseDate      DateTime         @default(now())
  dueDate          DateTime?
  paymentStatus    String           @default("PAID")
  paymentMethod    String?
  reference        String?
  recurring        Boolean          @default(false)
  recurringPeriod  String?
  recurringEndDate DateTime?
  categoryId       Int?
  supplierId       Int?
  companyId        Int?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  attachments      Attachment[]
  company          Company?         @relation(fields: [companyId], references: [id])
  supplier         Supplier?        @relation(fields: [supplierId], references: [id])
  category         ExpenseCategory? @relation(fields: [categoryId], references: [id])
  tags             ExpenseTag[]     @relation("ExpenseToExpenseTag")

  @@index([categoryId])
  @@index([supplierId])
  @@index([paymentStatus])
  @@index([expenseDate])
  @@index([recurring])
  @@index([recurringPeriod])
}

model ExpenseTag {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  color     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  expenses  Expense[] @relation("ExpenseToExpenseTag")

  @@index([name])
}

model Attachment {
  id        Int      @id @default(autoincrement())
  fileName  String
  fileUrl   String
  fileType  String
  fileSize  Int?
  expenseId Int?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expense   Expense? @relation(fields: [expenseId], references: [id])

  @@index([expenseId])
  @@index([fileType])
}

model Supplier {
  id            Int        @id @default(autoincrement())
  name          String
  nameEn        String?
  email         String?
  phone         String?
  mobile        String?
  address       String?
  city          String?
  country       String?    @default("الإمارات العربية المتحدة")
  postalCode    String?
  taxNumber     String?
  website       String?
  contactPerson String?
  contactPhone  String?
  contactEmail  String?
  bankDetails   String?
  paymentTerms  String?    @default("30")
  creditLimit   Float?     @default(0)
  supplierType  String?    @default("vendor")
  category      String?
  rating        Float?     @default(0)
  notes         String?
  isActive      Boolean    @default(true)
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  expenses      Expense[]
  purchases     Purchase[]

  @@index([email])
  @@index([phone])
  @@index([supplierType])
  @@index([isActive])
  @@index([rating])
}

model Purchase {
  id           Int            @id @default(autoincrement())
  supplierId   Int
  purchaseDate DateTime       @default(now())
  total        Float
  status       String         @default("RECEIVED")
  reference    String?
  notes        String?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  supplier     Supplier       @relation(fields: [supplierId], references: [id])
  items        PurchaseItem[]

  @@index([supplierId])
  @@index([status])
}

model PurchaseItem {
  id         Int      @id @default(autoincrement())
  purchaseId Int
  product    String
  quantity   Int
  price      Float
  total      Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  purchase   Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)

  @@index([purchaseId])
}

model Settings {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     String
  section   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([key])
}

model RecipeItem {
  id           Int      @id @default(autoincrement())
  productId    Int
  ingredientId Int
  quantity     Float
  unit         String?
  notes        String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  ingredient   Product  @relation("IngredientProduct", fields: [ingredientId], references: [id])
  product      Product  @relation(fields: [productId], references: [id])

  @@unique([productId, ingredientId])
  @@index([productId])
  @@index([ingredientId])
}

model ProductionPlan {
  id             Int                    @id @default(autoincrement())
  productId      Int
  quantity       Int
  plannedDate    DateTime
  actualDate     DateTime?
  status         String
  notes          String?
  createdAt      DateTime               @default(now())
  updatedAt      DateTime               @updatedAt
  batchNumber    String?
  expiryDate     DateTime?
  productionDate DateTime?
  product        Product                @relation(fields: [productId], references: [id])
  details        ProductionPlanDetail[]
  wastage        ProductionWastage[]
  qualityChecks  QualityCheck[]

  @@index([productId])
  @@index([status])
  @@index([plannedDate])
  @@index([batchNumber])
  @@index([expiryDate])
}

model ProductionPlanDetail {
  id                Int            @id @default(autoincrement())
  productionPlanId  Int
  ingredientId      Int
  requiredQuantity  Float
  availableQuantity Float
  unit              String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  ingredient        Product        @relation(fields: [ingredientId], references: [id])
  productionPlan    ProductionPlan @relation(fields: [productionPlanId], references: [id])

  @@index([productionPlanId])
  @@index([ingredientId])
}

model ProductionWastage {
  id               Int            @id @default(autoincrement())
  productionPlanId Int
  ingredientId     Int
  quantity         Float
  unit             String?
  reason           String?
  cost             Float?
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  ingredient       Product        @relation(fields: [ingredientId], references: [id])
  productionPlan   ProductionPlan @relation(fields: [productionPlanId], references: [id])

  @@index([productionPlanId])
  @@index([ingredientId])
}

model QualityCheck {
  id               Int                @id @default(autoincrement())
  productionPlanId Int
  checkDate        DateTime
  checkedBy        String?
  status           String
  notes            String?
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  productionPlan   ProductionPlan     @relation(fields: [productionPlanId], references: [id])
  parameters       QualityParameter[]

  @@index([productionPlanId])
  @@index([status])
  @@index([checkDate])
}

model QualityParameter {
  id             Int          @id @default(autoincrement())
  qualityCheckId Int
  name           String
  expectedValue  String?
  actualValue    String?
  isPassed       Boolean
  notes          String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  qualityCheck   QualityCheck @relation(fields: [qualityCheckId], references: [id])

  @@index([qualityCheckId])
}

model PurchaseRequest {
  id           Int                   @id @default(autoincrement())
  title        String
  status       String
  requestedBy  String
  requestDate  DateTime
  approvedBy   String?
  approvalDate DateTime?
  notes        String?
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  items        PurchaseRequestItem[]

  @@index([status])
  @@index([requestDate])
}

model PurchaseRequestItem {
  id                Int             @id @default(autoincrement())
  purchaseRequestId Int
  productId         Int
  quantity          Float
  unit              String?
  estimatedPrice    Float?
  notes             String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  product           Product         @relation(fields: [productId], references: [id])
  purchaseRequest   PurchaseRequest @relation(fields: [purchaseRequestId], references: [id])

  @@index([purchaseRequestId])
  @@index([productId])
}

model Notification {
  id          Int      @id @default(autoincrement())
  title       String
  message     String
  type        String
  relatedId   String?
  relatedType String?
  isRead      Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type])
  @@index([isRead])
  @@index([createdAt])
}

model SecurityLog {
  id        Int      @id @default(autoincrement())
  type      String
  userId    Int?
  ip        String?
  userAgent String?
  path      String?
  method    String?
  details   String?
  severity  String
  timestamp DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User?    @relation(fields: [userId], references: [id])

  @@index([type])
  @@index([userId])
  @@index([ip])
  @@index([severity])
  @@index([timestamp])
  @@index([type, severity])
  @@index([userId, timestamp])
  @@index([ip, timestamp])
}
