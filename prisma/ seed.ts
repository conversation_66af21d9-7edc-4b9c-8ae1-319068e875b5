// prisma/seed.ts
import { PrismaClient, Permission } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // إنشاء الأدوار الأساسية
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: 'مسؤول النظام (كافة الصلاحيات)',
      permissions: [
        'VIEW_DASHBOARD', 'MANAGE_CUSTOMERS', 'MANAGE_INVOICES',
        'MANAGE_PRODUCTS', 'VIEW_REPORTS', 'MANAGE_USERS', 'MANAGE_SETTINGS'
      ],
    },
  });

  const accountantRole = await prisma.role.upsert({
    where: { name: 'accountant' },
    update: {},
    create: {
      name: 'accountant',
      description: 'محا<PERSON><PERSON> (صلاحيات معالجة البيانات المالية)',
      permissions: [
        'VIEW_DASHBOARD', 'MANAGE_CUSTOMERS', 'MANAGE_INVOICES',
        'VIEW_REPORTS'
      ],
    },
  });

  const salesRole = await prisma.role.upsert({
    where: { name: 'sales' },
    update: {},
    create: {
      name: 'sales',
      description: 'موظف مبيعات (إنشاء فواتير وإدارة العملاء)',
      permissions: [
        'VIEW_DASHBOARD', 'MANAGE_CUSTOMERS', 'MANAGE_INVOICES'
      ],
    },
  });

  // إنشاء مستخدم مسؤول أولي
  const hashedPassword = await hash('admin123', 12);
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      roleId: adminRole.id,
    },
    create: {
      name: 'مسؤول النظام',
      email: '<EMAIL>',
      password: hashedPassword,
      roleId: adminRole.id,
    },
  });

  console.log({ adminRole, accountantRole, salesRole, adminUser });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });