import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
    try {
        // إنشاء صلاحيات
        const viewDashboardPermission = await prisma.permission.create({
            data: {
                permission: 'VIEW_DASHBOARD',
                description: 'عرض لوحة التحكم'
            }
        });

        // إنشاء دور المدير
        const adminRole = await prisma.role.create({
            data: {
                name: 'admin',
                description: 'مدير النظام'
            }
        });

        // ربط الصلاحيات بالدور
        await prisma.rolePermission.create({
            data: {
                roleId: adminRole.id,
                permissionId: viewDashboardPermission.id
            }
        });

        // إنشاء مستخدم مدير
        const hashedPassword = await hash('12345678', 10);
        await prisma.user.create({
            data: {
                email: '<EMAIL>',
                name: 'مدير النظام',
                password: hashedPassword,
                isActive: true,
                roleId: adminRole.id
            }
        });

        console.log('تم إنشاء بيانات الاختبار بنجاح');
    } catch (error) {
        console.error('خطأ:', error);
    } finally {
        await prisma.$disconnect();
    }
}

main();