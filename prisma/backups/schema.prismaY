
model Permission {
  id          String           @id @default(cuid())
  permission  String           @unique
  description String?
  roles       RolePermission[]
}

model RolePermission {
  roleId     Int
  permission String
  role       Role       @relation(fields: [roleId], references: [id])
  perm       Permission @relation(fields: [permission], references: [permission])

  @@id([roleId, permission])
}// This is your Prisma schema file

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String      @id @default(cuid())
  name      String
  email     String      @unique
  password  String
  role      String      @default("user")
  isActive  Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  customers Customer[]
  invoices  Invoice[]
}

model Product {
  id          String        @id @default(cuid())
  name        String
  description String?
  sku         String?       @unique
  price       Float
  cost        Float?
  categoryId  String?
  category    Category?     @relation(fields: [categoryId], references: [id])
  stock       Int           @default(0)
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  invoiceItems InvoiceItem[]
}

model Category {
  id          String    @id @default(cuid())
  name        String
  description String?
  products    Product[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Customer {
  id        String    @id @default(cuid())
  name      String
  email     String?
  phone     String?
  address   String?
  notes     String?
  userId    String
  user      User      @relation(fields: [userId], references: [id])
  invoices  Invoice[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Invoice {
  id            String        @id @default(cuid())
  invoiceNumber String        @unique
  customerId    String
  customer      Customer      @relation(fields: [customerId], references: [id])
  userId        String
  user          User          @relation(fields: [userId], references: [id])
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime      @default(now())
  dueDate       DateTime?
  subtotal      Float
  taxRate       Float         @default(15)
  taxAmount     Float
  discount      Float         @default(0)
  total         Float
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  items         InvoiceItem[]
}

model InvoiceItem {
  id          String    @id @default(cuid())
  invoiceId   String
  invoice     Invoice   @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  productId   String
  product     Product   @relation(fields: [productId], references: [id])
  description String
  quantity    Float
  unitPrice   Float
  taxRate     Float     @default(15)
  taxAmount   Float
  discount    Float     @default(0)
  total       Float
}

model Role {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  users       User[]
  permissions RolePermission[]
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}
