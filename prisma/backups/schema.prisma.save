=npx prisma db pusmnpx prisma generate
odel Permission {
  permission     String           @id
  description    String?
  rolePermissions RolePermission[]
}

model Role {
  id             Int              @id @default(autoincrement())
  name           String           @unique
  description    String?
  users          User[]
  rolePermissions RolePermission[]
}

model RolePermission {
  role           Role             @relation(fields: [roleId], references: [id])
  roleId         Int
  permission     Permission       @relation(fields: [permissionName], references: [permission])
  permissionName String
  
  @@id([roleId, permissionName])
}
