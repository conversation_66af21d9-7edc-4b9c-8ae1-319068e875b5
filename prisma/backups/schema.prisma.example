// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Company {
  id          Int       @id @default(autoincrement())
  name        String
  logo        String?
  address     String?
  phone       String?
  email       String?
  taxNumber   String?   // Tax registration number
  website     String?
  bankDetails String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  invoices    Invoice[]
  expenses    Expense[]
  products    Product[]
}

model Customer {
  id           Int       @id @default(autoincrement())
  name         String
  email        String?
  phone        String?
  address      String?
  city         String?
  country      String?   @default("United Arab Emirates")
  notes        String?
  customerType String?   // Customer type (individual, company, distributor)
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  invoices     Invoice[]

  @@index([email])
  @@index([phone])
}

model ProductCategory {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Product {
  id              Int             @id @default(autoincrement())
  name            String
  description     String?
  sku             String?         // Product code
  barcode         String?         // Product barcode
  price           Float
  cost            Float?          // Product cost
  wholesalePrice  Float?          // Wholesale price
  stock           Int             @default(0)
  unit            String?         // Unit of measurement (ml, piece)
  minStock        Int?            // Minimum stock level
  isActive        Boolean         @default(true)
  category        ProductCategory? @relation(fields: [categoryId], references: [id])
  categoryId      Int?
  company         Company?        @relation(fields: [companyId], references: [id])
  companyId       Int?
  imageUrl        String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  invoiceItems    InvoiceItem[]

  @@index([sku])
  @@index([barcode])
}

model Invoice {
  id              Int           @id @default(autoincrement())
  invoiceNumber   String        @unique
  customer        Customer      @relation(fields: [customerId], references: [id])
  customerId      Int
  company         Company?      @relation(fields: [companyId], references: [id])
  companyId       Int?
  issueDate       DateTime      @default(now())
  dueDate         DateTime?
  subtotal        Float
  taxRate         Float         @default(0)
  taxAmount       Float         @default(0)
  discount        Float         @default(0)
  total           Float
  notes           String?
  paymentStatus   String        @default("PENDING") // PENDING, PAID, PARTIAL, CANCELLED
  paymentMethod   String?       // CASH, BANK_TRANSFER, CREDIT_CARD
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  items           InvoiceItem[]
  payments        Payment[]

  @@index([invoiceNumber])
  @@index([paymentStatus])
}

model InvoiceItem {
  id         Int      @id @default(autoincrement())
  invoice    Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId  Int
  product    Product  @relation(fields: [productId], references: [id])
  productId  Int
  quantity   Int
  price      Float
  discount   Float    @default(0)
  total      Float
  notes      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([invoiceId])
  @@index([productId])
}

model Payment {
  id            Int      @id @default(autoincrement())
  invoice       Invoice  @relation(fields: [invoiceId], references: [id])
  invoiceId     Int
  amount        Float
  paymentMethod String
  paymentDate   DateTime @default(now())
  reference     String?  // Payment reference such as bank transaction number
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([invoiceId])
}

model Expense {
  id          Int      @id @default(autoincrement())
  expenseType String?  // Expense type (rent, salaries, purchases)
  description String
  amount      Float
  receiptUrl  String?  // Link to receipt image
  expenseDate DateTime @default(now())
  company     Company? @relation(fields: [companyId], references: [id])
  companyId   Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([expenseType])
  @@index([expenseDate])
}

model Supplier {
  id        Int       @id @default(autoincrement())
  name      String
  email     String?
  phone     String?
  address   String?
  notes     String?
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  purchases Purchase[]
}

model Purchase {
  id            Int           @id @default(autoincrement())
  supplier      Supplier      @relation(fields: [supplierId], references: [id])
  supplierId    Int
  purchaseDate  DateTime      @default(now())
  total         Float
  status        String        @default("RECEIVED") // ORDERED, PARTIAL, RECEIVED
  reference     String?       // Purchase reference number
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  items         PurchaseItem[]

  @@index([supplierId])
  @@index([status])
}

model PurchaseItem {
  id         Int      @id @default(autoincrement())
  purchase   Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)
  purchaseId Int
  product    String   // Name of purchased product
  quantity   Int
  price      Float
  total      Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([purchaseId])
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String
  role      String   @default("USER") // ADMIN, MANAGER, USER
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
}

model Settings {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     String
  section   String?  // Settings section such as "general", "invoices", "system"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([key])
}
