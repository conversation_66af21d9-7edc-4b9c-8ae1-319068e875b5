// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// نظام المستخدمين والصلاحيات
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  roleId    String?
  role      Role?    @relation(fields: [roleId], references: [id])
}

model Role {
  id          String          @id @default(cuid())
  name        String          @unique
  description String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  users       User[]
  permissions RolePermission[]
}

model Permission {
  permission  String          @id
  description String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  roles       RolePermission[]
}

model RolePermission {
  roleId     String
  permission String
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  perm       Permission @relation(fields: [permission], references: [permission], onDelete: Cascade)

  @@id([roleId, permission])
}
