import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 بدء إدخال البيانات التجريبية...');

  // إنشاء شركة افتراضية
  const company = await prisma.company.upsert({
    where: { id: 1 },
    update: {},
    create: {
      name: 'شركة أمين بلس للحلول التقنية',
      nameEn: 'Amin Plus Technical Solutions',
      email: '<EMAIL>',
      phone: '+971 4 123 4567',
      address: 'شارع الشيخ زايد، دبي، الإمارات العربية المتحدة',
      city: 'دبي',
      country: 'الإمارات العربية المتحدة',
      taxNumber: '100123456789003',
      website: 'https://aminplus.ae',
      logo: '/images/logo.png',
      currency: 'AED',
      taxRate: 5,
      isActive: true,
    },
  });

  console.log('✅ تم إنشاء الشركة:', company.name);

  // إنشاء العملاء
  const customers = await Promise.all([
    prisma.customer.upsert({
      where: { id: 1 },
      update: {},
      create: {
        name: 'شركة الإمارات للتجارة',
        nameEn: 'Emirates Trading Company',
        email: '<EMAIL>',
        phone: '+971 4 123 4567',
        address: 'شارع الشيخ زايد، دبي، الإمارات العربية المتحدة',
        city: 'دبي',
        country: 'الإمارات العربية المتحدة',
        taxNumber: '100123456789003',
        contactPerson: 'أحمد محمد',
        contactPhone: '+971 50 123 4567',
        contactEmail: '<EMAIL>',
        status: 'active',
        customerType: 'company',
      },
    }),
    prisma.customer.upsert({
      where: { id: 2 },
      update: {},
      create: {
        name: 'مؤسسة الخليج للمقاولات',
        nameEn: 'Gulf Contracting Est.',
        email: '<EMAIL>',
        phone: '+971 2 987 6543',
        address: 'شارع الكورنيش، أبوظبي، الإمارات العربية المتحدة',
        city: 'أبوظبي',
        country: 'الإمارات العربية المتحدة',
        taxNumber: '100987654321003',
        contactPerson: 'فاطمة علي',
        contactPhone: '+971 55 987 6543',
        contactEmail: '<EMAIL>',
        status: 'active',
        customerType: 'company',
      },
    }),
    prisma.customer.upsert({
      where: { id: 3 },
      update: {},
      create: {
        name: 'شركة النور للتكنولوجيا',
        nameEn: 'Al Noor Technology LLC',
        email: '<EMAIL>',
        phone: '+971 6 555 1234',
        address: 'المدينة الصناعية، الشارقة، الإمارات العربية المتحدة',
        city: 'الشارقة',
        country: 'الإمارات العربية المتحدة',
        taxNumber: '100555123456003',
        contactPerson: 'محمد خالد',
        contactPhone: '+971 52 555 1234',
        contactEmail: '<EMAIL>',
        status: 'active',
        customerType: 'company',
      },
    }),
  ]);

  console.log('✅ تم إنشاء العملاء:', customers.length);

  // إنشاء فئات المنتجات
  const categories = await Promise.all([
    prisma.productCategory.upsert({
      where: { id: 1 },
      update: {},
      create: {
        name: 'خدمات تقنية',
        nameEn: 'Technical Services',
        description: 'خدمات تقنية متخصصة',
        isActive: true,
      },
    }),
    prisma.productCategory.upsert({
      where: { id: 2 },
      update: {},
      create: {
        name: 'تطوير البرمجيات',
        nameEn: 'Software Development',
        description: 'خدمات تطوير البرمجيات والتطبيقات',
        isActive: true,
      },
    }),
    prisma.productCategory.upsert({
      where: { id: 3 },
      update: {},
      create: {
        name: 'الأجهزة والمعدات',
        nameEn: 'Hardware & Equipment',
        description: 'أجهزة ومعدات تقنية',
        isActive: true,
      },
    }),
  ]);

  console.log('✅ تم إنشاء فئات المنتجات:', categories.length);

  // إنشاء المنتجات
  const products = await Promise.all([
    prisma.product.upsert({
      where: { id: 1 },
      update: {},
      create: {
        name: 'خدمات استشارية تقنية',
        nameEn: 'Technical Consulting Services',
        description: 'خدمات استشارية متخصصة في مجال التكنولوجيا والتطوير',
        descriptionEn: 'Specialized consulting services in technology and development',
        sku: 'TECH-CONS-001',
        price: 500,
        cost: 300,
        category: 'خدمات',
        categoryEn: 'Services',
        unit: 'ساعة',
        unitEn: 'Hour',
        taxRate: 5,
        status: 'active',
        trackInventory: false,
        categoryId: 1,
      },
    }),
    prisma.product.upsert({
      where: { id: 2 },
      update: {},
      create: {
        name: 'تطوير تطبيقات الويب',
        nameEn: 'Web Application Development',
        description: 'تطوير تطبيقات ويب متقدمة باستخدام أحدث التقنيات',
        descriptionEn: 'Advanced web application development using latest technologies',
        sku: 'WEB-DEV-001',
        price: 2500,
        cost: 1500,
        category: 'تطوير',
        categoryEn: 'Development',
        unit: 'مشروع',
        unitEn: 'Project',
        taxRate: 5,
        status: 'active',
        trackInventory: false,
        categoryId: 2,
        companyId: 1,
      },
    }),
    prisma.product.upsert({
      where: { id: 3 },
      update: {},
      create: {
        name: 'تطبيقات الهواتف الذكية',
        nameEn: 'Mobile App Development',
        description: 'تطوير تطبيقات للهواتف الذكية (iOS و Android)',
        descriptionEn: 'Mobile application development for iOS and Android',
        sku: 'MOB-DEV-001',
        price: 3500,
        cost: 2000,
        category: 'تطوير',
        categoryEn: 'Development',
        unit: 'تطبيق',
        unitEn: 'App',
        taxRate: 5,
        status: 'active',
        trackInventory: false,
        categoryId: 2,
      },
    }),
    prisma.product.upsert({
      where: { id: 4 },
      update: {},
      create: {
        name: 'أجهزة كمبيوتر محمولة',
        nameEn: 'Laptop Computers',
        description: 'أجهزة كمبيوتر محمولة عالية الأداء للأعمال',
        descriptionEn: 'High-performance laptop computers for business',
        sku: 'LAPTOP-001',
        price: 3000,
        cost: 2200,
        category: 'أجهزة',
        categoryEn: 'Hardware',
        unit: 'جهاز',
        unitEn: 'Unit',
        taxRate: 5,
        status: 'active',
        trackInventory: true,
        currentStock: 25,
        minStock: 5,
        maxStock: 100,
        categoryId: 3,
      },
    }),
    prisma.product.upsert({
      where: { id: 5 },
      update: {},
      create: {
        name: 'برامج إدارة المشاريع',
        nameEn: 'Project Management Software',
        description: 'برامج متخصصة في إدارة المشاريع والفرق',
        descriptionEn: 'Specialized software for project and team management',
        sku: 'PM-SOFT-001',
        price: 1200,
        cost: 800,
        category: 'برمجيات',
        categoryEn: 'Software',
        unit: 'ترخيص',
        unitEn: 'License',
        taxRate: 5,
        status: 'active',
        trackInventory: true,
        currentStock: 50,
        minStock: 10,
        maxStock: 200,
        categoryId: 2,
      },
    }),
  ]);

  console.log('✅ تم إنشاء المنتجات:', products.length);

  // إنشاء فواتير تجريبية
  const invoices = await Promise.all([
    prisma.invoice.create({
      data: {
        number: 'INV-202412-0001',
        customerId: 1,

        issueDate: '2024-12-01',
        dueDate: '2024-12-31',
        status: 'paid',
        subtotal: 20000,
        taxAmount: 1000,
        discountAmount: 0,
        total: 21000,
        currency: 'AED',
        notes: 'شكراً لتعاملكم معنا',
        terms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
        paymentStatus: 'paid',
        paidAt: new Date('2024-12-05'),
        items: {
          create: [
            {
              productId: 1,
              name: 'خدمات استشارية تقنية',
              nameEn: 'Technical Consulting Services',
              description: 'استشارات تقنية متخصصة لمدة 40 ساعة',
              quantity: 40,
              unitPrice: 500,
              taxRate: 5,
              taxAmount: 1000,
              total: 21000,
            },
          ],
        },
      },
    }),
    prisma.invoice.create({
      data: {
        number: 'INV-202412-0002',
        customerId: 2,

        issueDate: '2024-12-10',
        dueDate: '2024-12-25',
        status: 'pending',
        subtotal: 11500,
        taxAmount: 575,
        discountAmount: 0,
        total: 12075,
        currency: 'AED',
        notes: 'يرجى الدفع في الموعد المحدد',
        terms: 'الدفع خلال 15 يوم من تاريخ الفاتورة',
        paymentStatus: 'pending',
        items: {
          create: [
            {
              productId: 2,
              name: 'تطوير تطبيقات الويب',
              nameEn: 'Web Application Development',
              description: 'تطوير موقع إلكتروني متكامل',
              quantity: 1,
              unitPrice: 2500,
              taxRate: 5,
              taxAmount: 125,
              total: 2625,
            },
            {
              productId: 4,
              name: 'أجهزة كمبيوتر محمولة',
              nameEn: 'Laptop Computers',
              description: 'أجهزة كمبيوتر محمولة للفريق',
              quantity: 3,
              unitPrice: 3000,
              taxRate: 5,
              taxAmount: 450,
              total: 9450,
            },
          ],
        },
      },
    }),
  ]);

  console.log('✅ تم إنشاء الفواتير:', invoices.length);

  console.log('🎉 تم إكمال إدخال البيانات التجريبية بنجاح!');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ خطأ في إدخال البيانات:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
