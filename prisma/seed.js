const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient()

async function main() {
  // إنشاء الأدوار الأساسية
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: { description: 'مسؤول النظام (كافة الصلاحيات)' },
    create: {
      name: 'admin',
      description: 'مسؤول النظام (كافة الصلاحيات)',
    },
  })

  const accountantRole = await prisma.role.upsert({
    where: { name: 'accountant' },
    update: { description: 'محاسب (صلاحيات مالية)' },
    create: {
      name: 'accountant',
      description: 'محاسب (صلاحيات مالية)',
    },
  })

  const salesRole = await prisma.role.upsert({
    where: { name: 'sales' },
    update: { description: 'مبيعات (إدارة العملاء والفواتير)' },
    create: {
      name: 'sales',
      description: 'مبيعات (إدارة العملاء والفواتير)',
    },
  })

  // إعداد صلاحيات كل دور
  const rolePermissions = {
    admin: [
      'VIEW_DASHBOARD', 'MANAGE_CUSTOMERS', 'MANAGE_INVOICES',
      'MANAGE_PRODUCTS', 'VIEW_REPORTS', 'MANAGE_USERS', 'MANAGE_SETTINGS'
    ],
    accountant: [
      'VIEW_DASHBOARD', 'VIEW_REPORTS', 'MANAGE_INVOICES', 'MANAGE_CUSTOMERS'
    ],
    sales: ['VIEW_DASHBOARD', 'MANAGE_CUSTOMERS', 'MANAGE_INVOICES']
  }

  // إضافة الصلاحيات لكل دور
  for (const [roleName, permissions] of Object.entries(rolePermissions)) {
    const role = await prisma.role.findUnique({ where: { name: roleName } })
    if (!role) continue

    // حذف الصلاحيات القديمة
    await prisma.rolePermission.deleteMany({
      where: { roleId: role.id }
    })

    // إضافة الصلاحيات الجديدة
    for (const perm of permissions) {
      // أولاً، تحقق من وجود الصلاحية أو قم بإنشائها
      const permission = await prisma.permission.upsert({
        where: { permission: perm },
        update: {},
        create: {
          permission: perm,
          description: `صلاحية ${perm}`
        }
      });

      // ثم قم بربط الصلاحية بالدور
      await prisma.rolePermission.create({
        data: {
          role: {
            connect: { id: role.id }
          },
          permission: {
            connect: { id: permission.id }
          }
        }
      })
    }

    console.log(`تم تعيين ${permissions.length} صلاحيات لدور ${roleName}`)
  }

  // إنشاء مستخدم مسؤول افتراضي
  const hashedPassword = await bcrypt.hash('admin123', 12)
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: { roleId: adminRole.id },
    create: {
      name: 'مسؤول النظام',
      email: '<EMAIL>',
      password: hashedPassword,
      roleId: adminRole.id,
    },
  })

  // إنشاء مستخدم تجريبي
  const testPassword = await bcrypt.hash('password123', 12)
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: { roleId: adminRole.id },
    create: {
      name: 'مستخدم تجريبي',
      email: '<EMAIL>',
      password: testPassword,
      roleId: adminRole.id,
      isActive: true
    },
  })

  console.log('تم إنشاء المستخدمين بنجاح')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
