-- AlterTable
ALTER TABLE "ProductionPlan" ADD COLUMN "batchNumber" TEXT;
ALTER TABLE "ProductionPlan" ADD COLUMN "expiryDate" DATETIME;
ALTER TABLE "ProductionPlan" ADD COLUMN "productionDate" DATETIME;

-- CreateTable
CREATE TABLE "QualityCheck" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "productionPlanId" INTEGER NOT NULL,
    "checkDate" DATETIME NOT NULL,
    "checkedBy" TEXT,
    "status" TEXT NOT NULL,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "QualityCheck_productionPlanId_fkey" FOREIGN KEY ("productionPlanId") REFERENCES "ProductionPlan" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "QualityParameter" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "qualityCheckId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "expectedValue" TEXT,
    "actualValue" TEXT,
    "isPassed" BOOLEAN NOT NULL,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "QualityParameter_qualityCheckId_fkey" FOREIGN KEY ("qualityCheckId") REFERENCES "QualityCheck" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "PurchaseRequest" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "title" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "requestedBy" TEXT NOT NULL,
    "requestDate" DATETIME NOT NULL,
    "approvedBy" TEXT,
    "approvalDate" DATETIME,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "PurchaseRequestItem" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "purchaseRequestId" INTEGER NOT NULL,
    "productId" INTEGER NOT NULL,
    "quantity" REAL NOT NULL,
    "unit" TEXT,
    "estimatedPrice" REAL,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "PurchaseRequestItem_purchaseRequestId_fkey" FOREIGN KEY ("purchaseRequestId") REFERENCES "PurchaseRequest" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "PurchaseRequestItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "QualityCheck_productionPlanId_idx" ON "QualityCheck"("productionPlanId");

-- CreateIndex
CREATE INDEX "QualityCheck_status_idx" ON "QualityCheck"("status");

-- CreateIndex
CREATE INDEX "QualityCheck_checkDate_idx" ON "QualityCheck"("checkDate");

-- CreateIndex
CREATE INDEX "QualityParameter_qualityCheckId_idx" ON "QualityParameter"("qualityCheckId");

-- CreateIndex
CREATE INDEX "PurchaseRequest_status_idx" ON "PurchaseRequest"("status");

-- CreateIndex
CREATE INDEX "PurchaseRequest_requestDate_idx" ON "PurchaseRequest"("requestDate");

-- CreateIndex
CREATE INDEX "PurchaseRequestItem_purchaseRequestId_idx" ON "PurchaseRequestItem"("purchaseRequestId");

-- CreateIndex
CREATE INDEX "PurchaseRequestItem_productId_idx" ON "PurchaseRequestItem"("productId");

-- CreateIndex
CREATE INDEX "ProductionPlan_batchNumber_idx" ON "ProductionPlan"("batchNumber");

-- CreateIndex
CREATE INDEX "ProductionPlan_expiryDate_idx" ON "ProductionPlan"("expiryDate");
