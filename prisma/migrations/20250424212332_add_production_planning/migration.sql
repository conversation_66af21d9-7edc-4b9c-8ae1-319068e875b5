-- CreateTable
CREATE TABLE "ProductionPlan" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "productId" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "plannedDate" DATETIME NOT NULL,
    "actualDate" DATETIME,
    "status" TEXT NOT NULL,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "ProductionPlan_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ProductionPlanDetail" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "productionPlanId" INTEGER NOT NULL,
    "ingredientId" INTEGER NOT NULL,
    "requiredQuantity" REAL NOT NULL,
    "availableQuantity" REAL NOT NULL,
    "unit" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "ProductionPlanDetail_productionPlanId_fkey" FOREIGN KEY ("productionPlanId") REFERENCES "ProductionPlan" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "ProductionPlanDetail_ingredientId_fkey" FOREIGN KEY ("ingredientId") REFERENCES "Product" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ProductionWastage" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "productionPlanId" INTEGER NOT NULL,
    "ingredientId" INTEGER NOT NULL,
    "quantity" REAL NOT NULL,
    "unit" TEXT,
    "reason" TEXT,
    "cost" REAL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "ProductionWastage_productionPlanId_fkey" FOREIGN KEY ("productionPlanId") REFERENCES "ProductionPlan" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "ProductionWastage_ingredientId_fkey" FOREIGN KEY ("ingredientId") REFERENCES "Product" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "ProductionPlan_productId_idx" ON "ProductionPlan"("productId");

-- CreateIndex
CREATE INDEX "ProductionPlan_status_idx" ON "ProductionPlan"("status");

-- CreateIndex
CREATE INDEX "ProductionPlan_plannedDate_idx" ON "ProductionPlan"("plannedDate");

-- CreateIndex
CREATE INDEX "ProductionPlanDetail_productionPlanId_idx" ON "ProductionPlanDetail"("productionPlanId");

-- CreateIndex
CREATE INDEX "ProductionPlanDetail_ingredientId_idx" ON "ProductionPlanDetail"("ingredientId");

-- CreateIndex
CREATE INDEX "ProductionWastage_productionPlanId_idx" ON "ProductionWastage"("productionPlanId");

-- CreateIndex
CREATE INDEX "ProductionWastage_ingredientId_idx" ON "ProductionWastage"("ingredientId");
