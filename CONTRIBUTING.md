# دليل المساهمة في مشروع أمين بلس

شكراً لاهتمامك بالمساهمة في مشروع أمين بلس! هذا الدليل يوضح كيفية المساهمة في المشروع وأفضل الممارسات التي يجب اتباعها.

## كيفية المساهمة

1. قم بعمل fork للمشروع
2. قم بإنشاء فرع جديد للميزة أو الإصلاح: `git checkout -b feature/amazing-feature` أو `git checkout -b fix/bug-fix`
3. قم بإجراء التغييرات اللازمة
4. تأكد من أن الكود يتبع معايير التنسيق والجودة
5. قم بإضافة اختبارات للتغييرات الجديدة
6. قم بتشغيل الاختبارات للتأكد من أنها تعمل بشكل صحيح
7. قم بعمل commit للتغييرات: `git commit -m 'إضافة ميزة رائعة'`
8. قم بدفع التغييرات إلى الفرع الخاص بك: `git push origin feature/amazing-feature`
9. قم بإنشاء طلب سحب (Pull Request)

## معايير كتابة الكود

### التنسيق

- استخدم مسافتين للإزاحة (indentation)
- استخدم نمط kebab-case لأسماء الملفات (مثال: `user-profile.tsx`)
- استخدم نمط PascalCase لأسماء المكونات (مثال: `UserProfile`)
- استخدم نمط camelCase للمتغيرات والدوال (مثال: `getUserData`)

### التعليقات

- اكتب تعليقات باللغتين العربية والإنجليزية للأجزاء المهمة من الكود
- استخدم JSDoc لتوثيق الدوال والمكونات

```typescript
/**
 * مكون يعرض معلومات المستخدم
 * Component that displays user information
 * @param user - بيانات المستخدم / User data
 * @returns مكون معلومات المستخدم / User information component
 */
```

### الاختبارات

- اكتب اختبارات لكل مكون جديد
- تأكد من أن الاختبارات تغطي جميع حالات الاستخدام
- استخدم Jest و React Testing Library للاختبارات

## هيكل المشروع

```
amin-plus/
├── public/              # الملفات العامة (الصور، الأيقونات، إلخ)
├── src/                 # مصدر الكود
│   ├── app/             # صفحات التطبيق (Next.js App Router)
│   │   ├── api/         # واجهات برمجة التطبيق (API)
│   │   ├── auth/        # صفحات المصادقة
│   │   ├── dashboard/   # صفحات لوحة التحكم
│   │   └── ...
│   ├── components/      # مكونات التطبيق
│   │   ├── auth/        # مكونات المصادقة
│   │   ├── customers/   # مكونات العملاء
│   │   ├── dashboard/   # مكونات لوحة التحكم
│   │   ├── invoices/    # مكونات الفواتير
│   │   ├── layout/      # مكونات التخطيط
│   │   ├── reports/     # مكونات التقارير
│   │   ├── settings/    # مكونات الإعدادات
│   │   ├── theme/       # مكونات السمة
│   │   └── ui/          # مكونات واجهة المستخدم العامة
│   ├── lib/             # مكتبات ووظائف مساعدة
│   ├── types/           # تعريفات الأنواع
│   └── ...
├── prisma/              # نماذج Prisma وملفات الهجرة
└── ...
```

## إضافة ميزات جديدة

عند إضافة ميزة جديدة، يرجى اتباع الخطوات التالية:

1. تأكد من أن الميزة متوافقة مع أهداف المشروع
2. قم بإنشاء فرع جديد للميزة
3. قم بتنفيذ الميزة مع الالتزام بمعايير كتابة الكود
4. قم بإضافة اختبارات للميزة
5. قم بتحديث التوثيق إذا لزم الأمر
6. قم بإنشاء طلب سحب (Pull Request) مع وصف مفصل للميزة

## إصلاح الأخطاء

عند إصلاح خطأ، يرجى اتباع الخطوات التالية:

1. قم بإنشاء فرع جديد للإصلاح
2. قم بإصلاح الخطأ مع الالتزام بمعايير كتابة الكود
3. قم بإضافة اختبار يثبت أن الخطأ تم إصلاحه
4. قم بإنشاء طلب سحب (Pull Request) مع وصف مفصل للخطأ والإصلاح

## الاتصال

إذا كان لديك أي أسئلة أو استفسارات، يرجى التواصل معنا عبر:

- البريد الإلكتروني: <EMAIL>
- قسم المشكلات (Issues) في GitHub

شكراً لمساهمتك في مشروع أمين بلس!
