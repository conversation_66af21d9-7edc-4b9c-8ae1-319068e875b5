/**
 * ملف تكوين Next.js لمشروع أمين بلس
 * Next.js configuration file for Amin Plus project
 *
 * يحتوي هذا الملف على إعدادات Next.js المخصصة للمشروع
 * This file contains custom Next.js settings for the project
 */

/** @type {import('next').NextConfig} */
const nextConfig = {
  // تفعيل الوضع الصارم في React
  // Enable React strict mode
  reactStrictMode: true,

  // تعطيل TypeScript checking أثناء البناء
  // Disable TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // تعطيل ESLint أثناء البناء
  // Disable ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // إعدادات الصور
  // Images configuration
  images: {
    domains: ['localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

  // إعدادات الحزم الخارجية
  // External packages settings
  serverExternalPackages: ['@prisma/client', 'bcryptjs'],

  // إعدادات webpack
  // Webpack settings
  webpack: (config, { dev, isServer }) => {
    // إضافة دعم للملفات الثنائية
    config.externals = config.externals || [];
    config.externals.push({
      'utf-8-validate': 'commonjs utf-8-validate',
      'bufferutil': 'commonjs bufferutil',
    });

    // تحسين الأداء في الإنتاج
    if (!dev) {
      config.devtool = 'source-map';
    }

    return config;
  },

  // إعدادات البيئة
  // Environment settings
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // إعدادات إعادة التوجيه
  // Redirect settings
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
      },
    ];
  },
};

module.exports = nextConfig;
