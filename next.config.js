/** @type {import('next').NextConfig} */
const nextConfig = {
  // تحسينات الأداء
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  // ضغط الصور
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 يوم
  },

  // تحسين الحزمة
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // تقسيم الحزمة
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      };

      // ضغط إضافي
      config.optimization.minimize = true;
    }

    return config;
  },

  // إزالة الكود غير المستخدم
  modularizeImports: {
    'lucide-react': {
      transform: 'lucide-react/dist/esm/icons/{{member}}',
    },
  },

  // إعدادات Tauri
  output: 'export',
  trailingSlash: true,
  distDir: 'out',
  assetPrefix: process.env.NODE_ENV === 'production' ? './' : '',
};

module.exports = nextConfig;
