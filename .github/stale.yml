# ملف تكوين Stale لمشروع أمين بلس
# Stale configuration file for Amin Plus project

# عدد الأيام قبل وضع علامة على المشكلة كقديمة
# Number of days of inactivity before an issue becomes stale
daysUntilStale: 60

# عدد الأيام قبل إغلاق المشكلة القديمة
# Number of days of inactivity before a stale issue is closed
daysUntilClose: 7

# المشكلات ذات هذه التسميات لن تصبح قديمة أبدًا
# Issues with these labels will never be considered stale
exemptLabels:
  - pinned
  - security
  - bug
  - enhancement
  - documentation

# التسمية التي سيتم وضعها على المشكلات القديمة
# Label to use when marking an issue as stale
staleLabel: wontfix

# التعليق الذي سيتم نشره عند وضع علامة على المشكلة كقديمة
# Comment to post when marking an issue as stale
markComment: >
  هذه المشكلة تم وضع علامة عليها تلقائيًا كقديمة بسبب عدم وجود نشاط عليها.
  سيتم إغلاقها إذا لم يكن هناك نشاط إضافي. شكرًا لمساهمتك.
  
  This issue has been automatically marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs. Thank you
  for your contributions.

# التعليق الذي سيتم نشره عند إغلاق المشكلة القديمة
# Comment to post when closing a stale issue
closeComment: >
  هذه المشكلة تم إغلاقها تلقائيًا بسبب عدم وجود نشاط عليها.
  يرجى إعادة فتح المشكلة إذا كانت لا تزال ذات صلة.
  
  This issue has been automatically closed because it has not had
  recent activity. Please reopen the issue if it is still relevant.
