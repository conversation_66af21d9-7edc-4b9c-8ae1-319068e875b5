# ملف تكوين Dependabot لمشروع أمين بلس
# Dependabot configuration file for Amin Plus project

version: 2
updates:
  # تحديثات تبعيات npm
  # npm dependencies updates
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    target-branch: "develop"
    labels:
      - "dependencies"
      - "npm"
    commit-message:
      prefix: "npm"
      include: "scope"
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]

  # تحديثات تبعيات npm في مجلد electron
  # npm dependencies updates in electron directory
  - package-ecosystem: "npm"
    directory: "/electron"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    target-branch: "develop"
    labels:
      - "dependencies"
      - "npm"
      - "electron"
    commit-message:
      prefix: "electron"
      include: "scope"
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]

  # تحديثات GitHub Actions
  # GitHub Actions updates
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    target-branch: "develop"
    labels:
      - "dependencies"
      - "github-actions"
    commit-message:
      prefix: "github-actions"
      include: "scope"

  # تحديثات Docker
  # Docker updates
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    target-branch: "develop"
    labels:
      - "dependencies"
      - "docker"
    commit-message:
      prefix: "docker"
      include: "scope"
