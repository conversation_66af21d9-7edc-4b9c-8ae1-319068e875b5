# دليل المساهمة

شكرًا لاهتمامك بالمساهمة في مشروع أمين بلس! نحن نرحب بمساهماتك ونقدرها.

## كيفية المساهمة

### الإبلاغ عن الأخطاء

1. تأكد من أن الخطأ لم يتم الإبلاغ عنه بالفعل من خلال البحث في [قائمة المشكلات](https://github.com/aminplus/amin-plus/issues).
2. إذا لم تتمكن من العثور على مشكلة مفتوحة تعالج الخطأ، [افتح مشكلة جديدة](https://github.com/aminplus/amin-plus/issues/new/choose).
3. استخدم قالب الإبلاغ عن الأخطاء وقدم أكبر قدر ممكن من المعلومات.

### اقتراح تحسينات

1. تأكد من أن الاقتراح لم يتم تقديمه بالفعل من خلال البحث في [قائمة المشكلات](https://github.com/aminplus/amin-plus/issues).
2. إذا لم تتمكن من العثور على مشكلة مفتوحة تتضمن اقتراحك، [افتح مشكلة جديدة](https://github.com/aminplus/amin-plus/issues/new/choose).
3. استخدم قالب طلب الميزة وقدم أكبر قدر ممكن من المعلومات.

### إرسال تغييرات

1. قم بعمل fork للمستودع.
2. قم بإنشاء فرع جديد لميزتك أو إصلاحك.
3. قم بإجراء التغييرات الخاصة بك.
4. تأكد من أن التغييرات الخاصة بك تتبع [أسلوب الكود](#أسلوب-الكود).
5. تأكد من أن جميع الاختبارات تمر.
6. قم بإرسال طلب سحب.

## عملية التطوير

### إعداد بيئة التطوير

1. قم بتثبيت Node.js (الإصدار 18 أو أحدث).
2. قم بتثبيت npm أو yarn أو pnpm.
3. قم بعمل fork للمستودع وقم بنسخه محليًا.
4. قم بتثبيت التبعيات:

```bash
npm install
# أو
yarn install
# أو
pnpm install
```

5. قم بإعداد ملف البيئة:

```bash
cp .env.example .env
```

6. قم بتشغيل التطبيق في وضع التطوير:

```bash
npm run dev
```

### أسلوب الكود

نحن نستخدم ESLint و Prettier للحفاظ على أسلوب الكود متسق. يرجى التأكد من أن الكود الخاص بك يتوافق مع قواعد ESLint و Prettier قبل إرسال طلب سحب.

```bash
npm run lint
```

### الاختبارات

يرجى التأكد من أن جميع الاختبارات تمر قبل إرسال طلب سحب. إذا قمت بإضافة ميزة جديدة، يرجى إضافة اختبارات لها.

```bash
npm test
```

## هيكل المشروع

```
amin-plus/
├── electron/            # ملفات تطبيق سطح المكتب (Electron)
├── public/              # الملفات العامة (الصور، الأيقونات، إلخ)
├── src/                 # مصدر الكود
│   ├── app/             # صفحات التطبيق (Next.js App Router)
│   ├── components/      # مكونات التطبيق
│   ├── lib/             # مكتبات ووظائف مساعدة
│   └── types/           # تعريفات الأنواع
├── prisma/              # نماذج Prisma وملفات الهجرة
└── ...
```

## اتفاقيات التسمية

- **الملفات**: استخدم kebab-case (مثل `file-name.ts`).
- **المكونات**: استخدم PascalCase (مثل `ComponentName.tsx`).
- **الوظائف**: استخدم camelCase (مثل `functionName`).
- **المتغيرات**: استخدم camelCase (مثل `variableName`).
- **الثوابت**: استخدم UPPER_SNAKE_CASE (مثل `CONSTANT_NAME`).
- **الواجهات والأنواع**: استخدم PascalCase (مثل `InterfaceName`).

## اتفاقيات الالتزام

نحن نستخدم [Conventional Commits](https://www.conventionalcommits.org/) لرسائل الالتزام. يرجى اتباع هذه الاتفاقية عند إرسال التزامات.

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

أنواع الالتزام:

- **feat**: ميزة جديدة
- **fix**: إصلاح خطأ
- **docs**: تغييرات في التوثيق
- **style**: تغييرات لا تؤثر على معنى الكود (مسافات، تنسيق، إلخ)
- **refactor**: تغيير الكود الذي لا يصلح خطأ ولا يضيف ميزة
- **perf**: تغيير الكود الذي يحسن الأداء
- **test**: إضافة اختبارات مفقودة أو تصحيح اختبارات موجودة
- **chore**: تغييرات في عملية البناء أو الأدوات المساعدة

## الترخيص

من خلال المساهمة في مشروع أمين بلس، فإنك توافق على أن مساهماتك ستكون مرخصة بموجب [رخصة MIT](../LICENSE).
