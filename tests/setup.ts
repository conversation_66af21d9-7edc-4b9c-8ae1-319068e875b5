import { test as base } from '@playwright/test';

// Extend the base test with custom fixtures
export const test = base.extend({
  // Add custom fixtures here if needed
});

export { expect } from '@playwright/test';

// Global test configuration
export const TEST_CONFIG = {
  // Test user credentials
  ADMIN_EMAIL: '<EMAIL>',
  ADMIN_PASSWORD: 'admin123',
  USER_EMAIL: '<EMAIL>',
  USER_PASSWORD: 'user123',
  
  // Test data
  TEST_CUSTOMER: {
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '+971501234567',
    address: '123 Test Street, Dubai'
  },
  
  TEST_PRODUCT: {
    name: 'Test Product',
    barcode: '1234567890',
    price: 99.99,
    cost: 50.00,
    stockQty: 100
  },
  
  // Timeouts
  DEFAULT_TIMEOUT: 30000,
  SLOW_TIMEOUT: 60000,
  
  // URLs
  BASE_URL: 'http://localhost:3000'
};

// Helper functions for common test operations
export class TestHelpers {
  static async login(page: any, email: string = TEST_CONFIG.ADMIN_EMAIL, password: string = TEST_CONFIG.ADMIN_PASSWORD) {
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', email);
    await page.fill('input[type="password"]', password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/.*\/dashboard/);
  }

  static async logout(page: any) {
    await page.click('[data-testid="logout-button"]');
    await page.waitForURL(/.*\/auth\/login/);
  }

  static async createTestCustomer(page: any, customerData = TEST_CONFIG.TEST_CUSTOMER) {
    await page.goto('/customers');
    await page.click('[data-testid="add-customer-button"]');
    
    await page.fill('input[name="name"]', customerData.name);
    await page.fill('input[name="email"]', customerData.email);
    await page.fill('input[name="phone"]', customerData.phone);
    await page.fill('textarea[name="address"]', customerData.address);
    
    await page.click('button[type="submit"]');
    await page.waitForSelector('text=تم إضافة العميل بنجاح');
  }

  static async createTestProduct(page: any, productData = TEST_CONFIG.TEST_PRODUCT) {
    await page.goto('/products');
    await page.click('[data-testid="add-product-button"]');
    
    await page.fill('input[name="name"]', productData.name);
    await page.fill('input[name="barcode"]', productData.barcode);
    await page.fill('input[name="price"]', productData.price.toString());
    await page.fill('input[name="cost"]', productData.cost.toString());
    await page.fill('input[name="stockQty"]', productData.stockQty.toString());
    
    await page.click('button[type="submit"]');
    await page.waitForSelector('text=تم إضافة المنتج بنجاح');
  }

  static async waitForApiResponse(page: any, urlPattern: string | RegExp, timeout: number = TEST_CONFIG.DEFAULT_TIMEOUT) {
    return page.waitForResponse(
      response => {
        const url = response.url();
        if (typeof urlPattern === 'string') {
          return url.includes(urlPattern);
        }
        return urlPattern.test(url);
      },
      { timeout }
    );
  }

  static async interceptApiCall(page: any, urlPattern: string | RegExp, mockResponse: any) {
    await page.route(urlPattern, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockResponse)
      });
    });
  }

  static async simulateNetworkError(page: any, urlPattern: string | RegExp) {
    await page.route(urlPattern, route => {
      route.abort('failed');
    });
  }

  static generateRandomEmail(): string {
    const timestamp = Date.now();
    return `test${timestamp}@example.com`;
  }

  static generateRandomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  static async takeScreenshot(page: any, name: string) {
    await page.screenshot({ 
      path: `tests/screenshots/${name}-${Date.now()}.png`,
      fullPage: true 
    });
  }

  static async clearDatabase(page: any) {
    // This would typically call an API endpoint to reset test data
    // Implementation depends on your backend setup
    await page.request.post('/api/test/reset-database');
  }

  static async seedTestData(page: any) {
    // This would typically call an API endpoint to seed test data
    // Implementation depends on your backend setup
    await page.request.post('/api/test/seed-data');
  }
}
