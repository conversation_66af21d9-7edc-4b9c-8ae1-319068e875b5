import { test, expect } from '@playwright/test';

test.describe('Security Features', () => {
  test.describe('Authentication Security', () => {
    test('should prevent access to protected routes without authentication', async ({ page }) => {
      const protectedRoutes = [
        '/dashboard',
        '/customers',
        '/products',
        '/invoices',
        '/reports',
        '/settings'
      ];

      for (const route of protectedRoutes) {
        await page.goto(route);
        // Should redirect to login
        await expect(page).toHaveURL(/.*\/auth\/login/);
      }
    });

    test('should implement rate limiting on login attempts', async ({ page }) => {
      await page.goto('/auth/login');

      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'wrongpassword');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(500);
      }

      // Should show rate limit error
      await expect(page.locator('text=تم تجاوز الحد المسموح من المحاولات')).toBeVisible();
    });

    test('should sanitize user input', async ({ page }) => {
      // Login first
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');

      // Try to create customer with malicious input
      await page.goto('/customers');
      await page.click('[data-testid="add-customer-button"]');

      const maliciousInput = '<script>alert("XSS")</script>Test Customer';
      await page.fill('input[name="name"]', maliciousInput);
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.click('button[type="submit"]');

      // Input should be sanitized (script tags removed)
      await expect(page.locator('text=Test Customer')).toBeVisible();
      await expect(page.locator('text=<script>')).not.toBeVisible();
    });

    test('should validate CSRF protection', async ({ page }) => {
      // Login first
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');

      // Try to make a request without proper CSRF token
      const response = await page.request.post('/api/customers', {
        data: {
          name: 'Test Customer',
          email: '<EMAIL>'
        },
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Should be rejected due to missing CSRF token
      expect(response.status()).toBe(403);
    });
  });

  test.describe('Data Protection', () => {
    test('should not expose sensitive data in client-side code', async ({ page }) => {
      await page.goto('/');

      // Check that sensitive environment variables are not exposed
      const pageContent = await page.content();
      expect(pageContent).not.toContain('DATABASE_URL');
      expect(pageContent).not.toContain('NEXTAUTH_SECRET');
      expect(pageContent).not.toContain('JWT_SECRET');
    });

    test('should implement proper session management', async ({ page }) => {
      // Login
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');

      // Check that session cookie is secure
      const cookies = await page.context().cookies();
      const sessionCookie = cookies.find(cookie => 
        cookie.name.includes('session') || cookie.name.includes('token')
      );

      if (sessionCookie) {
        expect(sessionCookie.secure).toBe(true);
        expect(sessionCookie.httpOnly).toBe(true);
      }
    });

    test('should validate file upload security', async ({ page }) => {
      // Login first
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');

      // Navigate to a page with file upload (if exists)
      await page.goto('/products');
      await page.click('[data-testid="add-product-button"]');

      // Try to upload a malicious file
      const fileInput = page.locator('input[type="file"]');
      if (await fileInput.isVisible()) {
        // Create a fake malicious file
        await fileInput.setInputFiles({
          name: 'malicious.php',
          mimeType: 'application/x-php',
          buffer: Buffer.from('<?php echo "malicious code"; ?>')
        });

        await page.click('button[type="submit"]');

        // Should show error for invalid file type
        await expect(page.locator('text=نوع الملف غير مدعوم')).toBeVisible();
      }
    });
  });

  test.describe('API Security', () => {
    test('should require authentication for protected API endpoints', async ({ page }) => {
      const protectedEndpoints = [
        '/api/customers',
        '/api/products',
        '/api/invoices',
        '/api/backup',
        '/api/security-logs'
      ];

      for (const endpoint of protectedEndpoints) {
        const response = await page.request.get(endpoint);
        expect(response.status()).toBe(401);
      }
    });

    test('should implement proper CORS headers', async ({ page }) => {
      const response = await page.request.get('/api/health');
      const headers = response.headers();

      // Check for security headers
      expect(headers['x-frame-options']).toBe('DENY');
      expect(headers['x-content-type-options']).toBe('nosniff');
      expect(headers['x-xss-protection']).toBe('1; mode=block');
    });

    test('should validate input on API endpoints', async ({ page }) => {
      // Login to get session
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');

      // Try to send invalid data to API
      const response = await page.request.post('/api/customers', {
        data: {
          name: '', // Empty name should be invalid
          email: 'invalid-email', // Invalid email format
          price: -100 // Negative price should be invalid
        }
      });

      expect(response.status()).toBe(400);
      const responseBody = await response.json();
      expect(responseBody.errors).toBeDefined();
    });
  });

  test.describe('Security Headers', () => {
    test('should include security headers in responses', async ({ page }) => {
      const response = await page.goto('/');
      const headers = response?.headers() || {};

      // Check for important security headers
      expect(headers['x-frame-options']).toBeDefined();
      expect(headers['x-content-type-options']).toBeDefined();
      expect(headers['x-xss-protection']).toBeDefined();
      expect(headers['content-security-policy']).toBeDefined();
      expect(headers['referrer-policy']).toBeDefined();
    });

    test('should prevent clickjacking attacks', async ({ page }) => {
      const response = await page.goto('/');
      const headers = response?.headers() || {};

      expect(headers['x-frame-options']).toBe('DENY');
    });
  });

  test.describe('Role-Based Access Control', () => {
    test('should restrict admin features to admin users', async ({ page }) => {
      // Login as regular user (if such user exists)
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'user123');
      await page.click('button[type="submit"]');

      // Try to access admin features
      await page.goto('/admin/users');
      
      // Should show access denied or redirect
      await expect(page.locator('text=غير مصرح لك بالوصول')).toBeVisible();
    });

    test('should allow admin users to access all features', async ({ page }) => {
      // Login as admin
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');

      // Should be able to access admin features
      await page.goto('/admin/users');
      await expect(page.locator('[data-testid="admin-panel"]')).toBeVisible();
    });
  });

  test.describe('Security Monitoring', () => {
    test('should log security events', async ({ page }) => {
      // Make a failed login attempt
      await page.goto('/auth/login');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'wrongpassword');
      await page.click('button[type="submit"]');

      // Login as admin to check security logs
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');

      // Check security logs
      await page.goto('/admin/security-logs');
      await expect(page.locator('text=LOGIN_FAILURE')).toBeVisible();
    });
  });
});
