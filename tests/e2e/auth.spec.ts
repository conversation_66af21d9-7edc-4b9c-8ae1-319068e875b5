import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should redirect to login page when not authenticated', async ({ page }) => {
    // Should be redirected to login page
    await expect(page).toHaveURL(/.*\/auth\/login/);
    
    // Check if login form is visible
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
  });

  test('should show validation errors for invalid login', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Should show validation errors
    await expect(page.locator('text=البريد الإلكتروني مطلوب')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Fill invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show error message
    await expect(page.locator('text=البريد الإلكتروني أو كلمة المرور غير صحيحة')).toBeVisible();
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Fill valid credentials (assuming test user exists)
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Should show user info or dashboard content
    await expect(page.locator('text=لوحة التحكم')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // First login
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Find and click logout button
    await page.click('[data-testid="logout-button"]');
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*\/auth\/login/);
  });

  test('should maintain session across page refreshes', async ({ page }) => {
    // Login
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Refresh page
    await page.reload();
    
    // Should still be on dashboard
    await expect(page).toHaveURL(/.*\/dashboard/);
    await expect(page.locator('text=لوحة التحكم')).toBeVisible();
  });

  test('should handle session expiry', async ({ page }) => {
    // Login
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Clear session storage to simulate session expiry
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    
    // Try to navigate to protected page
    await page.goto('/customers');
    
    // Should redirect to login
    await expect(page).toHaveURL(/.*\/auth\/login/);
  });

  test('should show loading state during login', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Fill credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    
    // Click submit and immediately check for loading state
    await page.click('button[type="submit"]');
    
    // Should show loading indicator
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept login request and make it fail
    await page.route('**/api/auth/**', route => {
      route.abort('failed');
    });
    
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Should show network error message
    await expect(page.locator('text=حدث خطأ في الاتصال')).toBeVisible();
  });

  test('should validate email format', async ({ page }) => {
    await page.goto('/auth/login');
    
    // Enter invalid email format
    await page.fill('input[type="email"]', 'invalid-email');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Should show email validation error
    await expect(page.locator('text=البريد الإلكتروني غير صالح')).toBeVisible();
  });

  test('should handle auto-login feature', async ({ page }) => {
    // Navigate to auto-login page (if exists)
    await page.goto('/auto-login');
    
    // Should automatically login and redirect to dashboard
    await expect(page).toHaveURL(/.*\/dashboard/);
    await expect(page.locator('text=لوحة التحكم')).toBeVisible();
  });

  test('should show password strength indicator', async ({ page }) => {
    // Navigate to registration page (if exists)
    await page.goto('/auth/register');
    
    // Fill weak password
    await page.fill('input[type="password"]', 'weak');
    
    // Should show weak password indicator
    await expect(page.locator('[data-testid="password-strength-weak"]')).toBeVisible();
    
    // Fill strong password
    await page.fill('input[type="password"]', 'StrongP@ssw0rd123!');
    
    // Should show strong password indicator
    await expect(page.locator('[data-testid="password-strength-strong"]')).toBeVisible();
  });
});
