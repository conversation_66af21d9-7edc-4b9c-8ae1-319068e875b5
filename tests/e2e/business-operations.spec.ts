import { test, expect } from '@playwright/test';

test.describe('Business Operations', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test.describe('Customer Management', () => {
    test('should create a new customer', async ({ page }) => {
      await page.goto('/customers');
      
      // Click add customer button
      await page.click('[data-testid="add-customer-button"]');
      
      // Fill customer form
      await page.fill('input[name="name"]', 'Test Customer');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="phone"]', '+971501234567');
      await page.fill('textarea[name="address"]', '123 Test Street, Dubai');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show success message
      await expect(page.locator('text=تم إضافة العميل بنجاح')).toBeVisible();
      
      // Should appear in customer list
      await expect(page.locator('text=Test Customer')).toBeVisible();
    });

    test('should edit existing customer', async ({ page }) => {
      await page.goto('/customers');
      
      // Click edit button for first customer
      await page.click('[data-testid="edit-customer-button"]:first-child');
      
      // Update customer name
      await page.fill('input[name="name"]', 'Updated Customer Name');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show success message
      await expect(page.locator('text=تم تحديث العميل بنجاح')).toBeVisible();
      
      // Should show updated name
      await expect(page.locator('text=Updated Customer Name')).toBeVisible();
    });

    test('should delete customer', async ({ page }) => {
      await page.goto('/customers');
      
      // Click delete button
      await page.click('[data-testid="delete-customer-button"]:first-child');
      
      // Confirm deletion
      await page.click('[data-testid="confirm-delete-button"]');
      
      // Should show success message
      await expect(page.locator('text=تم حذف العميل بنجاح')).toBeVisible();
    });

    test('should search customers', async ({ page }) => {
      await page.goto('/customers');
      
      // Enter search term
      await page.fill('[data-testid="customer-search"]', 'Test Customer');
      
      // Should filter results
      await expect(page.locator('[data-testid="customer-row"]')).toHaveCount(1);
      await expect(page.locator('text=Test Customer')).toBeVisible();
    });
  });

  test.describe('Product Management', () => {
    test('should create a new product', async ({ page }) => {
      await page.goto('/products');
      
      // Click add product button
      await page.click('[data-testid="add-product-button"]');
      
      // Fill product form
      await page.fill('input[name="name"]', 'Test Product');
      await page.fill('input[name="barcode"]', '1234567890');
      await page.fill('input[name="price"]', '99.99');
      await page.fill('input[name="cost"]', '50.00');
      await page.fill('input[name="stockQty"]', '100');
      
      // Select category
      await page.selectOption('select[name="categoryId"]', { label: 'General' });
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show success message
      await expect(page.locator('text=تم إضافة المنتج بنجاح')).toBeVisible();
      
      // Should appear in product list
      await expect(page.locator('text=Test Product')).toBeVisible();
    });

    test('should update product stock', async ({ page }) => {
      await page.goto('/products');
      
      // Click stock update button
      await page.click('[data-testid="update-stock-button"]:first-child');
      
      // Enter new stock quantity
      await page.fill('input[name="newStock"]', '150');
      
      // Submit
      await page.click('button[type="submit"]');
      
      // Should show success message
      await expect(page.locator('text=تم تحديث المخزون بنجاح')).toBeVisible();
    });

    test('should show low stock warning', async ({ page }) => {
      await page.goto('/products');
      
      // Should show low stock indicator for products with low stock
      await expect(page.locator('[data-testid="low-stock-warning"]')).toBeVisible();
    });
  });

  test.describe('Invoice Management', () => {
    test('should create a new invoice', async ({ page }) => {
      await page.goto('/invoices');
      
      // Click create invoice button
      await page.click('[data-testid="create-invoice-button"]');
      
      // Select customer
      await page.selectOption('select[name="customerId"]', { index: 1 });
      
      // Add product to invoice
      await page.click('[data-testid="add-product-button"]');
      await page.selectOption('select[name="productId"]', { index: 1 });
      await page.fill('input[name="quantity"]', '2');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show success message
      await expect(page.locator('text=تم إنشاء الفاتورة بنجاح')).toBeVisible();
      
      // Should redirect to invoice details
      await expect(page).toHaveURL(/.*\/invoices\/\d+/);
    });

    test('should calculate invoice totals correctly', async ({ page }) => {
      await page.goto('/invoices/create');
      
      // Select customer
      await page.selectOption('select[name="customerId"]', { index: 1 });
      
      // Add product with quantity 2 and price 50
      await page.click('[data-testid="add-product-button"]');
      await page.selectOption('select[name="productId"]', { index: 1 });
      await page.fill('input[name="quantity"]', '2');
      
      // Check if subtotal is calculated correctly (2 * 50 = 100)
      await expect(page.locator('[data-testid="subtotal"]')).toHaveText('100.00');
      
      // Add tax (5%)
      await page.fill('input[name="taxRate"]', '5');
      
      // Check if total is calculated correctly (100 + 5 = 105)
      await expect(page.locator('[data-testid="total"]')).toHaveText('105.00');
    });

    test('should print invoice', async ({ page }) => {
      await page.goto('/invoices/1');
      
      // Click print button
      await page.click('[data-testid="print-invoice-button"]');
      
      // Should open print dialog (we can't test actual printing, but can test the action)
      // This would typically open a new window or trigger browser print dialog
    });

    test('should update invoice status', async ({ page }) => {
      await page.goto('/invoices/1');
      
      // Change status to paid
      await page.selectOption('select[name="paymentStatus"]', 'PAID');
      await page.click('[data-testid="update-status-button"]');
      
      // Should show success message
      await expect(page.locator('text=تم تحديث حالة الفاتورة')).toBeVisible();
      
      // Status should be updated
      await expect(page.locator('[data-testid="invoice-status"]')).toHaveText('مدفوعة');
    });
  });

  test.describe('Dashboard Analytics', () => {
    test('should display key metrics', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Should show revenue metrics
      await expect(page.locator('[data-testid="total-revenue"]')).toBeVisible();
      await expect(page.locator('[data-testid="monthly-revenue"]')).toBeVisible();
      
      // Should show customer count
      await expect(page.locator('[data-testid="customer-count"]')).toBeVisible();
      
      // Should show product count
      await expect(page.locator('[data-testid="product-count"]')).toBeVisible();
      
      // Should show pending invoices
      await expect(page.locator('[data-testid="pending-invoices"]')).toBeVisible();
    });

    test('should display charts', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Should show revenue chart
      await expect(page.locator('[data-testid="revenue-chart"]')).toBeVisible();
      
      // Should show sales chart
      await expect(page.locator('[data-testid="sales-chart"]')).toBeVisible();
    });

    test('should show recent activities', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Should show recent invoices
      await expect(page.locator('[data-testid="recent-invoices"]')).toBeVisible();
      
      // Should show recent customers
      await expect(page.locator('[data-testid="recent-customers"]')).toBeVisible();
    });
  });

  test.describe('Reports', () => {
    test('should generate sales report', async ({ page }) => {
      await page.goto('/reports');
      
      // Select date range
      await page.fill('input[name="startDate"]', '2023-01-01');
      await page.fill('input[name="endDate"]', '2023-12-31');
      
      // Generate report
      await page.click('[data-testid="generate-sales-report"]');
      
      // Should show report data
      await expect(page.locator('[data-testid="sales-report-table"]')).toBeVisible();
    });

    test('should export report to PDF', async ({ page }) => {
      await page.goto('/reports');
      
      // Generate report first
      await page.click('[data-testid="generate-sales-report"]');
      
      // Export to PDF
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="export-pdf-button"]');
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toContain('.pdf');
    });
  });
});
