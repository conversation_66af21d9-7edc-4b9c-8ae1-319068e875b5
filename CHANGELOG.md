# سجل التغييرات

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

تنسيق هذا الملف يستند إلى [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يلتزم بـ [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.0] - 2024-06-15

### إضافات

- إضافة دعم تطبيق سطح المكتب باستخدام Electron
- إضافة واجهة مستخدم لعرض ميزات تطبيق سطح المكتب
- إضافة وظائف مساعدة للتفاعل مع Electron
- إضافة تعريفات TypeScript لواجهة برمجة تطبيقات Electron
- إضافة ملفات توثيق مهمة مثل CONTRIBUTING.md و CODE_OF_CONDUCT.md و SECURITY.md و ROADMAP.md
- إضافة ملفات تكوين مثل .editorconfig و .gitattributes و .npmrc و .nvmrc
- إضافة ملفات Docker مثل Dockerfile و docker-compose.yml
- إضافة ملفات GitHub مثل ISSUE_TEMPLATE و PULL_REQUEST_TEMPLATE و workflows
- إضافة دعم تغيير العملة
- إضافة ميزة تتبع المصاريف
- إضافة ميزة إدارة الائتمان
- إضافة ميزة إدارة المخزون
- إضافة ميزة إدارة الميزانية
- إضافة ميزة إدارة الضرائب
- إضافة ميزة تتبع المشتريات
- إضافة ميزة إدارة النفايات

### تغييرات

- تحسين هيكل المشروع ليكون أكثر قابلية للصيانة والتطوير
- تحسين التوثيق وإضافة تعليقات للكود
- تحسين ملفات التكوين لتسهيل عملية التطوير
- تحديث ملف README.md ليشمل معلومات أكثر تفصيلاً
- تحسين واجهة المستخدم
- تحسين أداء التطبيق
- تحسين تجربة المستخدم

### إصلاحات

- إصلاح مشاكل الاختبارات
- إصلاح مشكلة مكون breadcrumb.tsx لجعل الخصائص للقراءة فقط (readonly)
- إصلاح مشكلة في ملف route.ts للتسجيل
- إصلاح مشاكل في التوافق مع المتصفحات المختلفة

## [1.0.0] - 2023-12-01

### الميزات الأساسية

- إطلاق النسخة الأولى من التطبيق
- إضافة ميزة إدارة العملاء
- إضافة ميزة إدارة الفواتير
- إضافة ميزة إدارة المنتجات
- إضافة ميزة التقارير
- إضافة ميزة إدارة المستخدمين
- إضافة ميزة إدارة الصلاحيات
- إضافة ميزة المصادقة
- إضافة ميزة الإعدادات

### التحسينات

- تحسين واجهة المستخدم
- تحسين أداء التطبيق
- تحسين تجربة المستخدم

### الإصلاحات

- إصلاح مشاكل في الاختبارات
- إصلاح مشاكل في التوافق مع المتصفحات المختلفة
