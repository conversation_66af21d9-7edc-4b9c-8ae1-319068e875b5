# متغيرات البيئة لمشروع أمين بلس
# Environment variables for Amin Plus project

# قاعدة البيانات
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/aminplus"

# المصادقة
# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# واجهة برمجة التطبيق
# API
API_URL="http://localhost:3000/api"

# الإعدادات العامة
# General settings
DEFAULT_CURRENCY="AED"
DEFAULT_LANGUAGE="ar"
DEFAULT_TIMEZONE="Asia/Dubai"
DEFAULT_TAX_RATE="5"

# إعدادات البريد الإلكتروني
# Email settings
SMTP_HOST="smtp.example.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-password"
SMTP_FROM="<EMAIL>"

# إعدادات التخزين
# Storage settings
STORAGE_PROVIDER="local" # local, s3, etc.
S3_ACCESS_KEY="your-access-key"
S3_SECRET_KEY="your-secret-key"
S3_BUCKET="your-bucket-name"
S3_REGION="your-region"

# إعدادات التطوير
# Development settings
NODE_ENV="development" # development, production, test
