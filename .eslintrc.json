{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:jsx-a11y/recommended", "prettier"], "plugins": ["react", "@typescript-eslint", "jsx-a11y", "prettier"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/jsx-props-no-spreading": "off", "react/no-unescaped-entities": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "jsx-a11y/anchor-is-valid": "off", "prettier/prettier": ["warn", {}, {"usePrettierrc": true}], "no-console": ["warn", {"allow": ["warn", "error"]}]}, "settings": {"react": {"version": "detect"}}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2021, "sourceType": "module"}, "env": {"browser": true, "node": true, "es6": true, "jest": true}, "ignorePatterns": ["node_modules/", ".next/", "out/", "public/", "*.config.js", "*.config.mjs"]}