# دليل النشر - نظام أمين بلس

## نظرة عامة

هذا الدليل يوضح كيفية نشر نظام أمين بلس في بيئات الإنتاج المختلفة مع ضمان الأمان والأداء الأمثل.

## متطلبات الإنتاج

### الحد الأدنى للمتطلبات

- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **Network**: 100 Mbps
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+

### المتطلبات الموصى بها

- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: 1 Gbps
- **Load Balancer**: للتطبيقات عالية التوفر

## إعداد البيئة

### 1. إعداد الخادم

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# تثبيت PM2 لإدارة العمليات
sudo npm install -g pm2

# تثبيت Nginx
sudo apt install nginx -y

# تثبيت PostgreSQL
sudo apt install postgresql postgresql-contrib -y
```

### 2. إعداد قاعدة البيانات

```bash
# إنشاء مستخدم قاعدة البيانات
sudo -u postgres createuser --interactive aminplus

# إنشاء قاعدة البيانات
sudo -u postgres createdb aminplus_production

# تعيين كلمة مرور
sudo -u postgres psql
ALTER USER aminplus PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE aminplus_production TO aminplus;
\q
```

### 3. إعداد SSL/TLS

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx -y

# الحصول على شهادة SSL
sudo certbot --nginx -d yourdomain.com
```

## نشر التطبيق

### 1. تحضير الكود

```bash
# استنساخ المشروع
git clone https://github.com/your-org/amin-plus.git
cd amin-plus

# تثبيت التبعيات
npm ci --only=production

# بناء التطبيق
npm run build
```

### 2. إعداد متغيرات البيئة

```bash
# إنشاء ملف البيئة
sudo nano /opt/amin-plus/.env.production
```

```env
# قاعدة البيانات
DATABASE_URL="postgresql://aminplus:secure_password_here@localhost:5432/aminplus_production"

# NextAuth
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="production-secret-key-very-long-and-secure"

# الأمان
CORS_ORIGINS="https://yourdomain.com"
ENCRYPTION_KEY="production-encryption-key-32-chars"
JWT_SECRET="production-jwt-secret-key"

# النسخ الاحتياطية
BACKUP_ENABLED="true"
BACKUP_INTERVAL_HOURS="6"
BACKUP_RETENTION_DAYS="30"

# المراقبة
LOG_LEVEL="warn"
RATE_LIMIT_ENABLED="true"

# البريد الإلكتروني
EMAIL_SERVER_HOST="smtp.yourdomain.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="email_password"
EMAIL_FROM="<EMAIL>"
```

### 3. تطبيق migrations

```bash
# تطبيق migrations على قاعدة البيانات
npx prisma migrate deploy
npx prisma generate
```

### 4. إعداد PM2

```bash
# إنشاء ملف تكوين PM2
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'amin-plus',
    script: 'npm',
    args: 'start',
    cwd: '/opt/amin-plus',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/amin-plus/error.log',
    out_file: '/var/log/amin-plus/out.log',
    log_file: '/var/log/amin-plus/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

```bash
# إنشاء مجلد السجلات
sudo mkdir -p /var/log/amin-plus
sudo chown $USER:$USER /var/log/amin-plus

# بدء التطبيق
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 5. إعداد Nginx

```bash
# إنشاء ملف تكوين Nginx
sudo nano /etc/nginx/sites-available/amin-plus
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/auth/login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files caching
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /favicon.ico {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/amin-plus /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## نشر باستخدام Docker

### 1. إنشاء Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 2. إنشاء docker-compose.yml

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/aminplus
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: aminplus
      POSTGRES_USER: aminplus
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. تشغيل Docker

```bash
# بناء وتشغيل الحاويات
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f
```

## المراقبة والصيانة

### 1. إعداد المراقبة

```bash
# تثبيت htop لمراقبة النظام
sudo apt install htop -y

# مراقبة PM2
pm2 monit

# مراقبة سجلات التطبيق
pm2 logs amin-plus

# مراقبة Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. النسخ الاحتياطية

```bash
# إنشاء script للنسخ الاحتياطية
sudo nano /opt/scripts/backup.sh
```

```bash
#!/bin/bash

# متغيرات
DB_NAME="aminplus_production"
DB_USER="aminplus"
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# إنشاء مجلد النسخ الاحتياطية
mkdir -p $BACKUP_DIR

# نسخة احتياطية من قاعدة البيانات
pg_dump -U $DB_USER -h localhost $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# ضغط النسخة الاحتياطية
gzip $BACKUP_DIR/db_backup_$DATE.sql

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: db_backup_$DATE.sql.gz"
```

```bash
# جعل الملف قابل للتنفيذ
sudo chmod +x /opt/scripts/backup.sh

# إضافة مهمة cron للنسخ الاحتياطية اليومية
sudo crontab -e
# إضافة السطر التالي:
0 2 * * * /opt/scripts/backup.sh
```

### 3. التحديثات

```bash
# إنشاء script للتحديث
sudo nano /opt/scripts/update.sh
```

```bash
#!/bin/bash

cd /opt/amin-plus

# إيقاف التطبيق
pm2 stop amin-plus

# تحديث الكود
git pull origin main

# تثبيت التبعيات الجديدة
npm ci --only=production

# تطبيق migrations
npx prisma migrate deploy

# بناء التطبيق
npm run build

# إعادة تشغيل التطبيق
pm2 start amin-plus

echo "Update completed successfully"
```

## الأمان في الإنتاج

### 1. جدار الحماية

```bash
# تفعيل UFW
sudo ufw enable

# السماح بـ SSH
sudo ufw allow ssh

# السماح بـ HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# منع الوصول المباشر لقاعدة البيانات
sudo ufw deny 5432
```

### 2. تحديثات الأمان

```bash
# تحديثات تلقائية للأمان
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 3. مراقبة الأمان

```bash
# تثبيت fail2ban
sudo apt install fail2ban -y

# إعداد fail2ban للنظام
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **التطبيق لا يبدأ**
   ```bash
   pm2 logs amin-plus
   ```

2. **مشاكل قاعدة البيانات**
   ```bash
   sudo -u postgres psql aminplus_production
   ```

3. **مشاكل SSL**
   ```bash
   sudo certbot renew --dry-run
   ```

4. **مشاكل الذاكرة**
   ```bash
   pm2 restart amin-plus
   ```

## الدعم

للحصول على المساعدة في النشر:
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: [docs/](docs/)
- **GitHub Issues**: للمشاكل التقنية
