# دليل تحديث مشروع أمين بلس

هذا الدليل يوضح كيفية تحديث مشروع أمين بلس إلى أحدث إصدار.

## قبل التحديث

1. قم بعمل نسخة احتياطية من قاعدة البيانات والملفات المهمة:

```bash
# نسخ قاعدة البيانات
cp prisma/dev.db prisma/dev.db.backup

# نسخ ملفات البيئة
cp .env .env.backup
cp .env.local .env.local.backup
```

2. تأكد من حفظ جميع التغييرات في Git:

```bash
git add .
git commit -m "حفظ التغييرات قبل التحديث"
```

## خطوات التحديث

### 1. تحديث التبعيات

```bash
# تحديث التبعيات
npm update

# تثبيت التبعيات الجديدة
npm install
```

### 2. تحديث قاعدة البيانات

```bash
# تطبيق التغييرات على قاعدة البيانات
npx prisma migrate dev
```

### 3. تحديث ملفات البيئة

تأكد من تحديث ملفات البيئة (.env و .env.local) بأي متغيرات جديدة مطلوبة.

### 4. تشغيل التطبيق

```bash
# تشغيل التطبيق في وضع التطوير
npm run dev

# تشغيل تطبيق سطح المكتب
npm run electron:dev
```

## بعد التحديث

1. تأكد من أن جميع الميزات تعمل بشكل صحيح.
2. قم بتشغيل الاختبارات للتأكد من عدم وجود أخطاء:

```bash
npm test
```

3. قم بتحديث التوثيق إذا لزم الأمر.

## حل المشاكل الشائعة

### مشاكل قاعدة البيانات

إذا واجهت مشاكل في قاعدة البيانات، يمكنك إعادة تعيينها:

```bash
npx prisma migrate reset
```

### مشاكل التبعيات

إذا واجهت مشاكل في التبعيات، يمكنك حذف مجلد node_modules وإعادة تثبيت التبعيات:

```bash
rm -rf node_modules
npm install
```

### مشاكل Electron

إذا واجهت مشاكل في تطبيق Electron، تأكد من تحديث ملف main.js وpreload.js بأي تغييرات جديدة.

## ملاحظات إضافية

- تأكد من تحديث رقم المنفذ في ملفات التكوين إذا تم تغييره.
- تأكد من تحديث معلومات تسجيل الدخول إذا تم تغييرها.
- تأكد من تحديث أي مسارات URL إذا تم تغييرها.
