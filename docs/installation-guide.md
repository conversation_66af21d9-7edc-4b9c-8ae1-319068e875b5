# دليل تثبيت مشروع أمين بلس

هذا الدليل يوضح كيفية تثبيت وإعداد مشروع أمين بلس.

## متطلبات النظام

- Node.js 18.0.0 أو أحدث
- npm أو yarn أو pnpm
- قاعدة بيانات (يدعم PostgreSQL، MySQL، SQLite، SQL Server)

## خطوات التثبيت

### 1. استنساخ المشروع

```bash
git clone https://github.com/yourusername/amin-plus.git
cd amin-plus
```

### 2. تثبيت التبعيات

```bash
npm install
# أو
yarn install
# أو
pnpm install
```

### 3. إعداد ملف البيئة

```bash
cp .env.example .env
```

قم بتعديل ملف البيئة (.env) لإعداد قاعدة البيانات وإعدادات أخرى:

```
DATABASE_URL="file:./prisma/dev.db"
NEXTAUTH_SECRET="aminplus-secure-secret-key"
NEXTAUTH_URL="http://localhost:3002"
JWT_SECRET="aminplus-jwt-secure-secret-key"
```

### 4. إنشاء قاعدة البيانات

```bash
npx prisma migrate dev
```

### 5. تهيئة قاعدة البيانات بالبيانات الأولية

```bash
npx prisma db seed
```

### 6. تشغيل التطبيق في وضع التطوير

```bash
npm run dev
```

يمكنك الآن الوصول إلى التطبيق على http://localhost:3002

## تشغيل تطبيق سطح المكتب

### 1. تشغيل التطبيق في وضع التطوير

```bash
npm run electron:dev
```

### 2. بناء التطبيق

```bash
npm run electron:build
```

### 3. تشغيل التطبيق المبني

```bash
npm run electron:start
```

## معلومات تسجيل الدخول

بعد تهيئة قاعدة البيانات، يمكنك تسجيل الدخول باستخدام المعلومات التالية:

### المستخدم الرئيسي (Admin)

- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

### مستخدم تجريبي (في بيئة التطوير)

- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password123`

## هيكل المشروع

```bash
amin-plus/
├── electron/            # ملفات تطبيق سطح المكتب (Electron)
│   ├── main.js          # الملف الرئيسي لتطبيق Electron
│   ├── preload.js       # ملف preload لتوفير واجهة برمجة التطبيق
│   └── package.json     # ملف تكوين Electron
├── public/              # الملفات العامة (الصور، الأيقونات، إلخ)
├── src/                 # مصدر الكود
│   ├── app/             # صفحات التطبيق (Next.js App Router)
│   │   ├── api/         # واجهات برمجة التطبيق (API)
│   │   ├── auth/        # صفحات المصادقة
│   │   ├── dashboard/   # صفحات لوحة التحكم
│   │   └── ...
│   ├── components/      # مكونات التطبيق
│   ├── lib/             # مكتبات ووظائف مساعدة
│   ├── types/           # تعريفات الأنواع
│   └── ...
├── prisma/              # نماذج Prisma وملفات الهجرة
└── ...
```

## حل المشاكل الشائعة

### مشاكل قاعدة البيانات

إذا واجهت مشاكل في قاعدة البيانات، يمكنك إعادة تعيينها:

```bash
npx prisma migrate reset
```

### مشاكل التبعيات

إذا واجهت مشاكل في التبعيات، يمكنك حذف مجلد node_modules وإعادة تثبيت التبعيات:

```bash
rm -rf node_modules
npm install
```

### مشاكل المنافذ

إذا كان المنفذ 3002 مستخدمًا بالفعل، يمكنك تغيير المنفذ في ملف package.json:

```json
"dev": "next dev -p 3003"
```

وتحديث المنفذ في ملفات .env و .env.local و electron/main.js.
