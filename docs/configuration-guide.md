# دليل تكوين مشروع أمين بلس

هذا الدليل يوضح كيفية تكوين مشروع أمين بلس حسب احتياجاتك.

## ملفات التكوين

### ملف .env

ملف البيئة الرئيسي للمشروع. يحتوي على المتغيرات التالية:

```
DATABASE_URL="file:./prisma/dev.db"
NEXTAUTH_SECRET="aminplus-secure-secret-key"
NEXTAUTH_URL="http://localhost:3002"
JWT_SECRET="aminplus-jwt-secure-secret-key"
```

- `DATABASE_URL`: رابط قاعدة البيانات
- `NEXTAUTH_SECRET`: مفتاح سري لتشفير جلسات NextAuth.js
- `NEXTAUTH_URL`: عنوان URL للتطبيق
- `JWT_SECRET`: مفتاح سري لتشفير رموز JWT

### ملف .env.local

ملف البيئة المحلي للمشروع. يحتوي على المتغيرات التالية:

```
NEXTAUTH_URL=http://localhost:3002
NEXTAUTH_SECRET=abcdefghijklmnopqrstuvwxyz123456789
DATABASE_URL="file:./dev.db"
NODE_ENV=development
```

### ملف next.config.js

ملف تكوين Next.js. يمكنك تعديل الإعدادات التالية:

- `reactStrictMode`: تفعيل الوضع الصارم في React
- `images`: إعدادات الصور
- `trailingSlash`: التأكد من أن جميع عناوين URL تنتهي بشرطة مائلة

### ملف package.json

ملف تكوين npm. يمكنك تعديل الإعدادات التالية:

- `scripts`: سكريبتات npm
- `dependencies`: التبعيات
- `devDependencies`: تبعيات التطوير
- `build`: إعدادات بناء تطبيق Electron

### ملف electron/main.js

ملف تكوين Electron الرئيسي. يمكنك تعديل الإعدادات التالية:

- `startUrl`: عنوان URL لتحميل التطبيق
- `width`, `height`: أبعاد النافذة الرئيسية
- `minWidth`, `minHeight`: الحد الأدنى لأبعاد النافذة
- `icon`: أيقونة التطبيق

### ملف prisma/schema.prisma

ملف تكوين Prisma. يمكنك تعديل الإعدادات التالية:

- `datasource`: مصدر البيانات
- `generator`: مولد العميل
- نماذج قاعدة البيانات

## تكوين المظهر

### ملف tailwind.config.js

ملف تكوين Tailwind CSS. يمكنك تعديل الإعدادات التالية:

- `theme`: سمة التطبيق
- `colors`: ألوان التطبيق
- `fontFamily`: خطوط التطبيق
- `extend`: توسيع الإعدادات الافتراضية

### ملف src/styles/globals.css

ملف CSS العام للتطبيق. يمكنك تعديل الأنماط العامة للتطبيق.

## تكوين المصادقة

### ملف src/app/api/auth/[...nextauth]/route.ts

ملف تكوين NextAuth.js. يمكنك تعديل الإعدادات التالية:

- `providers`: مزودي المصادقة
- `pages`: صفحات المصادقة
- `session`: إعدادات الجلسة
- `callbacks`: دوال رد الاتصال

## تكوين قاعدة البيانات

### ملف prisma/seed.ts

ملف تهيئة قاعدة البيانات. يمكنك تعديل البيانات الأولية التي يتم إنشاؤها عند تهيئة قاعدة البيانات.

## تكوين Electron

### ملف electron/preload.js

ملف preload لتطبيق Electron. يمكنك تعديل واجهة برمجة التطبيق للتواصل بين العمليات الرئيسية والعمليات المعروضة.

### ملف src/types/electron.d.ts

ملف تعريفات TypeScript لواجهة برمجة تطبيقات Electron. يمكنك تعديل تعريفات الأنواع لواجهة برمجة تطبيقات Electron.
