# دليل تطوير مشروع أمين بلس

هذا الدليل يوضح كيفية تطوير مشروع أمين بلس وإضافة ميزات جديدة.

## بيئة التطوير

### متطلبات التطوير

- Node.js 18.0.0 أو أحدث
- npm أو yarn أو pnpm
- قاعدة بيانات (يدعم PostgreSQL، MySQL، SQLite، SQL Server)
- محرر نصوص (يفضل Visual Studio Code)

### إعداد بيئة التطوير

1. استنساخ المشروع:

```bash
git clone https://github.com/yourusername/amin-plus.git
cd amin-plus
```

2. تثبيت التبعيات:

```bash
npm install
```

3. إعداد ملف البيئة:

```bash
cp .env.example .env
```

4. تهيئة قاعدة البيانات:

```bash
npx prisma migrate dev
npx prisma db seed
```

5. تشغيل التطبيق في وضع التطوير:

```bash
npm run dev
```

## هيكل المشروع

```bash
amin-plus/
├── electron/            # ملفات تطبيق سطح المكتب (Electron)
├── public/              # الملفات العامة (الصور، الأيقونات، إلخ)
├── src/                 # مصدر الكود
│   ├── app/             # صفحات التطبيق (Next.js App Router)
│   │   ├── api/         # واجهات برمجة التطبيق (API)
│   │   ├── auth/        # صفحات المصادقة
│   │   ├── dashboard/   # صفحات لوحة التحكم
│   │   └── ...
│   ├── components/      # مكونات التطبيق
│   ├── lib/             # مكتبات ووظائف مساعدة
│   ├── types/           # تعريفات الأنواع
│   └── ...
├── prisma/              # نماذج Prisma وملفات الهجرة
└── ...
```

## إضافة ميزات جديدة

### إضافة صفحة جديدة

1. إنشاء ملف جديد في مجلد `src/app`:

```tsx
'use client';

import React from 'react';

export default function NewPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">صفحة جديدة</h1>
      <p>محتوى الصفحة الجديدة</p>
    </div>
  );
}
```

2. إضافة رابط للصفحة الجديدة في الشريط الجانبي:

```tsx
// src/components/layout/side-bar.tsx
const navItems = [
  // ...
  {
    title: 'صفحة جديدة',
    href: '/dashboard/new-page',
    icon: Icon,
  },
  // ...
];
```

### إضافة مكون جديد

1. إنشاء ملف جديد في مجلد `src/components`:

```tsx
'use client';

import React from 'react';

interface NewComponentProps {
  title: string;
  children: React.ReactNode;
}

export function NewComponent({ title, children }: NewComponentProps) {
  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <div>{children}</div>
    </div>
  );
}
```

2. استخدام المكون الجديد في الصفحات:

```tsx
import { NewComponent } from '@/components/new-component';

export default function SomePage() {
  return (
    <div>
      <NewComponent title="عنوان المكون">
        <p>محتوى المكون</p>
      </NewComponent>
    </div>
  );
}
```

### إضافة واجهة برمجة تطبيق (API) جديدة

1. إنشاء ملف جديد في مجلد `src/app/api`:

```ts
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(request: Request) {
  try {
    const data = await prisma.someModel.findMany();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب البيانات' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const data = await prisma.someModel.create({
      data: body,
    });
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء البيانات' },
      { status: 500 }
    );
  }
}
```

### إضافة نموذج قاعدة بيانات جديد

1. تعديل ملف `prisma/schema.prisma`:

```prisma
model NewModel {
  id        Int      @id @default(autoincrement())
  name      String
  description String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

2. تطبيق التغييرات على قاعدة البيانات:

```bash
npx prisma migrate dev --name add_new_model
```

## اختبار الكود

### اختبار الوحدات

1. إنشاء ملف اختبار جديد في مجلد `__tests__`:

```tsx
import { render, screen } from '@testing-library/react';
import { NewComponent } from '../new-component';

describe('NewComponent', () => {
  it('renders correctly', () => {
    render(<NewComponent title="عنوان الاختبار">محتوى الاختبار</NewComponent>);
    
    expect(screen.getByText('عنوان الاختبار')).toBeInTheDocument();
    expect(screen.getByText('محتوى الاختبار')).toBeInTheDocument();
  });
});
```

2. تشغيل الاختبارات:

```bash
npm test
```

### اختبار التكامل

1. إنشاء ملف اختبار تكامل جديد:

```tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SomePage } from '../some-page';

describe('SomePage Integration', () => {
  it('interacts correctly', async () => {
    render(<SomePage />);
    
    // اختبار التفاعل
    await userEvent.click(screen.getByText('زر'));
    
    // انتظار النتيجة
    await waitFor(() => {
      expect(screen.getByText('نتيجة')).toBeInTheDocument();
    });
  });
});
```

## نشر التطبيق

### بناء تطبيق الويب

```bash
npm run build
```

### بناء تطبيق سطح المكتب

```bash
npm run electron:build
```

## أفضل الممارسات

- استخدم TypeScript لجميع الملفات
- اكتب اختبارات لجميع المكونات والوظائف
- استخدم التعليقات لتوثيق الكود
- اتبع نمط التسمية المتفق عليه
- استخدم مكونات واجهة المستخدم من مكتبة shadcn/ui
- استخدم Tailwind CSS للتنسيق
- استخدم React Hook Form للنماذج
- استخدم Zod للتحقق من صحة البيانات
