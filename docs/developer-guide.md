# دليل المطور - نظام أمين بلس

## نظرة عامة

نظام أمين بلس هو تطبيق ويب حديث مبني باستخدام Next.js 14 مع TypeScript، ويستخدم Prisma كـ ORM وSQLite كقاعدة بيانات في التطوير.

## البنية التقنية

### التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, TypeScript
- **Backend**: Next.js API Routes
- **Database**: SQLite (تطوير), PostgreSQL (إنتاج)
- **ORM**: Prisma
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS
- **Testing**: Jest, Playwright
- **Security**: bcryptjs, Rate Limiting, Input Validation

### هيكل المشروع

```
amin-plus/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API Routes
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # Dashboard pages
│   │   └── globals.css        # Global styles
│   ├── components/            # React Components
│   │   ├── ui/               # UI Components
│   │   └── __tests__/        # Component tests
│   ├── hooks/                # Custom React Hooks
│   ├── lib/                  # Utility libraries
│   │   ├── __tests__/        # Library tests
│   │   ├── prisma.ts         # Prisma client
│   │   ├── auth-options.ts   # NextAuth configuration
│   │   ├── validation.ts     # Input validation
│   │   ├── encryption.ts     # Encryption utilities
│   │   ├── security-logger.ts # Security logging
│   │   ├── rate-limit.ts     # Rate limiting
│   │   ├── backup-system.ts  # Backup management
│   │   └── health-monitor.ts # System monitoring
│   └── types/                # TypeScript type definitions
├── prisma/                   # Database schema and migrations
├── tests/                    # Test files
│   └── e2e/                 # End-to-end tests
├── docs/                     # Documentation
└── public/                   # Static assets
```

## إعداد بيئة التطوير

### المتطلبات

- Node.js 18+ 
- npm أو yarn
- Git

### التثبيت

```bash
# استنساخ المشروع
git clone https://github.com/your-org/amin-plus.git
cd amin-plus

# تثبيت التبعيات
npm install

# إعداد قاعدة البيانات
npx prisma migrate dev
npx prisma generate

# تشغيل الخادم
npm run dev
```

### متغيرات البيئة

إنشئ ملف `.env.local`:

```env
# Database
DATABASE_URL="file:./dev.db"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# Security
CORS_ORIGINS="http://localhost:3000"
ENCRYPTION_KEY="your-encryption-key"
JWT_SECRET="your-jwt-secret"

# Backup
BACKUP_ENABLED="true"
BACKUP_INTERVAL_HOURS="24"
BACKUP_RETENTION_DAYS="30"
```

## البنية المعمارية

### طبقة قاعدة البيانات

#### نماذج البيانات الرئيسية

```typescript
// User - المستخدمون
model User {
  id           Int           @id @default(autoincrement())
  email        String        @unique
  name         String
  password     String
  isActive     Boolean       @default(true)
  roleId       Int
  role         Role          @relation(fields: [roleId], references: [id])
  securityLogs SecurityLog[]
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
}

// Customer - العملاء
model Customer {
  id        Int      @id @default(autoincrement())
  name      String
  email     String?
  phone     String?
  address   String?
  taxNumber String?
  invoices  Invoice[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Product - المنتجات
model Product {
  id          Int           @id @default(autoincrement())
  name        String
  description String?
  barcode     String        @unique
  price       Float
  cost        Float
  stockQty    Int
  categoryId  Int
  category    Category      @relation(fields: [categoryId], references: [id])
  invoiceItems InvoiceItem[]
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
}
```

### طبقة API

#### حماية المسارات

```typescript
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';

export const GET = protectApiRoute(
  async (request: NextRequest, user: any) => {
    // منطق API
    return NextResponse.json({ data: 'protected data' });
  },
  {
    requiredRole: 'admin',
    requiredPermissions: ['read:customers'],
    rateLimit: RATE_LIMITS.API_GENERAL,
    validateInput: true
  }
);
```

#### التحقق من صحة المدخلات

```typescript
import { validateInput } from '@/lib/validation';

const result = validateInput(requestData, '/api/customers');
if (!result.isValid) {
  return NextResponse.json(
    { errors: result.errors },
    { status: 400 }
  );
}
```

### طبقة الأمان

#### تسجيل الأحداث الأمنية

```typescript
import { logSecurityEvent } from '@/lib/security-logger';

await logSecurityEvent({
  type: 'LOGIN_SUCCESS',
  userId: user.id,
  ip: getClientIP(request),
  details: 'User logged in successfully'
});
```

#### Rate Limiting

```typescript
import { rateLimit, RATE_LIMITS } from '@/lib/rate-limit';

const rateLimitResult = await rateLimit(request, RATE_LIMITS.AUTH_LOGIN);
if (!rateLimitResult.success) {
  return NextResponse.json(
    { error: 'Rate limit exceeded' },
    { status: 429 }
  );
}
```

## إرشادات التطوير

### معايير الكود

#### TypeScript

```typescript
// استخدم types واضحة
interface CustomerData {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

// استخدم enums للثوابت
enum PaymentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  CANCELLED = 'CANCELLED'
}

// استخدم Generic types عند الحاجة
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
```

#### React Components

```typescript
// استخدم functional components مع hooks
interface CustomerFormProps {
  customer?: Customer;
  onSubmit: (data: CustomerData) => void;
  onCancel: () => void;
}

export function CustomerForm({ customer, onSubmit, onCancel }: CustomerFormProps) {
  const [formData, setFormData] = useState<CustomerData>({
    name: customer?.name || '',
    email: customer?.email || '',
    phone: customer?.phone || '',
    address: customer?.address || ''
  });

  // باقي منطق المكون
}
```

### إدارة الحالة

#### استخدام React Hooks

```typescript
// Custom hook للعملاء
export function useCustomers() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCustomers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/customers');
      const data = await response.json();
      setCustomers(data.customers);
    } catch (err) {
      setError('Failed to fetch customers');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  return { customers, loading, error, refetch: fetchCustomers };
}
```

### الاختبارات

#### Unit Tests

```typescript
// src/lib/__tests__/validation.test.ts
import { validateEmail } from '../validation';

describe('validateEmail', () => {
  it('should validate correct email addresses', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  it('should reject invalid email addresses', () => {
    expect(validateEmail('invalid-email')).toBe(false);
    expect(validateEmail('@example.com')).toBe(false);
  });
});
```

#### Integration Tests

```typescript
// src/app/api/__tests__/customers.test.ts
import { NextRequest } from 'next/server';
import { GET, POST } from '../customers/route';

describe('/api/customers', () => {
  it('should return customers list', async () => {
    const request = new NextRequest('http://localhost:3000/api/customers');
    const response = await GET(request);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.customers).toBeDefined();
  });
});
```

#### E2E Tests

```typescript
// tests/e2e/customers.spec.ts
import { test, expect } from '@playwright/test';

test('should create a new customer', async ({ page }) => {
  await page.goto('/customers');
  await page.click('[data-testid="add-customer-button"]');
  
  await page.fill('input[name="name"]', 'Test Customer');
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.click('button[type="submit"]');
  
  await expect(page.locator('text=تم إضافة العميل بنجاح')).toBeVisible();
});
```

## النشر والإنتاج

### إعداد الإنتاج

```bash
# بناء التطبيق
npm run build

# تشغيل في وضع الإنتاج
npm start
```

### متغيرات البيئة للإنتاج

```env
# Database
DATABASE_URL="postgresql://user:password@host:port/database"

# NextAuth
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="production-secret-key"

# Security
CORS_ORIGINS="https://yourdomain.com"
ENCRYPTION_KEY="production-encryption-key"
JWT_SECRET="production-jwt-secret"
```

### Docker

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## المساهمة في المشروع

### Git Workflow

```bash
# إنشاء فرع جديد
git checkout -b feature/new-feature

# إجراء التغييرات والاختبار
npm test
npm run test:e2e

# رفع التغييرات
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature

# إنشاء Pull Request
```

### معايير Commit Messages

```
feat: إضافة ميزة جديدة
fix: إصلاح خطأ
docs: تحديث التوثيق
style: تحسينات التنسيق
refactor: إعادة هيكلة الكود
test: إضافة أو تحديث الاختبارات
chore: مهام صيانة
```

## الأمان والأداء

### أفضل الممارسات الأمنية

1. **التحقق من المدخلات**: استخدم `validateInput` لجميع البيانات
2. **Rate Limiting**: طبق حدود على جميع API endpoints
3. **تسجيل الأحداث**: سجل جميع العمليات الحساسة
4. **التشفير**: استخدم `hashPassword` لكلمات المرور
5. **HTTPS**: استخدم HTTPS في الإنتاج دائماً

### تحسين الأداء

1. **Database Indexing**: أضف فهارس للاستعلامات المتكررة
2. **Caching**: استخدم Next.js caching للصفحات الثابتة
3. **Code Splitting**: قسم الكود لتحسين أوقات التحميل
4. **Image Optimization**: استخدم Next.js Image component

## الدعم والمساعدة

### الموارد

- **التوثيق**: `/docs`
- **API Reference**: `/docs/api`
- **Examples**: `/examples`

### التواصل

- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions
- **Email**: <EMAIL>
