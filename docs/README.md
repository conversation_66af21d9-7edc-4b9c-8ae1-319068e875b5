# توثيق مشروع أمين بلس

هذا المجلد يحتوي على توثيق مشروع أمين بلس.

## محتويات المجلد

- [معلومات تسجيل الدخول](./login-info.md): معلومات تسجيل الدخول للمستخدمين الافتراضيين
- [دليل التثبيت](./installation-guide.md): دليل تثبيت وإعداد المشروع
- [دليل التحديث](./update-guide.md): دليل تحديث المشروع إلى أحدث إصدار
- [دليل التكوين](./configuration-guide.md): دليل تكوين المشروع حسب احتياجاتك
- [دليل التطوير](./development-guide.md): دليل تطوير المشروع وإضافة ميزات جديدة
- [دليل الميزات](./features-guide.md): دليل ميزات المشروع وكيفية استخدامها

## دليل المستخدم

### تسجيل الدخول

1. افتح التطبيق في المتصفح أو تطبيق سطح المكتب
2. أدخل البريد الإلكتروني وكلمة المرور
3. اضغط على زر "تسجيل الدخول"

### لوحة التحكم

بعد تسجيل الدخول، ستظهر لوحة التحكم التي تحتوي على:

- إحصائيات عامة
- الفواتير الأخيرة
- العملاء الجدد
- المنتجات الأكثر مبيعًا
- روابط سريعة للوظائف الأكثر استخدامًا

### إدارة العملاء

يمكنك إدارة العملاء من خلال:

- عرض قائمة العملاء
- إضافة عميل جديد
- تعديل بيانات العميل
- حذف العميل
- عرض فواتير العميل

### إدارة الفواتير

يمكنك إدارة الفواتير من خلال:

- عرض قائمة الفواتير
- إنشاء فاتورة جديدة
- تعديل الفاتورة
- حذف الفاتورة
- طباعة الفاتورة
- تصدير الفاتورة إلى PDF أو Excel

### إدارة المنتجات

يمكنك إدارة المنتجات من خلال:

- عرض قائمة المنتجات
- إضافة منتج جديد
- تعديل بيانات المنتج
- حذف المنتج
- إدارة المخزون

### إدارة المستخدمين

يمكنك إدارة المستخدمين من خلال:

- عرض قائمة المستخدمين
- إضافة مستخدم جديد
- تعديل بيانات المستخدم
- تغيير كلمة المرور
- تعطيل/تفعيل المستخدم
- تعيين الأدوار والصلاحيات

### الإعدادات

يمكنك تعديل إعدادات النظام من خلال:

- إعدادات الشركة
- إعدادات الفواتير
- إعدادات الضرائب
- إعدادات العملة
- إعدادات البريد الإلكتروني
- إعدادات النسخ الاحتياطي

## دليل المطور

### متطلبات التطوير

- Node.js 18.0.0 أو أحدث
- npm أو yarn أو pnpm
- قاعدة بيانات (يدعم PostgreSQL، MySQL، SQLite، SQL Server)

### تثبيت بيئة التطوير

خطوات تثبيت بيئة التطوير:

**الخطوة 1:** استنساخ المشروع:

```bash
git clone https://github.com/yourusername/amin-plus.git
cd amin-plus
```

**الخطوة 2:** تثبيت التبعيات:

```bash
npm install
# أو
yarn install
# أو
pnpm install
```

**الخطوة 3:** إعداد ملف البيئة:

```bash
cp .env.example .env
```

**الخطوة 4:** تعديل ملف البيئة (.env) لإعداد قاعدة البيانات وإعدادات أخرى.

**الخطوة 5:** إنشاء قاعدة البيانات:

```bash
npx prisma migrate dev
```

**الخطوة 6:** تشغيل التطبيق في وضع التطوير:

```bash
npm run dev
```

### تشغيل تطبيق سطح المكتب

خطوات تشغيل تطبيق سطح المكتب:

**الخطوة 1:** تشغيل التطبيق في وضع التطوير:

```bash
npm run electron:dev
```

**الخطوة 2:** بناء التطبيق:

```bash
npm run electron:build
```

**الخطوة 3:** تشغيل التطبيق المبني:

```bash
npm run electron:start
```
