# مرجع API - نظام أمين بلس

## نظرة عامة

يوفر نظام أمين بلس API RESTful شامل لإدارة جميع عمليات النظام. جميع endpoints محمية بنظام مصادقة متقدم ومحدودة بـ rate limiting.

## المصادقة

### تسجيل الدخول

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "user": {
    "id": "1",
    "name": "اسم المستخدم",
    "email": "<EMAIL>",
    "role": "admin"
  },
  "token": "jwt-token"
}
```

### تسجيل الخروج

```http
POST /api/auth/logout
Authorization: Bearer <token>
```

## إدارة العملاء

### الحصول على قائمة العملاء

```http
GET /api/customers
Authorization: Bearer <token>
```

**المعاملات الاختيارية:**
- `page`: رقم الصفحة (افتراضي: 1)
- `limit`: عدد العناصر في الصفحة (افتراضي: 10)
- `search`: البحث في الاسم أو البريد الإلكتروني

**الاستجابة:**
```json
{
  "success": true,
  "customers": [
    {
      "id": 1,
      "name": "أحمد محمد",
      "email": "<EMAIL>",
      "phone": "+971501234567",
      "address": "دبي، الإمارات",
      "taxNumber": "*********",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  }
}
```

### إضافة عميل جديد

```http
POST /api/customers
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "عميل جديد",
  "email": "<EMAIL>",
  "phone": "+971501234567",
  "address": "أبوظبي، الإمارات",
  "taxNumber": "*********"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "customer": {
    "id": 2,
    "name": "عميل جديد",
    "email": "<EMAIL>",
    "phone": "+971501234567",
    "address": "أبوظبي، الإمارات",
    "taxNumber": "*********",
    "createdAt": "2023-01-02T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### تحديث بيانات العميل

```http
PUT /api/customers/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "اسم محدث",
  "email": "<EMAIL>"
}
```

### حذف عميل

```http
DELETE /api/customers/{id}
Authorization: Bearer <token>
```

## إدارة المنتجات

### الحصول على قائمة المنتجات

```http
GET /api/products
Authorization: Bearer <token>
```

**المعاملات الاختيارية:**
- `category`: فلترة حسب الفئة
- `lowStock`: عرض المنتجات منخفضة المخزون فقط
- `search`: البحث في اسم المنتج أو الباركود

**الاستجابة:**
```json
{
  "success": true,
  "products": [
    {
      "id": 1,
      "name": "منتج تجريبي",
      "description": "وصف المنتج",
      "barcode": "*********0",
      "price": 99.99,
      "cost": 50.00,
      "stockQty": 100,
      "categoryId": 1,
      "category": {
        "id": 1,
        "name": "فئة عامة"
      },
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### إضافة منتج جديد

```http
POST /api/products
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "منتج جديد",
  "description": "وصف المنتج الجديد",
  "barcode": "*********0",
  "price": 149.99,
  "cost": 75.00,
  "stockQty": 50,
  "categoryId": 1
}
```

### تحديث المخزون

```http
PATCH /api/products/{id}/stock
Authorization: Bearer <token>
Content-Type: application/json

{
  "quantity": 25,
  "operation": "add", // add, subtract, set
  "notes": "إضافة مخزون جديد"
}
```

## إدارة الفواتير

### الحصول على قائمة الفواتير

```http
GET /api/invoices
Authorization: Bearer <token>
```

**المعاملات الاختيارية:**
- `status`: فلترة حسب حالة الدفع (PENDING, PAID, CANCELLED)
- `customerId`: فلترة حسب العميل
- `startDate`: من تاريخ
- `endDate`: إلى تاريخ

**الاستجابة:**
```json
{
  "success": true,
  "invoices": [
    {
      "id": 1,
      "invoiceNumber": "INV-001",
      "customerId": 1,
      "customer": {
        "id": 1,
        "name": "أحمد محمد"
      },
      "subtotal": 199.98,
      "taxAmount": 10.00,
      "total": 209.98,
      "paymentStatus": "PENDING",
      "notes": "ملاحظات الفاتورة",
      "items": [
        {
          "id": 1,
          "productId": 1,
          "product": {
            "name": "منتج تجريبي"
          },
          "quantity": 2,
          "unitPrice": 99.99,
          "total": 199.98
        }
      ],
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### إنشاء فاتورة جديدة

```http
POST /api/invoices
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerId": 1,
  "items": [
    {
      "productId": 1,
      "quantity": 2,
      "unitPrice": 99.99
    }
  ],
  "taxRate": 5,
  "notes": "ملاحظات الفاتورة"
}
```

### تحديث حالة الدفع

```http
PATCH /api/invoices/{id}/payment-status
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentStatus": "PAID",
  "paidAmount": 209.98,
  "paymentDate": "2023-01-02T00:00:00.000Z",
  "paymentMethod": "CASH"
}
```

## التقارير

### تقرير المبيعات

```http
GET /api/reports/sales
Authorization: Bearer <token>
```

**المعاملات:**
- `startDate`: تاريخ البداية (مطلوب)
- `endDate`: تاريخ النهاية (مطلوب)
- `groupBy`: تجميع حسب (day, week, month)

**الاستجابة:**
```json
{
  "success": true,
  "report": {
    "period": {
      "startDate": "2023-01-01",
      "endDate": "2023-01-31"
    },
    "summary": {
      "totalSales": 15000.00,
      "totalInvoices": 75,
      "averageInvoiceValue": 200.00,
      "totalTax": 750.00
    },
    "dailyBreakdown": [
      {
        "date": "2023-01-01",
        "sales": 500.00,
        "invoices": 3
      }
    ],
    "topProducts": [
      {
        "productId": 1,
        "productName": "منتج تجريبي",
        "quantitySold": 50,
        "revenue": 4999.50
      }
    ]
  }
}
```

### تقرير المخزون

```http
GET /api/reports/inventory
Authorization: Bearer <token>
```

**الاستجابة:**
```json
{
  "success": true,
  "report": {
    "totalProducts": 150,
    "totalValue": 75000.00,
    "lowStockProducts": [
      {
        "id": 1,
        "name": "منتج منخفض المخزون",
        "currentStock": 5,
        "minimumStock": 10
      }
    ],
    "categoryBreakdown": [
      {
        "categoryId": 1,
        "categoryName": "فئة عامة",
        "productCount": 50,
        "totalValue": 25000.00
      }
    ]
  }
}
```

## إدارة النظام

### فحص صحة النظام

```http
GET /api/health
```

**الاستجابة:**
```json
{
  "overall": "healthy",
  "checks": [
    {
      "service": "database",
      "status": "healthy",
      "responseTime": 50,
      "details": "Connected successfully"
    }
  ],
  "uptime": 3600,
  "memory": {
    "used": 52428800,
    "total": 104857600,
    "percentage": 50
  }
}
```

### النسخ الاحتياطية

#### إنشاء نسخة احتياطية

```http
POST /api/backup
Authorization: Bearer <token>
Content-Type: application/json

{
  "compression": true,
  "encryption": true
}
```

#### قائمة النسخ الاحتياطية

```http
GET /api/backup
Authorization: Bearer <token>
```

**الاستجابة:**
```json
{
  "success": true,
  "backups": [
    {
      "fileName": "backup-2023-01-01T00-00-00-000Z.db.gz.enc",
      "size": 1048576,
      "sizeFormatted": "1 MB",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "isEncrypted": true,
      "isCompressed": true
    }
  ]
}
```

### سجلات الأمان

```http
GET /api/security-logs
Authorization: Bearer <token>
```

**المعاملات الاختيارية:**
- `type`: نوع الحدث
- `severity`: مستوى الخطورة
- `startDate`: من تاريخ
- `endDate`: إلى تاريخ

## رموز الحالة

- `200`: نجح الطلب
- `201`: تم إنشاء المورد بنجاح
- `400`: خطأ في البيانات المرسلة
- `401`: غير مصرح بالوصول
- `403`: ممنوع - صلاحيات غير كافية
- `404`: المورد غير موجود
- `429`: تم تجاوز حد الطلبات
- `500`: خطأ في الخادم

## Rate Limiting

| Endpoint | الحد الأقصى | النافذة الزمنية |
|----------|-------------|-----------------|
| `/api/auth/login` | 5 طلبات | 15 دقيقة |
| `/api/auth/register` | 3 طلبات | ساعة واحدة |
| `/api/*` (عام) | 100 طلب | دقيقة واحدة |
| العمليات الحساسة | 10 طلبات | دقيقة واحدة |

## أمثلة الاستخدام

### JavaScript/TypeScript

```typescript
// إنشاء عميل جديد
const createCustomer = async (customerData: CustomerData) => {
  const response = await fetch('/api/customers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(customerData)
  });
  
  if (!response.ok) {
    throw new Error('Failed to create customer');
  }
  
  return response.json();
};
```

### cURL

```bash
# الحصول على قائمة العملاء
curl -X GET "https://api.aminplus.com/api/customers" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# إنشاء عميل جديد
curl -X POST "https://api.aminplus.com/api/customers" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "عميل جديد",
    "email": "<EMAIL>",
    "phone": "+971501234567"
  }'
```

## معالجة الأخطاء

جميع الاستجابات تتبع نفس التنسيق:

```json
{
  "success": false,
  "error": "رسالة الخطأ",
  "details": ["تفاصيل إضافية عن الخطأ"],
  "code": "ERROR_CODE"
}
```

## الدعم

للحصول على المساعدة في استخدام API:
- **التوثيق**: `/docs`
- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: للإبلاغ عن الأخطاء
