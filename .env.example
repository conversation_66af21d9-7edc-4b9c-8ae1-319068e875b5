# متغيرات البيئة لمشروع أمين بلس
# Environment variables for Amin Plus project

# معلومات قاعدة البيانات
# Database information
DATABASE_URL="file:./prisma/dev.db"
# DATABASE_URL="postgresql://username:password@localhost:5432/aminplus"
# DATABASE_URL="mysql://username:password@localhost:3306/aminplus"

# إعدادات NextAuth.js
# NextAuth.js settings
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="HYVkTs4Hs95BjT3PXGr6jYrTuDqDy3Byt8Mt1jFFC9Y="
# وقت انتهاء الجلسة (بالثواني)
# Session expiration time (in seconds)
SESSION_MAXAGE=86400

# إعدادات API
# API Configuration
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
# حد معدل API (في الدقيقة)
# API rate limit (per minute)
RATE_LIMIT=60

# توافق Electron/Tauri
# Electron/Tauri Compatibility
NEXT_PUBLIC_ELECTRON=true
NEXT_PUBLIC_TAURI=false

# التخزين المؤقت والأداء
# Cache and Performance
CACHE_TTL=3600
REVALIDATE_TIME=60

# الأمان
# Security
CORS_ORIGINS="http://localhost:3000,http://localhost:3001"
SECURITY_HEADERS_ENABLED=true
JWT_SECRET="your-jwt-secret-key-here"
JWT_EXPIRES_IN="30d"

# التسجيل والمراقبة
# Logging and Monitoring
LOG_LEVEL="info"
SENTRY_DSN=""

# الخدمات الخارجية
# External Services
# معرفات المصادقة الاجتماعية (اختياري)
# Social Authentication IDs (optional)
GITHUB_ID=""
GITHUB_SECRET=""
GOOGLE_ID=""
GOOGLE_SECRET=""

# وسائط التخزين (اختياري)
# Storage Media (optional)
CLOUDINARY_CLOUD_NAME=""
CLOUDINARY_API_KEY=""
CLOUDINARY_API_SECRET=""

# إعدادات البريد الإلكتروني
# Email settings
SMTP_HOST="smtp.example.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-smtp-password"
SMTP_FROM="Amin Plus <<EMAIL>>"

# ميزات التطبيق
# Application Features
FEATURE_FLAG_DARK_MODE=true
FEATURE_FLAG_NOTIFICATIONS=true
FEATURE_FLAG_OFFLINE_MODE=true

# إعدادات التطبيق
# Application settings
APP_NAME="أمين بلس"
APP_URL="http://localhost:3000"
APP_LOGO_URL="http://localhost:3000/logo.png"
APP_CURRENCY="درهم"
APP_CURRENCY_SYMBOL="د.إ"
APP_TAX_RATE="5"
APP_LANGUAGE="ar"

# معلومات المنتج
# Product Information
NEXT_PUBLIC_APP_NAME="أمين بلس | Amin Plus"
NEXT_PUBLIC_APP_DESCRIPTION="نظام إدارة الأعمال والمحاسبة المتكامل"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_LOGO_URL="http://localhost:3000/logo.png"
NEXT_PUBLIC_APP_CURRENCY="درهم"
NEXT_PUBLIC_APP_CURRENCY_SYMBOL="د.إ"
NEXT_PUBLIC_APP_TAX_RATE="5"
NEXT_PUBLIC_APP_LANGUAGE="ar"
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"

# إعدادات Electron
# Electron settings
ELECTRON_START_URL="http://localhost:3000"
ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true
