<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Amin Plus Logo</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#4F46E5" offset="0%"></stop>
            <stop stop-color="#7C3AED" offset="100%"></stop>
        </linearGradient>
        <filter x="-15.0%" y="-15.0%" width="130.0%" height="130.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="Logo" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Logo-Icon" filter="url(#filter-2)" transform="translate(25.000000, 25.000000)">
            <circle id="Background" fill="url(#linearGradient-1)" cx="75" cy="75" r="75"></circle>
            <path d="M75,30 C79.9705627,30 84,34.0294373 84,39 L84,111 C84,115.970563 79.9705627,120 75,120 C70.0294373,120 66,115.970563 66,111 L66,39 C66,34.0294373 70.0294373,30 75,30 Z" id="Plus-Vertical" fill="#FFFFFF"></path>
            <path d="M120,75 C120,79.9705627 115.970563,84 111,84 L39,84 C34.0294373,84 30,79.9705627 30,75 C30,70.0294373 34.0294373,66 39,66 L111,66 C115.970563,66 120,70.0294373 120,75 Z" id="Plus-Horizontal" fill="#FFFFFF"></path>
            <path d="M50.5,45 C55.1944204,45 59,48.8055796 59,53.5 L59,96.5 C59,101.19442 55.1944204,105 50.5,105 C45.8055796,105 42,101.19442 42,96.5 L42,53.5 C42,48.8055796 45.8055796,45 50.5,45 Z" id="A-Left" fill="#FFFFFF"></path>
            <path d="M99.5,45 C104.19442,45 108,48.8055796 108,53.5 L108,96.5 C108,101.19442 104.19442,105 99.5,105 C94.8055796,105 91,101.19442 91,96.5 L91,53.5 C91,48.8055796 94.8055796,45 99.5,45 Z" id="A-Right" fill="#FFFFFF"></path>
            <path d="M42,75 C42,70.0294373 45.8055796,66 50.5,66 L99.5,66 C104.19442,66 108,70.0294373 108,75 C108,79.9705627 104.19442,84 99.5,84 L50.5,84 C45.8055796,84 42,79.9705627 42,75 Z" id="A-Middle" fill="#FFFFFF"></path>
        </g>
        <text id="Amin-Plus" font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="#4F46E5">
            <tspan x="40" y="180">Amin Plus</tspan>
        </text>
        <text id="أمين-بلس" font-family="Arial-BoldMT, Arial" font-size="20" font-weight="bold" fill="#7C3AED">
            <tspan x="65" y="155">أمين بلس</tspan>
        </text>
    </g>
</svg>
