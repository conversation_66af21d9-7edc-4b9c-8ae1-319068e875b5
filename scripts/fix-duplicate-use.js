#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function getAllFiles(dir) {
  const files = [];
  if (!fs.existsSync(dir)) return files;
  
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...getAllFiles(fullPath));
    } else if (stat.isFile()) {
      files.push(fullPath);
    }
  }

  return files;
}

const pageFiles = getAllFiles('src/app').filter(file => 
  file.endsWith('.tsx')
);

console.log('🔧 إصلاح use المكرر...');

for (const file of pageFiles) {
  let content = fs.readFileSync(file, 'utf8');
  const originalContent = content;

  // إصلاح use المكرر
  content = content.replace(
    /import \{ ([^}]*), use, use \}/g,
    'import { $1, use }'
  );

  content = content.replace(
    /import \{ use, ([^}]*), use \}/g,
    'import { use, $1 }'
  );

  content = content.replace(
    /import \{ ([^}]*), use, ([^}]*), use \}/g,
    'import { $1, use, $2 }'
  );

  if (content !== originalContent) {
    fs.writeFileSync(file, content);
    console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
  }
}

console.log('✅ تم إصلاح use المكرر');
