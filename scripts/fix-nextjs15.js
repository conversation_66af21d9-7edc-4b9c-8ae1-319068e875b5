#!/usr/bin/env node

/**
 * إصلاح مشاكل Next.js 15
 * Fix Next.js 15 compatibility issues
 */

const fs = require('fs');
const path = require('path');

class NextJS15Fixer {
  constructor() {
    this.fixedFiles = 0;
  }

  /**
   * تشغيل الإصلاحات
   */
  async runFixes() {
    console.log('🔧 إصلاح مشاكل Next.js 15...\n');

    // 1. إصلاح ملفات API routes
    await this.fixApiRoutes();
    
    // 2. إصلاح ملفات الصفحات
    await this.fixPages();
    
    // 3. إصلاح ملفات layout
    await this.fixLayouts();

    console.log(`\n✅ تم إصلاح ${this.fixedFiles} ملف`);
  }

  /**
   * إصلاح ملفات API routes
   */
  async fixApiRoutes() {
    console.log('🔧 إصلاح ملفات API routes...');
    
    const apiFiles = this.getAllFiles('src/app/api').filter(file => 
      file.endsWith('route.ts') && file.includes('[')
    );

    for (const file of apiFiles) {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;

      // إصلاح نمط params القديم
      content = content.replace(
        /export async function (GET|POST|PUT|DELETE|PATCH)\(\s*request: NextRequest,\s*\{\s*params\s*\}:\s*\{\s*params:\s*\{\s*([^}]+)\s*\}\s*\}\s*\)/g,
        'export async function $1(\n  request: NextRequest,\n  context: { params: Promise<{ $2 }> }\n) {\n  const params = await context.params;'
      );

      if (content !== originalContent) {
        fs.writeFileSync(file, content);
        this.fixedFiles++;
        console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
      }
    }
  }

  /**
   * إصلاح ملفات الصفحات
   */
  async fixPages() {
    console.log('🔧 إصلاح ملفات الصفحات...');
    
    const pageFiles = this.getAllFiles('src/app').filter(file => 
      file.endsWith('page.tsx') && file.includes('[')
    );

    for (const file of pageFiles) {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;

      // إصلاح نمط params في الصفحات
      content = content.replace(
        /export default function \w+\(\{\s*params\s*\}:\s*\{\s*params:\s*\{\s*([^}]+)\s*\}\s*\}\)/g,
        'export default async function Page({ params }: { params: Promise<{ $1 }> }) {\n  const resolvedParams = await params;'
      );

      // تحديث استخدام params
      content = content.replace(/params\.(\w+)/g, 'resolvedParams.$1');

      if (content !== originalContent) {
        fs.writeFileSync(file, content);
        this.fixedFiles++;
        console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
      }
    }
  }

  /**
   * إصلاح ملفات layout
   */
  async fixLayouts() {
    console.log('🔧 إصلاح ملفات layout...');
    
    const layoutFiles = this.getAllFiles('src/app').filter(file => 
      file.endsWith('layout.tsx') && file.includes('[')
    );

    for (const file of layoutFiles) {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;

      // إصلاح نمط params في layouts
      content = content.replace(
        /export default function \w+\(\{\s*children,\s*params\s*\}:\s*\{\s*children:\s*React\.ReactNode;\s*params:\s*\{\s*([^}]+)\s*\}\s*\}\)/g,
        'export default async function Layout({ children, params }: { children: React.ReactNode; params: Promise<{ $1 }> }) {\n  const resolvedParams = await params;'
      );

      // تحديث استخدام params
      content = content.replace(/params\.(\w+)/g, 'resolvedParams.$1');

      if (content !== originalContent) {
        fs.writeFileSync(file, content);
        this.fixedFiles++;
        console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
      }
    }
  }

  /**
   * الحصول على جميع الملفات
   */
  getAllFiles(dir) {
    const files = [];
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...this.getAllFiles(fullPath));
      } else if (stat.isFile()) {
        files.push(fullPath);
      }
    }

    return files;
  }
}

// تشغيل الإصلاحات
if (require.main === module) {
  const fixer = new NextJS15Fixer();
  fixer.runFixes().catch(console.error);
}

module.exports = NextJS15Fixer;
