#!/usr/bin/env node

/**
 * الإصلاح النهائي لجميع مشاكل Next.js 15
 */

const fs = require('fs');
const path = require('path');

function getAllFiles(dir) {
  const files = [];
  if (!fs.existsSync(dir)) return files;
  
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...getAllFiles(fullPath));
    } else if (stat.isFile()) {
      files.push(fullPath);
    }
  }

  return files;
}

// إصلاح جميع ملفات الصفحات
const pageFiles = getAllFiles('src/app').filter(file => 
  file.endsWith('page.tsx') && file.includes('[')
);

console.log('🔧 إصلاح ملفات الصفحات...');

for (const file of pageFiles) {
  let content = fs.readFileSync(file, 'utf8');
  const originalContent = content;

  // إصلاح client components
  if (content.includes("'use client'")) {
    // تحويل إلى دالة عادية مع params عادي
    content = content.replace(
      /export default async function \w*\(\{\s*params\s*\}:\s*\{\s*params:\s*Promise<[^>]+>\s*\}\)/g,
      'export default function Page({ params }: { params: { id: string } })'
    );
    
    content = content.replace(
      /export default function \w*\(\{\s*params\s*\}:\s*\{\s*params:\s*Promise<[^>]+>\s*\}\)/g,
      'export default function Page({ params }: { params: { id: string } })'
    );
  } else {
    // server components - استخدام async مع Promise
    content = content.replace(
      /export default function \w*\(\{\s*params\s*\}:\s*\{\s*params:\s*\{\s*([^}]+)\s*\}\s*\}\)/g,
      'export default async function Page({ params }: { params: Promise<{ $1 }> }) {\n  const resolvedParams = await params;'
    );
    
    // تحديث استخدام params
    content = content.replace(/\bparams\.(\w+)/g, 'resolvedParams.$1');
  }

  // إزالة الأسطر المكسورة
  content = content.replace(/const resolvedParams = await params;\s*{/g, 'const resolvedParams = await params;');

  if (content !== originalContent) {
    fs.writeFileSync(file, content);
    console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
  }
}

// إصلاح ملفات API
const apiFiles = getAllFiles('src/app/api').filter(file => 
  file.endsWith('route.ts') && file.includes('[')
);

console.log('🔧 إصلاح ملفات API...');

for (const file of apiFiles) {
  let content = fs.readFileSync(file, 'utf8');
  const originalContent = content;

  // إصلاح API routes
  content = content.replace(
    /export async function (GET|POST|PUT|DELETE|PATCH)\(\s*request: NextRequest,\s*\{\s*params\s*\}:\s*\{\s*params:\s*\{\s*([^}]+)\s*\}\s*\}\s*\)/g,
    'export async function $1(\n  request: NextRequest,\n  context: { params: Promise<{ $2 }> }\n) {\n  const params = await context.params;'
  );

  if (content !== originalContent) {
    fs.writeFileSync(file, content);
    console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
  }
}

console.log('✅ تم إصلاح جميع الملفات');
