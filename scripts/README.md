# سكريبتات مشروع أمين بلس

هذا المجلد يحتوي على سكريبتات مفيدة لتطوير وإدارة مشروع أمين بلس.

## السكريبتات المتاحة

### setup-db.sh

سكريبت لتهيئة قاعدة البيانات وملؤها بالبيانات الأولية.

```bash
./scripts/setup-db.sh
```

هذا السكريبت يقوم بما يلي:
1. إعادة تعيين قاعدة البيانات (حذف جميع البيانات الموجودة)
2. تطبيق جميع ملفات الترحيل (migrations)
3. ملء قاعدة البيانات بالبيانات الأولية
4. عرض معلومات تسجيل الدخول

### معلومات تسجيل الدخول

بعد تشغيل سكريبت `setup-db.sh`، يمكنك تسجيل الدخول باستخدام المعلومات التالية:

- **المستخدم الرئيسي (Admin):**
  - البريد الإلكتروني: `<EMAIL>`
  - كلمة المرور: `admin123`

- **مستخدم تجريبي (في بيئة التطوير):**
  - البريد الإلكتروني: `<EMAIL>`
  - كلمة المرور: `password123`

## إضافة سكريبتات جديدة

إذا كنت ترغب في إضافة سكريبت جديد، يرجى اتباع الإرشادات التالية:

1. إنشاء ملف جديد في مجلد `scripts`
2. جعل الملف قابلاً للتنفيذ باستخدام الأمر `chmod +x scripts/your-script.sh`
3. إضافة وصف للسكريبت في هذا الملف README.md
4. التأكد من أن السكريبت يعمل بشكل صحيح في جميع بيئات التطوير

## ملاحظات

- يجب تشغيل جميع السكريبتات من مجلد المشروع الرئيسي
- يجب أن تكون جميع السكريبتات متوافقة مع أنظمة التشغيل المختلفة (Linux, macOS, Windows)
- يجب أن تكون جميع السكريبتات مزودة بتعليقات توضيحية
