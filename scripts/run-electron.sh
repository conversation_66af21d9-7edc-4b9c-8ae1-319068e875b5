#!/bin/bash

# سكريبت لتشغيل تطبيق Electron لمشروع أمين بلس
# Script to run Electron app for Amin Plus project

# تعيين متغيرات البيئة
# Set environment variables
export NODE_ENV=development

# الانتقال إلى مجلد المشروع
# Change to project directory
cd "$(dirname "$0")/.."

# عرض رسالة ترحيب
# Display welcome message
echo "==================================================="
echo "  تشغيل تطبيق سطح المكتب أمين بلس"
echo "  Running Amin Plus Desktop Application"
echo "==================================================="

# التحقق من وجود Node.js
# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "خطأ: لم يتم العثور على Node.js. يرجى تثبيت Node.js."
    echo "Error: Node.js not found. Please install Node.js."
    exit 1
fi

# التحقق من وجود npm
# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "خطأ: لم يتم العثور على npm. يرجى تثبيت npm."
    echo "Error: npm not found. Please install npm."
    exit 1
fi

# تثبيت التبعيات إذا لم تكن موجودة
# Install dependencies if not present
if [ ! -d "node_modules" ]; then
    echo "تثبيت التبعيات..."
    echo "Installing dependencies..."
    npm install
fi

# تشغيل التطبيق
# Run the app
echo "تشغيل تطبيق سطح المكتب..."
echo "Running desktop application..."
npm run electron:dev

# عرض رسالة الخروج
# Display exit message
echo ""
echo "==================================================="
echo "تم إغلاق تطبيق سطح المكتب."
echo "Desktop application has been closed."
echo "==================================================="
