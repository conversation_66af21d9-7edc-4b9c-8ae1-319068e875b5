#!/usr/bin/env node

/**
 * إصلاح سريع للملفات المكسورة
 */

const fs = require('fs');
const path = require('path');

const brokenFiles = [
  'src/app/customers/[id]/page.tsx',
  'src/app/dashboard/pos/receipt/[id]/page.tsx',
  'src/app/dashboard/recipes/[id]/page.tsx',
  'src/app/dashboard/recipes/[id]/view/page.tsx'
];

for (const file of brokenFiles) {
  if (fs.existsSync(file)) {
    let content = fs.readFileSync(file, 'utf8');
    
    // إصلاح مشاكل async/await في client components
    if (content.includes("'use client'") && content.includes('export default async function')) {
      content = content.replace('export default async function', 'export default function');
      content = content.replace(/const resolvedParams = await params;\s*{?/g, '');
      content = content.replace(/resolvedParams\./g, 'params.');
      content = content.replace(/\{\s*params\s*\}:\s*\{\s*params:\s*Promise<[^>]+>\s*\}/g, '{ params }: { params: { id: string } }');
    }
    
    // إصلاح الأقواس المفقودة
    const lines = content.split('\n');
    const lastLine = lines[lines.length - 1];
    if (lastLine.trim() === '}' && !content.includes('export default')) {
      // إضافة سطر فارغ في النهاية إذا لزم الأمر
      if (!content.endsWith('\n')) {
        content += '\n';
      }
    }
    
    fs.writeFileSync(file, content);
    console.log(`✅ تم إصلاح ${file}`);
  }
}

console.log('✅ تم إصلاح جميع الملفات المكسورة');
