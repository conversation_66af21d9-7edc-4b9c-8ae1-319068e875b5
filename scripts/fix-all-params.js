#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function getAllFiles(dir) {
  const files = [];
  if (!fs.existsSync(dir)) return files;
  
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...getAllFiles(fullPath));
    } else if (stat.isFile()) {
      files.push(fullPath);
    }
  }

  return files;
}

const pageFiles = getAllFiles('src/app').filter(file => 
  file.endsWith('page.tsx') && file.includes('[')
);

console.log('🔧 إصلاح جميع ملفات params...');

for (const file of pageFiles) {
  let content = fs.readFileSync(file, 'utf8');
  const originalContent = content;

  // إصلاح params type
  content = content.replace(
    /\{\s*params\s*\}:\s*\{\s*params:\s*\{\s*id:\s*string\s*\}\s*\}/g,
    '{ params }: { params: Promise<{ id: string }> }'
  );

  // إضافة use import للـ client components
  if (content.includes("'use client'") && content.includes('params') && !content.includes('import { use }')) {
    content = content.replace(
      /import \{ ([^}]+) \} from 'react';/,
      "import { $1, use } from 'react';"
    );
  }

  // إضافة resolvedParams للـ client components
  if (content.includes("'use client'") && content.includes('params') && !content.includes('const resolvedParams = use(params);')) {
    content = content.replace(
      /export default function \w*\(\{\s*params\s*\}:\s*\{\s*params:\s*Promise<[^>]+>\s*\}\)\s*\{/,
      '$&\n  const resolvedParams = use(params);'
    );
  }

  // تحديث استخدام params في client components
  if (content.includes("'use client'") && content.includes('resolvedParams')) {
    content = content.replace(/\bparams\.(\w+)/g, 'resolvedParams.$1');
  }

  // للـ server components
  if (!content.includes("'use client'") && content.includes('params')) {
    content = content.replace(
      /export default function \w*\(\{\s*params\s*\}:\s*\{\s*params:\s*Promise<[^>]+>\s*\}\)\s*\{/,
      'export default async function Page({ params }: { params: Promise<{ id: string }> }) {\n  const resolvedParams = await params;'
    );
    content = content.replace(/\bparams\.(\w+)/g, 'resolvedParams.$1');
  }

  if (content !== originalContent) {
    fs.writeFileSync(file, content);
    console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
  }
}

console.log('✅ تم إصلاح جميع الملفات');
