#!/usr/bin/env node

/**
 * إصلاح client components لـ Next.js 15
 */

const fs = require('fs');
const path = require('path');

function getAllFiles(dir) {
  const files = [];
  if (!fs.existsSync(dir)) return files;
  
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...getAllFiles(fullPath));
    } else if (stat.isFile()) {
      files.push(fullPath);
    }
  }

  return files;
}

// البحث عن جميع client components مع params
const pageFiles = getAllFiles('src/app').filter(file => 
  file.endsWith('page.tsx') && file.includes('[')
);

console.log('🔧 إصلاح client components...');

for (const file of pageFiles) {
  let content = fs.readFileSync(file, 'utf8');
  const originalContent = content;

  // فقط client components
  if (content.includes("'use client'") && content.includes('params')) {
    
    // إضافة use import إذا لم يكن موجوداً
    if (!content.includes('import { use }') && !content.includes('import { useEffect, useState, use }')) {
      content = content.replace(
        /import \{ ([^}]+) \} from 'react';/,
        "import { $1, use } from 'react';"
      );
    }
    
    // إضافة resolvedParams
    if (!content.includes('const resolvedParams = use(params);')) {
      content = content.replace(
        /export default function \w*\(\{\s*params\s*\}:\s*\{\s*params:\s*Promise<[^>]+>\s*\}\)\s*\{/,
        '$&\n  const resolvedParams = use(params);'
      );
    }
    
    // تحديث استخدام params
    content = content.replace(/\bparams\.(\w+)/g, 'resolvedParams.$1');
    
    // إصلاح dependency arrays
    content = content.replace(/\[params\.(\w+)\]/g, '[resolvedParams.$1]');
  }

  if (content !== originalContent) {
    fs.writeFileSync(file, content);
    console.log(`  ✅ ${path.relative(process.cwd(), file)}`);
  }
}

console.log('✅ تم إصلاح جميع client components');
