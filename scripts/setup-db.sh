#!/bin/bash

# ملف لتهيئة قاعدة البيانات لمشروع أمين بلس
# Script to setup database for Amin Plus project

# تعيين متغيرات البيئة
# Set environment variables
export NODE_ENV=development

# الانتقال إلى مجلد المشروع
# Change to project directory
cd "$(dirname "$0")/.."

# عرض رسالة ترحيب
# Display welcome message
echo "==================================================="
echo "  تهيئة قاعدة البيانات لمشروع أمين بلس"
echo "  Database setup for Amin Plus project"
echo "==================================================="

# التحقق من وجود Prisma
# Check if Prisma is installed
if ! command -v npx &> /dev/null; then
    echo "خطأ: لم يتم العثور على npx. يرجى تثبيت Node.js."
    echo "Error: npx not found. Please install Node.js."
    exit 1
fi

# إنشاء قاعدة البيانات
# Create database
echo "إنشاء قاعدة البيانات..."
echo "Creating database..."
npx prisma migrate reset --force

# تهيئة قاعدة البيانات
# Seed database
echo "تهيئة قاعدة البيانات بالبيانات الأولية..."
echo "Seeding database with initial data..."
npx prisma db seed

# عرض معلومات تسجيل الدخول
# Display login information
echo ""
echo "==================================================="
echo "  معلومات تسجيل الدخول"
echo "  Login information"
echo "==================================================="
echo "البريد الإلكتروني: <EMAIL>"
echo "Email: <EMAIL>"
echo "كلمة المرور: admin123"
echo "Password: admin123"
echo "==================================================="
echo ""
echo "تم تهيئة قاعدة البيانات بنجاح!"
echo "Database setup completed successfully!"
