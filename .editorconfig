# ملف .editorconfig لمشروع أمين بلس
# .editorconfig file for Amin Plus project

# إعدادات أساسية لجميع الملفات
# Basic settings for all files
root = true

[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

# إعدادات خاصة لملفات Markdown
# Special settings for Markdown files
[*.md]
trim_trailing_whitespace = false

# إعدادات خاصة لملفات Python
# Special settings for Python files
[*.py]
indent_size = 4

# إعدادات خاصة لملفات Makefile
# Special settings for Makefile files
[Makefile]
indent_style = tab

# إعدادات خاصة لملفات JSON
# Special settings for JSON files
[*.json]
insert_final_newline = false

# إعدادات خاصة لملفات YAML
# Special settings for YAML files
[*.{yml,yaml}]
indent_size = 2

# إعدادات خاصة لملفات HTML
# Special settings for HTML files
[*.{htm,html}]
indent_size = 2

# إعدادات خاصة لملفات CSS
# Special settings for CSS files
[*.{css,scss,sass}]
indent_size = 2

# إعدادات خاصة لملفات JavaScript و TypeScript
# Special settings for JavaScript and TypeScript files
[*.{js,jsx,ts,tsx}]
indent_size = 2

# إعدادات خاصة لملفات Prisma
# Special settings for Prisma files
[*.prisma]
indent_size = 2

# إعدادات خاصة لملفات SQL
# Special settings for SQL files
[*.sql]
indent_size = 2

# إعدادات خاصة لملفات Shell
# Special settings for Shell files
[*.sh]
indent_size = 2

# إعدادات خاصة لملفات Windows
# Special settings for Windows files
[*.{bat,cmd,ps1}]
end_of_line = crlf
