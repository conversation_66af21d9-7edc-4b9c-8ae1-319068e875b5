import globals from "globals";
import pluginReact from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import nextPlugin from "@next/eslint-plugin-next";

export default [
  {
    files: ["**/*.{js,mjs,cjs,jsx}"],
    ignores: [
      "node_modules/**",
      ".next/**",
      "dist/**",
      "build/**",
      "public/**",
      "coverage/**",
      "*.config.{js,ts}",
      "**/*.d.ts",
      "src/**/*.ts",
      "src/**/*.tsx"
    ],
    languageOptions: {
      ecmaVersion: 2024,
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: { jsx: true }
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2021,
        React: true,
        JSX: true,
        NodeJS: true,
        // إضافة متغيرات Tauri/Electron
        __TAURI__: "readonly",
        global: "writable"
      }
    },
    plugins: {
      react: pluginReact,
      "react-hooks": reactHooks,
      next: nextPlugin
    },
    settings: {
      react: { version: "detect" },
      next: { rootDir: "." }
    },
    rules: {
      // قواعد أساسية
      "no-unused-vars": "off", // تم تعطيلها لتجنب التضارب مع TypeScript

      // قواعد React
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/no-unescaped-entities": "off",
      "react/display-name": "off",

      // قواعد React Hooks
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "off", // تم تعطيلها مؤقتاً

      // قواعد Next.js
      "next/no-html-link-for-pages": "off", // تم تعطيلها مؤقتاً

      // قواعد عامة
      "no-console": "off",
      "prefer-const": "off",
      "eqeqeq": "off"
    }
  }
];