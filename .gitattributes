# ملف .gitattributes لمشروع أمين بلس
# .gitattributes file for Amin Plus project

# تعيين نهاية السطر الافتراضية لجميع الملفات النصية
# Set default line ending for all text files
* text=auto eol=lf

# ملفات النص
# Text files
*.txt text
*.md text
*.json text
*.yaml text
*.yml text
*.html text
*.css text
*.scss text
*.js text
*.jsx text
*.ts text
*.tsx text
*.graphql text
*.sql text
*.prisma text

# ملفات ثنائية
# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.otf binary
*.mp3 binary
*.mp4 binary
*.webm binary
*.ogg binary
*.pdf binary
*.zip binary
*.gz binary
*.tar binary

# ملفات خاصة
# Special files
*.sh text eol=lf
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# ملفات لا يجب تغيير نهاية السطر فيها
# Files that should not have line ending conversion
*.patch -text

# ملفات لا يجب مقارنتها في عمليات الدمج
# Files that should not be diffed in merges
package-lock.json -diff
yarn.lock -diff
pnpm-lock.yaml -diff
*.min.js -diff
*.min.css -diff
