# ملف .dockerignore لمشروع أمين بلس
# .dockerignore file for Amin Plus project

# ملفات ومجلدات النظام
# System files and folders
.git
.github
.vscode
.idea
.DS_Store
Thumbs.db

# ملفات ومجلدات التطوير
# Development files and folders
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.next
out
dist
coverage
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ملفات ومجلدات Electron
# Electron files and folders
electron/node_modules
release

# ملفات ومجلدات التوثيق
# Documentation files and folders
docs
*.md
LICENSE
CHANGELOG.md
CODE_OF_CONDUCT.md
CONTRIBUTING.md
SECURITY.md
ROADMAP.md

# ملفات ومجلدات الاختبار
# Test files and folders
__tests__
__mocks__
*.test.js
*.test.jsx
*.test.ts
*.test.tsx
*.spec.js
*.spec.jsx
*.spec.ts
*.spec.tsx

# ملفات ومجلدات أخرى
# Other files and folders
.husky
.github
.storybook
storybook-static
*.log
*.lock
*.tgz
*.tar.gz
*.zip
