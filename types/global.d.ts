// تعريفات لبيئة Node.js
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test'
    DATABASE_URL: string
    NEXT_PUBLIC_API_URL?: string
    NEXT_PUBLIC_APP_VERSION?: string
  }
}

// تعريفات لاستيراد ملفات الوسائط
declare module '*.svg' {
  const content: React.FunctionComponent<React.SVGAttributes<SVGElement>>
  export default content
}

declare module '*.png' {
  const content: string
  export default content
}

declare module '*.jpg' {
  const content: string
  export default content
}

declare module '*.jpeg' {
  const content: string
  export default content
}

declare module '*.gif' {
  const content: string
  export default content
}

declare module '*.webp' {
  const content: string
  export default content
}

// تعريفات خاصة بـ Tauri
declare interface Window {
  __TAURI__?: {
    invoke: <T>(cmd: string, args?: unknown) => Promise<T>
    event: {
      listen: (event: string, callback: (data: any) => void) => Promise<() => void>
      emit: (event: string, payload?: unknown) => Promise<void>
    }
    fs: {
      readTextFile: (path: string) => Promise<string>
      writeTextFile: (path: string, contents: string) => Promise<void>
    }
    dialog: {
      open: (options?: any) => Promise<string | string[] | null>
      save: (options?: any) => Promise<string | null>
    }
    notification: {
      sendNotification: (options: { title: string; body: string }) => void
    }
    shell: {
      open: (url: string) => Promise<void>
    }
  }
  __TAURI_IPC__?: any

  // تعريفات خاصة بـ Electron
  electron?: {
    showSaveDialog: (options: any) => Promise<{ canceled: boolean; filePath?: string }>
    showOpenDialog: (options: any) => Promise<{ canceled: boolean; filePaths: string[] }>
    showMessageBox: (options: any) => Promise<{ response: number; checkboxChecked: boolean }>
    getSystemInfo: () => {
      platform: string
      arch: string
      version: string
      nodeVersion: string
      chromeVersion: string
    }
  }
}

// تعريفات مساعدة إضافية
declare global {
  var global: typeof globalThis

  // واجهة للإعدادات
  interface AppSettings {
    theme: 'light' | 'dark' | 'system'
    language: string
    notifications: boolean
  }

  // واجهة للمستخدم
  interface User {
    id: string
    name: string
    email: string
  }
}

// متغير عالمي لـ Prisma
const globalForPrisma = typeof global !== 'undefined' ? global : (typeof window !== 'undefined' ? window : {});
export { globalForPrisma };
// تعريفات لـ Prisma
declare global {
  namespace NodeJS {
    interface Global {
      prisma: PrismaClient
    }
  }
}
// تعريفات لـ PrismaClient
import { PrismaClient } from '@prisma/client'
declare global {
  namespace NodeJS {
    interface Global {
      prisma: PrismaClient
    }
  }
}
