import NextAuth from 'next-auth'

declare module 'next-auth' {
    interface User {
        id: string
        name?: string | null
        email: string
        role: string
        permissions: string[]
    }

    interface Session {
        user: {
            id: string
            name?: string | null
            email?: string | null
            role: string
            permissions: string[]
        }
    }
}

declare module 'next-auth/jwt' {
    interface JWT {
        id: string
        role: string
        permissions: string[]
    }
}