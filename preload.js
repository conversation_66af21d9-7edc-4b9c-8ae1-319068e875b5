// src-tauri/preload.js (أو أي مكان عندك)

// استيراد contextBridge من Electron
const { contextBridge, ipcRenderer } = require('electron');

// إصلاح مشكلة "global is not defined" بطريقة آمنة
if (typeof globalThis.global === 'undefined') {
  globalThis.global = globalThis;
}

// كشف واجهة API آمنة إلى النافذة (Window)
contextBridge.exposeInMainWorld('electron', {
  // دوال التواصل الأساسية
  send: (channel, data) => ipcRenderer.send(channel, data),
  receive: (channel, func) => {
    const subscription = (_event, ...args) => func(...args);
    ipcRenderer.on(channel, subscription);
    return () => ipcRenderer.removeListener(channel, subscription);
  },
  invoke: (channel, data) => ipcRenderer.invoke(channel, data),

  // وظائف الواجهة
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  getSystemInfo: () => ipcRenderer.invoke('get-system-info')
});
