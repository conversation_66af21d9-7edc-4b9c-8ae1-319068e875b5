use rusqlite::{Connection, Result};
use std::fs;
use std::path::Path;
use std::sync::Mutex;
use tauri::AppHandle;

// تعريف هيكل قاعدة البيانات
// Database structure definition
pub struct Database {
    pub connection: Mutex<Connection>,
}

impl Database {
    // إنشاء اتصال جديد بقاعدة البيانات
    // Create a new database connection
    pub fn new(app_handle: &AppHandle) -> Result<Self> {
        let app_dir = app_handle
            .path_resolver()
            .app_data_dir()
            .expect("Failed to get app data directory");
        
        // التأكد من وجود المجلد
        // Ensure the directory exists
        fs::create_dir_all(&app_dir).expect("Failed to create app data directory");
        
        let db_path = app_dir.join("amin_plus.db");
        let db_path_str = db_path.to_str().expect("Failed to convert path to string");
        
        println!("Database path: {}", db_path_str);
        
        let connection = Connection::open(&db_path)?;
        
        // تهيئة قاعدة البيانات
        // Initialize the database
        initialize_database(&connection)?;
        
        Ok(Self {
            connection: Mutex::new(connection),
        })
    }
    
    // الحصول على اتصال قاعدة البيانات
    // Get database connection
    pub fn get_connection(&self) -> Result<std::sync::MutexGuard<Connection>> {
        self.connection
            .lock()
            .map_err(|_| rusqlite::Error::InvalidQuery)
    }
}

// تهيئة قاعدة البيانات وإنشاء الجداول
// Initialize database and create tables
fn initialize_database(connection: &Connection) -> Result<()> {
    // إنشاء جداول المستخدمين والصلاحيات
    // Create users and permissions tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            display_name TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS roles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS user_roles (
            user_id INTEGER NOT NULL,
            role_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, role_id),
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS role_permissions (
            role_id INTEGER NOT NULL,
            permission_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (role_id, permission_id),
            FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول العملاء والموردين
    // Create customers and suppliers tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            tax_number TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            tax_number TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS contact_persons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            position TEXT,
            entity_type TEXT NOT NULL,
            entity_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    // إنشاء جداول المنتجات والمخزون
    // Create products and inventory tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            parent_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            sku TEXT,
            barcode TEXT,
            category_id INTEGER,
            purchase_price REAL NOT NULL DEFAULT 0,
            selling_price REAL NOT NULL DEFAULT 0,
            tax_rate REAL NOT NULL DEFAULT 0,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS inventory_locations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            location_id INTEGER NOT NULL,
            quantity REAL NOT NULL DEFAULT 0,
            min_quantity REAL NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
            FOREIGN KEY (location_id) REFERENCES inventory_locations (id) ON DELETE CASCADE,
            UNIQUE (product_id, location_id)
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS inventory_movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            location_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            movement_type TEXT NOT NULL,
            reference_type TEXT,
            reference_id INTEGER,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
            FOREIGN KEY (location_id) REFERENCES inventory_locations (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول المبيعات
    // Create sales tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT NOT NULL UNIQUE,
            customer_id INTEGER,
            invoice_date TIMESTAMP NOT NULL,
            due_date TIMESTAMP,
            subtotal REAL NOT NULL DEFAULT 0,
            tax_amount REAL NOT NULL DEFAULT 0,
            discount_amount REAL NOT NULL DEFAULT 0,
            total_amount REAL NOT NULL DEFAULT 0,
            paid_amount REAL NOT NULL DEFAULT 0,
            status TEXT NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            tax_rate REAL NOT NULL DEFAULT 0,
            tax_amount REAL NOT NULL DEFAULT 0,
            discount_amount REAL NOT NULL DEFAULT 0,
            total_amount REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS invoice_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            payment_date TIMESTAMP NOT NULL,
            amount REAL NOT NULL,
            payment_method TEXT NOT NULL,
            reference TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS invoice_attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول المشتريات
    // Create purchases tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS purchases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_number TEXT NOT NULL UNIQUE,
            supplier_id INTEGER,
            purchase_date TIMESTAMP NOT NULL,
            due_date TIMESTAMP,
            subtotal REAL NOT NULL DEFAULT 0,
            tax_amount REAL NOT NULL DEFAULT 0,
            discount_amount REAL NOT NULL DEFAULT 0,
            total_amount REAL NOT NULL DEFAULT 0,
            paid_amount REAL NOT NULL DEFAULT 0,
            status TEXT NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS purchase_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            unit_price REAL NOT NULL,
            tax_rate REAL NOT NULL DEFAULT 0,
            tax_amount REAL NOT NULL DEFAULT 0,
            discount_amount REAL NOT NULL DEFAULT 0,
            total_amount REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (purchase_id) REFERENCES purchases (id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS purchase_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_id INTEGER NOT NULL,
            payment_date TIMESTAMP NOT NULL,
            amount REAL NOT NULL,
            payment_method TEXT NOT NULL,
            reference TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (purchase_id) REFERENCES purchases (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS purchase_attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_id INTEGER NOT NULL,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (purchase_id) REFERENCES purchases (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول المصروفات
    // Create expenses tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS expense_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            expense_date TIMESTAMP NOT NULL,
            category_id INTEGER,
            amount REAL NOT NULL,
            tax_amount REAL NOT NULL DEFAULT 0,
            description TEXT,
            payment_method TEXT,
            reference TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES expense_categories (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS expense_attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            expense_id INTEGER NOT NULL,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (expense_id) REFERENCES expenses (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول الائتمان
    // Create credits tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS credits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            description TEXT,
            status TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS credit_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            credit_id INTEGER NOT NULL,
            payment_date TIMESTAMP NOT NULL,
            amount REAL NOT NULL,
            payment_method TEXT NOT NULL,
            reference TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (credit_id) REFERENCES credits (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS credit_schedules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            credit_id INTEGER NOT NULL,
            due_date TIMESTAMP NOT NULL,
            amount REAL NOT NULL,
            status TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (credit_id) REFERENCES credits (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول الهدر
    // Create waste tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS waste_reasons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS waste (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            reason_id INTEGER,
            notes TEXT,
            waste_date TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
            FOREIGN KEY (reason_id) REFERENCES waste_reasons (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS waste_attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            waste_id INTEGER NOT NULL,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (waste_id) REFERENCES waste (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول الميزانية
    // Create budget tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS budget_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS budgets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            start_date TIMESTAMP NOT NULL,
            end_date TIMESTAMP NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS budget_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            budget_id INTEGER NOT NULL,
            category_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES budget_categories (id) ON DELETE CASCADE
        )",
        [],
    )?;
    
    // إنشاء جداول الضرائب
    // Create tax tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS tax_rates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            rate REAL NOT NULL,
            is_default BOOLEAN NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS tax_filings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filing_period TEXT NOT NULL,
            start_date TIMESTAMP NOT NULL,
            end_date TIMESTAMP NOT NULL,
            filing_date TIMESTAMP,
            sales_amount REAL NOT NULL DEFAULT 0,
            purchases_amount REAL NOT NULL DEFAULT 0,
            sales_tax REAL NOT NULL DEFAULT 0,
            purchases_tax REAL NOT NULL DEFAULT 0,
            net_tax REAL NOT NULL DEFAULT 0,
            status TEXT NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS tax_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filing_id INTEGER,
            transaction_date TIMESTAMP NOT NULL,
            transaction_type TEXT NOT NULL,
            reference_type TEXT NOT NULL,
            reference_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            tax_amount REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (filing_id) REFERENCES tax_filings (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    // إنشاء جداول النظام
    // Create system tables
    connection.execute(
        "CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT NOT NULL UNIQUE,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS audit_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            entity_type TEXT,
            entity_id INTEGER,
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
        )",
        [],
    )?;
    
    connection.execute(
        "CREATE TABLE IF NOT EXISTS backups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_size INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    
    // إدخال البيانات الأولية
    // Insert initial data
    insert_initial_data(connection)?;
    
    Ok(())
}

// إدخال البيانات الأولية
// Insert initial data
fn insert_initial_data(connection: &Connection) -> Result<()> {
    // التحقق من وجود بيانات في جدول الإعدادات
    // Check if settings table has data
    let settings_count: i64 = connection.query_row(
        "SELECT COUNT(*) FROM settings",
        [],
        |row| row.get(0),
    )?;
    
    if settings_count == 0 {
        // إدخال الإعدادات الافتراضية
        // Insert default settings
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('company_name', 'أمين بلس')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('company_name_en', 'Amin Plus')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('company_address', '')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('company_phone', '')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('company_email', '')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('company_tax_number', '')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('currency', 'AED')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('language', 'ar')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('invoice_prefix', 'INV-')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO settings (key, value) VALUES ('purchase_prefix', 'PUR-')",
            [],
        )?;
    }
    
    // التحقق من وجود بيانات في جدول معدلات الضرائب
    // Check if tax_rates table has data
    let tax_rates_count: i64 = connection.query_row(
        "SELECT COUNT(*) FROM tax_rates",
        [],
        |row| row.get(0),
    )?;
    
    if tax_rates_count == 0 {
        // إدخال معدلات الضرائب الافتراضية
        // Insert default tax rates
        connection.execute(
            "INSERT INTO tax_rates (name, rate, is_default) VALUES ('ضريبة القيمة المضافة 5%', 5.0, 1)",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO tax_rates (name, rate, is_default) VALUES ('معفى من الضريبة', 0.0, 0)",
            [],
        )?;
    }
    
    // التحقق من وجود بيانات في جدول الأدوار
    // Check if roles table has data
    let roles_count: i64 = connection.query_row(
        "SELECT COUNT(*) FROM roles",
        [],
        |row| row.get(0),
    )?;
    
    if roles_count == 0 {
        // إدخال الأدوار الافتراضية
        // Insert default roles
        connection.execute(
            "INSERT INTO roles (name, description) VALUES ('admin', 'مدير النظام')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO roles (name, description) VALUES ('manager', 'مدير')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO roles (name, description) VALUES ('cashier', 'كاشير')",
            [],
        )?;
        
        connection.execute(
            "INSERT INTO roles (name, description) VALUES ('inventory', 'مسؤول المخزون')",
            [],
        )?;
    }
    
    // التحقق من وجود بيانات في جدول المستخدمين
    // Check if users table has data
    let users_count: i64 = connection.query_row(
        "SELECT COUNT(*) FROM users",
        [],
        |row| row.get(0),
    )?;
    
    if users_count == 0 {
        // إدخال المستخدم الافتراضي
        // Insert default user
        connection.execute(
            "INSERT INTO users (username, display_name) VALUES ('admin', 'مدير النظام')",
            [],
        )?;
        
        // الحصول على معرف المستخدم
        // Get user id
        let user_id: i64 = connection.query_row(
            "SELECT id FROM users WHERE username = 'admin'",
            [],
            |row| row.get(0),
        )?;
        
        // الحصول على معرف دور المدير
        // Get admin role id
        let role_id: i64 = connection.query_row(
            "SELECT id FROM roles WHERE name = 'admin'",
            [],
            |row| row.get(0),
        )?;
        
        // ربط المستخدم بدور المدير
        // Link user to admin role
        connection.execute(
            "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)",
            [user_id, role_id],
        )?;
    }
    
    // التحقق من وجود بيانات في جدول مواقع المخزون
    // Check if inventory_locations table has data
    let locations_count: i64 = connection.query_row(
        "SELECT COUNT(*) FROM inventory_locations",
        [],
        |row| row.get(0),
    )?;
    
    if locations_count == 0 {
        // إدخال موقع المخزون الافتراضي
        // Insert default inventory location
        connection.execute(
            "INSERT INTO inventory_locations (name, description) VALUES ('المخزن الرئيسي', 'المخزن الرئيسي للمنتجات')",
            [],
        )?;
    }
    
    Ok(())
}
