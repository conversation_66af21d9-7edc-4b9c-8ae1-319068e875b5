use crate::database::Database;
use crate::AppState;
use chrono::Local;
use rusqlite::Result;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use tauri::api::dialog;
use tauri::{AppHandle, State};

// تعريف هيكل النسخة الاحتياطية
// Backup structure definition
#[derive(Debug, Serialize, Deserialize)]
pub struct Backup {
    pub id: i64,
    pub file_name: String,
    pub file_path: String,
    pub file_size: Option<i64>,
    pub created_at: String,
}

// إنشاء نسخة احتياطية
// Create backup
#[tauri::command]
pub async fn create_backup(app_handle: AppHandle, state: State<'_, AppState>) -> Result<Backup, String> {
    // الحصول على مسار مجلد النسخ الاحتياطية
    // Get backup directory path
    let app_dir = app_handle
        .path_resolver()
        .app_data_dir()
        .expect("Failed to get app data directory");
    
    let backup_dir = app_dir.join("backups");
    
    // التأكد من وجود المجلد
    // Ensure the directory exists
    fs::create_dir_all(&backup_dir).map_err(|e| e.to_string())?;
    
    // إنشاء اسم ملف النسخة الاحتياطية
    // Create backup file name
    let now = Local::now();
    let file_name = format!("amin_plus_backup_{}.db", now.format("%Y%m%d_%H%M%S"));
    let backup_path = backup_dir.join(&file_name);
    
    // الحصول على مسار قاعدة البيانات الحالية
    // Get current database path
    let db_path = app_dir.join("amin_plus.db");
    
    // نسخ ملف قاعدة البيانات
    // Copy database file
    fs::copy(&db_path, &backup_path).map_err(|e| e.to_string())?;
    
    // الحصول على حجم الملف
    // Get file size
    let metadata = fs::metadata(&backup_path).map_err(|e| e.to_string())?;
    let file_size = metadata.len() as i64;
    
    // تسجيل النسخة الاحتياطية في قاعدة البيانات
    // Register backup in database
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    conn.execute(
        "INSERT INTO backups (file_name, file_path, file_size) VALUES (?, ?, ?)",
        [
            &file_name,
            &backup_path.to_string_lossy().to_string(),
            &file_size.to_string(),
        ],
    ).map_err(|e| e.to_string())?;
    
    let backup_id = conn.last_insert_rowid();
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "create_backup",
            "backups",
            &backup_id.to_string(),
            &format!("Created backup: {}", file_name),
        ],
    ).map_err(|e| e.to_string())?;
    
    // إرجاع معلومات النسخة الاحتياطية
    // Return backup information
    let backup = Backup {
        id: backup_id,
        file_name,
        file_path: backup_path.to_string_lossy().to_string(),
        file_size: Some(file_size),
        created_at: now.to_string(),
    };
    
    Ok(backup)
}

// استعادة نسخة احتياطية
// Restore backup
#[tauri::command]
pub async fn restore_backup(backup_id: i64, app_handle: AppHandle, state: State<'_, AppState>) -> Result<(), String> {
    // الحصول على معلومات النسخة الاحتياطية
    // Get backup information
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    let backup: Backup = conn.query_row(
        "SELECT id, file_name, file_path, file_size, created_at FROM backups WHERE id = ?",
        [backup_id],
        |row| {
            Ok(Backup {
                id: row.get(0)?,
                file_name: row.get(1)?,
                file_path: row.get(2)?,
                file_size: row.get(3)?,
                created_at: row.get(4)?,
            })
        },
    ).map_err(|e| e.to_string())?;
    
    // التحقق من وجود ملف النسخة الاحتياطية
    // Check if backup file exists
    if !Path::new(&backup.file_path).exists() {
        return Err(format!("Backup file not found: {}", backup.file_path));
    }
    
    // الحصول على مسار قاعدة البيانات الحالية
    // Get current database path
    let app_dir = app_handle
        .path_resolver()
        .app_data_dir()
        .expect("Failed to get app data directory");
    
    let db_path = app_dir.join("amin_plus.db");
    
    // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
    // Create backup of current database before restoration
    let now = Local::now();
    let temp_backup_name = format!("amin_plus_before_restore_{}.db", now.format("%Y%m%d_%H%M%S"));
    let temp_backup_path = app_dir.join("backups").join(&temp_backup_name);
    
    fs::copy(&db_path, &temp_backup_path).map_err(|e| e.to_string())?;
    
    // استعادة النسخة الاحتياطية
    // Restore backup
    fs::copy(&backup.file_path, &db_path).map_err(|e| e.to_string())?;
    
    // إضافة سجل تدقيق (في قاعدة البيانات المستعادة)
    // Add audit log (in restored database)
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "restore_backup",
            "backups",
            &backup_id.to_string(),
            &format!("Restored backup: {}", backup.file_name),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(())
}

// تصدير نسخة احتياطية
// Export backup
#[tauri::command]
pub async fn export_backup(backup_id: i64, app_handle: AppHandle, state: State<'_, AppState>) -> Result<String, String> {
    // الحصول على معلومات النسخة الاحتياطية
    // Get backup information
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    let backup: Backup = conn.query_row(
        "SELECT id, file_name, file_path, file_size, created_at FROM backups WHERE id = ?",
        [backup_id],
        |row| {
            Ok(Backup {
                id: row.get(0)?,
                file_name: row.get(1)?,
                file_path: row.get(2)?,
                file_size: row.get(3)?,
                created_at: row.get(4)?,
            })
        },
    ).map_err(|e| e.to_string())?;
    
    // التحقق من وجود ملف النسخة الاحتياطية
    // Check if backup file exists
    if !Path::new(&backup.file_path).exists() {
        return Err(format!("Backup file not found: {}", backup.file_path));
    }
    
    // فتح مربع حوار لاختيار مسار الحفظ
    // Open dialog to choose save path
    let save_path = dialog::blocking::FileDialogBuilder::new()
        .set_title("حفظ النسخة الاحتياطية")
        .set_file_name(&backup.file_name)
        .save_file();
    
    if let Some(path) = save_path {
        // نسخ ملف النسخة الاحتياطية إلى المسار المحدد
        // Copy backup file to specified path
        fs::copy(&backup.file_path, &path).map_err(|e| e.to_string())?;
        
        // إضافة سجل تدقيق
        // Add audit log
        conn.execute(
            "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
            [
                "export_backup",
                "backups",
                &backup_id.to_string(),
                &format!("Exported backup: {} to {}", backup.file_name, path.to_string_lossy()),
            ],
        ).map_err(|e| e.to_string())?;
        
        Ok(path.to_string_lossy().to_string())
    } else {
        Err("Export cancelled".to_string())
    }
}

// استيراد نسخة احتياطية
// Import backup
#[tauri::command]
pub async fn import_backup(app_handle: AppHandle, state: State<'_, AppState>) -> Result<Backup, String> {
    // فتح مربع حوار لاختيار ملف النسخة الاحتياطية
    // Open dialog to choose backup file
    let file_path = dialog::blocking::FileDialogBuilder::new()
        .set_title("استيراد نسخة احتياطية")
        .add_filter("ملفات قاعدة البيانات", &["db"])
        .pick_file();
    
    if let Some(path) = file_path {
        // الحصول على اسم الملف
        // Get file name
        let file_name = path.file_name()
            .ok_or("Invalid file name")?
            .to_string_lossy()
            .to_string();
        
        // الحصول على مسار مجلد النسخ الاحتياطية
        // Get backup directory path
        let app_dir = app_handle
            .path_resolver()
            .app_data_dir()
            .expect("Failed to get app data directory");
        
        let backup_dir = app_dir.join("backups");
        
        // التأكد من وجود المجلد
        // Ensure the directory exists
        fs::create_dir_all(&backup_dir).map_err(|e| e.to_string())?;
        
        // إنشاء مسار النسخة الاحتياطية
        // Create backup path
        let now = Local::now();
        let new_file_name = format!("amin_plus_imported_{}.db", now.format("%Y%m%d_%H%M%S"));
        let backup_path = backup_dir.join(&new_file_name);
        
        // نسخ ملف النسخة الاحتياطية
        // Copy backup file
        fs::copy(&path, &backup_path).map_err(|e| e.to_string())?;
        
        // الحصول على حجم الملف
        // Get file size
        let metadata = fs::metadata(&backup_path).map_err(|e| e.to_string())?;
        let file_size = metadata.len() as i64;
        
        // تسجيل النسخة الاحتياطية في قاعدة البيانات
        // Register backup in database
        let conn = state.db.get_connection().map_err(|e| e.to_string())?;
        
        conn.execute(
            "INSERT INTO backups (file_name, file_path, file_size) VALUES (?, ?, ?)",
            [
                &new_file_name,
                &backup_path.to_string_lossy().to_string(),
                &file_size.to_string(),
            ],
        ).map_err(|e| e.to_string())?;
        
        let backup_id = conn.last_insert_rowid();
        
        // إضافة سجل تدقيق
        // Add audit log
        conn.execute(
            "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
            [
                "import_backup",
                "backups",
                &backup_id.to_string(),
                &format!("Imported backup: {} from {}", new_file_name, path.to_string_lossy()),
            ],
        ).map_err(|e| e.to_string())?;
        
        // إرجاع معلومات النسخة الاحتياطية
        // Return backup information
        let backup = Backup {
            id: backup_id,
            file_name: new_file_name,
            file_path: backup_path.to_string_lossy().to_string(),
            file_size: Some(file_size),
            created_at: now.to_string(),
        };
        
        Ok(backup)
    } else {
        Err("Import cancelled".to_string())
    }
}

// حذف نسخة احتياطية
// Delete backup
#[tauri::command]
pub fn delete_backup(backup_id: i64, state: State<AppState>) -> Result<(), String> {
    // الحصول على معلومات النسخة الاحتياطية
    // Get backup information
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    let backup: Backup = conn.query_row(
        "SELECT id, file_name, file_path, file_size, created_at FROM backups WHERE id = ?",
        [backup_id],
        |row| {
            Ok(Backup {
                id: row.get(0)?,
                file_name: row.get(1)?,
                file_path: row.get(2)?,
                file_size: row.get(3)?,
                created_at: row.get(4)?,
            })
        },
    ).map_err(|e| e.to_string())?;
    
    // حذف ملف النسخة الاحتياطية
    // Delete backup file
    if Path::new(&backup.file_path).exists() {
        fs::remove_file(&backup.file_path).map_err(|e| e.to_string())?;
    }
    
    // حذف النسخة الاحتياطية من قاعدة البيانات
    // Delete backup from database
    conn.execute(
        "DELETE FROM backups WHERE id = ?",
        [backup_id],
    ).map_err(|e| e.to_string())?;
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "delete_backup",
            "backups",
            &backup_id.to_string(),
            &format!("Deleted backup: {}", backup.file_name),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(())
}

// الحصول على جميع النسخ الاحتياطية
// Get all backups
#[tauri::command]
pub fn get_backups(state: State<AppState>) -> Result<Vec<Backup>, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    let mut stmt = conn.prepare("
        SELECT id, file_name, file_path, file_size, created_at
        FROM backups
        ORDER BY created_at DESC
    ").map_err(|e| e.to_string())?;
    
    let backups_iter = stmt.query_map([], |row| {
        Ok(Backup {
            id: row.get(0)?,
            file_name: row.get(1)?,
            file_path: row.get(2)?,
            file_size: row.get(3)?,
            created_at: row.get(4)?,
        })
    }).map_err(|e| e.to_string())?;
    
    let mut backups = Vec::new();
    for backup in backups_iter {
        backups.push(backup.map_err(|e| e.to_string())?);
    }
    
    Ok(backups)
}

// إنشاء نسخة احتياطية تلقائية
// Create automatic backup
pub fn create_automatic_backup(app_handle: &AppHandle, db: &Database) -> Result<(), String> {
    // الحصول على مسار مجلد النسخ الاحتياطية
    // Get backup directory path
    let app_dir = app_handle
        .path_resolver()
        .app_data_dir()
        .expect("Failed to get app data directory");
    
    let backup_dir = app_dir.join("backups").join("auto");
    
    // التأكد من وجود المجلد
    // Ensure the directory exists
    fs::create_dir_all(&backup_dir).map_err(|e| e.to_string())?;
    
    // إنشاء اسم ملف النسخة الاحتياطية
    // Create backup file name
    let now = Local::now();
    let file_name = format!("amin_plus_auto_backup_{}.db", now.format("%Y%m%d_%H%M%S"));
    let backup_path = backup_dir.join(&file_name);
    
    // الحصول على مسار قاعدة البيانات الحالية
    // Get current database path
    let db_path = app_dir.join("amin_plus.db");
    
    // نسخ ملف قاعدة البيانات
    // Copy database file
    fs::copy(&db_path, &backup_path).map_err(|e| e.to_string())?;
    
    // الحصول على حجم الملف
    // Get file size
    let metadata = fs::metadata(&backup_path).map_err(|e| e.to_string())?;
    let file_size = metadata.len() as i64;
    
    // تسجيل النسخة الاحتياطية في قاعدة البيانات
    // Register backup in database
    let conn = db.get_connection().map_err(|e| e.to_string())?;
    
    conn.execute(
        "INSERT INTO backups (file_name, file_path, file_size) VALUES (?, ?, ?)",
        [
            &file_name,
            &backup_path.to_string_lossy().to_string(),
            &file_size.to_string(),
        ],
    ).map_err(|e| e.to_string())?;
    
    let backup_id = conn.last_insert_rowid();
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "create_automatic_backup",
            "backups",
            &backup_id.to_string(),
            &format!("Created automatic backup: {}", file_name),
        ],
    ).map_err(|e| e.to_string())?;
    
    // حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 10 نسخ)
    // Delete old backups (keep last 10)
    let old_backups: Vec<(i64, String)> = {
        let mut stmt = conn.prepare("
            SELECT id, file_path
            FROM backups
            WHERE file_name LIKE 'amin_plus_auto_backup_%'
            ORDER BY created_at DESC
            LIMIT -1 OFFSET 10
        ").map_err(|e| e.to_string())?;
        
        let backups_iter = stmt.query_map([], |row| {
            Ok((row.get(0)?, row.get(1)?))
        }).map_err(|e| e.to_string())?;
        
        let mut backups = Vec::new();
        for backup in backups_iter {
            backups.push(backup.map_err(|e| e.to_string())?);
        }
        
        backups
    };
    
    for (id, path) in old_backups {
        // حذف ملف النسخة الاحتياطية
        // Delete backup file
        if Path::new(&path).exists() {
            fs::remove_file(&path).map_err(|e| e.to_string())?;
        }
        
        // حذف النسخة الاحتياطية من قاعدة البيانات
        // Delete backup from database
        conn.execute(
            "DELETE FROM backups WHERE id = ?",
            [id],
        ).map_err(|e| e.to_string())?;
    }
    
    Ok(())
}
