// استيراد المكتبات اللازمة
// Import required libraries
use std::sync::Mutex;
use tauri::State;

// استيراد وحدة قاعدة البيانات
// Import database module
mod database;
use database::Database;

// استيراد وحدة المصادقة
// Import authentication module
mod auth;
use auth::*;

// استيراد وحدة النسخ الاحتياطي
// Import backup module
mod backup;
use backup::*;

// تعريف حالة التطبيق
// Define application state
pub struct AppState {
    db: Database,
}

// تعريف أوامر Tauri
// Define Tauri commands

#[tauri::command]
fn get_app_info() -> String {
    "Amin Plus - نظام إدارة المبيعات والمخازن".to_string()
}

#[tauri::command]
fn get_settings(state: State<AppState>) -> Result<Vec<(String, String)>, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;

    let mut stmt = conn.prepare("SELECT key, value FROM settings")
        .map_err(|e| e.to_string())?;

    let settings_iter = stmt.query_map([], |row| {
        Ok((row.get(0)?, row.get(1)?))
    }).map_err(|e| e.to_string())?;

    let mut settings = Vec::new();
    for setting in settings_iter {
        settings.push(setting.map_err(|e| e.to_string())?);
    }

    Ok(settings)
}

#[tauri::command]
fn update_setting(key: String, value: String, state: State<AppState>) -> Result<(), String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;

    conn.execute(
        "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
        [&value, &key],
    ).map_err(|e| e.to_string())?;

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // إنشاء قاعدة البيانات
      // Create database
      let db = Database::new(&app.handle()).expect("Failed to initialize database");

      // إنشاء نسخة احتياطية تلقائية
      // Create automatic backup
      if let Err(e) = backup::create_automatic_backup(&app.handle(), &db) {
          eprintln!("Failed to create automatic backup: {}", e);
      }

      // تعيين حالة التطبيق
      // Set application state
      app.manage(AppState { db });

      Ok(())
    })
    .invoke_handler(tauri::generate_handler![
        // أوامر التطبيق العامة
        // General application commands
        get_app_info,
        get_settings,
        update_setting,

        // أوامر المصادقة والتفويض
        // Authentication and authorization commands
        get_current_user,
        get_users,
        add_user,
        update_user,
        delete_user,
        get_roles,
        add_role,
        update_role,
        delete_role,
        check_permission,
        add_audit_log,
        get_audit_logs,

        // أوامر النسخ الاحتياطي
        // Backup commands
        create_backup,
        restore_backup,
        export_backup,
        import_backup,
        delete_backup,
        get_backups,
    ])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
