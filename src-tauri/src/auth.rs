use crate::database::Database;
use rusqlite::Result;
use serde::{Deserialize, Serialize};
use tauri::State;

use crate::AppState;

// تعريف هيكل المستخدم
// User structure definition
#[derive(Debug, Serialize, Deserialize)]
pub struct User {
    pub id: i64,
    pub username: String,
    pub display_name: String,
    pub roles: Vec<String>,
}

// تعريف هيكل الدور
// Role structure definition
#[derive(Debug, Serialize, Deserialize)]
pub struct Role {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
}

// تعريف هيكل الصلاحية
// Permission structure definition
#[derive(Debug, Serialize, Deserialize)]
pub struct Permission {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
}

// الحصول على المستخدم الحالي
// Get current user
#[tauri::command]
pub fn get_current_user(state: State<AppState>) -> Result<User, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    // في هذا النظام، نفترض أن المستخدم الأول هو المستخدم الحالي
    // In this system, we assume the first user is the current user
    let mut stmt = conn.prepare("
        SELECT u.id, u.username, u.display_name
        FROM users u
        ORDER BY u.id
        LIMIT 1
    ").map_err(|e| e.to_string())?;
    
    let user = stmt.query_row([], |row| {
        Ok(User {
            id: row.get(0)?,
            username: row.get(1)?,
            display_name: row.get(2)?,
            roles: Vec::new(), // سيتم ملؤها لاحقاً
        })
    }).map_err(|e| e.to_string())?;
    
    // الحصول على أدوار المستخدم
    // Get user roles
    let mut stmt = conn.prepare("
        SELECT r.name
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    ").map_err(|e| e.to_string())?;
    
    let roles_iter = stmt.query_map([user.id], |row| {
        row.get(0)
    }).map_err(|e| e.to_string())?;
    
    let mut roles = Vec::new();
    for role in roles_iter {
        roles.push(role.map_err(|e| e.to_string())?);
    }
    
    let mut user = user;
    user.roles = roles;
    
    Ok(user)
}

// الحصول على جميع المستخدمين
// Get all users
#[tauri::command]
pub fn get_users(state: State<AppState>) -> Result<Vec<User>, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    let mut stmt = conn.prepare("
        SELECT u.id, u.username, u.display_name
        FROM users u
        ORDER BY u.id
    ").map_err(|e| e.to_string())?;
    
    let users_iter = stmt.query_map([], |row| {
        Ok(User {
            id: row.get(0)?,
            username: row.get(1)?,
            display_name: row.get(2)?,
            roles: Vec::new(), // سيتم ملؤها لاحقاً
        })
    }).map_err(|e| e.to_string())?;
    
    let mut users = Vec::new();
    for user_result in users_iter {
        let user = user_result.map_err(|e| e.to_string())?;
        users.push(user);
    }
    
    // الحصول على أدوار كل مستخدم
    // Get roles for each user
    for user in &mut users {
        let mut stmt = conn.prepare("
            SELECT r.name
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ?
        ").map_err(|e| e.to_string())?;
        
        let roles_iter = stmt.query_map([user.id], |row| {
            row.get(0)
        }).map_err(|e| e.to_string())?;
        
        let mut roles = Vec::new();
        for role in roles_iter {
            roles.push(role.map_err(|e| e.to_string())?);
        }
        
        user.roles = roles;
    }
    
    Ok(users)
}

// إضافة مستخدم جديد
// Add new user
#[tauri::command]
pub fn add_user(username: String, display_name: String, roles: Vec<String>, state: State<AppState>) -> Result<i64, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    // بدء المعاملة
    // Start transaction
    let tx = conn.transaction().map_err(|e| e.to_string())?;
    
    // إضافة المستخدم
    // Add user
    tx.execute(
        "INSERT INTO users (username, display_name) VALUES (?, ?)",
        [&username, &display_name],
    ).map_err(|e| e.to_string())?;
    
    // الحصول على معرف المستخدم المضاف
    // Get added user id
    let user_id = tx.last_insert_rowid();
    
    // إضافة أدوار المستخدم
    // Add user roles
    for role_name in roles {
        // الحصول على معرف الدور
        // Get role id
        let role_id: i64 = tx.query_row(
            "SELECT id FROM roles WHERE name = ?",
            [&role_name],
            |row| row.get(0),
        ).map_err(|e| e.to_string())?;
        
        // إضافة الدور للمستخدم
        // Add role to user
        tx.execute(
            "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)",
            [user_id, role_id],
        ).map_err(|e| e.to_string())?;
    }
    
    // إنهاء المعاملة
    // Commit transaction
    tx.commit().map_err(|e| e.to_string())?;
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, details) VALUES (?, ?, ?, ?, ?)",
        [
            &user_id.to_string(),
            "add_user",
            "users",
            &user_id.to_string(),
            &format!("Added user: {}", username),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(user_id)
}

// تحديث مستخدم
// Update user
#[tauri::command]
pub fn update_user(id: i64, display_name: String, roles: Vec<String>, state: State<AppState>) -> Result<(), String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    // بدء المعاملة
    // Start transaction
    let tx = conn.transaction().map_err(|e| e.to_string())?;
    
    // تحديث المستخدم
    // Update user
    tx.execute(
        "UPDATE users SET display_name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [&display_name, &id.to_string()],
    ).map_err(|e| e.to_string())?;
    
    // حذف أدوار المستخدم الحالية
    // Delete current user roles
    tx.execute(
        "DELETE FROM user_roles WHERE user_id = ?",
        [id],
    ).map_err(|e| e.to_string())?;
    
    // إضافة أدوار المستخدم الجديدة
    // Add new user roles
    for role_name in roles {
        // الحصول على معرف الدور
        // Get role id
        let role_id: i64 = tx.query_row(
            "SELECT id FROM roles WHERE name = ?",
            [&role_name],
            |row| row.get(0),
        ).map_err(|e| e.to_string())?;
        
        // إضافة الدور للمستخدم
        // Add role to user
        tx.execute(
            "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)",
            [id, role_id],
        ).map_err(|e| e.to_string())?;
    }
    
    // إنهاء المعاملة
    // Commit transaction
    tx.commit().map_err(|e| e.to_string())?;
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, details) VALUES (?, ?, ?, ?, ?)",
        [
            &id.to_string(),
            "update_user",
            "users",
            &id.to_string(),
            &format!("Updated user: {}", id),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(())
}

// حذف مستخدم
// Delete user
#[tauri::command]
pub fn delete_user(id: i64, state: State<AppState>) -> Result<(), String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    // الحصول على اسم المستخدم قبل الحذف
    // Get username before deletion
    let username: String = conn.query_row(
        "SELECT username FROM users WHERE id = ?",
        [id],
        |row| row.get(0),
    ).map_err(|e| e.to_string())?;
    
    // بدء المعاملة
    // Start transaction
    let tx = conn.transaction().map_err(|e| e.to_string())?;
    
    // حذف أدوار المستخدم
    // Delete user roles
    tx.execute(
        "DELETE FROM user_roles WHERE user_id = ?",
        [id],
    ).map_err(|e| e.to_string())?;
    
    // حذف المستخدم
    // Delete user
    tx.execute(
        "DELETE FROM users WHERE id = ?",
        [id],
    ).map_err(|e| e.to_string())?;
    
    // إنهاء المعاملة
    // Commit transaction
    tx.commit().map_err(|e| e.to_string())?;
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "delete_user",
            "users",
            &id.to_string(),
            &format!("Deleted user: {}", username),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(())
}

// الحصول على جميع الأدوار
// Get all roles
#[tauri::command]
pub fn get_roles(state: State<AppState>) -> Result<Vec<Role>, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    let mut stmt = conn.prepare("
        SELECT id, name, description
        FROM roles
        ORDER BY id
    ").map_err(|e| e.to_string())?;
    
    let roles_iter = stmt.query_map([], |row| {
        Ok(Role {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
        })
    }).map_err(|e| e.to_string())?;
    
    let mut roles = Vec::new();
    for role in roles_iter {
        roles.push(role.map_err(|e| e.to_string())?);
    }
    
    Ok(roles)
}

// إضافة دور جديد
// Add new role
#[tauri::command]
pub fn add_role(name: String, description: Option<String>, state: State<AppState>) -> Result<i64, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    conn.execute(
        "INSERT INTO roles (name, description) VALUES (?, ?)",
        [&name, &description.unwrap_or_default()],
    ).map_err(|e| e.to_string())?;
    
    let role_id = conn.last_insert_rowid();
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "add_role",
            "roles",
            &role_id.to_string(),
            &format!("Added role: {}", name),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(role_id)
}

// تحديث دور
// Update role
#[tauri::command]
pub fn update_role(id: i64, name: String, description: Option<String>, state: State<AppState>) -> Result<(), String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    conn.execute(
        "UPDATE roles SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [&name, &description.unwrap_or_default(), &id.to_string()],
    ).map_err(|e| e.to_string())?;
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "update_role",
            "roles",
            &id.to_string(),
            &format!("Updated role: {}", name),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(())
}

// حذف دور
// Delete role
#[tauri::command]
pub fn delete_role(id: i64, state: State<AppState>) -> Result<(), String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    // الحصول على اسم الدور قبل الحذف
    // Get role name before deletion
    let role_name: String = conn.query_row(
        "SELECT name FROM roles WHERE id = ?",
        [id],
        |row| row.get(0),
    ).map_err(|e| e.to_string())?;
    
    // بدء المعاملة
    // Start transaction
    let tx = conn.transaction().map_err(|e| e.to_string())?;
    
    // حذف صلاحيات الدور
    // Delete role permissions
    tx.execute(
        "DELETE FROM role_permissions WHERE role_id = ?",
        [id],
    ).map_err(|e| e.to_string())?;
    
    // حذف أدوار المستخدمين المرتبطة بهذا الدور
    // Delete user roles associated with this role
    tx.execute(
        "DELETE FROM user_roles WHERE role_id = ?",
        [id],
    ).map_err(|e| e.to_string())?;
    
    // حذف الدور
    // Delete role
    tx.execute(
        "DELETE FROM roles WHERE id = ?",
        [id],
    ).map_err(|e| e.to_string())?;
    
    // إنهاء المعاملة
    // Commit transaction
    tx.commit().map_err(|e| e.to_string())?;
    
    // إضافة سجل تدقيق
    // Add audit log
    conn.execute(
        "INSERT INTO audit_logs (action, entity_type, entity_id, details) VALUES (?, ?, ?, ?)",
        [
            "delete_role",
            "roles",
            &id.to_string(),
            &format!("Deleted role: {}", role_name),
        ],
    ).map_err(|e| e.to_string())?;
    
    Ok(())
}

// التحقق من صلاحية المستخدم
// Check user permission
#[tauri::command]
pub fn check_permission(permission: String, state: State<AppState>) -> Result<bool, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    // الحصول على المستخدم الحالي
    // Get current user
    let user = get_current_user(state).map_err(|e| e.to_string())?;
    
    // التحقق مما إذا كان المستخدم لديه دور المدير
    // Check if user has admin role
    if user.roles.contains(&"admin".to_string()) {
        return Ok(true);
    }
    
    // التحقق من صلاحية المستخدم
    // Check user permission
    let count: i64 = conn.query_row(
        "
        SELECT COUNT(*)
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN roles r ON rp.role_id = r.id
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ? AND p.name = ?
        ",
        [user.id, permission.clone()],
        |row| row.get(0),
    ).map_err(|e| e.to_string())?;
    
    Ok(count > 0)
}

// إضافة سجل تدقيق
// Add audit log
#[tauri::command]
pub fn add_audit_log(action: String, entity_type: Option<String>, entity_id: Option<i64>, details: Option<String>, state: State<AppState>) -> Result<i64, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    // الحصول على المستخدم الحالي
    // Get current user
    let user = get_current_user(state.clone()).map_err(|e| e.to_string())?;
    
    conn.execute(
        "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, details) VALUES (?, ?, ?, ?, ?)",
        [
            &user.id.to_string(),
            &action,
            &entity_type.unwrap_or_default(),
            &entity_id.map(|id| id.to_string()).unwrap_or_default(),
            &details.unwrap_or_default(),
        ],
    ).map_err(|e| e.to_string())?;
    
    let log_id = conn.last_insert_rowid();
    
    Ok(log_id)
}

// الحصول على سجلات التدقيق
// Get audit logs
#[tauri::command]
pub fn get_audit_logs(limit: Option<i64>, offset: Option<i64>, state: State<AppState>) -> Result<Vec<serde_json::Value>, String> {
    let conn = state.db.get_connection().map_err(|e| e.to_string())?;
    
    let limit = limit.unwrap_or(100);
    let offset = offset.unwrap_or(0);
    
    let mut stmt = conn.prepare("
        SELECT al.id, al.user_id, u.username, al.action, al.entity_type, al.entity_id, al.details, al.created_at
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT ? OFFSET ?
    ").map_err(|e| e.to_string())?;
    
    let logs_iter = stmt.query_map([limit, offset], |row| {
        let user_id: Option<i64> = row.get(1)?;
        let username: Option<String> = row.get(2)?;
        let entity_type: Option<String> = row.get(4)?;
        let entity_id: Option<i64> = row.get(5)?;
        
        Ok(serde_json::json!({
            "id": row.get::<_, i64>(0)?,
            "user_id": user_id,
            "username": username,
            "action": row.get::<_, String>(3)?,
            "entity_type": entity_type,
            "entity_id": entity_id,
            "details": row.get::<_, Option<String>>(6)?,
            "created_at": row.get::<_, String>(7)?
        }))
    }).map_err(|e| e.to_string())?;
    
    let mut logs = Vec::new();
    for log in logs_iter {
        logs.push(log.map_err(|e| e.to_string())?);
    }
    
    Ok(logs)
}
