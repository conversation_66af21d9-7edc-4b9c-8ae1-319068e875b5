// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// تعريف الوظائف المستخدمة في التطبيق
// Define functions used in the application
#[tauri::command]
fn get_app_info() -> String {
    "Amin Plus | أمين بلس - نظام إدارة المبيعات والمخازن".to_string()
}

fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![get_app_info])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
