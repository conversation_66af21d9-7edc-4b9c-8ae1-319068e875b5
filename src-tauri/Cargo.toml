[package]
name = "amin_plus"
version = "1.0.0"
description = "Amin Plus - نظام إدارة المبيعات والمخازن"
authors = ["Amin Plus Team"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0.0-beta", features = [] }

[dependencies]
tauri = { version = "2.0.0-beta", features = ["api-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
log = "0.4"
tauri-plugin-log = "2.0.0-rc"
rusqlite = { version = "0.30.0", features = ["bundled"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.6.1", features = ["v4", "serde"] }
thiserror = "1.0"
anyhow = "1.0"
directories = "5.0"
