{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "identifier": "com.aminplus.app", "productName": "أمين بلس | Amin Plus", "version": "1.0.0", "build": {"beforeBuildCommand": "npm run build", "beforeDevCommand": "npm run dev", "frontendDist": "../out", "devUrl": "http://localhost:3000"}, "app": {"security": {"csp": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline';"}, "windows": [{"title": "أمين بلس | Amin Plus - نظام إدارة الأعمال والمحاسبة المتكامل | Integrated Business & Accounting Management System", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true, "decorations": true}]}, "bundle": {"active": true, "targets": ["msi", "dmg", "appimage"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {}}