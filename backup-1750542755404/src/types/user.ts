// أنواع المستخدمين
export type UserRole = 'admin' | 'manager' | 'accountant' | 'sales' | 'inventory' | 'viewer';

// نموذج المستخدم
export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: UserRole;
  permissions: Permission[];
  active: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

// نموذج الصلاحيات
export interface Permission {
  id: string;
  name: string;
  description: string;
  module: 'invoices' | 'customers' | 'products' | 'inventory' | 'pos' | 'reports' | 'settings' | 'users';
  action: 'view' | 'create' | 'edit' | 'delete' | 'approve' | 'export';
}

// الصلاحيات الافتراضية حسب الدور
export const DEFAULT_PERMISSIONS: Record<UserRole, string[]> = {
  admin: [
    'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.delete', 'invoices.approve', 'invoices.export',
    'customers.view', 'customers.create', 'customers.edit', 'customers.delete', 'customers.export',
    'products.view', 'products.create', 'products.edit', 'products.delete', 'products.export',
    'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.delete', 'inventory.export',
    'pos.view', 'pos.create', 'pos.edit', 'pos.delete', 'pos.export',
    'reports.view', 'reports.export',
    'settings.view', 'settings.edit',
    'users.view', 'users.create', 'users.edit', 'users.delete'
  ],
  manager: [
    'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.approve', 'invoices.export',
    'customers.view', 'customers.create', 'customers.edit', 'customers.export',
    'products.view', 'products.create', 'products.edit', 'products.export',
    'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.export',
    'pos.view', 'pos.create', 'pos.edit', 'pos.export',
    'reports.view', 'reports.export',
    'settings.view'
  ],
  accountant: [
    'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.export',
    'customers.view', 'customers.export',
    'products.view', 'products.export',
    'reports.view', 'reports.export'
  ],
  sales: [
    'invoices.view', 'invoices.create', 'invoices.edit',
    'customers.view', 'customers.create', 'customers.edit',
    'products.view',
    'pos.view', 'pos.create'
  ],
  inventory: [
    'products.view', 'products.create', 'products.edit',
    'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.export'
  ],
  viewer: [
    'invoices.view',
    'customers.view',
    'products.view',
    'inventory.view',
    'reports.view'
  ]
};

// التحقق من صلاحية المستخدم
export function hasPermission(user: User, module: Permission['module'], action: Permission['action']): boolean {
  const permissionKey = `${module}.${action}`;
  return user.permissions.some(permission => `${permission.module}.${permission.action}` === permissionKey);
}
