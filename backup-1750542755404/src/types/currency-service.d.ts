/**
 * تعريف واجهة خدمة العملات
 * Currency Service Interface Definition
 */

import { CurrencyCode } from '@/lib/utils';

/**
 * واجهة أسعار الصرف
 * Exchange rates interface
 */
interface ExchangeRates {
  base: CurrencyCode;
  date: string;
  rates: Record<CurrencyCode, number>;
}

/**
 * واجهة سجل التحويل
 * Conversion history entry interface
 */
interface ConversionHistoryEntry {
  id: string;
  fromCurrency: CurrencyCode;
  toCurrency: CurrencyCode;
  fromAmount: number;
  toAmount: number;
  rate: number;
  date: string;
}

/**
 * واجهة خدمة العملات
 * Currency Service Interface
 */
interface CurrencyService {
  /**
   * الحصول على العملة الافتراضية
   * Get default currency
   */
  getDefaultCurrency(): CurrencyCode;

  /**
   * تعيين العملة الافتراضية
   * Set default currency
   */
  setDefaultCurrency(currency: CurrencyCode): void;

  /**
   * الحصول على أسعار الصرف المخزنة
   * Get stored exchange rates
   */
  getStoredExchangeRates(): ExchangeRates;

  /**
   * تخزين أسعار الصرف
   * Store exchange rates
   */
  storeExchangeRates(rates: ExchangeRates): void;

  /**
   * تحديث أسعار الصرف من مصدر خارجي
   * Update exchange rates from external source
   */
  updateExchangeRates(): Promise<ExchangeRates>;

  /**
   * تحويل مبلغ من عملة إلى أخرى
   * Convert amount from one currency to another
   */
  convertCurrency(
    amount: number,
    fromCurrency: CurrencyCode,
    toCurrency: CurrencyCode
  ): number;

  /**
   * الحصول على سعر الصرف بين عملتين
   * Get exchange rate between two currencies
   */
  getExchangeRate(
    fromCurrency: CurrencyCode,
    toCurrency: CurrencyCode
  ): number;

  /**
   * الحصول على سجل التحويلات
   * Get conversion history
   */
  getConversionHistory(): ConversionHistoryEntry[];

  /**
   * إضافة تحويل إلى سجل التحويلات
   * Add conversion to history
   */
  addConversionToHistory(
    fromCurrency: CurrencyCode,
    toCurrency: CurrencyCode,
    fromAmount: number,
    toAmount: number
  ): void;

  /**
   * مسح سجل التحويلات
   * Clear conversion history
   */
  clearConversionHistory(): void;

  /**
   * تنسيق مبلغ بعملة معينة
   * Format amount in specific currency
   */
  formatCurrencyAmount(
    amount: number,
    currency?: CurrencyCode,
    language?: string,
    options?: Intl.NumberFormatOptions
  ): string;

  /**
   * الحصول على رمز العملة
   * Get currency symbol
   */
  getCurrencySymbol(
    currency?: CurrencyCode,
    language?: string
  ): string;

  /**
   * الحصول على اسم العملة
   * Get currency name
   */
  getCurrencyName(
    currency?: CurrencyCode,
    language?: string
  ): string;

  /**
   * تحويل مبلغ إلى كلمات
   * Convert amount to words
   */
  amountToWords(
    amount: number,
    currency?: CurrencyCode,
    language?: string
  ): string;
}

/**
 * تعريف خدمة العملات في النافذة
 * Define currency service in window
 */
interface Window {
  CurrencyService?: CurrencyService;
}
