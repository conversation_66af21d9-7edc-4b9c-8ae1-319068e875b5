// أنواع المنتجات
export type ProductType = 'physical' | 'service' | 'digital';

// وحدات القياس
export type UnitOfMeasure = 'piece' | 'kg' | 'liter' | 'meter' | 'box' | 'hour' | 'day' | 'custom';

// نموذج المنتج
export interface Product {
  id: string;
  sku: string;
  barcode?: string;
  name: string;
  nameEn?: string;
  description?: string;
  type: ProductType;
  category: string;
  price: number;
  costPrice: number;
  unit: UnitOfMeasure;
  customUnit?: string;
  taxable: boolean;
  active: boolean;
  images?: string[];
  attributes?: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

// نموذج المخزون
export interface InventoryItem {
  productId: string;
  locationId: string;
  quantity: number;
  minQuantity: number;
  maxQuantity: number;
  reorderPoint: number;
  lastStockTake: string;
  shelfLocation?: string;
  updatedAt: string;
}

// نموذج حركة المخزون
export interface InventoryMovement {
  id: string;
  productId: string;
  locationId: string;
  type: 'in' | 'out' | 'transfer' | 'adjustment';
  quantity: number;
  previousQuantity: number;
  newQuantity: number;
  reference: string;
  referenceType: 'purchase' | 'sale' | 'return' | 'transfer' | 'adjustment' | 'stocktake';
  notes?: string;
  createdBy: string;
  createdAt: string;
}

// نموذج موقع المخزون
export interface InventoryLocation {
  id: string;
  name: string;
  nameEn?: string;
  type: 'store' | 'warehouse' | 'virtual';
  address?: string;
  isDefault: boolean;
  active: boolean;
}

// نموذج الجرد
export interface StockTake {
  id: string;
  locationId: string;
  status: 'draft' | 'in_progress' | 'completed';
  startDate: string;
  endDate?: string;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// نموذج عنصر الجرد
export interface StockTakeItem {
  stockTakeId: string;
  productId: string;
  expectedQuantity: number;
  actualQuantity: number;
  discrepancy: number;
  notes?: string;
  updatedBy: string;
  updatedAt: string;
}

// نموذج تنبيه المخزون
export interface InventoryAlert {
  id: string;
  productId: string;
  locationId: string;
  type: 'low_stock' | 'out_of_stock' | 'overstock' | 'expiring';
  status: 'new' | 'acknowledged' | 'resolved';
  message: string;
  createdAt: string;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  resolvedBy?: string;
  resolvedAt?: string;
}

// وظائف مساعدة للمخزون
export function calculateInventoryValue(items: InventoryItem[], products: Product[]): number {
  return items.reduce((total, item) => {
    const product = products.find(p => p.id === item.productId);
    if (product) {
      return total + (product.costPrice * item.quantity);
    }
    return total;
  }, 0);
}

export function calculateReorderQuantity(item: InventoryItem): number {
  if (item.quantity <= item.reorderPoint) {
    return item.maxQuantity - item.quantity;
  }
  return 0;
}

export function isLowStock(item: InventoryItem): boolean {
  return item.quantity <= item.reorderPoint;
}

export function isOutOfStock(item: InventoryItem): boolean {
  return item.quantity <= 0;
}

export function isOverstock(item: InventoryItem): boolean {
  return item.quantity >= item.maxQuantity;
}
