:root {
  /* الألوان الرئيسية */
  --primary: #4F46E5;
  --primary-hover: #4338CA;
  --primary-light: #EEF2FF;
  --primary-dark: #3730A3;
  
  --secondary: #7C3AED;
  --secondary-hover: #6D28D9;
  --secondary-light: #F5F3FF;
  --secondary-dark: #5B21B6;
  
  /* ألوان محايدة */
  --background: #FFFFFF;
  --foreground: #0F172A;
  
  --card: #FFFFFF;
  --card-foreground: #0F172A;
  
  --popover: #FFFFFF;
  --popover-foreground: #0F172A;
  
  /* ألوان الحالة */
  --success: #10B981;
  --success-light: #ECFDF5;
  --success-dark: #059669;
  
  --warning: #F59E0B;
  --warning-light: #FFFBEB;
  --warning-dark: #D97706;
  
  --error: #EF4444;
  --error-light: #FEF2F2;
  --error-dark: #DC2626;
  
  --info: #3B82F6;
  --info-light: #EFF6FF;
  --info-dark: #2563EB;
  
  /* ألوان الواجهة */
  --muted: #F1F5F9;
  --muted-foreground: #64748B;
  
  --accent: #F8FAFC;
  --accent-foreground: #0F172A;
  
  --border: #E2E8F0;
  --input: #E2E8F0;
  --ring: #94A3B8;
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  
  /* الخطوط */
  --font-sans: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-heading: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  /* الزوايا */
  --radius-sm: 0.125rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* المسافات */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* الانتقالات */
  --transition-all: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-colors: background-color 0.15s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.15s cubic-bezier(0.4, 0, 0.2, 1), color 0.15s cubic-bezier(0.4, 0, 0.2, 1), fill 0.15s cubic-bezier(0.4, 0, 0.2, 1), stroke 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-opacity: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-shadow: box-shadow 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-transform: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* الوضع الداكن */
.dark {
  --background: #0F172A;
  --foreground: #F8FAFC;
  
  --card: #1E293B;
  --card-foreground: #F8FAFC;
  
  --popover: #1E293B;
  --popover-foreground: #F8FAFC;
  
  --primary: #6366F1;
  --primary-hover: #818CF8;
  --primary-light: #312E81;
  --primary-dark: #A5B4FC;
  
  --secondary: #8B5CF6;
  --secondary-hover: #A78BFA;
  --secondary-light: #4C1D95;
  --secondary-dark: #C4B5FD;
  
  --muted: #334155;
  --muted-foreground: #94A3B8;
  
  --accent: #1E293B;
  --accent-foreground: #F8FAFC;
  
  --success: #10B981;
  --success-light: #064E3B;
  --success-dark: #34D399;
  
  --warning: #F59E0B;
  --warning-light: #78350F;
  --warning-dark: #FBBF24;
  
  --error: #EF4444;
  --error-light: #7F1D1D;
  --error-dark: #F87171;
  
  --info: #3B82F6;
  --info-light: #1E3A8A;
  --info-dark: #60A5FA;
  
  --border: #334155;
  --input: #334155;
  --ring: #1E293B;
}
