'use client';

import { useState } from 'react';
import { User, Mail, Phone, MapPin, Building, CreditCard, Star, Save, X } from 'lucide-react';
import { Button, Input, Card, CardContent, CardHeader, CardTitle, Loading } from '@/components/ui';
import { useI18n } from '@/lib/i18n';
import { toast } from 'sonner';

interface SupplierFormData {
  name: string;
  nameEn: string;
  email: string;
  phone: string;
  mobile: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  taxNumber: string;
  website: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  bankDetails: string;
  paymentTerms: string;
  creditLimit: string;
  supplierType: string;
  category: string;
  rating: string;
  notes: string;
  isActive: boolean;
}

interface SupplierFormProps {
  initialData?: Partial<SupplierFormData>;
  onSubmit: (data: SupplierFormData) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
  loading?: boolean;
}

export default function SupplierForm({
  initialData = {},
  onSubmit,
  onCancel,
  isEditing = false,
  loading = false
}: SupplierFormProps) {
  const { language } = useI18n();
  
  const [formData, setFormData] = useState<SupplierFormData>({
    name: '',
    nameEn: '',
    email: '',
    phone: '',
    mobile: '',
    address: '',
    city: '',
    country: 'الإمارات العربية المتحدة',
    postalCode: '',
    taxNumber: '',
    website: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    bankDetails: '',
    paymentTerms: '30',
    creditLimit: '0',
    supplierType: 'vendor',
    category: '',
    rating: '0',
    notes: '',
    isActive: true,
    ...initialData
  });

  const [errors, setErrors] = useState<Partial<SupplierFormData>>({});

  const handleInputChange = (field: keyof SupplierFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // مسح الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<SupplierFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = language === 'ar' ? 'اسم المورد مطلوب' : 'Supplier name is required';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = language === 'ar' ? 'بريد إلكتروني غير صحيح' : 'Invalid email address';
    }

    if (formData.contactEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = language === 'ar' ? 'بريد إلكتروني غير صحيح' : 'Invalid email address';
    }

    if (formData.rating && (parseFloat(formData.rating) < 0 || parseFloat(formData.rating) > 5)) {
      newErrors.rating = language === 'ar' ? 'التقييم يجب أن يكون بين 0 و 5' : 'Rating must be between 0 and 5';
    }

    if (formData.creditLimit && parseFloat(formData.creditLimit) < 0) {
      newErrors.creditLimit = language === 'ar' ? 'الحد الائتماني لا يمكن أن يكون سالباً' : 'Credit limit cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error(language === 'ar' ? 'يرجى تصحيح الأخطاء في النموذج' : 'Please correct the errors in the form');
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const supplierTypes = [
    { value: 'vendor', label: language === 'ar' ? 'مورد' : 'Vendor' },
    { value: 'distributor', label: language === 'ar' ? 'موزع' : 'Distributor' },
    { value: 'manufacturer', label: language === 'ar' ? 'مصنع' : 'Manufacturer' },
    { value: 'service', label: language === 'ar' ? 'خدمات' : 'Service Provider' }
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'اسم المورد (عربي) *' : 'Supplier Name (Arabic) *'}
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder={language === 'ar' ? 'أدخل اسم المورد' : 'Enter supplier name'}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'اسم المورد (إنجليزي)' : 'Supplier Name (English)'}
            </label>
            <Input
              value={formData.nameEn}
              onChange={(e) => handleInputChange('nameEn', e.target.value)}
              placeholder={language === 'ar' ? 'أدخل الاسم بالإنجليزية' : 'Enter English name'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'نوع المورد' : 'Supplier Type'}
            </label>
            <select
              value={formData.supplierType}
              onChange={(e) => handleInputChange('supplierType', e.target.value)}
              className="w-full px-3 py-2 border border-input bg-background rounded-md"
            >
              {supplierTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'الفئة' : 'Category'}
            </label>
            <Input
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              placeholder={language === 'ar' ? 'مثال: مواد خام، معدات' : 'e.g. Raw Materials, Equipment'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'الرقم الضريبي' : 'Tax Number'}
            </label>
            <Input
              value={formData.taxNumber}
              onChange={(e) => handleInputChange('taxNumber', e.target.value)}
              placeholder={language === 'ar' ? 'TRN123456789' : 'TRN123456789'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'التقييم (1-5)' : 'Rating (1-5)'}
            </label>
            <Input
              type="number"
              min="0"
              max="5"
              step="0.1"
              value={formData.rating}
              onChange={(e) => handleInputChange('rating', e.target.value)}
              placeholder="0"
              className={errors.rating ? 'border-red-500' : ''}
            />
            {errors.rating && (
              <p className="text-sm text-red-500">{errors.rating}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
            </label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder={language === 'ar' ? '<EMAIL>' : '<EMAIL>'}
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
            </label>
            <Input
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder={language === 'ar' ? '+971-4-1234567' : '+971-4-1234567'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'رقم الجوال' : 'Mobile Number'}
            </label>
            <Input
              value={formData.mobile}
              onChange={(e) => handleInputChange('mobile', e.target.value)}
              placeholder={language === 'ar' ? '+971-50-1234567' : '+971-50-1234567'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'الموقع الإلكتروني' : 'Website'}
            </label>
            <Input
              value={formData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder={language === 'ar' ? 'www.supplier.com' : 'www.supplier.com'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'الشخص المسؤول' : 'Contact Person'}
            </label>
            <Input
              value={formData.contactPerson}
              onChange={(e) => handleInputChange('contactPerson', e.target.value)}
              placeholder={language === 'ar' ? 'اسم الشخص المسؤول' : 'Contact person name'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'هاتف الشخص المسؤول' : 'Contact Phone'}
            </label>
            <Input
              value={formData.contactPhone}
              onChange={(e) => handleInputChange('contactPhone', e.target.value)}
              placeholder={language === 'ar' ? '+971-50-1234567' : '+971-50-1234567'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'بريد الشخص المسؤول' : 'Contact Email'}
            </label>
            <Input
              type="email"
              value={formData.contactEmail}
              onChange={(e) => handleInputChange('contactEmail', e.target.value)}
              placeholder={language === 'ar' ? '<EMAIL>' : '<EMAIL>'}
              className={errors.contactEmail ? 'border-red-500' : ''}
            />
            {errors.contactEmail && (
              <p className="text-sm text-red-500">{errors.contactEmail}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Address Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {language === 'ar' ? 'معلومات العنوان' : 'Address Information'}
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2 md:col-span-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'العنوان' : 'Address'}
            </label>
            <Input
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder={language === 'ar' ? 'العنوان الكامل' : 'Full address'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'المدينة' : 'City'}
            </label>
            <Input
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              placeholder={language === 'ar' ? 'دبي' : 'Dubai'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'الدولة' : 'Country'}
            </label>
            <Input
              value={formData.country}
              onChange={(e) => handleInputChange('country', e.target.value)}
              placeholder={language === 'ar' ? 'الإمارات العربية المتحدة' : 'United Arab Emirates'}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'الرمز البريدي' : 'Postal Code'}
            </label>
            <Input
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              placeholder={language === 'ar' ? '12345' : '12345'}
            />
          </div>
        </CardContent>
      </Card>

      {/* Financial Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {language === 'ar' ? 'المعلومات المالية' : 'Financial Information'}
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'شروط الدفع (أيام)' : 'Payment Terms (Days)'}
            </label>
            <Input
              type="number"
              value={formData.paymentTerms}
              onChange={(e) => handleInputChange('paymentTerms', e.target.value)}
              placeholder="30"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'الحد الائتماني (درهم)' : 'Credit Limit (AED)'}
            </label>
            <Input
              type="number"
              value={formData.creditLimit}
              onChange={(e) => handleInputChange('creditLimit', e.target.value)}
              placeholder="0"
              className={errors.creditLimit ? 'border-red-500' : ''}
            />
            {errors.creditLimit && (
              <p className="text-sm text-red-500">{errors.creditLimit}</p>
            )}
          </div>

          <div className="space-y-2 md:col-span-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'تفاصيل البنك' : 'Bank Details'}
            </label>
            <textarea
              value={formData.bankDetails}
              onChange={(e) => handleInputChange('bankDetails', e.target.value)}
              placeholder={language === 'ar' ? 'تفاصيل الحساب البنكي' : 'Bank account details'}
              className="w-full px-3 py-2 border border-input bg-background rounded-md min-h-[80px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Notes and Status */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'ملاحظات وحالة' : 'Notes and Status'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'ar' ? 'ملاحظات' : 'Notes'}
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder={language === 'ar' ? 'ملاحظات إضافية...' : 'Additional notes...'}
              className="w-full px-3 py-2 border border-input bg-background rounded-md min-h-[100px]"
            />
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              className="rounded border-input"
            />
            <label htmlFor="isActive" className="text-sm font-medium">
              {language === 'ar' ? 'مورد نشط' : 'Active Supplier'}
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Submit Buttons */}
      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          <X className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'إلغاء' : 'Cancel'}
        </Button>
        <Button
          type="submit"
          variant="gradient"
          disabled={loading}
          className="min-w-[120px]"
        >
          {loading ? (
            <Loading />
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {isEditing 
                ? (language === 'ar' ? 'تحديث المورد' : 'Update Supplier')
                : (language === 'ar' ? 'حفظ المورد' : 'Save Supplier')
              }
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
