import React from 'react';
import { render, screen } from '@testing-library/react';
import { Breadcrumb } from '../ui/breadcrumb';

describe('Breadcrumb Component', () => {
  const mockSegments = [
    { title: 'الرئيسية', href: '/dashboard' },
    { title: 'العملاء', href: '/dashboard/customers' },
    { title: 'إضافة عميل', href: '/dashboard/customers/new' },
  ];

  it('renders all segments correctly', () => {
    render(<Breadcrumb segments={mockSegments} />);
    
    // التحقق من وجود جميع العناوين
    expect(screen.getByText('الرئيسية')).toBeInTheDocument();
    expect(screen.getByText('العملاء')).toBeInTheDocument();
    expect(screen.getByText('إضافة عميل')).toBeInTheDocument();
  });

  it('renders the last segment without a link', () => {
    render(<Breadcrumb segments={mockSegments} />);
    
    // التحقق من أن العنصر الأخير ليس رابطًا
    const lastSegment = screen.getByText('إضافة عميل');
    expect(lastSegment.tagName).not.toBe('A');
  });

  it('renders links for all segments except the last one', () => {
    render(<Breadcrumb segments={mockSegments} />);
    
    // التحقق من أن العناصر الأخرى روابط
    const homeLink = screen.getByText('الرئيسية');
    const customersLink = screen.getByText('العملاء');
    
    expect(homeLink.closest('a')).toHaveAttribute('href', '/dashboard');
    expect(customersLink.closest('a')).toHaveAttribute('href', '/dashboard/customers');
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-breadcrumb';
    render(<Breadcrumb segments={mockSegments} className={customClass} />);
    
    // التحقق من تطبيق الكلاس المخصص
    const nav = screen.getByRole('navigation');
    expect(nav).toHaveClass(customClass);
  });
});
