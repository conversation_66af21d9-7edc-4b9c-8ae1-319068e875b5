import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Navbar from '../layout/nav-bar';
import { useSession } from 'next-auth/react';
import '@testing-library/jest-dom';
import { LanguageProvider } from '@/contexts/language-context';

// Mock useSession
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock console.error to suppress expected error messages during tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

// Test wrapper with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <LanguageProvider>
    {children}
  </LanguageProvider>
);

describe('Navbar Component', () => {
  const mockToggleSidebar = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with unauthenticated user', () => {
    (useSession as jest.Mock).mockReturnValue({
      data: null,
      status: 'unauthenticated',
    });

    render(
      <TestWrapper>
        <Navbar toggleSidebar={mockToggleSidebar} />
      </TestWrapper>
    );
    
    // التحقق من وجود العناصر الأساسية
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(0);
  });

  it('calls toggleSidebar when the menu button is clicked', () => {
    (useSession as jest.Mock).mockReturnValue({
      data: null,
      status: 'unauthenticated',
    });

    render(
      <TestWrapper>
        <Navbar toggleSidebar={mockToggleSidebar} />
      </TestWrapper>
    );
    
    // النقر على أول زر (زر القائمة)
    const buttons = screen.getAllByRole('button');
    fireEvent.click(buttons[0]);
    
    expect(mockToggleSidebar).toHaveBeenCalledTimes(1);
  });

  it('renders with authenticated user', () => {
    (useSession as jest.Mock).mockReturnValue({
      data: {
        user: {
          name: 'Test User',
          email: '<EMAIL>',
        },
      },
      status: 'authenticated',
    });

    render(
      <TestWrapper>
        <Navbar toggleSidebar={mockToggleSidebar} />
      </TestWrapper>
    );
    
    // التحقق من وجود العناصر الأساسية
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(0);
  });

  it('shows not authenticated state when user is not logged in', () => {
    (useSession as jest.Mock).mockReturnValue({
      data: null,
      status: 'unauthenticated',
    });

    render(
      <TestWrapper>
        <Navbar toggleSidebar={mockToggleSidebar} />
      </TestWrapper>
    );
    
    // التحقق من عدم وجود معلومات المستخدم
    expect(screen.queryByText('Test User')).not.toBeInTheDocument();
  });
});
