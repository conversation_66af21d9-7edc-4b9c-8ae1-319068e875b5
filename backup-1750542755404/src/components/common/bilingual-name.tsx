'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface BilingualNameProps {
  nameAr: string;
  nameEn: string;
  onNameChange?: (nameAr: string, nameEn: string) => void;
  label?: string;
  labelAr?: string;
  labelEn?: string;
  required?: boolean;
  readOnly?: boolean;
  className?: string;
  showPreview?: boolean;
  previewClassName?: string;
  size?: 'default' | 'sm' | 'lg';
  separator?: string;
}

/**
 * مكون لإدارة الأسماء ثنائية اللغة (العربية والإنجليزية)
 * Bilingual Name Component for managing names in both Arabic and English
 */
export function BilingualName({
  nameAr = '',
  nameEn = '',
  onNameChange,
  label = 'الاسم | Name',
  labelAr = 'الاسم بالعربية',
  labelEn = 'Name in English',
  required = false,
  readOnly = false,
  className = '',
  showPreview = true,
  previewClassName = '',
  size = 'default',
  separator = ' | '
}: BilingualNameProps) {
  const [localNameAr, setLocalNameAr] = useState(nameAr);
  const [localNameEn, setLocalNameEn] = useState(nameEn);
  const [activeTab, setActiveTab] = useState<string>('arabic');

  // تحديث القيم المحلية عند تغيير القيم الخارجية
  React.useEffect(() => {
    setLocalNameAr(nameAr);
    setLocalNameEn(nameEn);
  }, [nameAr, nameEn]);

  // معالجة تغيير الاسم العربي
  const handleArabicNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalNameAr(newValue);
    if (onNameChange) {
      onNameChange(newValue, localNameEn);
    }
  };

  // معالجة تغيير الاسم الإنجليزي
  const handleEnglishNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalNameEn(newValue);
    if (onNameChange) {
      onNameChange(localNameAr, newValue);
    }
  };

  // تحديد حجم المكون
  const inputSize = {
    sm: 'h-8 text-sm',
    default: 'h-10',
    lg: 'h-12 text-lg'
  }[size];

  return (
    <div className={cn('bilingual-name-container', className)}>
      {showPreview && (localNameAr || localNameEn) && (
        <div className={cn('bilingual-name-preview mb-2', previewClassName)}>
          <p className="text-sm font-medium text-gray-500">
            {localNameAr || '---'}{separator}{localNameEn || '---'}
          </p>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex items-center justify-between mb-2">
          <Label className="text-base font-medium">{label}</Label>
          <TabsList className="grid grid-cols-2 w-auto">
            <TabsTrigger value="arabic" className="px-3 py-1">العربية</TabsTrigger>
            <TabsTrigger value="english" className="px-3 py-1">English</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="arabic" className="mt-0">
          <div className="space-y-2">
            <Label htmlFor="name-ar" className="text-right block">
              {labelAr}
              {required && <span className="text-red-500 mr-1">*</span>}
            </Label>
            <Input
              id="name-ar"
              dir="rtl"
              value={localNameAr}
              onChange={handleArabicNameChange}
              placeholder="أدخل الاسم بالعربية"
              required={required}
              readOnly={readOnly}
              className={cn(inputSize, "text-right")}
            />
          </div>
        </TabsContent>

        <TabsContent value="english" className="mt-0">
          <div className="space-y-2">
            <Label htmlFor="name-en" className="text-left block">
              {labelEn}
              {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id="name-en"
              dir="ltr"
              value={localNameEn}
              onChange={handleEnglishNameChange}
              placeholder="Enter name in English"
              required={required}
              readOnly={readOnly}
              className={cn(inputSize, "text-left")}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

/**
 * مكون لعرض الاسم ثنائي اللغة
 * Component to display bilingual name
 */
export function BilingualNameDisplay({
  nameAr,
  nameEn,
  className = '',
  separator = ' | ',
  primaryLanguage = 'ar'
}: {
  nameAr: string;
  nameEn: string;
  className?: string;
  separator?: string;
  primaryLanguage?: 'ar' | 'en';
}) {
  // إذا كانت إحدى اللغتين غير متوفرة، نعرض اللغة المتوفرة فقط
  if (!nameAr) return <span className={className}>{nameEn}</span>;
  if (!nameEn) return <span className={className}>{nameAr}</span>;

  // عرض الاسم حسب اللغة الأساسية المحددة
  return (
    <span className={cn('bilingual-name', className)}>
      {primaryLanguage === 'ar' ? (
        <>
          <span className="name-ar">{nameAr}</span>
          {separator}
          <span className="name-en">{nameEn}</span>
        </>
      ) : (
        <>
          <span className="name-en">{nameEn}</span>
          {separator}
          <span className="name-ar">{nameAr}</span>
        </>
      )}
    </span>
  );
}

/**
 * مكون لإدخال الاسم ثنائي اللغة في بطاقة
 * Card component for bilingual name input
 */
export function BilingualNameCard({
  nameAr = '',
  nameEn = '',
  onNameChange,
  title = 'الاسم | Name',
  required = false,
  className = ''
}: {
  nameAr?: string;
  nameEn?: string;
  onNameChange?: (nameAr: string, nameEn: string) => void;
  title?: string;
  required?: boolean;
  className?: string;
}) {
  return (
    <Card className={cn('bilingual-name-card', className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <BilingualName
          nameAr={nameAr}
          nameEn={nameEn}
          onNameChange={onNameChange}
          required={required}
        />
      </CardContent>
    </Card>
  );
}
