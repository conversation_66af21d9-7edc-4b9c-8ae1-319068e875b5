'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Languages, Check, Globe } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { SUPPORTED_LANGUAGES, SupportedLanguage } from '@/config/languages';

interface LanguageSwitcherProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showText?: boolean;
  className?: string;
}

/**
 * مكون تبديل اللغة بين العربية والإنجليزية
 * Language Switcher Component between Arabic and English
 */
export function LanguageSwitcher({
  variant = 'outline',
  size = 'default',
  showText = true,
  className = '',
}: Readonly<LanguageSwitcherProps>) {
  const { language, setLanguage } = useLanguage();
  const [loading, setLoading] = useState(false);

  // تغيير اللغة
  const changeLanguage = (newLanguage: SupportedLanguage) => {
    if (newLanguage === language || loading) return;

    setLoading(true);
    setLanguage(newLanguage);

    // سيتم إعادة تحميل الصفحة بواسطة مزود سياق اللغة
  };

  // الحصول على نص اللغة الحالية
  const getLanguageText = () => {
    return SUPPORTED_LANGUAGES[language].nativeName;
  };

  // تحويل قائمة اللغات المدعومة إلى مصفوفة
  const languages = Object.values(SUPPORTED_LANGUAGES);

  if (size === 'icon') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant={variant} size="icon" disabled={loading} className={className}>
            <Globe className="h-[1.2rem] w-[1.2rem]" />
            <span className="sr-only">تغيير اللغة | Change Language</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>اختر اللغة | Select Language</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {languages.map((lang) => (
            <DropdownMenuItem
              key={lang.code}
              onClick={() => changeLanguage(lang.code)}
              className={`flex items-center justify-between ${language === lang.code ? 'bg-accent' : ''}`}
              disabled={loading || language === lang.code}
            >
              <div className="flex items-center">
                <span className="mx-2">{lang.flag}</span>
                <span>{lang.nativeName}</span>
              </div>
              {language === lang.code && (
                <Check className="h-4 w-4" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={className} disabled={loading}>
          <Languages className={`h-4 w-4 ${showText ? 'ml-2' : ''}`} />
          {showText && <span>{getLanguageText()}</span>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>اختر اللغة | Select Language</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {languages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onClick={() => changeLanguage(lang.code)}
            className={`flex items-center justify-between ${language === lang.code ? 'bg-accent' : ''}`}
            disabled={loading || language === lang.code}
          >
            <div className="flex items-center">
              <span className="mx-2">{lang.flag}</span>
              <span>{lang.nativeName}</span>
            </div>
            {language === lang.code && (
              <Check className="h-4 w-4" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

/**
 * مكون زر تبديل اللغة البسيط
 * Simple Language Toggle Button Component
 */
export function LanguageToggle({
  variant = 'outline',
  size = 'default',
  className = '',
}: Readonly<{
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
}>) {
  const { language, setLanguage } = useLanguage();
  const [loading, setLoading] = useState(false);

  // تبديل اللغة
  const toggleLanguage = () => {
    if (loading) return;

    setLoading(true);
    const newLanguage = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLanguage);

    // سيتم إعادة تحميل الصفحة بواسطة مزود سياق اللغة
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleLanguage}
      className={className}
      disabled={loading}
    >
      {language === 'ar' ? 'English' : 'العربية'}
    </Button>
  );
}
