/**
 * مكونات النماذج المحسنة
 * Enhanced Form Components
 */

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

/**
 * حقل إدخال محسن مع تأثيرات
 * Enhanced Input Field with Effects
 */
interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  hint?: string;
  variant?: 'default' | 'floating' | 'outlined' | 'filled';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export const EnhancedInput: React.FC<EnhancedInputProps> = ({
  label,
  error,
  hint,
  variant = 'default',
  icon,
  iconPosition = 'left',
  className,
  id,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue);
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHasValue(!!e.target.value);
    props.onChange?.(e);
  };

  const variants = {
    default: 'border border-input bg-background',
    floating: 'border border-input bg-background',
    outlined: 'border-2 border-input bg-transparent',
    filled: 'border-0 bg-muted'
  };

  const inputClasses = cn(
    'w-full px-3 py-2 text-sm transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    'disabled:cursor-not-allowed disabled:opacity-50',
    variants[variant],
    variant === 'floating' ? 'rounded-lg pt-6 pb-2' : 'rounded-lg',
    icon && iconPosition === 'left' && 'pl-10',
    icon && iconPosition === 'right' && 'pr-10',
    error && 'border-destructive focus:ring-destructive',
    className
  );

  return (
    <div className="space-y-2">
      <div className="relative">
        {variant === 'floating' ? (
          <div className="relative">
            <input
              id={inputId}
              className={inputClasses}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onChange={handleChange}
              placeholder=" "
              {...props}
            />
            {label && (
              <label
                htmlFor={inputId}
                className={cn(
                  'absolute left-3 transition-all duration-200 pointer-events-none',
                  'text-muted-foreground',
                  (isFocused || hasValue)
                    ? 'top-2 text-xs text-primary'
                    : 'top-1/2 -translate-y-1/2 text-sm'
                )}
              >
                {label}
              </label>
            )}
          </div>
        ) : (
          <>
            {label && (
              <label
                htmlFor={inputId}
                className="block text-sm font-medium text-foreground mb-1"
              >
                {label}
              </label>
            )}
            <input
              id={inputId}
              className={inputClasses}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onChange={handleChange}
              {...props}
            />
          </>
        )}

        {icon && (
          <div
            className={cn(
              'absolute top-1/2 -translate-y-1/2 text-muted-foreground',
              iconPosition === 'left' ? 'left-3' : 'right-3',
              variant === 'floating' && label && 'top-1/2'
            )}
          >
            {icon}
          </div>
        )}
      </div>

      {error && (
        <p className="text-sm text-destructive animate-fade-in">{error}</p>
      )}

      {hint && !error && (
        <p className="text-sm text-muted-foreground">{hint}</p>
      )}
    </div>
  );
};

/**
 * منطقة نص محسنة مع تغيير الحجم التلقائي
 * Enhanced Textarea with Auto-resize
 */
interface EnhancedTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  hint?: string;
  autoResize?: boolean;
  maxHeight?: number;
}

export const EnhancedTextarea: React.FC<EnhancedTextareaProps> = ({
  label,
  error,
  hint,
  autoResize = false,
  maxHeight = 200,
  className,
  id,
  onChange,
  ...props
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (autoResize && textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const newHeight = Math.min(textareaRef.current.scrollHeight, maxHeight);
      textareaRef.current.style.height = `${newHeight}px`;
    }
    onChange?.(e);
  };

  useEffect(() => {
    if (autoResize && textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const newHeight = Math.min(textareaRef.current.scrollHeight, maxHeight);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  }, [autoResize, maxHeight]);

  return (
    <div className="space-y-2">
      {label && (
        <label
          htmlFor={textareaId}
          className="block text-sm font-medium text-foreground"
        >
          {label}
        </label>
      )}

      <textarea
        ref={textareaRef}
        id={textareaId}
        className={cn(
          'w-full px-3 py-2 text-sm border border-input bg-background rounded-lg',
          'transition-all duration-200 resize-none',
          'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
          'disabled:cursor-not-allowed disabled:opacity-50',
          error && 'border-destructive focus:ring-destructive',
          autoResize && 'overflow-hidden',
          className
        )}
        onChange={handleChange}
        style={{ minHeight: '80px' }}
        {...props}
      />

      {error && (
        <p className="text-sm text-destructive animate-fade-in">{error}</p>
      )}

      {hint && !error && (
        <p className="text-sm text-muted-foreground">{hint}</p>
      )}
    </div>
  );
};

/**
 * قائمة اختيار محسنة
 * Enhanced Select Component
 */
interface EnhancedSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  hint?: string;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  placeholder?: string;
}

export const EnhancedSelect: React.FC<EnhancedSelectProps> = ({
  label,
  error,
  hint,
  options,
  placeholder,
  className,
  id,
  ...props
}) => {
  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="space-y-2">
      {label && (
        <label
          htmlFor={selectId}
          className="block text-sm font-medium text-foreground"
        >
          {label}
        </label>
      )}

      <div className="relative">
        <select
          id={selectId}
          className={cn(
            'w-full px-3 py-2 text-sm border border-input bg-background rounded-lg',
            'transition-all duration-200 appearance-none cursor-pointer',
            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
            'disabled:cursor-not-allowed disabled:opacity-50',
            error && 'border-destructive focus:ring-destructive',
            className
          )}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>

        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg
            className="w-4 h-4 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>

      {error && (
        <p className="text-sm text-destructive animate-fade-in">{error}</p>
      )}

      {hint && !error && (
        <p className="text-sm text-muted-foreground">{hint}</p>
      )}
    </div>
  );
};

/**
 * مربع اختيار محسن
 * Enhanced Checkbox Component
 */
interface EnhancedCheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  error?: string;
}

export const EnhancedCheckbox: React.FC<EnhancedCheckboxProps> = ({
  label,
  description,
  error,
  className,
  id,
  ...props
}) => {
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="space-y-2">
      <div className="flex items-start space-x-3">
        <input
          type="checkbox"
          id={checkboxId}
          className={cn(
            'w-4 h-4 mt-1 text-primary border border-input rounded',
            'transition-all duration-200 cursor-pointer',
            'focus:ring-2 focus:ring-ring focus:ring-offset-2',
            'disabled:cursor-not-allowed disabled:opacity-50',
            error && 'border-destructive',
            className
          )}
          {...props}
        />

        <div className="flex-1">
          {label && (
            <label
              htmlFor={checkboxId}
              className="text-sm font-medium text-foreground cursor-pointer"
            >
              {label}
            </label>
          )}

          {description && (
            <p className="text-sm text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      </div>

      {error && (
        <p className="text-sm text-destructive animate-fade-in">{error}</p>
      )}
    </div>
  );
};

/**
 * مجموعة أزرار اختيار محسنة
 * Enhanced Radio Group Component
 */
interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

interface EnhancedRadioGroupProps {
  name: string;
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  label?: string;
  error?: string;
  orientation?: 'horizontal' | 'vertical';
}

export const EnhancedRadioGroup: React.FC<EnhancedRadioGroupProps> = ({
  name,
  options,
  value,
  onChange,
  label,
  error,
  orientation = 'vertical'
}) => {
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-foreground">
          {label}
        </label>
      )}

      <div
        className={cn(
          'space-y-3',
          orientation === 'horizontal' && 'flex space-x-6 space-y-0'
        )}
      >
        {options.map((option) => {
          const radioId = `${name}-${option.value}`;
          
          return (
            <div key={option.value} className="flex items-start space-x-3">
              <input
                type="radio"
                id={radioId}
                name={name}
                value={option.value}
                checked={value === option.value}
                onChange={(e) => onChange?.(e.target.value)}
                disabled={option.disabled}
                className={cn(
                  'w-4 h-4 mt-1 text-primary border border-input',
                  'transition-all duration-200 cursor-pointer',
                  'focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  'disabled:cursor-not-allowed disabled:opacity-50',
                  error && 'border-destructive'
                )}
              />

              <div className="flex-1">
                <label
                  htmlFor={radioId}
                  className="text-sm font-medium text-foreground cursor-pointer"
                >
                  {option.label}
                </label>

                {option.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {option.description}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {error && (
        <p className="text-sm text-destructive animate-fade-in">{error}</p>
      )}
    </div>
  );
};
