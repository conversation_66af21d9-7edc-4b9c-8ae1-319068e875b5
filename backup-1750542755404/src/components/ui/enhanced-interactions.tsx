/**
 * مكونات التفاعل المحسنة
 * Enhanced Interaction Components
 */

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

/**
 * زر محسن مع تأثيرات تفاعلية متقدمة
 * Enhanced Button with Advanced Interactive Effects
 */
interface EnhancedInteractiveButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'magnetic' | 'ripple' | 'morphing' | 'particle';
  effect?: 'bounce' | 'pulse' | 'shake' | 'glow';
  children: React.ReactNode;
}

export const EnhancedInteractiveButton: React.FC<EnhancedInteractiveButtonProps> = ({
  variant = 'magnetic',
  effect,
  className,
  children,
  onClick,
  ...props
}) => {
  const [isClicked, setIsClicked] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (variant === 'ripple') {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const newRipple = { id: Date.now(), x, y };
      
      setRipples(prev => [...prev, newRipple]);
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 600);
    }

    if (effect) {
      setIsClicked(true);
      setTimeout(() => setIsClicked(false), 300);
    }

    onClick?.(e);
  };

  const variants = {
    magnetic: 'btn-magnetic',
    ripple: 'relative overflow-hidden',
    morphing: 'transition-all duration-300 hover:rounded-full',
    particle: 'relative'
  };

  const effects = {
    bounce: isClicked ? 'animate-bounce-in' : '',
    pulse: isClicked ? 'animate-pulse-soft' : '',
    shake: isClicked ? 'animate-wobble' : '',
    glow: isClicked ? 'animate-glow' : ''
  };

  return (
    <button
      className={cn(
        'relative px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium',
        'transition-all duration-200 focus-ring-enhanced',
        variants[variant],
        effect && effects[effect],
        className
      )}
      onClick={handleClick}
      {...props}
    >
      {variant === 'ripple' && ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute bg-white/30 rounded-full animate-ping"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
          }}
        />
      ))}
      {children}
    </button>
  );
};

/**
 * بطاقة تفاعلية محسنة
 * Enhanced Interactive Card
 */
interface EnhancedInteractiveCardProps extends React.HTMLAttributes<HTMLDivElement> {
  tilt?: boolean;
  parallax?: boolean;
  reveal?: boolean;
  children: React.ReactNode;
}

export const EnhancedInteractiveCard: React.FC<EnhancedInteractiveCardProps> = ({
  tilt = false,
  parallax = false,
  reveal = false,
  className,
  children,
  ...props
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!tilt && !parallax) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;
    setMousePosition({ x, y });
  };

  const tiltStyle = tilt && isHovered ? {
    transform: `perspective(1000px) rotateX(${(mousePosition.y - 0.5) * 10}deg) rotateY(${(mousePosition.x - 0.5) * -10}deg) scale3d(1.05, 1.05, 1.05)`
  } : {};

  const parallaxStyle = parallax && isHovered ? {
    transform: `translate3d(${(mousePosition.x - 0.5) * 20}px, ${(mousePosition.y - 0.5) * 20}px, 0)`
  } : {};

  return (
    <div
      className={cn(
        'relative bg-card border border-border rounded-xl p-6',
        'transition-all duration-300 ease-out',
        tilt && 'transform-gpu',
        reveal && 'animate-fade-in-up',
        className
      )}
      style={{ ...tiltStyle, ...parallaxStyle }}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * مؤشر تحميل محسن
 * Enhanced Loading Indicator
 */
interface EnhancedLoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'wave' | 'skeleton';
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'accent';
  text?: string;
}

export const EnhancedLoading: React.FC<EnhancedLoadingProps> = ({
  variant = 'spinner',
  size = 'md',
  color = 'primary',
  text
}) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const colors = {
    primary: 'text-primary',
    secondary: 'text-secondary',
    accent: 'text-accent'
  };

  const renderVariant = () => {
    switch (variant) {
      case 'spinner':
        return (
          <div className={cn(
            'animate-spin rounded-full border-2 border-current border-t-transparent',
            sizes[size],
            colors[color]
          )} />
        );

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map(i => (
              <div
                key={i}
                className={cn(
                  'rounded-full bg-current animate-pulse',
                  size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4',
                  colors[color]
                )}
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <div className={cn(
            'rounded-full bg-current animate-pulse-soft',
            sizes[size],
            colors[color]
          )} />
        );

      case 'wave':
        return (
          <div className="flex items-end space-x-1">
            {[0, 1, 2, 3, 4].map(i => (
              <div
                key={i}
                className={cn(
                  'bg-current animate-pulse',
                  size === 'sm' ? 'w-1 h-4' : size === 'md' ? 'w-1.5 h-6' : 'w-2 h-8',
                  colors[color]
                )}
                style={{ 
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: '1s'
                }}
              />
            ))}
          </div>
        );

      case 'skeleton':
        return (
          <div className="space-y-3">
            <div className="loading-shimmer h-4 rounded w-3/4" />
            <div className="loading-shimmer h-4 rounded w-1/2" />
            <div className="loading-shimmer h-4 rounded w-5/6" />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-2">
      {renderVariant()}
      {text && (
        <p className={cn('text-sm animate-pulse', colors[color])}>
          {text}
        </p>
      )}
    </div>
  );
};

/**
 * شريط تقدم محسن
 * Enhanced Progress Bar
 */
interface EnhancedProgressProps {
  value: number;
  max?: number;
  variant?: 'default' | 'gradient' | 'striped' | 'glow';
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  animated?: boolean;
}

export const EnhancedProgress: React.FC<EnhancedProgressProps> = ({
  value,
  max = 100,
  variant = 'default',
  size = 'md',
  showValue = false,
  animated = true
}) => {
  const percentage = Math.min((value / max) * 100, 100);

  const sizes = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  };

  const variants = {
    default: 'bg-primary',
    gradient: 'bg-gradient-to-r from-primary to-primary-600',
    striped: 'bg-primary bg-striped',
    glow: 'bg-primary shadow-glow'
  };

  return (
    <div className="w-full">
      <div className={cn(
        'w-full bg-muted rounded-full overflow-hidden',
        sizes[size]
      )}>
        <div
          className={cn(
            'h-full transition-all duration-500 ease-out',
            variants[variant],
            animated && 'animate-pulse-soft'
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      {showValue && (
        <div className="mt-1 text-sm text-muted-foreground text-center">
          {Math.round(percentage)}%
        </div>
      )}
    </div>
  );
};

/**
 * إشعار محسن مع تأثيرات
 * Enhanced Notification with Effects
 */
interface EnhancedNotificationProps {
  type?: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose?: () => void;
}

export const EnhancedNotification: React.FC<EnhancedNotificationProps> = ({
  type = 'info',
  title,
  message,
  duration = 5000,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => onClose?.(), 300);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const types = {
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800'
  };

  if (!isVisible) return null;

  return (
    <div className={cn(
      'fixed top-4 right-4 max-w-sm p-4 rounded-lg border shadow-lg z-50',
      'notification-slide-in',
      types[type]
    )}>
      <div className="flex justify-between items-start">
        <div>
          <h4 className="font-medium">{title}</h4>
          {message && <p className="mt-1 text-sm opacity-90">{message}</p>}
        </div>
        {onClose && (
          <button
            onClick={() => {
              setIsVisible(false);
              setTimeout(() => onClose(), 300);
            }}
            className="ml-4 text-current opacity-50 hover:opacity-100 transition-opacity"
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
};
