'use client';

import { useState, useEffect } from 'react';
import { Moon, Sun } from 'lucide-react';
import { useI18n } from '@/lib/i18n';

export function ThemeToggle() {
  const { t } = useI18n();
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    try {
      // تحقق من الوضع المحفوظ في التخزين المحلي
      const savedTheme = localStorage.getItem('theme');
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

      if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        setIsDarkMode(true);
        document.documentElement.classList.add('dark');
      } else {
        setIsDarkMode(false);
        document.documentElement.classList.remove('dark');
      }
    } catch (error) {
      console.error(t('theme.errors.loadError'), error);
      // استخدام الوضع النهاري كوضع افتراضي في حالة حدوث خطأ
      setIsDarkMode(false);
    }
  }, []);

  const toggleTheme = () => {
    try {
      if (isDarkMode) {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
        setIsDarkMode(false);
      } else {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
        setIsDarkMode(true);
      }
    } catch (error) {
      console.error(t('theme.errors.toggleError'), error);
      // تغيير حالة الوضع في الواجهة حتى لو فشل التخزين
      setIsDarkMode(!isDarkMode);
      if (isDarkMode) {
        document.documentElement.classList.remove('dark');
      } else {
        document.documentElement.classList.add('dark');
      }
    }
  };

  return (
    <button
      onClick={toggleTheme}
      className="relative p-2 rounded-full bg-secondary/50 hover:bg-secondary/80 dark:bg-secondary/30 dark:hover:bg-secondary/50 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/30 shadow-sm"
      aria-label={isDarkMode ? t('theme.switchToLight') : t('theme.switchToDark')}
      title={isDarkMode ? t('theme.switchToLight') : t('theme.switchToDark')}
    >
      <span className="sr-only">{isDarkMode ? t('theme.switchToLight') : t('theme.switchToDark')}</span>
      <span
        className={`absolute inset-0 rounded-full transition-opacity duration-300 ease-in-out ${isDarkMode ? 'opacity-0' : 'opacity-100'
          }`}
      >
        <Moon className="h-5 w-5 text-primary absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
      </span>
      <span
        className={`absolute inset-0 rounded-full transition-opacity duration-300 ease-in-out ${isDarkMode ? 'opacity-100' : 'opacity-0'
          }`}
      >
        <Sun className="h-5 w-5 text-yellow-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
      </span>
      <span className="invisible">
        {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
      </span>
    </button>
  );
}
