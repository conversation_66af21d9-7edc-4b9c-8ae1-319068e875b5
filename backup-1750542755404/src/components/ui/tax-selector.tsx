'use client';

import { useState, useEffect } from 'react';
import { TaxType, DEFAULT_TAX_TYPE, TAXES, DEFAULT_TAX_RATE, cn } from '@/lib/utils';
import { useI18n } from '@/lib/i18n';
import { Input } from '@/components/ui/enhanced-form';

interface TaxSelectorProps {
  readonly onChangeTaxType?: (taxType: TaxType) => void;
  readonly onChangeTaxRate?: (taxRate: number) => void;
  readonly taxType?: TaxType;
  readonly taxRate?: number;
  readonly className?: string;
  readonly variant?: 'default' | 'compact' | 'minimal';
  readonly showRate?: boolean;
  readonly disabled?: boolean;
}

/**
 * مكون اختيار الضريبة - يسمح للمستخدم باختيار نوع الضريبة ونسبتها
 * Tax Selector Component - Allows user to select tax type and rate
 */
export default function TaxSelector({
  onChangeTaxType,
  onChangeTaxRate,
  taxType = DEFAULT_TAX_TYPE,
  taxRate = DEFAULT_TAX_RATE,
  className = '',
  variant = 'default',
  showRate = true,
  disabled = false
}: TaxSelectorProps) {
  const { t, language } = useI18n();
  const [selectedTaxType, setSelectedTaxType] = useState<TaxType>(taxType);
  const [selectedTaxRate, setSelectedTaxRate] = useState<number>(taxRate);

  // تحديث نوع الضريبة ونسبتها عند تغيير القيمة من الخارج
  // Update tax type and rate when value changes from outside
  useEffect(() => {
    setSelectedTaxType(taxType);
  }, [taxType]);

  useEffect(() => {
    setSelectedTaxRate(taxRate);
  }, [taxRate]);

  // معالجة تغيير نوع الضريبة
  // Handle tax type change
  const handleTaxTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newTaxType = e.target.value as TaxType;
    setSelectedTaxType(newTaxType);
    
    // تحديث نسبة الضريبة تلقائياً عند تغيير النوع
    // Automatically update tax rate when type changes
    const taxInfo = TAXES.find(tax => tax.type === newTaxType);
    if (taxInfo) {
      setSelectedTaxRate(taxInfo.defaultRate);
      if (onChangeTaxRate) {
        onChangeTaxRate(taxInfo.defaultRate);
      }
    }
    
    if (onChangeTaxType) {
      onChangeTaxType(newTaxType);
    }
  };

  // معالجة تغيير نسبة الضريبة
  // Handle tax rate change
  const handleTaxRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTaxRate = parseFloat(e.target.value);
    if (!isNaN(newTaxRate) && newTaxRate >= 0) {
      setSelectedTaxRate(newTaxRate);
      if (onChangeTaxRate) {
        onChangeTaxRate(newTaxRate);
      }
    }
  };

  // تحديد أنماط العرض المختلفة
  // Define different display styles
  const selectStyles = {
    default: "p-2 border border-gray-300 rounded-md text-sm transition-colors hover:border-primary focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 dark:bg-gray-800 dark:border-gray-700 dark:text-white",
    compact: "p-1 border border-gray-300 rounded-md text-xs transition-colors hover:border-primary focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary/20 dark:bg-gray-800 dark:border-gray-700 dark:text-white",
    minimal: "bg-transparent text-sm focus:outline-none focus:ring-0 border-none p-0 dark:text-white"
  };

  const inputStyles = {
    default: "w-20 p-2 border border-gray-300 rounded-md text-sm text-center",
    compact: "w-16 p-1 border border-gray-300 rounded-md text-xs text-center",
    minimal: "w-12 bg-transparent text-sm focus:outline-none focus:ring-0 border-none p-0 text-center"
  };

  return (
    <div className={cn('tax-selector flex items-center gap-2', className)}>
      <select
        value={selectedTaxType}
        onChange={handleTaxTypeChange}
        className={selectStyles[variant]}
        aria-label={t('common.tax')}
        disabled={disabled}
      >
        {TAXES.map(tax => (
          <option key={tax.type} value={tax.type}>
            {language === 'ar' ? tax.nameAr : tax.nameEn}
          </option>
        ))}
      </select>

      {showRate && selectedTaxType !== 'NONE' && (
        <div className="flex items-center">
          <Input
            type="number"
            min="0"
            max="100"
            step="0.01"
            value={selectedTaxRate}
            onChange={handleTaxRateChange}
            className={inputStyles[variant]}
            disabled={disabled || selectedTaxType === 'NONE'}
          />
          <span className="mr-1">%</span>
        </div>
      )}
    </div>
  );
}
