"use client";

import { useState } from "react";
import { Control, Controller, FieldErrors } from "react-hook-form";
import { cn } from "@/lib/utils";

interface DatePickerProps {
  name: string;
  control: Control<any>;
  errors: FieldErrors;
}

function DatePicker({ name, control, errors }: DatePickerProps) {
  const hasError = errors[name];
  
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <input
          type="date"
          value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
          onChange={(e) => {
            const date = new Date(e.target.value);
            field.onChange(date);
          }}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            hasError && "border-red-500 focus-visible:ring-red-500"
          )}
        />
      )}
    />
  );
}

export { DatePicker };
export default DatePicker;