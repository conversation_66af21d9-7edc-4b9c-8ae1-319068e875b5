/**
 * مكونات التخطيط المحسنة
 * Enhanced Layout Components
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * حاوية محسنة مع تأثيرات بصرية
 * Enhanced Container with Visual Effects
 */
interface EnhancedContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'gradient' | 'floating';
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  animated?: boolean;
  children: React.ReactNode;
}

export const EnhancedContainer: React.FC<EnhancedContainerProps> = ({
  variant = 'default',
  size = 'md',
  animated = true,
  className,
  children,
  ...props
}) => {
  const variants = {
    default: 'bg-background border border-border',
    glass: 'glass backdrop-blur-md border border-white/20',
    gradient: 'bg-gradient-to-br from-primary/5 to-secondary/5 border border-primary/20',
    floating: 'bg-background shadow-soft hover:shadow-medium border border-border'
  };

  const sizes = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'w-full'
  };

  return (
    <div
      className={cn(
        'rounded-xl p-6',
        variants[variant],
        sizes[size],
        animated && 'transition-all-smooth hover-lift',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * شبكة محسنة مع تأثيرات تفاعلية
 * Enhanced Grid with Interactive Effects
 */
interface EnhancedGridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  responsive?: boolean;
  animated?: boolean;
  children: React.ReactNode;
}

export const EnhancedGrid: React.FC<EnhancedGridProps> = ({
  cols = 3,
  gap = 'md',
  responsive = true,
  animated = true,
  className,
  children,
  ...props
}) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  };

  const gaps = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  return (
    <div
      className={cn(
        'grid',
        responsive ? gridCols[cols] : `grid-cols-${cols}`,
        gaps[gap],
        animated && 'animate-fade-in',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * بطاقة محسنة مع تأثيرات متقدمة
 * Enhanced Card with Advanced Effects
 */
interface EnhancedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'gradient';
  hover?: 'none' | 'lift' | 'scale' | 'glow' | 'rotate';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}

export const EnhancedCard: React.FC<EnhancedCardProps> = ({
  variant = 'default',
  hover = 'lift',
  padding = 'md',
  className,
  children,
  ...props
}) => {
  const variants = {
    default: 'bg-card border border-border',
    elevated: 'bg-card shadow-medium border border-border',
    outlined: 'bg-transparent border-2 border-primary/20',
    glass: 'glass backdrop-blur-md border border-white/20',
    gradient: 'bg-gradient-to-br from-primary/5 to-secondary/5 border border-primary/20'
  };

  const hovers = {
    none: '',
    lift: 'hover-lift',
    scale: 'hover-scale',
    glow: 'hover-glow',
    rotate: 'hover-rotate'
  };

  const paddings = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  };

  return (
    <div
      className={cn(
        'rounded-xl transition-all-smooth',
        variants[variant],
        hovers[hover],
        paddings[padding],
        'animate-fade-in',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * قسم محسن مع تأثيرات خلفية
 * Enhanced Section with Background Effects
 */
interface EnhancedSectionProps extends React.HTMLAttributes<HTMLElement> {
  background?: 'none' | 'subtle' | 'gradient' | 'mesh' | 'floating';
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}

export const EnhancedSection: React.FC<EnhancedSectionProps> = ({
  background = 'none',
  spacing = 'lg',
  className,
  children,
  ...props
}) => {
  const backgrounds = {
    none: '',
    subtle: 'bg-muted/30',
    gradient: 'bg-gradient-to-br from-primary/5 via-transparent to-secondary/5',
    mesh: 'bg-mesh-gradient',
    floating: 'bg-floating-shapes'
  };

  const spacings = {
    sm: 'py-8',
    md: 'py-12',
    lg: 'py-16',
    xl: 'py-24'
  };

  return (
    <section
      className={cn(
        'relative overflow-hidden',
        backgrounds[background],
        spacings[spacing],
        className
      )}
      {...props}
    >
      {children}
    </section>
  );
};

/**
 * عنوان محسن مع تأثيرات نصية
 * Enhanced Heading with Text Effects
 */
interface EnhancedHeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  variant?: 'default' | 'gradient' | 'glow' | 'outlined';
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  children: React.ReactNode;
}

export const EnhancedHeading: React.FC<EnhancedHeadingProps> = ({
  level = 2,
  variant = 'default',
  size = 'lg',
  className,
  children,
  ...props
}) => {
  const Component = `h${level}` as keyof JSX.IntrinsicElements;

  const variants = {
    default: 'text-foreground',
    gradient: 'text-gradient',
    glow: 'text-glow',
    outlined: 'text-transparent stroke-current stroke-1'
  };

  const sizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl',
    '2xl': 'text-4xl',
    '3xl': 'text-5xl'
  };

  return (
    <Component
      className={cn(
        'font-bold tracking-tight heading',
        variants[variant],
        sizes[size],
        'animate-fade-in-up',
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};

/**
 * فاصل محسن مع تأثيرات بصرية
 * Enhanced Divider with Visual Effects
 */
interface EnhancedDividerProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'line' | 'gradient' | 'dotted' | 'glow';
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'sm' | 'md' | 'lg';
  label?: string;
}

export const EnhancedDivider: React.FC<EnhancedDividerProps> = ({
  variant = 'line',
  orientation = 'horizontal',
  spacing = 'md',
  label,
  className,
  ...props
}) => {
  const variants = {
    line: 'border-border',
    gradient: 'border-transparent bg-gradient-to-r from-transparent via-border to-transparent',
    dotted: 'border-border border-dotted',
    glow: 'border-primary/50 shadow-glow'
  };

  const spacings = {
    sm: orientation === 'horizontal' ? 'my-2' : 'mx-2',
    md: orientation === 'horizontal' ? 'my-4' : 'mx-4',
    lg: orientation === 'horizontal' ? 'my-6' : 'mx-6'
  };

  if (label && orientation === 'horizontal') {
    return (
      <div className={cn('relative', spacings[spacing], className)} {...props}>
        <div className={cn('absolute inset-0 flex items-center')}>
          <span className={cn('w-full border-t', variants[variant])} />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">{label}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        orientation === 'horizontal' ? 'border-t w-full' : 'border-l h-full',
        variants[variant],
        spacings[spacing],
        className
      )}
      {...props}
    />
  );
};
