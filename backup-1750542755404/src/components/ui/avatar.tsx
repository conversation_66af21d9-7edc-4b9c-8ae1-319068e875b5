import { getInitials } from "@/lib/utils";

interface AvatarProps {
  name: string;
  image?: string;
  size?: "sm" | "md" | "lg";
}

export function Avatar({ name, image, size = "md" }: AvatarProps) {
  const sizeClasses = {
    sm: "w-8 h-8 text-xs",
    md: "w-10 h-10 text-sm",
    lg: "w-12 h-12 text-base",
  };
  
  const initials = getInitials(name);
  
  return (
    <div className={`${sizeClasses[size]} rounded-full flex items-center justify-center bg-primary text-white`}>
      {image ? (
        <img src={image} alt={name} className="rounded-full w-full h-full object-cover" />
      ) : (
        <span>{initials}</span>
      )}
    </div>
  );
}