'use client';

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

interface BilingualToastProps {
  messageAr: string;
  messageEn: string;
  type?: ToastType;
  duration?: number;
  onClose?: () => void;
  className?: string;
}

/**
 * مكون إشعار ثنائي اللغة
 * Bilingual Toast Component
 */
export function BilingualToast({
  messageAr,
  messageEn,
  type = 'info',
  duration = 5000,
  onClose,
  className,
}: Readonly<BilingualToastProps>) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onClose) onClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  if (!isVisible) return null;

  // تحديد لون الخلفية حسب نوع الإشعار
  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-600';
      case 'error':
        return 'bg-red-600';
      case 'warning':
        return 'bg-amber-600';
      case 'info':
        return 'bg-blue-600';
      default:
        return 'bg-blue-600';
    }
  };

  // تحديد أيقونة الإشعار حسب النوع
  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'info':
      default:
        return (
          <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <div
      className={cn(
        'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 min-w-[320px] max-w-md rounded-lg shadow-lg',
        getBackgroundColor(),
        className
      )}
    >
      <div className="flex items-start p-4">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 mr-3 flex-1">
          <div className="text-white">
            <p className="text-sm font-medium mb-1 text-right" dir="rtl">{messageAr}</p>
            <p className="text-sm font-medium" dir="ltr">{messageEn}</p>
          </div>
        </div>
        <button
          onClick={() => {
            setIsVisible(false);
            if (onClose) onClose();
          }}
          className="flex-shrink-0 ml-1 text-white focus:outline-none"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
}

// مدير الإشعارات
interface ToastItem {
  id: string;
  messageAr: string;
  messageEn: string;
  type: ToastType;
  duration?: number;
}

interface ToastContainerProps {
  className?: string;
}

/**
 * مكون حاوية الإشعارات
 * Toast Container Component
 */
export function BilingualToastContainer({ className }: Readonly<ToastContainerProps>) {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  // إضافة إشعار جديد إلى القائمة
  const addToast = (toast: Omit<ToastItem, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts((prev) => [...prev, { ...toast, id }]);
    return id;
  };

  // إزالة إشعار من القائمة
  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  // تعريف الدوال العامة
  useEffect(() => {
    // تعريف الدوال العامة في النافذة
    if (typeof window !== 'undefined') {
      window.showBilingualToast = (
        messageAr: string,
        messageEn: string,
        type: ToastType = 'info',
        duration = 5000
      ) => {
        return addToast({ messageAr, messageEn, type, duration });
      };

      window.closeBilingualToast = (id: string) => {
        removeToast(id);
      };
    }

    // تنظيف عند إزالة المكون
    return () => {
      if (typeof window !== 'undefined') {
        delete window.showBilingualToast;
        delete window.closeBilingualToast;
      }
    };
  }, []);

  return (
    <div className={cn('toast-container', className)}>
      {toasts.map((toast) => (
        <BilingualToast
          key={toast.id}
          messageAr={toast.messageAr}
          messageEn={toast.messageEn}
          type={toast.type}
          duration={toast.duration}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </div>
  );
}

// تعريف الأنواع العامة
declare global {
  interface Window {
    showBilingualToast: (
      messageAr: string,
      messageEn: string,
      type?: ToastType,
      duration?: number
    ) => string;
    closeBilingualToast: (id: string) => void;
  }
}

// دالة مساعدة لعرض الإشعارات
export const bilingualToast = {
  success: (messageAr: string, messageEn: string, duration = 5000) => {
    if (typeof window !== 'undefined') {
      return window.showBilingualToast(messageAr, messageEn, 'success', duration);
    }
    return '';
  },
  error: (messageAr: string, messageEn: string, duration = 5000) => {
    if (typeof window !== 'undefined') {
      return window.showBilingualToast(messageAr, messageEn, 'error', duration);
    }
    return '';
  },
  warning: (messageAr: string, messageEn: string, duration = 5000) => {
    if (typeof window !== 'undefined') {
      return window.showBilingualToast(messageAr, messageEn, 'warning', duration);
    }
    return '';
  },
  info: (messageAr: string, messageEn: string, duration = 5000) => {
    if (typeof window !== 'undefined') {
      return window.showBilingualToast(messageAr, messageEn, 'info', duration);
    }
    return '';
  },
};
