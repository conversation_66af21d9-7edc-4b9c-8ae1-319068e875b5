'use client';

import { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends Omit<ImageProps, 'onLoad' | 'onError'> {
  fallbackSrc?: string;
}

/**
 * مكون صورة محسن مع دعم للتحميل المتأخر وصورة بديلة في حالة الخطأ
 */
export function OptimizedImage({
  src,
  alt,
  fallbackSrc = '/images/placeholder.jpg',
  className,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  // التعامل مع اكتمال تحميل الصورة
  const handleLoad = () => {
    setIsLoading(false);
  };

  // التعامل مع خطأ تحميل الصورة
  const handleError = () => {
    setIsLoading(false);
    setError(true);
  };

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* عرض مؤشر التحميل أثناء تحميل الصورة */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="h-8 w-8 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700" />
        </div>
      )}

      {/* عرض الصورة الأصلية أو الصورة البديلة في حالة الخطأ */}
      <Image
        src={error ? fallbackSrc : src}
        alt={alt}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100'
        )}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
    </div>
  );
}
