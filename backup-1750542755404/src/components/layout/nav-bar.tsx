'use client';

import React from 'react';
import { Menu, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import AppLogo from '@/components/ui/app-logo';
import { NotificationsDropdown } from '@/components/notifications/notifications-dropdown';
import { LanguageSwitcher } from '@/components/common/language-switcher';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface NavbarProps {
    readonly toggleSidebar: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ toggleSidebar }) => {

    return (
        <nav className="bg-white shadow-sm border-b h-16 flex items-center px-4">
            <div className="flex-1 flex justify-between items-center">
                <div className="flex items-center">
                    <button
                        onClick={toggleSidebar}
                        className="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none ml-2"
                        aria-label="فتح/إغلاق القائمة الجانبية"
                    >
                        <Menu className="h-5 w-5" />
                    </button>
                    <AppLogo showName={true} showEnglishName={true} />
                </div>

                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <NotificationsDropdown />

                    <div className="mx-2">
                        <LanguageSwitcher />
                    </div>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="rounded-full">
                                <User className="h-5 w-5" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>
                                <div className="flex flex-col">
                                    <span>مستخدم تجريبي</span>
                                    <span className="text-xs text-gray-500"><EMAIL></span>
                                </div>
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>الملف الشخصي</DropdownMenuItem>
                            <DropdownMenuItem>الإعدادات</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>تسجيل الخروج</DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </nav>
    );
};

export default Navbar;
