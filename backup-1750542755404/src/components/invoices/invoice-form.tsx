"use client";

import { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRouter } from "next/navigation";
import { Trash2, Plus, Eye, Database } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FormField, Input, Textarea } from "@/components/ui/form-elements";
import { cn, formatCurrency } from "@/lib/utils";
import DatePicker from "@/components/ui/date-picker";
import { useHotkeys } from 'react-hotkeys-hook';

// تعريف مخطط التحقق
const invoiceItemSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, { message: "الرجاء إدخال وصف للمنتج" }),
  quantity: z.coerce.number().positive({ message: "الكمية يجب أن تكون أكبر من صفر" }),
  price: z.coerce.number().nonnegative({ message: "السعر يجب أن يكون صفر أو أكثر" }),
  discount: z.coerce.number().min(0).max(100, { message: "الخصم يجب أن يكون بين 0 و 100%" }).optional().default(0),
  tax: z.coerce.number().min(0).max(100, { message: "الضريبة يجب أن تكون بين 0 و 100%" }).optional().default(15),
});

// تعديل مخطط التحقق من الفاتورة
const invoiceSchema = z.object({
  customerId: z.string().min(1, { message: "الرجاء اختيار عميل" }),
  issueDate: z.date({ required_error: "الرجاء اختيار تاريخ الإصدار" }),
  dueDate: z.date({ required_error: "الرجاء اختيار تاريخ الاستحقاق" }),
  status: z.enum(["DRAFT", "SENT", "PAID", "OVERDUE", "CANCELLED"]).default("DRAFT"),
  items: z.array(invoiceItemSchema).min(1, { message: "يجب إضافة منتج واحد على الأقل" }),
  notes: z.string().optional().nullable(),
  terms: z.string().optional().nullable(),
}).refine(data => {
  return new Date(data.dueDate) >= new Date(data.issueDate);
}, {
  message: "تاريخ الاستحقاق يجب أن يكون بعد تاريخ الإصدار",
  path: ["dueDate"],
});

export type InvoiceFormData = z.infer<typeof invoiceSchema>;

interface InvoiceFormProps {
  initialData?: Partial<InvoiceFormData>;
  isEditing?: boolean;
  customers?: Array<{ id: string; name: string }>;
  onSubmit: (data: InvoiceFormData) => Promise<void>;
}

export default function InvoiceForm({
  initialData,
  isEditing = false,
  customers = [],
  onSubmit
}: Readonly<InvoiceFormProps>) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [subtotal, setSubtotal] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [total, setTotal] = useState(0);
  const [taxRate, setTaxRate] = useState(15); // Default tax rate

  // أضف حالة وإدخال دفعة مقدمة
  const [advancePayment, setAdvancePayment] = useState(initialData?.advancePayment ?? 0);

  // حساب الرصيد المستحق
  const balanceDue = total - advancePayment;

  // إعداد النموذج مع القيم الافتراضية
  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
    getValues,
    trigger
  } = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: {
      customerId: initialData?.customerId ?? "",
      issueDate: initialData?.issueDate ?? new Date(),
      dueDate: initialData?.dueDate ?? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوماً افتراضياً
      status: initialData?.status ?? "DRAFT",
      items: initialData?.items?.length
        ? initialData.items
        : [{ description: "", quantity: 1, price: 0, discount: 0, tax: 15 }],
      notes: initialData?.notes ?? "",
      terms: initialData?.terms ?? "الرجاء الدفع خلال 30 يوماً من تاريخ إصدار الفاتورة."
    }
  });

  // إعداد مصفوفة العناصر
  const { fields, append, remove } = useFieldArray({
    control,
    name: "items"
  });

  // مراقبة العناصر لحساب المجموع
  const items = watch("items");

  // إعادة حساب المجاميع عند تغيير العناصر
  useEffect(() => {
    let calculatedSubtotal = 0;
    let calculatedTaxAmount = 0;

    items.forEach(item => {
      const quantity = Number(item.quantity) || 0;
      const price = Number(item.price) || 0;
      const discount = Number(item.discount) || 0;
      const tax = Number(item.tax) || 0;

      const itemTotal = quantity * price;
      const itemAfterDiscount = itemTotal * (1 - discount / 100);
      const itemTax = itemAfterDiscount * (tax / 100);

      calculatedSubtotal += itemAfterDiscount;
      calculatedTaxAmount += itemTax;
    });

    setSubtotal(calculatedSubtotal);
    setTaxAmount(calculatedTaxAmount);
    setTotal(calculatedSubtotal + calculatedTaxAmount);
  }, [items]);

  // إضافة حالة للنجاح
  const [saveSuccess, setSaveSuccess] = useState(false);

  // تعديل وظيفة الإرسال
  const handleFormSubmit = async (data: InvoiceFormData) => {
    setLoading(true);
    setError(null);
    setSaveSuccess(false);

    try {
      // أضف معلومات الدفعة المقدمة
      const dataWithPayment = {
        ...data,
        advancePayment,
        balanceDue: total - advancePayment
      };

      await onSubmit(dataWithPayment);

      // عرض رسالة نجاح قبل التوجيه
      setSaveSuccess(true);

      // توجيه بعد 1.5 ثانية
      setTimeout(() => {
        router.push('/dashboard/invoices');
      }, 1500);
    } catch (err: any) {
      setError(err.message ?? 'حدث خطأ أثناء حفظ الفاتورة');
      // تمرير إلى حقل معين إذا كان الخطأ متعلق بحقل
      if (err.field) {
        setFocus(err.field);
      }
    } finally {
      setLoading(false);
    }
  };

  // استخراج نص الزر إلى متغير منفصل
  const buttonLabel = isEditing ? 'تحديث الفاتورة' : 'إنشاء الفاتورة';

  // تعديل في أزرار الإجراءات
  const handlePreviewInvoice = () => {
    const values = getValues();

    // التحقق من وجود أخطاء في النموذج
    const isValid = trigger();

    if (!isValid) {
      setError("يرجى إكمال جميع الحقول المطلوبة قبل المعاينة");
      return;
    }

    // فتح نافذة جديدة للمعاينة أو تحويل إلى واجهة المعاينة
    window.localStorage.setItem('invoicePreview', JSON.stringify(values));
    window.open('/dashboard/invoices/preview', '_blank');
  };

  // تسجيل اختصارات لوحة المفاتيح
  useHotkeys('ctrl+s, cmd+s', (e) => {
    e.preventDefault();
    handleSubmit(handleFormSubmit)();
  });

  useHotkeys('ctrl+p, cmd+p', (e) => {
    e.preventDefault();
    handlePreviewInvoice();
  });

  // إضافة إظهار تحذير إذا كان تاريخ الاستحقاق قريبًا من تاريخ الإصدار
  const issueDate = watch('issueDate');
  const dueDate = watch('dueDate');

  const issueDateObj = new Date(issueDate);
  const dueDateObj = new Date(dueDate);
  const daysDifference = Math.ceil((dueDateObj.getTime() - issueDateObj.getTime()) / (1000 * 60 * 60 * 24));

  // Function to load products (placeholder)
  const handleLoadProducts = () => {
    // This would be implemented to load products from a database
    console.log("Loading products...");
    // For now, just add a sample product
    append({ 
      description: "منتج عينة", 
      quantity: 1, 
      price: 100, 
      discount: 0, 
      tax: 15 
    });
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
      {/* عرض الأخطاء */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block">{error}</span>
        </div>
      )}

      {/* رسالة النجاح */}
      {saveSuccess && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative" role="alert">
          <span className="block">تم حفظ الفاتورة بنجاح! جاري التحويل...</span>
        </div>
      )}

      {/* بيانات الفاتورة الأساسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField id="customerId" label="العميل" error={errors.customerId?.message} required>
          <div className="relative">
            <select
              {...register("customerId")}
              id="customerId"
              className={cn(
                "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                errors.customerId && "border-red-500 focus-visible:ring-red-500"
              )}
            >
              <option value="">اختر العميل</option>
              {customers.map(customer => (
                <option key={customer.id} value={customer.id}>
                  {customer.name}
                </option>
              ))}
            </select>
          </div>
        </FormField>

        <FormField id="status" label="حالة الفاتورة" error={errors.status?.message}>
          <div className="relative">
            <select
              {...register("status")}
              id="status"
              className={cn(
                "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                errors.status && "border-red-500 focus-visible:ring-red-500"
              )}
            >
              <option value="DRAFT">مسودة</option>
              <option value="SENT">مرسلة</option>
              <option value="PAID">مدفوعة</option>
              <option value="OVERDUE">متأخرة</option>
              <option value="CANCELLED">ملغية</option>
            </select>
          </div>
        </FormField>

        {/* استبدلنا مكونات التاريخ بمكون أبسط متوافق */}
        <FormField id="issueDate" label="تاريخ الإصدار" error={errors.issueDate?.message} required>
          <DatePicker
            name="issueDate"
            control={control}
            errors={errors}
          />
        </FormField>

        <FormField id="dueDate" label="تاريخ الاستحقاق" error={errors.dueDate?.message} required>
          <DatePicker
            name="dueDate"
            control={control}
            errors={errors}
          />
        </FormField>
      </div>

      {/* جدول العناصر */}
      <Card>
        <CardContent className="p-4">
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-100">
                  <th className="p-3 text-right">الوصف</th>
                  <th className="p-3 text-center">الكمية</th>
                  <th className="p-3 text-center">السعر</th>
                  <th className="p-3 text-center">خصم %</th>
                  <th className="p-3 text-center">ضريبة %</th>
                  <th className="p-3 text-center">المجموع</th>
                  <th className="w-10"></th>
                </tr>
              </thead>
              <tbody>
                {fields.map((field, index) => {
                  // حساب قيمة العنصر
                  const quantity = Number(watch(`items.${index}.quantity`)) || 0;
                  const price = Number(watch(`items.${index}.price`)) || 0;
                  const discount = Number(watch(`items.${index}.discount`)) || 0;
                  const tax = Number(watch(`items.${index}.tax`)) || 0;

                  const itemTotal = quantity * price;
                  const itemAfterDiscount = itemTotal * (1 - discount / 100);
                  const itemWithTax = itemAfterDiscount * (1 + tax / 100);

                  return (
                    <tr key={field.id} className="border-b">
                      <td className="p-3">
                        <Input
                          {...register(`items.${index}.description`)}
                          placeholder="وصف المنتج أو الخدمة"
                          error={!!errors.items?.[index]?.description}
                        />
                      </td>
                      <td className="p-3">
                        <Input
                          {...register(`items.${index}.quantity`)}
                          type="number"
                          min="1"
                          className="text-center"
                          error={!!errors.items?.[index]?.quantity}
                        />
                      </td>
                      <td className="p-3">
                        <Input
                          {...register(`items.${index}.price`)}
                          type="number"
                          min="0"
                          step="0.01"
                          className="text-center"
                          error={!!errors.items?.[index]?.price}
                        />
                      </td>
                      <td className="p-3">
                        <Input
                          {...register(`items.${index}.discount`)}
                          type="number"
                          min="0"
                          max="100"
                          className="text-center"
                          error={!!errors.items?.[index]?.discount}
                        />
                      </td>
                      <td className="p-3">
                        <Input
                          {...register(`items.${index}.tax`)}
                          type="number"
                          min="0"
                          max="100"
                          className="text-center"
                          error={!!errors.items?.[index]?.tax}
                        />
                      </td>
                      <td className="p-3 text-center font-medium">
                        {formatCurrency(itemWithTax)}
                      </td>
                      <td className="p-3">
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          disabled={fields.length === 1}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
              <tfoot>
                <tr>
                  <td colSpan={3} className="p-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => append({ description: "", quantity: 1, price: 0, discount: 0, tax: 15 })}
                      className="w-full"
                    >
                      <Plus className="ml-2 h-4 w-4" />
                      إضافة منتج
                    </Button>
                  </td>
                  <td colSpan={4} className="p-3">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleLoadProducts}
                        className="w-full"
                      >
                        <Database className="ml-2 h-4 w-4" />
                        اختيار من المنتجات المحفوظة
                      </Button>
                    </div>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* ملخص الفاتورة */}
      <div className="flex justify-end">
        <div className="w-full max-w-md space-y-2">
          <div className="flex justify-between items-center p-2 border-b">
            <span className="text-gray-600">المجموع الفرعي:</span>
            <span className="font-medium">{formatCurrency(subtotal)}</span>
          </div>
          <div className="flex justify-between items-center p-2 border-b">
            <span className="text-gray-600">ضريبة القيمة المضافة ({taxRate}%):</span>
            <span className="font-medium">{formatCurrency(taxAmount)}</span>
          </div>
          <div className="flex justify-between items-center p-2 border-b font-semibold">
            <span>الإجمالي:</span>
            <span>{formatCurrency(total)}</span>
          </div>

          <div className="flex justify-between items-center p-2 border-b">
            <span className="text-gray-600">دفعة مقدمة:</span>
            <div className="flex items-center">
              <Input
                type="number"
                min="0"
                max={total}
                value={advancePayment}
                onChange={(e) => setAdvancePayment(Number(e.target.value))}
                className="w-24 text-center h-8"
              />
            </div>
          </div>

          <div className="flex justify-between items-center p-2 font-bold text-lg">
            <span>الرصيد المستحق:</span>
            <span>{formatCurrency(balanceDue)}</span>
          </div>
        </div>
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex justify-between gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => handlePreviewInvoice()}
          disabled={loading}
        >
          <Eye className="ml-2 h-4 w-4" />
          معاينة الفاتورة
        </Button>

        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={loading}
          >
            إلغاء
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading ? 'جاري الحفظ...' : buttonLabel}
          </Button>
        </div>
      </div>

      {/* قائمة بالاختصارات */}
      <div className="mt-4 text-sm text-gray-500 border-t pt-4">
        <p className="font-medium mb-1">اختصارات لوحة المفاتيح:</p>
        <ul className="text-xs space-y-1">
          <li>حفظ الفاتورة: <kbd className="px-1 py-0.5 bg-gray-100 rounded">Ctrl</kbd> + <kbd className="px-1 py-0.5 bg-gray-100 rounded">S</kbd></li>
          <li>معاينة الفاتورة: <kbd className="px-1 py-0.5 bg-gray-100 rounded">Ctrl</kbd> + <kbd className="px-1 py-0.5 bg-gray-100 rounded">P</kbd></li>
        </ul>
      </div>
    </form>
  );
}
