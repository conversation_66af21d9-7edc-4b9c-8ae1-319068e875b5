'use client';

import { useState, useEffect } from "react";
import { formatCurrency } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, Edit, Trash2, Printer, Search, ArrowUpDown } from "lucide-react";
import { useRouter } from "next/navigation";
import { Checkbox } from "@/components/ui/checkbox";
import { useI18n } from "@/lib/i18n";

// أنواع البيانات
type Invoice = {
    id: string;
    invoiceNumber: string;
    issueDate: string;
    dueDate?: string;
    status: string;
    total: number;
    subtotal: number;
    taxAmount: number;
    customer: {
        id: string;
        name: string;
        email?: string;
    };
};

export default function InvoicesList() {
    const router = useRouter();
    const { t, language } = useI18n();
    const [invoices, setInvoices] = useState<Invoice[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sortBy, setSortBy] = useState('date');
    const [sortOrder, setSortOrder] = useState('desc');
    const [activeTab, setActiveTab] = useState('all');
    const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);

    useEffect(() => {
        async function fetchInvoices() {
            try {
                setLoading(true);
                const response = await fetch('/api/invoices');
                if (!response.ok) {
                    throw new Error(t('invoices.errors.fetchFailed'));
                }
                const data = await response.json();

                // تحويل البيانات للتوافق مع المكون
                const formattedData = (data.invoices || []).map((invoice: any) => ({
                    id: invoice.id,
                    invoiceNumber: invoice.number,
                    issueDate: invoice.issueDate,
                    dueDate: invoice.dueDate,
                    status: invoice.status,
                    total: invoice.total,
                    subtotal: invoice.subtotal,
                    taxAmount: invoice.taxAmount,
                    customer: {
                        id: invoice.customer.id,
                        name: invoice.customer.name,
                        email: invoice.customer.email
                    }
                }));

                setInvoices(formattedData);
            } catch (err) {
                setError(t('invoices.errors.fetchError'));
                console.error(err);
            } finally {
                setLoading(false);
            }
        }

        fetchInvoices();
    }, []);

    const handleView = (id: string) => {
        router.push(`/dashboard/invoices/${id}`);
    };

    const handleEdit = (id: string) => {
        router.push(`/dashboard/invoices/${id}/edit`);
    };

    const handlePrint = (id: string) => {
        window.open(`/dashboard/invoices/${id}/print`, '_blank');
    };

    const handleDelete = async (id: string) => {
        if (!confirm(t('invoices.confirmDelete'))) {
            return;
        }

        try {
            setError(null);
            const response = await fetch(`/api/invoices/${id}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                throw new Error('Failed to delete invoice');
            }

            setInvoices(invoices.filter(invoice => invoice.id !== id));
        } catch (err) {
            setError('حدث خطأ أثناء حذف الفاتورة');
            console.error(err);
        }
    };

    const handleBulkDelete = async () => {
        if (selectedInvoices.length === 0) {
            return;
        }

        if (!confirm(t('invoices.confirmBulkDelete', { count: selectedInvoices.length }))) {
            return;
        }

        try {
            // محاكاة حذف متعدد
            setInvoices(invoices.filter(invoice => !selectedInvoices.includes(invoice.id)));
            setSelectedInvoices([]);
        } catch (err) {
            setError(t('invoices.errors.bulkDeleteError'));
            console.error(err);
        }
    };

    const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.checked) {
            setSelectedInvoices(filteredInvoices.map(invoice => invoice.id));
        } else {
            setSelectedInvoices([]);
        }
    };

    const handleSelectInvoice = (id: string) => {
        if (selectedInvoices.includes(id)) {
            setSelectedInvoices(selectedInvoices.filter(invoiceId => invoiceId !== id));
        } else {
            setSelectedInvoices([...selectedInvoices, id]);
        }
    };

    const toggleSortOrder = () => {
        setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    };

    // تصفية الفواتير حسب الحالة والبحث
    const filteredInvoices = invoices.filter(invoice => {
        // تصفية حسب علامة التبويب النشطة
        if (activeTab !== 'all' && invoice.status.toLowerCase() !== activeTab) {
            return false;
        }

        // تصفية حسب البحث
        if (searchTerm && !invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) &&
            !invoice.customer.name.toLowerCase().includes(searchTerm.toLowerCase())) {
            return false;
        }

        // تصفية حسب الحالة
        if (statusFilter !== 'all' && invoice.status.toLowerCase() !== statusFilter.toLowerCase()) {
            return false;
        }

        return true;
    });

    // ترتيب الفواتير
    const sortedInvoices = [...filteredInvoices].sort((a, b) => {
        let comparison = 0;

        if (sortBy === 'date') {
            comparison = new Date(b.issueDate).getTime() - new Date(a.issueDate).getTime();
        } else if (sortBy === 'number') {
            comparison = a.invoiceNumber.localeCompare(b.invoiceNumber);
        } else if (sortBy === 'customer') {
            comparison = a.customer.name.localeCompare(b.customer.name);
        } else if (sortBy === 'amount') {
            comparison = a.total - b.total;
        }

        return sortOrder === 'asc' ? comparison : -comparison;
    });

    // ترجمة حالة الفاتورة
    const getStatusText = (status: string) => {
        const statusKey = status.toLowerCase();
        return t(`invoices.status.${statusKey}`);
    };

    // تحديد لون حالة الفاتورة
    const getStatusClass = (status: string) => {
        switch (status.toLowerCase()) {
            case 'paid': return 'bg-green-100 text-green-800';
            case 'unpaid': return 'bg-yellow-100 text-yellow-800';
            case 'overdue': return 'bg-red-100 text-red-800';
            case 'draft': return 'bg-gray-100 text-gray-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            case 'sent': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <div className="flex h-64 items-center justify-center">
                <div className="animate-spin rounded-full border-4 border-t-4 border-gray-200 border-t-primary h-12 w-12" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center text-red-500 p-4 bg-red-50 rounded-md">
                <p className="font-medium">{error}</p>
                <Button
                    variant="outline"
                    className="mt-2"
                    onClick={() => window.location.reload()}
                >
                    {t('common.retry')}
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* شريط البحث والفلترة */}
            <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                        placeholder={t('invoices.searchPlaceholder')}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4"
                    />
                </div>
                <div className="flex gap-2">
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder={t('invoices.allStatuses')} />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">{t('invoices.allStatuses')}</SelectItem>
                            <SelectItem value="paid">{t('invoices.status.paid')}</SelectItem>
                            <SelectItem value="unpaid">{t('invoices.status.unpaid')}</SelectItem>
                            <SelectItem value="overdue">{t('invoices.status.overdue')}</SelectItem>
                            <SelectItem value="draft">{t('invoices.status.draft')}</SelectItem>
                            <SelectItem value="sent">{t('invoices.status.sent')}</SelectItem>
                            <SelectItem value="cancelled">{t('invoices.status.cancelled')}</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button variant="outline" onClick={toggleSortOrder} className="gap-1">
                        <ArrowUpDown className="h-4 w-4" />
                        {sortOrder === 'asc' ? t('common.ascending') : t('common.descending')}
                    </Button>
                </div>
            </div>

            {/* علامات التبويب */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid grid-cols-4 md:w-1/2">
                    <TabsTrigger value="all">{t('invoices.tabs.all')}</TabsTrigger>
                    <TabsTrigger value="paid">{t('invoices.status.paid')}</TabsTrigger>
                    <TabsTrigger value="unpaid">{t('invoices.status.unpaid')}</TabsTrigger>
                    <TabsTrigger value="overdue">{t('invoices.status.overdue')}</TabsTrigger>
                </TabsList>
            </Tabs>

            {/* جدول الفواتير */}
            <Card>
                <CardContent className="p-0">
                    {selectedInvoices.length > 0 && (
                        <div className="p-4 bg-blue-50 flex justify-between items-center">
                            <p className="text-sm text-blue-700">
                                {t('invoices.selectedCount', { count: selectedInvoices.length })}
                            </p>
                            <div className="flex gap-2">
                                <Button variant="outline" size="sm" onClick={() => setSelectedInvoices([])}>
                                    {t('invoices.deselectAll')}
                                </Button>
                                <Button variant="destructive" size="sm" onClick={handleBulkDelete}>
                                    <Trash2 className="h-4 w-4 ml-1" />
                                    {t('invoices.deleteSelected')}
                                </Button>
                            </div>
                        </div>
                    )}
                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                            <thead className="bg-gray-100">
                                <tr>
                                    <th className="p-4 text-right">
                                        <Checkbox
                                            checked={selectedInvoices.length === filteredInvoices.length && filteredInvoices.length > 0}
                                            onCheckedChange={handleSelectAll}
                                        />
                                    </th>
                                    <th className="p-4 text-right">
                                        <Button variant="ghost" size="sm" className="font-medium" onClick={() => setSortBy('number')}>
                                            <div>{t('invoices.table.invoiceNumber')}</div>
                                            <div className="text-xs font-normal text-gray-500">
                                                {language === 'ar' ? 'Invoice No.' : 'رقم الفاتورة'}
                                            </div>
                                        </Button>
                                    </th>
                                    <th className="p-4 text-right">
                                        <Button variant="ghost" size="sm" className="font-medium" onClick={() => setSortBy('date')}>
                                            <div>{t('invoices.table.date')}</div>
                                            <div className="text-xs font-normal text-gray-500">
                                                {language === 'ar' ? 'Date' : 'التاريخ'}
                                            </div>
                                        </Button>
                                    </th>
                                    <th className="p-4 text-right">
                                        <Button variant="ghost" size="sm" className="font-medium" onClick={() => setSortBy('customer')}>
                                            <div>{t('invoices.table.customer')}</div>
                                            <div className="text-xs font-normal text-gray-500">
                                                {language === 'ar' ? 'Customer' : 'العميل'}
                                            </div>
                                        </Button>
                                    </th>
                                    <th className="p-4 text-right">
                                        <Button variant="ghost" size="sm" className="font-medium" onClick={() => setSortBy('amount')}>
                                            <div>{t('invoices.table.amount')}</div>
                                            <div className="text-xs font-normal text-gray-500">
                                                {language === 'ar' ? 'Amount' : 'المبلغ'}
                                            </div>
                                        </Button>
                                    </th>
                                    <th className="p-4 text-right">
                                        <div>{t('invoices.table.status')}</div>
                                        <div className="text-xs font-normal text-gray-500">
                                            {language === 'ar' ? 'Status' : 'الحالة'}
                                        </div>
                                    </th>
                                    <th className="p-4 text-right">
                                        <div>{t('invoices.table.actions')}</div>
                                        <div className="text-xs font-normal text-gray-500">
                                            {language === 'ar' ? 'Actions' : 'الإجراءات'}
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {sortedInvoices.length === 0 ? (
                                    <tr>
                                        <td colSpan={7} className="p-8 text-center text-gray-500">
                                            {t('invoices.noMatchingInvoices')}
                                        </td>
                                    </tr>
                                ) : (
                                    sortedInvoices.map((invoice) => (
                                        <tr key={invoice.id} className="border-b hover:bg-gray-50">
                                            <td className="p-4">
                                                <Checkbox
                                                    checked={selectedInvoices.includes(invoice.id)}
                                                    onCheckedChange={() => handleSelectInvoice(invoice.id)}
                                                />
                                            </td>
                                            <td className="p-4">
                                                <a
                                                    href={`/dashboard/invoices/${invoice.id}`}
                                                    className="text-blue-600 hover:underline"
                                                >
                                                    {invoice.invoiceNumber}
                                                </a>
                                            </td>
                                            <td className="p-4">
                                                <div>{new Date(invoice.issueDate).toLocaleDateString(language === 'ar' ? 'ar-AE' : 'en-US')}</div>
                                                {invoice.dueDate && (
                                                    <div className="text-xs text-gray-500">
                                                        {t('invoices.dueDate')}: {new Date(invoice.dueDate).toLocaleDateString(language === 'ar' ? 'ar-AE' : 'en-US')}
                                                    </div>
                                                )}
                                            </td>
                                            <td className="p-4">{invoice.customer.name}</td>
                                            <td className="p-4">
                                                <div className="font-medium">{formatCurrency(invoice.total, language)}</div>
                                                <div className="text-xs text-gray-500">
                                                    {t('invoices.includingTax')} ({formatCurrency(invoice.taxAmount, language)})
                                                </div>
                                            </td>
                                            <td className="p-4">
                                                <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(invoice.status)}`}>
                                                    {getStatusText(invoice.status)}
                                                </span>
                                            </td>
                                            <td className="p-4">
                                                <div className="flex space-x-1 space-x-reverse">
                                                    <Button variant="ghost" size="icon" onClick={() => handleView(invoice.id)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="ghost" size="icon" onClick={() => handleEdit(invoice.id)}>
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="ghost" size="icon" onClick={() => handlePrint(invoice.id)}>
                                                        <Printer className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="ghost" size="icon" onClick={() => handleDelete(invoice.id)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
