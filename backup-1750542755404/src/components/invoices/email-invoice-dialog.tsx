'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Mail } from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface EmailInvoiceDialogProps {
  invoiceId: string;
  invoiceNumber: string;
  customerEmail?: string;
  customerName?: string;
}

export function EmailInvoiceDialog({
  invoiceId,
  invoiceNumber,
  customerEmail = '',
  customerName = 'العميل',
}: Readonly<EmailInvoiceDialogProps>) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState(customerEmail);
  const [subject, setSubject] = useState(`فاتورة رقم ${invoiceNumber} | Invoice #${invoiceNumber}`);
  const [message, setMessage] = useState(
    `عزيزي ${customerName}،\n\nمرفق طيه فاتورة رقم ${invoiceNumber}.\n\nشكراً لتعاملكم معنا.\n\nمع أطيب التحيات،\nفريق أمين بلس | Amin Plus Team\n\n---\n\nDear ${customerName},\n\nPlease find attached invoice #${invoiceNumber}.\n\nThank you for your business.\n\nBest regards,\nAmin Plus | أمين بلس Team`
  );
  const [sendCopy, setSendCopy] = useState(false);
  const [copyEmail, setCopyEmail] = useState('');

  const handleSendEmail = async () => {
    try {
      setLoading(true);

      // التحقق من صحة البيانات
      if (!email) {
        toast.error('يرجى إدخال البريد الإلكتروني');
        setLoading(false);
        return;
      }

      if (sendCopy && !copyEmail) {
        toast.error('يرجى إدخال البريد الإلكتروني للنسخة');
        setLoading(false);
        return;
      }

      // هنا يمكن إضافة كود لإرسال الفاتورة بالبريد الإلكتروني
      // يمكن استخدام API خاص بالبريد الإلكتروني

      // محاكاة إرسال البريد الإلكتروني
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast.success('تم إرسال الفاتورة بنجاح');
      setLoading(false);
      setOpen(false);
    } catch (error) {
      console.error('خطأ في إرسال الفاتورة:', error);
      toast.error('حدث خطأ أثناء إرسال الفاتورة');
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <Mail className="h-4 w-4" />
          <span>إرسال بالبريد | Email</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>إرسال الفاتورة بالبريد الإلكتروني | Send Invoice by Email</DialogTitle>
          <DialogDescription>
            أدخل عنوان البريد الإلكتروني والرسالة لإرسال الفاتورة.
            <br />
            Enter email address and message to send the invoice.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              البريد الإلكتروني | Email
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="col-span-3"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="subject" className="text-right">
              الموضوع | Subject
            </Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="message" className="text-right">
              الرسالة | Message
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="col-span-3"
              rows={6}
            />
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <Switch
              id="send-copy"
              checked={sendCopy}
              onCheckedChange={setSendCopy}
            />
            <Label htmlFor="send-copy">إرسال نسخة إلى | Send copy to</Label>
            <Input
              className="max-w-[200px]"
              placeholder="<EMAIL>"
              disabled={!sendCopy}
              value={copyEmail}
              onChange={(e) => setCopyEmail(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleSendEmail}
            disabled={loading || !email || (sendCopy && !copyEmail)}
          >
            {loading ? 'جاري الإرسال... | Sending...' : 'إرسال | Send'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
