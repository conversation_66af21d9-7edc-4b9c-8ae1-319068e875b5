'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useI18n } from '@/lib/i18n';
import { getTaxRegistrationNumber, formatTaxAmount, CalculatedTax } from '@/lib/services/tax-service';
import { getCompanyInfo } from '@/lib/settings';
import { TaxInvoiceQR } from './tax-invoice-qr';

interface TaxInformationProps {
  readonly invoiceDate: string;
  readonly invoiceTotal: number;
  readonly taxAmount: number;
  readonly taxRate: number;
  readonly taxType: string;
  readonly isExempt?: boolean;
  readonly isZeroRated?: boolean;
  readonly className?: string;
  readonly showQRCode?: boolean;
  readonly showTaxDetails?: boolean;
}

/**
 * مكون معلومات الضريبة للفواتير
 * Tax Information Component for Invoices
 */
export function TaxInformation({
  invoiceDate,
  invoiceTotal,
  taxAmount,
  taxRate,
  taxType,
  isExempt = false,
  isZeroRated = false,
  className = '',
  showQRCode = true,
  showTaxDetails = true
}: TaxInformationProps) {
  const { t, language } = useI18n();
  const [taxInfo, setTaxInfo] = useState<CalculatedTax | null>(null);
  
  useEffect(() => {
    // إنشاء معلومات الضريبة
    // Create tax information
    setTaxInfo({
      taxableAmount: invoiceTotal - taxAmount,
      taxAmount,
      totalWithTax: invoiceTotal,
      taxRate,
      taxType: taxType as any,
      taxTypeName: '',
      taxTypeNameAr: '',
      taxTypeNameEn: '',
      isInclusive: false,
      isExempt,
      isZeroRated
    });
  }, [invoiceTotal, taxAmount, taxRate, taxType, isExempt, isZeroRated]);
  
  // الحصول على معلومات الشركة ورقم التسجيل الضريبي
  // Get company information and tax registration number
  const companyInfo = getCompanyInfo();
  const trn = getTaxRegistrationNumber() || companyInfo.taxNumber;
  
  if (!taxInfo) {
    return null;
  }
  
  return (
    <Card className={`tax-information ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex justify-between items-center">
          <span>
            {t('invoice.taxInformation') || 'معلومات الضريبة'}
            <span className="text-sm text-muted-foreground mr-2">Tax Information</span>
          </span>
          
          {showQRCode && (
            <div className="w-24 h-24">
              <TaxInvoiceQR
                invoiceTimestamp={invoiceDate}
                invoiceTotal={invoiceTotal}
                taxAmount={taxAmount}
                width={96}
                showBorder={false}
                showLabel={false}
              />
            </div>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          {/* رقم التسجيل الضريبي */}
          {/* Tax Registration Number */}
          <div className="flex justify-between">
            <span className="text-sm font-medium">
              {t('tax.registrationNumber') || 'رقم التسجيل الضريبي'}
            </span>
            <span className="text-sm">
              {trn || t('tax.notAvailable') || 'غير متوفر'}
            </span>
          </div>
          
          {/* نوع الضريبة */}
          {/* Tax Type */}
          <div className="flex justify-between">
            <span className="text-sm font-medium">
              {t('tax.type') || 'نوع الضريبة'}
            </span>
            <span className="text-sm">
              {formatTaxAmount(taxInfo)}
            </span>
          </div>
          
          {showTaxDetails && (
            <>
              <Separator className="my-2" />
              
              {/* المبلغ الخاضع للضريبة */}
              {/* Taxable Amount */}
              <div className="flex justify-between">
                <span className="text-sm">
                  {t('tax.taxableAmount') || 'المبلغ الخاضع للضريبة'}
                </span>
                <span className="text-sm">
                  {new Intl.NumberFormat(language === 'ar' ? 'ar-AE' : 'en-AE', {
                    style: 'currency',
                    currency: 'AED'
                  }).format(taxInfo.taxableAmount)}
                </span>
              </div>
              
              {/* مبلغ الضريبة */}
              {/* Tax Amount */}
              <div className="flex justify-between">
                <span className="text-sm">
                  {t('tax.amount') || 'مبلغ الضريبة'}
                </span>
                <span className="text-sm">
                  {new Intl.NumberFormat(language === 'ar' ? 'ar-AE' : 'en-AE', {
                    style: 'currency',
                    currency: 'AED'
                  }).format(taxInfo.taxAmount)}
                </span>
              </div>
              
              {/* المبلغ الإجمالي مع الضريبة */}
              {/* Total Amount with Tax */}
              <div className="flex justify-between font-medium">
                <span className="text-sm">
                  {t('tax.totalWithTax') || 'المبلغ الإجمالي مع الضريبة'}
                </span>
                <span className="text-sm">
                  {new Intl.NumberFormat(language === 'ar' ? 'ar-AE' : 'en-AE', {
                    style: 'currency',
                    currency: 'AED'
                  }).format(taxInfo.totalWithTax)}
                </span>
              </div>
            </>
          )}
        </div>
        
        {/* ملاحظة الفاتورة الضريبية */}
        {/* Tax Invoice Note */}
        <div className="mt-4 text-xs text-muted-foreground border-t pt-2">
          <p className="text-center">
            {t('tax.invoiceNote') || 'هذه فاتورة ضريبية صادرة وفقًا لمتطلبات الهيئة الاتحادية للضرائب في دولة الإمارات العربية المتحدة'}
          </p>
          <p className="text-center">
            This is a tax invoice issued in accordance with the requirements of the Federal Tax Authority in the UAE
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
