'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Bell, Calendar, Clock } from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { formatCurrency } from '@/lib/utils';

interface ReminderDialogProps {
  invoiceId: string;
  invoiceNumber: string;
  invoiceTotal: number;
  dueDate?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
}

export function ReminderDialog({
  invoiceId,
  invoiceNumber,
  invoiceTotal,
  dueDate,
  customerName = 'العميل',
  customerEmail = '',
  customerPhone = '',
}: ReminderDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [reminderType, setReminderType] = useState('email');
  const [reminderSchedule, setReminderSchedule] = useState('now');
  const [customDate, setCustomDate] = useState('');
  const [customTime, setCustomTime] = useState('');
  const [email, setEmail] = useState(customerEmail);
  const [phone, setPhone] = useState(customerPhone);
  const [subject, setSubject] = useState(`تذكير بفاتورة مستحقة رقم ${invoiceNumber}`);
  const [message, setMessage] = useState(
    `عزيزي ${customerName}،\n\nنود تذكيركم بفاتورة مستحقة رقم ${invoiceNumber} بمبلغ ${formatCurrency(invoiceTotal)}${dueDate ? ` وتاريخ استحقاق ${new Date(dueDate).toLocaleDateString('ar-AE')}` : ''}.\n\nنرجو سداد المبلغ في أقرب وقت ممكن.\n\nمع أطيب التحيات،\nفريق أمين بلس`
  );
  const [sendCopy, setSendCopy] = useState(false);
  const [copyEmail, setCopyEmail] = useState('');

  const handleSendReminder = async () => {
    try {
      setLoading(true);

      // التحقق من صحة البيانات
      if (reminderType === 'email' && !email) {
        toast.error('يرجى إدخال البريد الإلكتروني');
        setLoading(false);
        return;
      }

      if (reminderType === 'sms' && !phone) {
        toast.error('يرجى إدخال رقم الهاتف');
        setLoading(false);
        return;
      }

      if (reminderSchedule === 'custom' && (!customDate || !customTime)) {
        toast.error('يرجى تحديد التاريخ والوقت');
        setLoading(false);
        return;
      }

      if (sendCopy && !copyEmail) {
        toast.error('يرجى إدخال البريد الإلكتروني للنسخة');
        setLoading(false);
        return;
      }

      // هنا يمكن إضافة كود لإرسال التذكير
      // محاكاة إرسال التذكير
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // تحديث حالة الفاتورة
      const response = await fetch(`/api/invoices/${invoiceId}/reminder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: reminderType,
          schedule: reminderSchedule,
          customDate: reminderSchedule === 'custom' ? `${customDate}T${customTime}:00` : null,
          email,
          phone,
          subject,
          message,
          sendCopy,
          copyEmail,
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في إرسال التذكير');
      }

      if (reminderSchedule === 'now') {
        toast.success('تم إرسال التذكير بنجاح');
      } else {
        toast.success('تمت جدولة التذكير بنجاح');
      }
      
      setLoading(false);
      setOpen(false);
    } catch (error) {
      console.error('خطأ في إرسال التذكير:', error);
      toast.error('حدث خطأ أثناء إرسال التذكير');
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <Bell className="h-4 w-4 mr-1" />
          <span>إرسال تذكير</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>إرسال تذكير بالفاتورة</DialogTitle>
          <DialogDescription>
            فاتورة رقم {invoiceNumber} للعميل {customerName} بمبلغ {formatCurrency(invoiceTotal)}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Tabs defaultValue="email" value={reminderType} onValueChange={setReminderType}>
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="email" className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                بريد إلكتروني
              </TabsTrigger>
              <TabsTrigger value="sms" className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                  <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                </svg>
                رسالة نصية
              </TabsTrigger>
            </TabsList>

            <TabsContent value="email" className="space-y-4">
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="subject">الموضوع</Label>
                <Input
                  id="subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
              </div>
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="message">الرسالة</Label>
                <Textarea
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={6}
                />
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="send-copy"
                  checked={sendCopy}
                  onCheckedChange={setSendCopy}
                />
                <Label htmlFor="send-copy">إرسال نسخة إلى</Label>
                <Input
                  className="max-w-[200px]"
                  placeholder="<EMAIL>"
                  disabled={!sendCopy}
                  value={copyEmail}
                  onChange={(e) => setCopyEmail(e.target.value)}
                />
              </div>
            </TabsContent>

            <TabsContent value="sms" className="space-y-4">
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="phone">رقم الهاتف</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="+971 XX XXX XXXX"
                />
              </div>
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="sms-message">الرسالة</Label>
                <Textarea
                  id="sms-message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={4}
                  maxLength={160}
                />
                <p className="text-xs text-gray-500 text-right">
                  {message.length}/160 حرف
                </p>
              </div>
            </TabsContent>
          </Tabs>

          <div className="border-t pt-4">
            <Label className="mb-2 block">توقيت إرسال التذكير</Label>
            <div className="grid grid-cols-3 gap-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="radio"
                  id="schedule-now"
                  name="schedule"
                  value="now"
                  checked={reminderSchedule === 'now'}
                  onChange={() => setReminderSchedule('now')}
                  className="h-4 w-4"
                />
                <Label htmlFor="schedule-now" className="cursor-pointer">الآن</Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="radio"
                  id="schedule-tomorrow"
                  name="schedule"
                  value="tomorrow"
                  checked={reminderSchedule === 'tomorrow'}
                  onChange={() => setReminderSchedule('tomorrow')}
                  className="h-4 w-4"
                />
                <Label htmlFor="schedule-tomorrow" className="cursor-pointer">غداً</Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="radio"
                  id="schedule-custom"
                  name="schedule"
                  value="custom"
                  checked={reminderSchedule === 'custom'}
                  onChange={() => setReminderSchedule('custom')}
                  className="h-4 w-4"
                />
                <Label htmlFor="schedule-custom" className="cursor-pointer">تاريخ محدد</Label>
              </div>
            </div>

            {reminderSchedule === 'custom' && (
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="grid gap-2">
                  <Label htmlFor="custom-date" className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    التاريخ
                  </Label>
                  <Input
                    id="custom-date"
                    type="date"
                    value={customDate}
                    onChange={(e) => setCustomDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="custom-time" className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    الوقت
                  </Label>
                  <Input
                    id="custom-time"
                    type="time"
                    value={customTime}
                    onChange={(e) => setCustomTime(e.target.value)}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleSendReminder}
            disabled={loading || (reminderType === 'email' && !email) || (reminderType === 'sms' && !phone) || (reminderSchedule === 'custom' && (!customDate || !customTime))}
          >
            {loading ? 'جاري الإرسال...' : reminderSchedule === 'now' ? 'إرسال التذكير' : 'جدولة التذكير'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
