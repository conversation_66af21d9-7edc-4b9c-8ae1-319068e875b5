'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileDown, FileText, Mail, Printer } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface ExportOptionsProps {
  invoiceId: string;
  invoiceNumber: string;
}

export function ExportOptions({ invoiceId, invoiceNumber }: ExportOptionsProps) {
  const [loading, setLoading] = useState(false);

  const handlePrint = () => {
    window.open(`/dashboard/invoices/${invoiceId}/print`, '_blank');
  };

  const handleExportPDF = async () => {
    try {
      setLoading(true);
      toast.info('جاري تحضير ملف PDF...');

      // محاكاة تصدير PDF
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast.success('تم تصدير الفاتورة بتنسيق PDF بنجاح');
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تصدير الفاتورة:', error);
      toast.error('حدث خطأ أثناء تصدير الفاتورة');
      setLoading(false);
    }
  };

  const handleExportExcel = async () => {
    try {
      setLoading(true);
      toast.info('جاري تحضير ملف Excel...');

      // محاكاة تصدير Excel
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('تم تصدير الفاتورة بتنسيق Excel بنجاح');
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تصدير الفاتورة:', error);
      toast.error('حدث خطأ أثناء تصدير الفاتورة');
      setLoading(false);
    }
  };

  const handleSendEmail = () => {
    // فتح نافذة إرسال البريد الإلكتروني
    toast.info('جاري فتح نافذة إرسال البريد الإلكتروني...');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" disabled={loading}>
          <FileDown className="h-4 w-4 ml-1" />
          <span>{loading ? 'جاري التصدير...' : 'تصدير'}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>خيارات التصدير</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handlePrint}>
          <Printer className="h-4 w-4 ml-2" />
          <span>طباعة الفاتورة</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleExportPDF}>
          <FileText className="h-4 w-4 ml-2" />
          <span>تصدير PDF</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleExportExcel}>
          <FileDown className="h-4 w-4 ml-2" />
          <span>تصدير Excel</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSendEmail}>
          <Mail className="h-4 w-4 ml-2" />
          <span>إرسال بالبريد الإلكتروني</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
