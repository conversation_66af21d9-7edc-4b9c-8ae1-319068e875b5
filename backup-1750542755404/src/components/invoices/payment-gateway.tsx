'use client';

/**
 * مكون بوابة الدفع - يوفر واجهة منبثقة لمعالجة المدفوعات
 * Payment Gateway Component - Provides a popup interface for payment processing
 *
 * ملاحظة: هذا المكون تم استبداله بمكون PaymentDialog الأكثر تطوراً
 * Note: This component has been replaced by the more advanced PaymentDialog component
 */

import { PaymentDialog } from '@/components/payments/payment-dialog';

interface PaymentGatewayProps {
  invoiceId: string;
  invoiceNumber: string;
  invoiceTotal: number;
  customerName?: string;
  onPaymentSuccess?: () => void;
}

/**
 * مكون بوابة الدفع - يوفر واجهة منبثقة لمعالجة المدفوعات
 * Payment Gateway Component - Provides a popup interface for payment processing
 *
 * @deprecated استخدم مكون PaymentDialog بدلاً من ذلك
 * @deprecated Use PaymentDialog component instead
 */
export function PaymentGateway(props: Readonly<PaymentGatewayProps>) {
  // استخدام المكون الجديد مع نفس الخصائص
  // Use the new component with the same props
  return <PaymentDialog {...props} buttonLabel="دفع الفاتورة | Pay Invoice" />;
}
