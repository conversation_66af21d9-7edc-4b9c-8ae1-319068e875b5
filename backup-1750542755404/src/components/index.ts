// مكونات النظام - تصدير موحد
// System Components - Unified Export

// UI Components
export * from './ui';

// Layout Components
export { ClientLayout } from './layout/client-layout';
export { NavBar } from './layout/nav-bar';
export { Sidebar } from './layout/side-bar';

// Auth Components
export { LoginForm } from './auth/login-form';
export { PermissionGuard } from './auth/permission-guard';
export { SessionProvider } from './auth/session-provider';
export { Unauthorized } from './auth/Unauthorized';

// Common Components
export { BilingualButton } from './common/bilingual-button';
export { BilingualCard } from './common/bilingual-card';
export { BilingualCurrency } from './common/bilingual-currency';
export { BilingualDate } from './common/bilingual-date';
export { BilingualForm } from './common/bilingual-form';
export { BilingualList } from './common/bilingual-list';
export { BilingualName } from './common/bilingual-name';
export { BilingualNavigation } from './common/bilingual-navigation';
export { BilingualTable } from './common/bilingual-table';
export { BilingualTabs } from './common/bilingual-tabs';
export { BilingualText } from './common/bilingual-text';
export { LanguageDetector } from './common/language-detector';
export { LanguageSwitcher } from './common/language-switcher';

// Business Components
export { CustomerCombobox } from './customers/customer-combobox';
export { CustomerForm } from './customers/customer-form';
export { CustomerList } from './customers/customer-list';

export { InvoiceForm } from './invoices/invoice-form';
export { InvoicesList } from './invoices/invoices-list';
export { InvoiceActions } from './invoices/invoice-actions';
export { ExportPDFButton } from './invoices/export-pdf-button';
export { PaymentDialog } from './invoices/payment-dialog';
export { EmailInvoiceDialog } from './invoices/email-invoice-dialog';
export { ReminderDialog } from './invoices/reminder-dialog';
export { RecurringSchedule } from './invoices/recurring-schedule';
export { TaxInvoiceQR } from './invoices/tax-invoice-qr';
export { ElectronicSignature } from './invoices/electronic-signature';
export { PaymentGateway } from './invoices/payment-gateway';
export { ExportOptions } from './invoices/export-options';

// Dashboard Components
export { StatusBadge } from './dashboard/StatusBadge';

// Settings Components
export { CompanyInfoForm } from './settings/company-info-form';
export { RoleManagement } from './settings/role-management';
export { SettingsForm } from './settings/settings-form';

// Reports Components
export { CustomerReportContent } from './reports/customer-report-content';
export { SalesReportContent } from './reports/sales-report-content';

// Users Components
export { UsersTable } from './users/users-table';

// Data Components
export { ExportImport } from './data/export-import';

// Backup Components
export { BackupManager } from './backup/backup-manager';

// Notifications Components
export { NotificationsCenter } from './notifications/notifications-center';
export { NotificationsDropdown } from './notifications/notifications-dropdown';

// Theme Components
export { ThemeProvider } from './theme/theme-provider';

// Providers
export { ElectronProvider } from './providers/electron-provider';
export { TauriProvider } from './providers/tauri-provider';
export { TauriApiProvider } from './providers/tauri-api-provider';

// Electron Components
export { ElectronFeatures } from './electron/electron-features';

// Payments Components
export { PaymentGateways } from './payments/payment-gateways';

// Recipes Components (if needed)
export { CopyRecipeDialog } from './recipes/copy-recipe-dialog';
export { IngredientsStockAlerts } from './recipes/ingredients-stock-alerts';

// Accounting Components
export { AccountingIntegration } from './accounting/accounting-integration';
