'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface CustomerFormData {
  id?: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  taxNumber?: string;
  contactPerson?: string;
  notes?: string;
}

interface CustomerFormProps {
  readonly initialData?: CustomerFormData;
  readonly isEditing?: boolean;
}

export default function CustomerForm({ initialData, isEditing = false }: CustomerFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState<CustomerFormData>(
    initialData || {
      name: '',
      email: '',
      phone: '',
      address: '',
      taxNumber: '',
      contactPerson: '',
      notes: '',
    }
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const url = isEditing
        ? `/api/customers/${initialData?.id}`
        : '/api/customers';

      const method = isEditing ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error ?? 'حدث خطأ أثناء حفظ بيانات العميل');
      }

      router.push('/dashboard/customers');
      router.refresh();
    } catch (err: any) {
      console.error(err);
      setError(err.message ?? 'حدث خطأ أثناء حفظ بيانات العميل');
    } finally {
      setLoading(false);
    }
  };

  const updateField = (field: keyof CustomerFormData, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="mb-4">
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
          الاسم
        </label>
        <input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => updateField('name', e.target.value)}
          required
          className="w-full p-2 border border-gray-300 rounded"
        />
      </div>

      <div className="mb-4">
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
          البريد الإلكتروني
        </label>
        <input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => updateField('email', e.target.value)}
          required
          className="w-full p-2 border border-gray-300 rounded"
        />
      </div>

      <div className="mb-4">
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
          رقم الهاتف
        </label>
        <input
          id="phone"
          type="tel"
          value={formData.phone || ''}
          onChange={(e) => updateField('phone', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
        />
      </div>

      <div className="mb-6">
        <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
          العنوان
        </label>
        <textarea
          id="address"
          value={formData.address || ''}
          onChange={(e) => updateField('address', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
          rows={3}
        />
      </div>

      <div className="flex justify-between items-center">
        <button
          type="button"
          onClick={() => router.back()}
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded"
        >
          إلغاء
        </button>
        <button
          type="submit"
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
        >
          {loading ? 'جاري الحفظ...' : isEditing ? 'تحديث البيانات' : 'إضافة عميل'}
        </button>
      </div>
    </form>
  );
}
