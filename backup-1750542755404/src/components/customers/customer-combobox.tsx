"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface CustomerComboboxProps {
  name: string
  onSelect?: (customer: { id: string; name: string }) => void
}

export function CustomerCombobox({ name, onSelect }: CustomerComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [customers, setCustomers] = React.useState<Array<{ id: string; name: string }>>([])
  const [selectedCustomer, setSelectedCustomer] = React.useState<string>("")

  React.useEffect(() => {
    fetch("/api/customers")
      .then((res) => res.json())
      .then((data) => setCustomers(data))
  }, [])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedCustomer
            ? customers.find((customer) => customer.id === selectedCustomer)?.name
            : "اختر العميل..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput placeholder="ابحث عن عميل..." />
          <CommandEmpty>لم يتم العثور على عملاء.</CommandEmpty>
          <CommandGroup>
            {customers.map((customer) => (
              <CommandItem
                key={customer.id}
                value={customer.id}
                onSelect={(currentValue) => {
                  setSelectedCustomer(currentValue)
                  onSelect?.(customer)
                  setOpen(false)
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selectedCustomer === customer.id ? "opacity-100" : "opacity-0"
                  )}
                />
                {customer.name}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
      <input type="hidden" name={name} value={selectedCustomer} />
    </Popover>
  )
}