"use client"

/**
 * مكون اختيار العملاء - يوفر واجهة لاختيار العملاء من قائمة منسدلة
 * Customer Selection Component - Provides an interface for selecting customers from a dropdown
 */

import * as React from "react"
import { Check, User, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useI18n } from "@/lib/i18n"

// واجهة العميل
// Customer interface
export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

// خصائص مكون اختيار العملاء
// Customer combobox props
interface CustomerComboboxProps {
  name: string;
  label?: string;
  placeholder?: string;
  defaultValue?: string;
  required?: boolean;
  disabled?: boolean;
  onSelect?: (customer: Customer) => void;
  className?: string;
}

/**
 * مكون اختيار العملاء - يوفر واجهة لاختيار العملاء من قائمة منسدلة
 * Customer Combobox - Provides an interface for selecting customers from a dropdown
 */
export function CustomerCombobox({
  name,
  label,
  placeholder,
  defaultValue,
  required = false,
  disabled = false,
  onSelect,
  className
}: CustomerComboboxProps) {
  const { t, language } = useI18n();
  const [open, setOpen] = React.useState(false);
  const [customers, setCustomers] = React.useState<Customer[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [selectedCustomer, setSelectedCustomer] = React.useState<string>(defaultValue || "");
  const [searchQuery, setSearchQuery] = React.useState("");

  // تحميل العملاء عند تحميل المكون
  // Load customers when component mounts
  React.useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        setError(null);

        const res = await fetch("/api/customers");

        if (!res.ok) {
          throw new Error(`Error ${res.status}: ${res.statusText}`);
        }

        const data = await res.json();
        setCustomers(data);
      } catch (err) {
        console.error("Failed to fetch customers:", err);
        setError(t("customers.fetchError") || "Failed to load customers");
      } finally {
        setLoading(false);
      }
    };

    fetchCustomers();
  }, [t]);

  // تصفية العملاء حسب البحث
  // Filter customers based on search
  const filteredCustomers = React.useMemo(() => {
    if (!searchQuery) return customers;

    return customers.filter(customer =>
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (customer.phone && customer.phone.includes(searchQuery))
    );
  }, [customers, searchQuery]);

  // الحصول على اسم العميل المحدد
  // Get selected customer name
  const selectedCustomerName = React.useMemo(() => {
    if (!selectedCustomer) return "";
    const customer = customers.find(c => c.id === selectedCustomer);
    return customer ? customer.name : "";
  }, [selectedCustomer, customers]);

  // معالجة تحديد العميل
  // Handle customer selection
  const handleSelect = (customerId: string) => {
    setSelectedCustomer(customerId);
    const customer = customers.find(c => c.id === customerId);
    if (customer && onSelect) {
      onSelect(customer);
    }
    setOpen(false);
  };

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <label className="block text-sm font-medium mb-1">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between font-normal",
              !selectedCustomer && "text-muted-foreground",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            disabled={disabled}
          >
            {selectedCustomerName || placeholder || t("customers.selectCustomer") || "Select customer"}
            <User className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0" align={language === 'ar' ? 'end' : 'start'}>
          <Command>
            <CommandInput
              placeholder={t("customers.searchPlaceholder") || "Search for customers..."}
              onValueChange={setSearchQuery}
              className="h-9"
            />

            {loading && (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
                <span className="ml-2">{t("common.loading") || "Loading..."}</span>
              </div>
            )}

            {error && (
              <div className="py-6 text-center text-sm text-destructive">
                <p>{error}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  className="mt-2"
                  onClick={() => setOpen(false)}
                >
                  {t("common.close") || "Close"}
                </Button>
              </div>
            )}

            <CommandEmpty>
              {t("customers.noResults") || "No customers found."}
            </CommandEmpty>

            <CommandList>
              <CommandGroup>
                {filteredCustomers.map((customer) => (
                  <CommandItem
                    key={customer.id}
                    value={customer.id}
                    onSelect={() => handleSelect(customer.id)}
                    className="flex items-center"
                  >
                    <div className="flex items-center">
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedCustomer === customer.id ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        {customer.email && (
                          <p className="text-xs text-muted-foreground">{customer.email}</p>
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <input
        type="hidden"
        name={name}
        value={selectedCustomer}
        required={required}
        disabled={disabled}
      />
    </div>
  )
}
