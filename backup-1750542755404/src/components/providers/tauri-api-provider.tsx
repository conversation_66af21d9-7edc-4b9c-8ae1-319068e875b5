'use client';

import React, { createContext, useContext, ReactNode, useEffect, useState, useMemo } from 'react';
import * as TauriAPI from '@/lib/tauri-api';
import { useTauri } from '@/components/providers/tauri-provider';

/**
 * واجهة سياق واجهة برمجة تطبيقات Tauri
 * Tauri API context interface
 */
interface TauriAPIContextType {
  // معلومات التطبيق
  // Application information
  appInfo: string | null;

  // المستخدم الحالي
  // Current user
  currentUser: TauriAPI.User | null;

  // الإعدادات
  // Settings
  settings: TauriAPI.Settings | null;

  // وظائف واجهة برمجة التطبيقات
  // API functions
  api: typeof TauriAPI;

  // حالة التحميل
  // Loading state
  loading: boolean;

  // خطأ
  // Error
  error: string | null;

  // إعادة تحميل البيانات
  // Reload data
  reload: () => Promise<void>;
}

/**
 * سياق واجهة برمجة تطبيقات Tauri
 * Tauri API context
 */
const TauriAPIContext = createContext<TauriAPIContextType>({
  appInfo: null,
  currentUser: null,
  settings: null,
  api: TauriAPI,
  loading: true,
  error: null,
  reload: async () => { },
});

/**
 * خطاف استخدام سياق واجهة برمجة تطبيقات Tauri
 * Hook to use Tauri API context
 */
export const useTauriAPI = () => useContext(TauriAPIContext);

/**
 * واجهة خصائص مزود واجهة برمجة تطبيقات Tauri
 * Tauri API provider props interface
 */
interface TauriAPIProviderProps {
  children: ReactNode;
}

/**
 * مزود سياق واجهة برمجة تطبيقات Tauri
 * Tauri API context provider
 */
export function TauriAPIProvider({ children }: Readonly<TauriAPIProviderProps>) {
  const { isTauriApp } = useTauri();
  const [appInfo, setAppInfo] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<TauriAPI.User | null>(null);
  const [settings, setSettings] = useState<TauriAPI.Settings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = async () => {
    if (!isTauriApp) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // تحميل معلومات التطبيق
      // Load application information
      try {
        const appInfoData = await TauriAPI.getAppInfo();
        setAppInfo(appInfoData);
      } catch (appError) {
        console.warn('Failed to load app info:', appError);
        // Continue with other data loading
      }

      // تحميل المستخدم الحالي
      // Load current user
      try {
        const currentUserData = await TauriAPI.getCurrentUser();
        setCurrentUser(currentUserData);
      } catch (userError) {
        console.warn('Failed to load current user:', userError);
        // Continue with other data loading
      }

      // تحميل الإعدادات
      // Load settings
      try {
        const settingsData = await TauriAPI.getSettings();
        setSettings(settingsData);
      } catch (settingsError) {
        console.warn('Failed to load settings:', settingsError);
        // Continue with other data loading
      }
    } catch (err) {
      console.error('Error loading Tauri API data:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [isTauriApp]);

  const reload = async () => {
    await loadData();
  };

  // Use useMemo to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    appInfo,
    currentUser,
    settings,
    api: TauriAPI,
    loading,
    error,
    reload,
  }), [appInfo, currentUser, settings, loading, error]);

  return (
    <TauriAPIContext.Provider value={contextValue}>
      {children}
    </TauriAPIContext.Provider>
  );
}
