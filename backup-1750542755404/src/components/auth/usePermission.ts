// src/hooks/usePermission.ts
import { useSession } from "next-auth/react";

// Define a local Permission type
type Permission = string;

// Extend the session user type to include permissions
interface ExtendedUser {
  id: string;
  role: string;
  permissions?: Permission[];
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

export function usePermission(requiredPermission: Permission): boolean {
  const { data: session } = useSession();

  // Ensure session.user is typed as ExtendedUser
  const user = session?.user as ExtendedUser | undefined;

  // إذا لم يكن المستخدم مسجل دخوله، فليس لديه أي صلاحيات
  if (!user) return false;

  // إذا كان المستخدم مسؤولاً، فلديه جميع الصلاحيات
  if (user.role === 'admin') return true;

  // التحقق من الصلاحيات المحددة للمستخدم
  return user.permissions?.includes(requiredPermission) || false;
}