import { renderHook } from '@testing-library/react';
import { usePermission } from '../usePermission';
import { useSession } from 'next-auth/react';

// Mock the useSession hook
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock the process.env
const originalNodeEnv = process.env.NODE_ENV;

describe('usePermission Hook', () => {
  afterAll(() => {
    // استعادة قيمة NODE_ENV الأصلية بعد الانتهاء من الاختبارات
    process.env.NODE_ENV = originalNodeEnv;
  });

  it('returns true in development environment regardless of permissions', () => {
    // تعيين بيئة التطوير
    process.env.NODE_ENV = 'development';

    // تهيئة قيمة افتراضية لـ useSession
    (useSession as jest.Mock).mockReturnValue({
      data: {
        user: {
          permissions: [],
        },
      },
    });

    // اختبار الـ hook
    const { result } = renderHook(() => usePermission());

    // التحقق من أن النتيجة هي true في بيئة التطوير
    expect(result.current.hasPermission('MANAGE_USERS')).toBe(true);
  });

  it('returns false when user has no session', () => {
    // تعيين بيئة الإنتاج
    process.env.NODE_ENV = 'production';

    // تهيئة قيمة فارغة لـ useSession
    (useSession as jest.Mock).mockReturnValue({
      data: null,
    });

    // اختبار الـ hook
    const { result } = renderHook(() => usePermission());

    // التحقق من أن النتيجة هي false عندما لا يكون هناك جلسة
    expect(result.current.hasPermission('MANAGE_USERS')).toBe(false);
  });

  it('returns true when user has the required permission', () => {
    // تعيين بيئة الإنتاج
    process.env.NODE_ENV = 'production';

    // تهيئة قيمة لـ useSession مع الصلاحية المطلوبة
    (useSession as jest.Mock).mockReturnValue({
      data: {
        user: {
          permissions: ['MANAGE_USERS', 'MANAGE_SETTINGS'],
        },
      },
    });

    // اختبار الـ hook
    const { result } = renderHook(() => usePermission());

    // التحقق من أن النتيجة هي true عندما يكون للمستخدم الصلاحية المطلوبة
    expect(result.current.hasPermission('MANAGE_USERS')).toBe(true);
  });

  it('returns false when user does not have the required permission', () => {
    // تعيين بيئة الإنتاج
    process.env.NODE_ENV = 'production';

    // تهيئة قيمة لـ useSession بدون الصلاحية المطلوبة
    (useSession as jest.Mock).mockReturnValue({
      data: {
        user: {
          permissions: ['MANAGE_SETTINGS'],
        },
      },
    });

    // اختبار الـ hook
    const { result } = renderHook(() => usePermission());

    // التحقق من أن النتيجة هي false عندما لا يكون للمستخدم الصلاحية المطلوبة
    expect(result.current.hasPermission('MANAGE_USERS')).toBe(false);
  });

  it('returns true when user has at least one of the required permissions', () => {
    // تعيين بيئة الإنتاج
    process.env.NODE_ENV = 'production';

    // تهيئة قيمة لـ useSession مع إحدى الصلاحيات المطلوبة
    (useSession as jest.Mock).mockReturnValue({
      data: {
        user: {
          permissions: ['MANAGE_SETTINGS'],
        },
      },
    });

    // اختبار الـ hook مع مصفوفة من الصلاحيات
    const { result } = renderHook(() => usePermission());

    // التحقق من أن النتيجة هي true عندما يكون للمستخدم إحدى الصلاحيات المطلوبة
    expect(result.current.hasAnyPermission(['MANAGE_USERS', 'MANAGE_SETTINGS'])).toBe(true);
  });

  it('returns false when user does not have any of the required permissions', () => {
    // تعيين بيئة الإنتاج
    process.env.NODE_ENV = 'production';

    // تهيئة قيمة لـ useSession بدون أي من الصلاحيات المطلوبة
    (useSession as jest.Mock).mockReturnValue({
      data: {
        user: {
          permissions: ['VIEW_REPORTS'],
        },
      },
    });

    // اختبار الـ hook مع مصفوفة من الصلاحيات
    const { result } = renderHook(() => usePermission());

    // التحقق من أن النتيجة هي false عندما لا يكون للمستخدم أي من الصلاحيات المطلوبة
    expect(result.current.hasAnyPermission(['MANAGE_USERS', 'MANAGE_SETTINGS'])).toBe(false);
  });
});
