'use client';

import { useSession } from 'next-auth/react';

// تعريف أنواع الصلاحيات المتاحة في النظام
export type Permission =
  | 'MANAGE_USERS'
  | 'MANAGE_CUSTOMERS'
  | 'MANAGE_INVOICES'
  | 'MANAGE_PRODUCTS'
  | 'MANAGE_SETTINGS'
  | 'VIEW_REPORTS';

/**
 * هوك للتحقق من صلاحيات المستخدم
 * Hook for checking user permissions
 */
export function usePermission() {
  const { data: session } = useSession();

  /**
   * التحقق مما إذا كان المستخدم لديه صلاحية معينة
   * Check if the user has a specific permission
   * @param permission الصلاحية المطلوبة / Required permission
   * @returns true إذا كان المستخدم لديه الصلاحية، false إذا لم يكن لديه الصلاحية / true if the user has the permission, false otherwise
   */
  const hasPermission = (permission: string): boolean => {
    // في بيئة التطوير، نسمح بجميع الصلاحيات
    if (process.env.NODE_ENV === 'development') {
      return true;
    }

    if (!session || !session.user) {
      return false;
    }

    // إذا كان المستخدم مسؤول، فلديه جميع الصلاحيات
    // If the user is an admin, they have all permissions
    if (session.user.role === 'ADMIN') {
      return true;
    }

    // التحقق من وجود الصلاحية في قائمة صلاحيات المستخدم
    // Check if the permission exists in the user's permissions list
    return session.user.permissions?.includes(permission) || false;
  };

  /**
   * التحقق مما إذا كان المستخدم لديه أي من الصلاحيات المحددة
   * Check if the user has any of the specified permissions
   * @param permissions قائمة الصلاحيات / List of permissions
   * @returns true إذا كان المستخدم لديه أي من الصلاحيات، false إذا لم يكن لديه أي منها / true if the user has any of the permissions, false otherwise
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  /**
   * التحقق مما إذا كان المستخدم لديه جميع الصلاحيات المحددة
   * Check if the user has all of the specified permissions
   * @param permissions قائمة الصلاحيات / List of permissions
   * @returns true إذا كان المستخدم لديه جميع الصلاحيات، false إذا لم يكن لديه جميعها / true if the user has all of the permissions, false otherwise
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  };
}
