import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - جلب مورد واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف المورد غير صحيح' },
        { status: 400 }
      );
    }

    const supplier = await prisma.supplier.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            purchases: true,
            expenses: true
          }
        },
        purchases: {
          select: {
            id: true,
            total: true,
            purchaseDate: true,
            status: true
          },
          orderBy: {
            purchaseDate: 'desc'
          },
          take: 5
        },
        expenses: {
          select: {
            id: true,
            title: true,
            totalAmount: true,
            expenseDate: true,
            paymentStatus: true
          },
          orderBy: {
            expenseDate: 'desc'
          },
          take: 5
        }
      }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: 'المورد غير موجود' },
        { status: 404 }
      );
    }

    // حساب إجمالي قيم المشتريات والمصروفات
    const purchaseSum = await prisma.purchase.aggregate({
      where: { supplierId: id },
      _sum: {
        total: true
      }
    });

    const expenseSum = await prisma.expense.aggregate({
      where: { supplierId: id },
      _sum: {
        totalAmount: true
      }
    });

    const supplierWithSums = {
      ...supplier,
      _sum: {
        purchaseTotal: purchaseSum._sum.total || 0,
        expenseTotal: expenseSum._sum.totalAmount || 0
      }
    };

    return NextResponse.json(supplierWithSums);

  } catch (error) {
    console.error('Error fetching supplier:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات المورد' },
      { status: 500 }
    );
  }
}

// PUT - تحديث مورد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const body = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف المورد غير صحيح' },
        { status: 400 }
      );
    }

    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id }
    });

    if (!existingSupplier) {
      return NextResponse.json(
        { error: 'المورد غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    if (body.email && body.email !== existingSupplier.email) {
      const emailExists = await prisma.supplier.findFirst({
        where: {
          email: body.email,
          id: { not: id }
        }
      });

      if (emailExists) {
        return NextResponse.json(
          { error: 'البريد الإلكتروني مستخدم بالفعل' },
          { status: 400 }
        );
      }
    }

    // تحديث المورد
    const supplier = await prisma.supplier.update({
      where: { id },
      data: {
        ...body,
        creditLimit: body.creditLimit ? parseFloat(body.creditLimit) : undefined,
        rating: body.rating ? parseFloat(body.rating) : undefined,
        updatedAt: new Date()
      },
      include: {
        _count: {
          select: {
            purchases: true,
            expenses: true
          }
        }
      }
    });

    return NextResponse.json(supplier);

  } catch (error) {
    console.error('Error updating supplier:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث المورد' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مورد
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف المورد غير صحيح' },
        { status: 400 }
      );
    }

    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            purchases: true,
            expenses: true
          }
        }
      }
    });

    if (!existingSupplier) {
      return NextResponse.json(
        { error: 'المورد غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود معاملات مرتبطة
    if (existingSupplier._count.purchases > 0 || existingSupplier._count.expenses > 0) {
      return NextResponse.json(
        { 
          error: 'لا يمكن حذف المورد لوجود معاملات مرتبطة به',
          details: {
            purchases: existingSupplier._count.purchases,
            expenses: existingSupplier._count.expenses
          }
        },
        { status: 400 }
      );
    }

    // حذف المورد
    await prisma.supplier.delete({
      where: { id }
    });

    return NextResponse.json({ 
      message: 'تم حذف المورد بنجاح',
      deletedId: id 
    });

  } catch (error) {
    console.error('Error deleting supplier:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف المورد' },
      { status: 500 }
    );
  }
}
