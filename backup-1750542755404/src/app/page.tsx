'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// استيراد متغيرات البيئة للواجهة الأمامية
// Import environment variables for frontend
const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME ?? 'أمين بلس';
const APP_NAME_EN = process.env.NEXT_PUBLIC_APP_NAME_EN ?? 'Amin Plus';
const APP_DESCRIPTION = process.env.NEXT_PUBLIC_APP_DESCRIPTION ?? 'نظام إدارة المبيعات والمخازن';
const APP_DESCRIPTION_EN = process.env.NEXT_PUBLIC_APP_DESCRIPTION_EN ?? 'Integrated Sales & Inventory Management System';

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // فحص إذا كان المستخدم مسجل دخول
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (isLoggedIn === 'true') {
      // توجيه مباشر إلى Dashboard
      router.push('/dashboard');
      return;
    }

    // توجيه تلقائي إلى صفحة الدخول التلقائي بعد 3 ثوان
    const timer = setTimeout(() => {
      router.push('/simple-login');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-6 bg-gradient-to-b from-blue-50 to-white" dir="rtl">
      <div className="w-full max-w-5xl px-4 py-8 bg-white rounded-2xl shadow-lg text-center">
        <div className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
            مرحباً بك في {APP_NAME}
          </h1>
          <h2 className="text-xl md:text-2xl font-normal text-gray-600">
            Welcome to {APP_NAME_EN}
          </h2>
        </div>

        <div className="mb-10">
          <p className="text-xl text-gray-800 mb-2">
            {APP_DESCRIPTION}
          </p>
          <p className="text-gray-600">
            {APP_DESCRIPTION_EN}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-blue-50 p-6 rounded-lg text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            <h3 className="text-lg font-semibold mb-2">إدارة الفواتير</h3>
            <p className="text-gray-600">إنشاء وإدارة الفواتير بكل سهولة</p>
          </div>

          <div className="bg-blue-50 p-6 rounded-lg text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            <h3 className="text-lg font-semibold mb-2">إدارة المخزون</h3>
            <p className="text-gray-600">تتبع المخزون بدقة وكفاءة</p>
          </div>

          <div className="bg-blue-50 p-6 rounded-lg text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 className="text-lg font-semibold mb-2">التقارير المالية</h3>
            <p className="text-gray-600">تقارير تفصيلية لمتابعة أداء عملك</p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/simple-login"
            className="px-8 py-4 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg font-bold hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg transform hover:scale-105"
          >
            🚀 دخول مباشر للنظام
          </a>
          <a
            href="/auth/login"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-md"
          >
            تسجيل الدخول
          </a>
          <a
            href="/auth/register"
            className="px-6 py-3 bg-white border border-blue-600 text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-colors shadow-sm"
          >
            إنشاء حساب جديد
          </a>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">
            💡 <strong>نصيحة:</strong> استخدم "دخول مباشر للنظام" للوصول السريع بدون كلمة مرور، أو انتظر 3 ثوان للتوجيه التلقائي
          </p>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-100">
          <p className="text-gray-500 text-sm">
            © {new Date().getFullYear()} {APP_NAME} | {APP_NAME_EN} - جميع الحقوق محفوظة
          </p>
        </div>
      </div>
    </main>
  );
}
