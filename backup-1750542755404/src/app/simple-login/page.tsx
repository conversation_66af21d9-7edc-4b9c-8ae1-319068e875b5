'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function SimpleLoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // تسجيل الدخول تلقائيًا
    const login = async () => {
      setIsLoading(true);
      
      try {
        // تخزين حالة تسجيل الدخول في localStorage
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('user', JSON.stringify({
          id: '1',
          name: 'مستخدم النظام',
          email: '<EMAIL>',
          role: 'admin',
          permissions: ['MANAGE_USERS', 'MANAGE_CUSTOMERS', 'MANAGE_INVOICES', 'MANAGE_PRODUCTS', 'MANAGE_SETTINGS', 'VIEW_REPORTS']
        }));
        
        // انتظار لحظة قبل التوجيه
        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);
      } catch (error) {
        console.error('Login error:', error);
      }
    };
    
    login();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6 text-center">تسجيل الدخول التلقائي</h1>
        <div className="text-center">
          <div className="animate-pulse mb-4">
            <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
          <p className="text-gray-600">
            {isLoading ? 'جاري تسجيل الدخول تلقائيًا...' : 'تم تسجيل الدخول بنجاح! جاري التوجيه إلى لوحة التحكم...'}
          </p>
        </div>
      </div>
    </div>
  );
}
