"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Product } from "@prisma/client"
import { Button } from "@/components/ui/button"
import * as DropdownMenu from "@/components/ui/dropdownـmenu"
import { MoreHorizontal } from "lucide-react"
import Link from "next/link"

export const columns: ColumnDef<Product>[] = [
  {
    accessorKey: "name",
    header: "اسم المنتج",
  },
  {
    accessorKey: "price",
    header: "السعر",
    cell: ({ row }) => {
      const price = parseFloat(row.getValue("price"))
      const formatted = new Intl.NumberFormat("ar-AE", {
        style: "currency",
        currency: "AED",
      }).format(price)
      return formatted
    },
  },
  {
    accessorKey: "stock",
    header: "المخزون",
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const product = row.original

      return (
        <DropdownMenu.Root>
          <DropdownMenu.Trigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="end">
            <DropdownMenu.Item asChild>
              <Link href={`/products/${product.id}`}>
                عرض التفاصيل
              </Link>
            </DropdownMenu.Item>
            <DropdownMenu.Item asChild>
              <Link href={`/products/${product.id}/edit`}>
                تعديل
              </Link>
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      )
    },
  },
]
