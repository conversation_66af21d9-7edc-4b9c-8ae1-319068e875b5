'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/language-context';
import {
  Building,
  CreditCard,
  FileText,
  Languages,
  Lock,
  Settings,
  Users,
  Database,
  Printer,
  Mail,
  Bell,
  Palette,
  Clock,
  Globe,
  HardDrive
} from 'lucide-react';

export default function SettingsIndexPage() {
  const { language } = useLanguage();
  const router = useRouter();
  
  // تعريف فئات الإعدادات
  const settingsCategories = [
    {
      id: 'company',
      titleAr: 'معلومات الشركة',
      titleEn: 'Company Information',
      descriptionAr: 'إدارة معلومات الشركة وبيانات الاتصال',
      descriptionEn: 'Manage company information and contact details',
      icon: <Building className="h-8 w-8 text-blue-500" />,
      href: '/settings/company',
      color: 'bg-blue-50'
    },
    {
      id: 'language',
      titleAr: 'إعدادات اللغة',
      titleEn: 'Language Settings',
      descriptionAr: 'تغيير لغة التطبيق وإعدادات التدويل',
      descriptionEn: 'Change application language and internationalization settings',
      icon: <Languages className="h-8 w-8 text-indigo-500" />,
      href: '/settings/language',
      color: 'bg-indigo-50'
    },
    {
      id: 'invoice',
      titleAr: 'إعدادات الفواتير',
      titleEn: 'Invoice Settings',
      descriptionAr: 'تخصيص قوالب الفواتير وأرقام التسلسل',
      descriptionEn: 'Customize invoice templates and sequence numbers',
      icon: <FileText className="h-8 w-8 text-amber-500" />,
      href: '/settings/invoice',
      color: 'bg-amber-50'
    },
    {
      id: 'payment',
      titleAr: 'إعدادات الدفع',
      titleEn: 'Payment Settings',
      descriptionAr: 'إدارة طرق الدفع وبوابات الدفع الإلكتروني',
      descriptionEn: 'Manage payment methods and payment gateways',
      icon: <CreditCard className="h-8 w-8 text-green-500" />,
      href: '/settings/payment',
      color: 'bg-green-50'
    },
    {
      id: 'users',
      titleAr: 'إدارة المستخدمين',
      titleEn: 'User Management',
      descriptionAr: 'إدارة حسابات المستخدمين والأذونات',
      descriptionEn: 'Manage user accounts and permissions',
      icon: <Users className="h-8 w-8 text-purple-500" />,
      href: '/settings/users',
      color: 'bg-purple-50'
    },
    {
      id: 'security',
      titleAr: 'الأمان',
      titleEn: 'Security',
      descriptionAr: 'إعدادات الأمان وكلمات المرور',
      descriptionEn: 'Security settings and passwords',
      icon: <Lock className="h-8 w-8 text-red-500" />,
      href: '/settings/security',
      color: 'bg-red-50'
    },
    {
      id: 'backup',
      titleAr: 'النسخ الاحتياطي',
      titleEn: 'Backup & Restore',
      descriptionAr: 'إدارة النسخ الاحتياطي واستعادة البيانات',
      descriptionEn: 'Manage data backup and restoration',
      icon: <Database className="h-8 w-8 text-cyan-500" />,
      href: '/settings/backup',
      color: 'bg-cyan-50'
    },
    {
      id: 'printer',
      titleAr: 'إعدادات الطباعة',
      titleEn: 'Printer Settings',
      descriptionAr: 'إعداد الطابعات وخيارات الطباعة',
      descriptionEn: 'Configure printers and printing options',
      icon: <Printer className="h-8 w-8 text-pink-500" />,
      href: '/settings/printer',
      color: 'bg-pink-50'
    },
    {
      id: 'email',
      titleAr: 'إعدادات البريد الإلكتروني',
      titleEn: 'Email Settings',
      descriptionAr: 'إعداد خادم البريد الإلكتروني وقوالب الرسائل',
      descriptionEn: 'Configure email server and message templates',
      icon: <Mail className="h-8 w-8 text-orange-500" />,
      href: '/settings/email',
      color: 'bg-orange-50'
    },
    {
      id: 'notifications',
      titleAr: 'الإشعارات',
      titleEn: 'Notifications',
      descriptionAr: 'إدارة إعدادات الإشعارات والتنبيهات',
      descriptionEn: 'Manage notification and alert settings',
      icon: <Bell className="h-8 w-8 text-yellow-500" />,
      href: '/settings/notifications',
      color: 'bg-yellow-50'
    },
    {
      id: 'appearance',
      titleAr: 'المظهر',
      titleEn: 'Appearance',
      descriptionAr: 'تخصيص مظهر التطبيق والسمات',
      descriptionEn: 'Customize application appearance and themes',
      icon: <Palette className="h-8 w-8 text-violet-500" />,
      href: '/settings/appearance',
      color: 'bg-violet-50'
    },
    {
      id: 'regional',
      titleAr: 'الإعدادات الإقليمية',
      titleEn: 'Regional Settings',
      descriptionAr: 'إعدادات التاريخ والوقت والعملة',
      descriptionEn: 'Date, time, and currency settings',
      icon: <Globe className="h-8 w-8 text-teal-500" />,
      href: '/settings/regional',
      color: 'bg-teal-50'
    },
    {
      id: 'system',
      titleAr: 'إعدادات النظام',
      titleEn: 'System Settings',
      descriptionAr: 'إعدادات النظام المتقدمة',
      descriptionEn: 'Advanced system settings',
      icon: <Settings className="h-8 w-8 text-gray-500" />,
      href: '/settings/system',
      color: 'bg-gray-50'
    },
    {
      id: 'storage',
      titleAr: 'التخزين',
      titleEn: 'Storage',
      descriptionAr: 'إدارة مساحة التخزين والملفات',
      descriptionEn: 'Manage storage space and files',
      icon: <HardDrive className="h-8 w-8 text-lime-500" />,
      href: '/settings/storage',
      color: 'bg-lime-50'
    }
  ];

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">
          {language === 'ar' ? 'الإعدادات' : 'Settings'}
        </h1>
        <h2 className="text-xl font-normal text-gray-700 mb-2">
          {language === 'ar' ? 'Settings' : 'الإعدادات'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'ar' 
            ? 'إدارة إعدادات التطبيق وتخصيصه | Manage application settings and customization'
            : 'Manage application settings and customization | إدارة إعدادات التطبيق وتخصيصه'}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {settingsCategories.map((category) => (
          <Link href={category.href} key={category.id}>
            <Card className="overflow-hidden h-full transition-all hover:shadow-md">
              <CardHeader className={`${category.color} pb-2`}>
                <div className="flex justify-center mb-2">
                  {category.icon}
                </div>
                <CardTitle className="text-center">
                  {language === 'ar' ? category.titleAr : category.titleEn}
                  <span className="block text-sm font-normal mt-1">
                    {language === 'ar' ? category.titleEn : category.titleAr}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <CardDescription className="text-center">
                  {language === 'ar' ? category.descriptionAr : category.descriptionEn}
                </CardDescription>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
