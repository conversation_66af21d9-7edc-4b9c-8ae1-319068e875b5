'use client';

import { CompanyInfoForm } from '@/components/settings/company-info-form';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { HomeIcon, Settings } from 'lucide-react';

export default function CompanySettingsPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">
                <HomeIcon className="h-4 w-4 mr-2" />
                <span>الرئيسية</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/settings">
                <Settings className="h-4 w-4 mr-2" />
                <span>الإعدادات</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <span>معلومات الشركة</span>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">معلومات الشركة</h1>
        <h2 className="text-xl font-normal text-gray-700 mb-2">Company Information</h2>
        <p className="text-muted-foreground">
          إدارة معلومات الشركة باللغتين العربية والإنجليزية | Manage company information in both Arabic and English
        </p>
      </div>

      <CompanyInfoForm />
    </div>
  );
}
