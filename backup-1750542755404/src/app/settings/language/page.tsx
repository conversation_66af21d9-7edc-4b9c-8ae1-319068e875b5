'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { HomeIcon, Settings, Languages, Check } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/language-context';
import { SUPPORTED_LANGUAGES, SupportedLanguage } from '@/config/languages';
import { bilingualToast } from '@/components/ui/bilingual-toast';

export default function LanguageSettingsPage() {
  const { language, setLanguage } = useLanguage();
  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>(language);
  const [loading, setLoading] = useState(false);

  // تحديث اللغة المحددة عند تغيير اللغة الحالية
  useEffect(() => {
    setSelectedLanguage(language);
  }, [language]);

  // تغيير اللغة
  const changeLanguage = () => {
    if (selectedLanguage === language || loading) return;

    setLoading(true);
    
    // عرض رسالة تأكيد
    bilingualToast.info(
      'جاري تغيير اللغة... سيتم إعادة تحميل الصفحة',
      'Changing language... The page will reload'
    );
    
    // تغيير اللغة بعد فترة قصيرة
    setTimeout(() => {
      setLanguage(selectedLanguage);
    }, 1000);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
            <li className="inline-flex items-center">
              <Link href="/dashboard" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <HomeIcon className="h-4 w-4 ml-2" />
                {language === 'ar' ? 'الرئيسية' : 'Home'}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/settings" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                  <Settings className="h-4 w-4 ml-2" />
                  {language === 'ar' ? 'الإعدادات' : 'Settings'}
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-sm font-medium text-gray-500">
                  {language === 'ar' ? 'إعدادات اللغة' : 'Language Settings'}
                </span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">
          {language === 'ar' ? 'إعدادات اللغة' : 'Language Settings'}
        </h1>
        <h2 className="text-xl font-normal text-gray-700 mb-2">
          {language === 'ar' ? 'Language Settings' : 'إعدادات اللغة'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'ar' 
            ? 'تخصيص إعدادات اللغة في تطبيق أمين بلس | Customize language settings in Amin Plus application'
            : 'Customize language settings in Amin Plus application | تخصيص إعدادات اللغة في تطبيق أمين بلس'}
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'اختر اللغة' : 'Select Language'}
          </CardTitle>
          <CardDescription>
            {language === 'ar' 
              ? 'اختر لغة واجهة المستخدم المفضلة لديك'
              : 'Choose your preferred user interface language'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
              <div
                key={lang.code}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedLanguage === lang.code
                    ? 'border-primary bg-primary/10'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => setSelectedLanguage(lang.code)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{lang.flag}</span>
                    <div>
                      <h3 className="font-medium">{lang.nativeName}</h3>
                      <p className="text-sm text-muted-foreground">{lang.name}</p>
                    </div>
                  </div>
                  {selectedLanguage === lang.code && (
                    <Check className="h-5 w-5 text-primary" />
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-end">
            <Button
              onClick={changeLanguage}
              disabled={selectedLanguage === language || loading}
            >
              {loading
                ? (language === 'ar' ? 'جاري التغيير...' : 'Changing...')
                : (language === 'ar' ? 'تغيير اللغة' : 'Change Language')}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'معلومات اللغة' : 'Language Information'}
          </CardTitle>
          <CardDescription>
            {language === 'ar'
              ? 'معلومات حول اللغة المحددة حالياً'
              : 'Information about the currently selected language'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">
                  {language === 'ar' ? 'الاسم' : 'Name'}
                </h3>
                <p>
                  {SUPPORTED_LANGUAGES[language].nativeName} ({SUPPORTED_LANGUAGES[language].name})
                </p>
              </div>
              
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">
                  {language === 'ar' ? 'الاتجاه' : 'Direction'}
                </h3>
                <p>
                  {SUPPORTED_LANGUAGES[language].direction === 'rtl'
                    ? (language === 'ar' ? 'من اليمين إلى اليسار (RTL)' : 'Right to Left (RTL)')
                    : (language === 'ar' ? 'من اليسار إلى اليمين (LTR)' : 'Left to Right (LTR)')}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">
                  {language === 'ar' ? 'تنسيق التاريخ' : 'Date Format'}
                </h3>
                <p>{SUPPORTED_LANGUAGES[language].dateFormat}</p>
              </div>
              
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">
                  {language === 'ar' ? 'تنسيق الوقت' : 'Time Format'}
                </h3>
                <p>{SUPPORTED_LANGUAGES[language].timeFormat}</p>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">
                {language === 'ar' ? 'العملة' : 'Currency'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'الرمز' : 'Symbol'}
                  </p>
                  <p>{SUPPORTED_LANGUAGES[language].currency.symbol}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'الاسم' : 'Name'}
                  </p>
                  <p>{SUPPORTED_LANGUAGES[language].currency.name}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'الرمز الدولي' : 'Code'}
                  </p>
                  <p>{SUPPORTED_LANGUAGES[language].currency.code}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
