'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface Invoice {
  id: string;
  number: string;
  date: string;
  dueDate: string | null;
  status: string;
  total: number;
  customer: {
    id: string;
    name: string;
    email: string;
    phone: string | null;
    address: string | null;
  };
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
  }[];
}

export default function InvoiceDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoice = async () => {
      try {
        const response = await fetch(`/api/invoices/${params.id}`);
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('الفاتورة غير موجودة');
          }
          throw new Error('فشل في جلب بيانات الفاتورة');
        }

        const data = await response.json();
        setInvoice(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [params.id]);

  const handleDeleteInvoice = async () => {
    if (!confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) return;

    try {
      const response = await fetch(`/api/invoices/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('فشل في حذف الفاتورة');
      }

      router.push('/invoices');
    } catch (err) {
      console.error(err);
      alert('حدث خطأ أثناء حذف الفاتورة');
    }
  };

  const handlePrintInvoice = () => {
    window.print();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'مدفوعة';
      case 'sent':
        return 'مرسلة';
      case 'overdue':
        return 'متأخرة';
      default:
        return 'مسودة';
    }
  };

  if (loading) return <div className="text-center py-10">جاري التحميل...</div>;
  if (error) return <div className="text-center text-red-500 py-10">{error}</div>;
  if (!invoice) return <div className="text-center py-10">لا توجد بيانات للفاتورة</div>;

  return (
    <div className="max-w-4xl mx-auto px-4 py-8 print:py-0">
      <div className="print:hidden mb-8 flex justify-between items-center">
        <h1 className="text-2xl font-bold">تفاصيل الفاتورة</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.back()}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded ml-2"
          >
            رجوع
          </button>
          <Link
            href={`/invoices/${params.id}/edit`}
            className="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded ml-2"
          >
            تعديل
          </Link>
          <button
            onClick={handlePrintInvoice}
            className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded ml-2"
          >
            طباعة
          </button>
          <button
            onClick={handleDeleteInvoice}
            className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded"
          >
            حذف
          </button>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">بيانات الفاتورة</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-muted-foreground">الرقم:</span>
            <span className="font-medium">{invoice.number}</span>
          </div>
          <div>
            <span className="text-muted-foreground">التاريخ:</span>
            <span className="font-medium">{new Date(invoice.date).toLocaleDateString('ar-SA')}</span>
          </div>
          <div>
            <span className="text-muted-foreground">تاريخ الاستحقاق:</span>
            <span className="font-medium">{invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString('ar-SA') : 'غير محدد'}</span>
          </div>
          <div>
            <span className="text-muted-foreground">الحالة:</span>
            <span className={`font-medium px-2 py-1 rounded ${getStatusColor(invoice.status)}`}>
              {getStatusText(invoice.status)}
            </span>
          </div>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mt-6">
        <h2 className="text-xl font-semibold mb-4">بيانات العميل</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-muted-foreground">الاسم:</span>
            <span className="font-medium">{invoice.customer.name}</span>
          </div>
          <div>
            <span className="text-muted-foreground">البريد الإلكتروني:</span>
            <span className="font-medium">{invoice.customer.email}</span>
          </div>
          {invoice.customer.phone && (
            <div>
              <span className="text-muted-foreground">رقم الهاتف:</span>
              <span className="font-medium">{invoice.customer.phone}</span>
            </div>
          )}
          {invoice.customer.address && (
            <div>
              <span className="text-muted-foreground">العنوان:</span>
              <span className="font-medium">{invoice.customer.address}</span>
            </div>
          )}
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg mt-6">
        <h2 className="text-xl font-semibold p-6">المنتجات</h2>
        <table className="min-w-full">
          <thead className="bg-muted">
            <tr>
              <th className="py-3 px-6 text-right">المنتج</th>
              <th className="py-3 px-6 text-right">الكمية</th>
              <th className="py-3 px-6 text-right">السعر</th>
              <th className="py-3 px-6 text-right">الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            {invoice.items.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="py-3 px-6">{item.name}</td>
                <td className="py-3 px-6">{item.quantity}</td>
                <td className="py-3 px-6">
                  {new Intl.NumberFormat('ar-AE', {
                    style: 'currency',
                    currency: 'AED',
                  }).format(item.price)}
                </td>
                <td className="py-3 px-6">
                  {new Intl.NumberFormat('ar-AE', {
                    style: 'currency',
                    currency: 'AED',
                  }).format(item.price * item.quantity)}
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot className="bg-muted font-bold">
            <tr>
              <td className="py-3 px-6" colSpan={3}>الإجمالي</td>
              <td className="py-3 px-6">
                {new Intl.NumberFormat('ar-AE', {
                  style: 'currency',
                  currency: 'AED',
                }).format(invoice.total)}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  );
}
