'use client';

import Link from 'next/link';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { HomeIcon, Languages, Building, Receipt, CreditCard, Settings, Bell, Layers, Blocks } from 'lucide-react';

export default function ExamplesPage() {
  const examples = [
    {
      title: 'المكونات المتقدمة',
      titleEn: 'Advanced Components',
      description: 'أمثلة على استخدام المكونات ثنائية اللغة المتقدمة',
      descriptionEn: 'Examples of using advanced bilingual components',
      icon: <Blocks className="h-8 w-8 text-emerald-500" />,
      href: '/examples/bilingual-advanced',
      color: 'bg-emerald-50'
    },
    {
      title: 'واجهة المستخدم ثنائية اللغة',
      titleEn: 'Bilingual UI',
      description: 'أمثلة على استخدام مكونات واجهة المستخدم ثنائية اللغة',
      descriptionEn: 'Examples of using bilingual UI components',
      icon: <Layers className="h-8 w-8 text-violet-500" />,
      href: '/examples/bilingual-ui',
      color: 'bg-violet-50'
    },
    {
      title: 'المكونات ثنائية اللغة',
      titleEn: 'Bilingual Components',
      description: 'أمثلة على استخدام المكونات ثنائية اللغة المتقدمة',
      descriptionEn: 'Examples of using advanced bilingual components',
      icon: <Languages className="h-8 w-8 text-indigo-500" />,
      href: '/examples/bilingual-components',
      color: 'bg-indigo-50'
    },
    {
      title: 'الإشعارات',
      titleEn: 'Toast Notifications',
      description: 'أمثلة على استخدام مكونات الإشعارات ثنائية اللغة',
      descriptionEn: 'Examples of using bilingual toast notification components',
      icon: <Bell className="h-8 w-8 text-rose-500" />,
      href: '/examples/toast',
      color: 'bg-rose-50'
    },
    {
      title: 'الأسماء ثنائية اللغة',
      titleEn: 'Bilingual Names',
      description: 'أمثلة على استخدام مكونات الأسماء ثنائية اللغة',
      descriptionEn: 'Examples of using bilingual name components',
      icon: <Languages className="h-8 w-8 text-blue-500" />,
      href: '/examples/bilingual-name',
      color: 'bg-blue-50'
    },
    {
      title: 'معلومات الشركة',
      titleEn: 'Company Information',
      description: 'نموذج إدارة معلومات الشركة ثنائية اللغة',
      descriptionEn: 'Bilingual company information management form',
      icon: <Building className="h-8 w-8 text-green-500" />,
      href: '/settings/company',
      color: 'bg-green-50'
    },
    {
      title: 'الفواتير ثنائية اللغة',
      titleEn: 'Bilingual Invoices',
      description: 'نماذج الفواتير ثنائية اللغة',
      descriptionEn: 'Bilingual invoice templates',
      icon: <Receipt className="h-8 w-8 text-purple-500" />,
      href: '/dashboard/invoices',
      color: 'bg-purple-50'
    },
    {
      title: 'بوابة الدفع',
      titleEn: 'Payment Gateway',
      description: 'واجهة بوابة الدفع ثنائية اللغة',
      descriptionEn: 'Bilingual payment gateway interface',
      icon: <CreditCard className="h-8 w-8 text-amber-500" />,
      href: '/dashboard/payments',
      color: 'bg-amber-50'
    },
    {
      title: 'الإعدادات',
      titleEn: 'Settings',
      description: 'صفحة الإعدادات ثنائية اللغة',
      descriptionEn: 'Bilingual settings page',
      icon: <Settings className="h-8 w-8 text-gray-500" />,
      href: '/settings',
      color: 'bg-gray-50'
    }
  ];

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
            <li className="inline-flex items-center">
              <Link href="/dashboard" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <HomeIcon className="h-4 w-4 ml-2" />
                الرئيسية
              </Link>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-sm font-medium text-gray-500">أمثلة</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">أمثلة المكونات</h1>
        <h2 className="text-xl font-normal text-gray-700 mb-2">Component Examples</h2>
        <p className="text-muted-foreground">
          أمثلة على استخدام المكونات ثنائية اللغة في تطبيق أمين بلس | Examples of using bilingual components in Amin Plus application
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {examples.map((example) => (
          <Card key={example.href} className="overflow-hidden">
            <CardHeader className={`${example.color} pb-2`}>
              <div className="flex justify-center mb-2">
                {example.icon}
              </div>
              <CardTitle className="text-center">
                {example.title}
                <span className="block text-sm font-normal mt-1">{example.titleEn}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <CardDescription className="text-center mb-2">
                {example.description}
              </CardDescription>
              <CardDescription className="text-center text-gray-500">
                {example.descriptionEn}
              </CardDescription>
            </CardContent>
            <CardFooter className="flex justify-center pb-4">
              <Button asChild>
                <Link href={example.href}>
                  عرض المثال | View Example
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
