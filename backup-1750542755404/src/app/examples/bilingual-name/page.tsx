'use client';

import { useState } from 'react';
import { BilingualName, BilingualNameDisplay, BilingualNameCard } from '@/components/common/bilingual-name';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { HomeIcon } from 'lucide-react';
import Link from 'next/link';

export default function BilingualNameExamplePage() {
  const [nameAr, setNameAr] = useState('أمين بلس');
  const [nameEn, setNameEn] = useState('Amin Plus');
  
  const handleNameChange = (newNameAr: string, newNameEn: string) => {
    setNameAr(newNameAr);
    setNameEn(newNameEn);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
            <li className="inline-flex items-center">
              <Link href="/dashboard" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <HomeIcon className="h-4 w-4 ml-2" />
                الرئيسية
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/examples" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                  أمثلة
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-sm font-medium text-gray-500">الأسماء ثنائية اللغة</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">الأسماء ثنائية اللغة</h1>
        <h2 className="text-xl font-normal text-gray-700 mb-2">Bilingual Names</h2>
        <p className="text-muted-foreground">
          أمثلة على استخدام مكونات الأسماء ثنائية اللغة | Examples of using bilingual name components
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* مثال بسيط - Simple Example */}
        <Card>
          <CardHeader>
            <CardTitle>مثال بسيط | Simple Example</CardTitle>
            <CardDescription>
              استخدام مكون BilingualName الأساسي | Using the basic BilingualName component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BilingualName
              nameAr={nameAr}
              nameEn={nameEn}
              onNameChange={handleNameChange}
              required={true}
            />
          </CardContent>
        </Card>

        {/* مثال العرض - Display Example */}
        <Card>
          <CardHeader>
            <CardTitle>مثال العرض | Display Example</CardTitle>
            <CardDescription>
              استخدام مكون BilingualNameDisplay لعرض الاسم | Using BilingualNameDisplay component
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 border rounded-md">
              <h3 className="text-sm font-medium mb-2">العرض الافتراضي (العربية أولاً) | Default Display (Arabic First)</h3>
              <BilingualNameDisplay
                nameAr={nameAr}
                nameEn={nameEn}
                className="text-lg font-bold"
              />
            </div>
            
            <div className="p-4 border rounded-md">
              <h3 className="text-sm font-medium mb-2">الإنجليزية أولاً | English First</h3>
              <BilingualNameDisplay
                nameAr={nameAr}
                nameEn={nameEn}
                primaryLanguage="en"
                className="text-lg font-bold"
              />
            </div>
            
            <div className="p-4 border rounded-md">
              <h3 className="text-sm font-medium mb-2">فاصل مخصص | Custom Separator</h3>
              <BilingualNameDisplay
                nameAr={nameAr}
                nameEn={nameEn}
                separator=" - "
                className="text-lg font-bold"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* مثال البطاقة - Card Example */}
      <BilingualNameCard
        nameAr={nameAr}
        nameEn={nameEn}
        onNameChange={handleNameChange}
        title="اسم التطبيق | Application Name"
        required={true}
        className="mb-8"
      />

      {/* أحجام مختلفة - Different Sizes */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>أحجام مختلفة | Different Sizes</CardTitle>
          <CardDescription>
            استخدام مكون BilingualName بأحجام مختلفة | Using BilingualName component with different sizes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <BilingualName
            nameAr={nameAr}
            nameEn={nameEn}
            onNameChange={handleNameChange}
            label="حجم صغير | Small Size"
            size="sm"
          />
          
          <BilingualName
            nameAr={nameAr}
            nameEn={nameEn}
            onNameChange={handleNameChange}
            label="الحجم الافتراضي | Default Size"
            size="default"
          />
          
          <BilingualName
            nameAr={nameAr}
            nameEn={nameEn}
            onNameChange={handleNameChange}
            label="حجم كبير | Large Size"
            size="lg"
          />
        </CardContent>
      </Card>

      {/* إعادة تعيين القيم - Reset Values */}
      <div className="flex justify-center mb-8">
        <Button 
          onClick={() => handleNameChange('أمين بلس', 'Amin Plus')}
          className="mx-2"
        >
          إعادة تعيين القيم | Reset Values
        </Button>
        
        <Button 
          variant="outline"
          onClick={() => handleNameChange('', '')}
          className="mx-2"
        >
          مسح القيم | Clear Values
        </Button>
      </div>
    </div>
  );
}
