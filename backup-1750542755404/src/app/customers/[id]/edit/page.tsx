'use client';

import CustomerForm from '@/components/customers/customer-form';
import { useEffect, useState } from 'react';
import { CustomerFormData } from '@/types'; // Ensure this type is defined and imported

export default function EditCustomerPage({ params }: { params: { id: string } }) {
  const [customer, setCustomer] = useState<CustomerFormData | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCustomer = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`/api/customers/${params.id}`);
      if (!response.ok) {
        throw new Error('فشل في جلب بيانات العميل');
      }
      const data = await response.json();
      setCustomer(data);
    } catch (err: any) {
      setError(err.message || 'حدث خطأ أثناء جلب بيانات العميل');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomer();
  }, [params.id]);

  if (loading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={fetchCustomer} />;
  }

  return (
    <div className="max-w-3xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">تعديل بيانات العميل</h1>
      <CustomerForm initialData={customer} isEditing />
    </div>
  );
}

function LoadingState() {
  return <div className="text-center py-10">جاري التحميل...</div>;
}

function ErrorState({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <div className="text-center text-red-600 py-10">
      <p>{error}</p>
      <button
        onClick={onRetry}
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        إعادة المحاولة
      </button>
    </div>
  );
}