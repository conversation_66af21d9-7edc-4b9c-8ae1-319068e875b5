import './globals.css';
import type { Metadata } from 'next';
import { ClientLayout } from '@/components/layout/client-layout';
import { SessionProvider } from '@/contexts/session-context';

// استيراد متغيرات البيئة للواجهة الأمامية
// Import environment variables for frontend
const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME ?? 'أمين بلس';
const APP_NAME_EN = process.env.NEXT_PUBLIC_APP_NAME_EN ?? 'Amin Plus';
const APP_DESCRIPTION = process.env.NEXT_PUBLIC_APP_DESCRIPTION ?? 'نظام إدارة المبيعات والمخازن';
const APP_DESCRIPTION_EN = process.env.NEXT_PUBLIC_APP_DESCRIPTION_EN ?? 'Integrated Sales & Inventory Management System';

export const metadata: Metadata = {
  title: `${APP_NAME} | ${APP_NAME_EN}`,
  description: `${APP_DESCRIPTION} | ${APP_DESCRIPTION_EN}`,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className="m-0 p-0 bg-gray-50">
        <SessionProvider>
          <ClientLayout>
            {children}
          </ClientLayout>
        </SessionProvider>
      </body>
    </html>
  );
}
