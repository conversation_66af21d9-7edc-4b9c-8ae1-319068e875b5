'use client';

import { useState, useEffect } from 'react';
import { formatCurrency } from '@/lib/utils';
import { getSystemSettings } from '@/lib/settings';

interface Expense {
  id: string;
  date: string;
  category: string;
  description: string;
  amount: number;
  paymentMethod: string;
  reference: string;
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [currentExpense, setCurrentExpense] = useState<Expense | null>(null);
  const [filter, setFilter] = useState({
    startDate: '',
    endDate: '',
    category: '',
    minAmount: '',
    maxAmount: ''
  });

  // فئات المصروفات
  const expenseCategories = [
    'إيجار | Rent',
    'رواتب | Salaries',
    'مرافق | Utilities',
    'معدات | Equipment',
    'تسويق | Marketing',
    'صيانة | Maintenance',
    'مشتريات | Purchases',
    'ضرائب | Taxes',
    'تأمين | Insurance',
    'أخرى | Other'
  ];

  // طرق الدفع
  const paymentMethods = [
    'نقدي | Cash',
    'بطاقة ائتمان | Credit Card',
    'تحويل بنكي | Bank Transfer',
    'شيك | Check',
    'محفظة إلكترونية | E-Wallet'
  ];

  useEffect(() => {
    // استدعاء API للحصول على المصروفات
    const fetchExpenses = async () => {
      setLoading(true);
      try {
        // بناء URL مع معلمات الاستعلام
        let url = '/api/expenses';
        const params = new URLSearchParams();

        if (filter.startDate) params.append('startDate', filter.startDate);
        if (filter.endDate) params.append('endDate', filter.endDate);
        if (filter.category) params.append('type', filter.category);
        if (filter.minAmount) params.append('minAmount', filter.minAmount);
        if (filter.maxAmount) params.append('maxAmount', filter.maxAmount);

        if (params.toString()) {
          url += `?${params.toString()}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('فشل في جلب المصاريف');
        }

        const data = await response.json();
        setExpenses(data.map(expense => ({
          id: expense.id.toString(),
          date: expense.expenseDate,
          category: expense.expenseType || 'أخرى | Other',
          description: expense.description,
          amount: expense.amount,
          paymentMethod: 'نقدي | Cash', // افتراضي
          reference: '',
          createdAt: expense.createdAt,
          updatedAt: expense.updatedAt
        })));
      } catch (error) {
        console.error('خطأ في جلب المصروفات:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchExpenses();
  }, [filter]);

  // حساب إجمالي المصروفات
  const totalExpenses = expenses.reduce((total, expense) => total + expense.amount, 0);

  // تصفية المصروفات
  const filteredExpenses = expenses.filter(expense => {
    let match = true;

    if (filter.startDate && new Date(expense.date) < new Date(filter.startDate)) {
      match = false;
    }

    if (filter.endDate && new Date(expense.date) > new Date(filter.endDate)) {
      match = false;
    }

    if (filter.category && expense.category !== filter.category) {
      match = false;
    }

    if (filter.minAmount && expense.amount < parseFloat(filter.minAmount)) {
      match = false;
    }

    if (filter.maxAmount && expense.amount > parseFloat(filter.maxAmount)) {
      match = false;
    }

    return match;
  });

  // إضافة مصروف جديد
  const handleAddExpense = () => {
    setCurrentExpense(null);
    setShowModal(true);
  };

  // تعديل مصروف
  const handleEditExpense = (expense: Expense) => {
    setCurrentExpense(expense);
    setShowModal(true);
  };

  // حذف مصروف
  const handleDeleteExpense = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
      try {
        const response = await fetch(`/api/expenses/${id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('فشل في حذف المصروف');
        }

        // تحديث القائمة بعد الحذف
        const updatedExpenses = expenses.filter(expense => expense.id !== id);
        setExpenses(updatedExpenses);

      } catch (error) {
        console.error('خطأ في حذف المصروف:', error);
        alert('حدث خطأ أثناء حذف المصروف');
      }
    }
  };

  // حفظ المصروف (إضافة أو تعديل)
  const handleSaveExpense = async (formData: any) => {
    try {
      if (currentExpense) {
        // تعديل مصروف موجود
        const response = await fetch(`/api/expenses/${currentExpense.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            description: formData.description,
            amount: parseFloat(formData.amount),
            expenseType: formData.category,
            expenseDate: formData.date,
          }),
        });

        if (!response.ok) {
          throw new Error('فشل في تحديث المصروف');
        }

        const updatedExpense = await response.json();

        // تحديث القائمة بعد التعديل
        setExpenses(expenses.map(expense =>
          expense.id === currentExpense.id
            ? {
              id: updatedExpense.id.toString(),
              date: updatedExpense.expenseDate,
              category: updatedExpense.expenseType ?? 'أخرى | Other',
              description: updatedExpense.description,
              amount: updatedExpense.amount,
              paymentMethod: 'نقدي | Cash',
              reference: '',
              createdAt: updatedExpense.createdAt,
              updatedAt: updatedExpense.updatedAt
            }
            : expense
        ));
      } else {
        // إضافة مصروف جديد
        const response = await fetch('/api/expenses', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            description: formData.description,
            amount: parseFloat(formData.amount),
            expenseType: formData.category,
            expenseDate: formData.date,
          }),
        });

        if (!response.ok) {
          throw new Error('فشل في إضافة المصروف');
        }

        const newExpense = await response.json();

        // تحديث القائمة بعد الإضافة
        setExpenses([...expenses, {
          id: newExpense.id.toString(),
          date: newExpense.expenseDate,
          category: newExpense.expenseType ?? 'أخرى | Other',
          description: newExpense.description,
          amount: newExpense.amount,
          paymentMethod: 'نقدي | Cash',
          reference: '',
          createdAt: newExpense.createdAt,
          updatedAt: newExpense.updatedAt
        }]);
      }

      setShowModal(false);
    } catch (error) {
      console.error('خطأ في حفظ المصروف:', error);
      alert('حدث خطأ أثناء حفظ المصروف');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1 dark:text-white">المصروفات</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">Expenses</p>
        </div>
        <button
          onClick={handleAddExpense}
          className="px-4 py-2 bg-primary text-white rounded-md text-sm"
        >
          إضافة مصروف | Add Expense
        </button>
      </div>

      {/* ملخص المصروفات */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
          <h3 className="text-sm text-blue-500 dark:text-blue-300">إجمالي المصروفات</h3>
          <p className="text-2xl font-bold text-blue-700 dark:text-blue-100">
            {formatCurrency(totalExpenses)}
          </p>
        </div>

        <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
          <h3 className="text-sm text-green-500 dark:text-green-300">متوسط المصروفات الشهرية</h3>
          <p className="text-2xl font-bold text-green-700 dark:text-green-100">
            {formatCurrency(totalExpenses / 12)}
          </p>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
          <h3 className="text-sm text-purple-500 dark:text-purple-300">عدد المصروفات</h3>
          <p className="text-2xl font-bold text-purple-700 dark:text-purple-100">
            {expenses.length}
          </p>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold mb-3 dark:text-white">تصفية المصروفات</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">من تاريخ</label>
            <input
              type="date"
              value={filter.startDate}
              onChange={(e) => setFilter({ ...filter, startDate: e.target.value })}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">إلى تاريخ</label>
            <input
              type="date"
              value={filter.endDate}
              onChange={(e) => setFilter({ ...filter, endDate: e.target.value })}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">الفئة</label>
            <select
              value={filter.category}
              onChange={(e) => setFilter({ ...filter, category: e.target.value })}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="">الكل</option>
              {expenseCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">الحد الأدنى للمبلغ</label>
            <input
              type="number"
              value={filter.minAmount}
              onChange={(e) => setFilter({ ...filter, minAmount: e.target.value })}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">الحد الأقصى للمبلغ</label>
            <input
              type="number"
              value={filter.maxAmount}
              onChange={(e) => setFilter({ ...filter, maxAmount: e.target.value })}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>
      </div>

      {/* جدول المصروفات */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="p-2 text-right border dark:border-gray-600">التاريخ</th>
              <th className="p-2 text-right border dark:border-gray-600">الفئة</th>
              <th className="p-2 text-right border dark:border-gray-600">الوصف</th>
              <th className="p-2 text-right border dark:border-gray-600">المبلغ</th>
              <th className="p-2 text-right border dark:border-gray-600">طريقة الدفع</th>
              <th className="p-2 text-center border dark:border-gray-600">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={6} className="p-4 text-center">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </td>
              </tr>
            ) : filteredExpenses.length === 0 ? (
              <tr>
                <td colSpan={6} className="p-4 text-center text-gray-500 dark:text-gray-400">
                  لا توجد مصروفات مطابقة للفلاتر المحددة
                </td>
              </tr>
            ) : (
              filteredExpenses.map(expense => (
                <tr key={expense.id} className="border-b dark:border-gray-700">
                  <td className="p-2 border dark:border-gray-600">
                    {new Date(expense.date).toLocaleDateString('ar-AE')}
                  </td>
                  <td className="p-2 border dark:border-gray-600">{expense.category}</td>
                  <td className="p-2 border dark:border-gray-600">{expense.description}</td>
                  <td className="p-2 border dark:border-gray-600">
                    {formatCurrency(expense.amount)}
                  </td>
                  <td className="p-2 border dark:border-gray-600">{expense.paymentMethod}</td>
                  <td className="p-2 border dark:border-gray-600 text-center">
                    <div className="flex justify-center space-x-1 space-x-reverse">
                      <button
                        onClick={() => handleEditExpense(expense)}
                        className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-md text-xs"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleDeleteExpense(expense.id)}
                        className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 rounded-md text-xs"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* نموذج إضافة/تعديل مصروف */}
      {showModal && (
        <ExpenseForm
          expense={currentExpense}
          categories={expenseCategories}
          paymentMethods={paymentMethods}
          onSave={handleSaveExpense}
          onCancel={() => setShowModal(false)}
        />
      )}
    </div>
  );
}

// مكون نموذج المصروفات
function ExpenseForm({ expense, categories, paymentMethods, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    date: expense?.date || new Date().toISOString().split('T')[0],
    category: expense?.category || categories[0],
    description: expense?.description || '',
    amount: expense?.amount || '',
    paymentMethod: expense?.paymentMethod || paymentMethods[0],
    reference: expense?.reference || ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' ? parseFloat(value) || '' : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4 dark:text-white">
          {expense ? 'تعديل مصروف' : 'إضافة مصروف جديد'}
        </h2>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              التاريخ <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              الفئة <span className="text-red-500">*</span>
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              الوصف <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              المبلغ <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              طريقة الدفع <span className="text-red-500">*</span>
            </label>
            <select
              name="paymentMethod"
              value={formData.paymentMethod}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            >
              {paymentMethods.map(method => (
                <option key={method} value={method}>{method}</option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              المرجع / رقم الإيصال
            </label>
            <input
              type="text"
              name="reference"
              value={formData.reference}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
            />
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:text-white rounded-md"
            >
              إلغاء | Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md"
            >
              {expense ? 'تحديث | Update' : 'إضافة | Add'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
