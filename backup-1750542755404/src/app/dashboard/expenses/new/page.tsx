'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BilingualPageTitle } from '@/components/common/bilingual-page-title';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeftIcon, SaveIcon, PlusIcon } from 'lucide-react';

interface ExpenseCategory {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
}

interface Supplier {
  id: number;
  name: string;
}

export default function NewExpensePage() {
  const router = useRouter();
  
  // حالة النموذج
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: '',
    taxAmount: '',
    expenseDate: new Date(),
    dueDate: null as Date | null,
    paymentStatus: 'PAID',
    paymentMethod: 'CASH',
    reference: '',
    recurring: false,
    recurringPeriod: '',
    recurringEndDate: null as Date | null,
    categoryId: '',
    supplierId: '',
    tags: [] as string[],
    attachments: [] as { fileName: string; fileUrl: string; fileType: string; fileSize: number }[],
  });
  
  // حالة التحميل
  // Loading state
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [suppliersLoading, setSuppliersLoading] = useState(true);
  
  // بيانات القوائم
  // List data
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  
  // حالة علامة التبويب النشطة
  // Active tab state
  const [activeTab, setActiveTab] = useState('basic');
  
  // جلب فئات المصروفات
  // Fetch expense categories
  const fetchCategories = async () => {
    setCategoriesLoading(true);
    
    try {
      const response = await fetch('/api/expense-categories');
      
      if (!response.ok) {
        throw new Error('فشل جلب فئات المصروفات');
      }
      
      const data: ExpenseCategory[] = await response.json();
      setCategories(data);
    } catch (error) {
      console.error('Error fetching expense categories:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب فئات المصروفات',
        variant: 'destructive',
      });
    } finally {
      setCategoriesLoading(false);
    }
  };
  
  // جلب الموردين
  // Fetch suppliers
  const fetchSuppliers = async () => {
    setSuppliersLoading(true);
    
    try {
      const response = await fetch('/api/suppliers');
      
      if (!response.ok) {
        throw new Error('فشل جلب الموردين');
      }
      
      const data: Supplier[] = await response.json();
      setSuppliers(data);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب الموردين',
        variant: 'destructive',
      });
    } finally {
      setSuppliersLoading(false);
    }
  };
  
  // جلب البيانات الأولية
  // Fetch initial data
  useEffect(() => {
    fetchCategories();
    fetchSuppliers();
  }, []);
  
  // تحديث حقل في النموذج
  // Update form field
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };
  
  // إضافة علامة
  // Add tag
  const handleAddTag = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag],
      }));
    }
  };
  
  // إزالة علامة
  // Remove tag
  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag),
    }));
  };
  
  // إضافة مرفق
  // Add attachment
  const handleAddAttachment = (attachment: { fileName: string; fileUrl: string; fileType: string; fileSize: number }) => {
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, attachment],
    }));
  };
  
  // إزالة مرفق
  // Remove attachment
  const handleRemoveAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index),
    }));
  };
  
  // حفظ المصروف
  // Save expense
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // التحقق من البيانات المطلوبة
      // Validate required fields
      if (!formData.title || !formData.amount) {
        toast({
          title: 'خطأ',
          description: 'يرجى ملء جميع الحقول المطلوبة',
          variant: 'destructive',
        });
        return;
      }
      
      // حساب المبلغ الإجمالي
      // Calculate total amount
      const amount = parseFloat(formData.amount);
      const taxAmount = formData.taxAmount ? parseFloat(formData.taxAmount) : 0;
      const totalAmount = amount + taxAmount;
      
      // إعداد بيانات الطلب
      // Prepare request data
      const requestData = {
        ...formData,
        amount,
        taxAmount,
        totalAmount,
        expenseDate: formData.expenseDate.toISOString(),
        dueDate: formData.dueDate ? formData.dueDate.toISOString() : null,
        recurringEndDate: formData.recurringEndDate ? formData.recurringEndDate.toISOString() : null,
      };
      
      // إرسال الطلب
      // Send request
      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل إنشاء المصروف');
      }
      
      const data = await response.json();
      
      toast({
        title: 'تم بنجاح',
        description: 'تم إنشاء المصروف بنجاح',
      });
      
      // الانتقال إلى صفحة المصروفات
      // Navigate to expenses page
      router.push('/dashboard/expenses');
    } catch (error) {
      console.error('Error creating expense:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء المصروف',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };
  
  // إلغاء وعودة إلى صفحة المصروفات
  // Cancel and return to expenses page
  const handleCancel = () => {
    router.push('/dashboard/expenses');
  };
  
  return (
    <div className="container py-6">
      <BilingualPageTitle
        titleAr="إضافة مصروف جديد"
        titleEn="Add New Expense"
        descriptionAr="إنشاء مصروف جديد وإضافته إلى النظام"
        descriptionEn="Create a new expense and add it to the system"
        icon={<span className="w-8 h-8 flex items-center justify-center rounded-md bg-red-100 text-red-700">💸</span>}
        actions={
          <div className="flex items-center gap-2">
            <Button onClick={handleCancel} variant="outline" size="sm">
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              إلغاء / Cancel
            </Button>
            <Button onClick={handleSubmit} size="sm" disabled={loading}>
              <SaveIcon className="w-4 h-4 mr-2" />
              {loading ? 'جاري الحفظ... / Saving...' : 'حفظ / Save'}
            </Button>
          </div>
        }
      />
      
      <Card>
        <CardContent className="p-6">
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="basic">معلومات أساسية / Basic Info</TabsTrigger>
              <TabsTrigger value="payment">معلومات الدفع / Payment Info</TabsTrigger>
              <TabsTrigger value="recurring">التكرار / Recurring</TabsTrigger>
              <TabsTrigger value="additional">معلومات إضافية / Additional Info</TabsTrigger>
            </TabsList>
            
            <form onSubmit={handleSubmit}>
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">
                      العنوان / Title <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="أدخل عنوان المصروف / Enter expense title"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="amount">
                      المبلغ / Amount <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.amount}
                      onChange={(e) => handleInputChange('amount', e.target.value)}
                      placeholder="أدخل مبلغ المصروف / Enter expense amount"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="taxAmount">
                      مبلغ الضريبة / Tax Amount
                    </Label>
                    <Input
                      id="taxAmount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.taxAmount}
                      onChange={(e) => handleInputChange('taxAmount', e.target.value)}
                      placeholder="أدخل مبلغ الضريبة / Enter tax amount"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="expenseDate">
                      تاريخ المصروف / Expense Date <span className="text-red-500">*</span>
                    </Label>
                    <DatePicker
                      value={formData.expenseDate}
                      onChange={(date) => handleInputChange('expenseDate', date)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="category">
                      الفئة / Category
                    </Label>
                    <Select
                      value={formData.categoryId}
                      onValueChange={(value) => handleInputChange('categoryId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر فئة / Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">بدون فئة / No Category</SelectItem>
                        {categoriesLoading ? (
                          <div className="p-2">
                            <Skeleton className="h-4 w-full mb-2" />
                            <Skeleton className="h-4 w-full mb-2" />
                            <Skeleton className="h-4 w-full" />
                          </div>
                        ) : (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id.toString()}>
                              {category.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="supplier">
                      المورد / Supplier
                    </Label>
                    <Select
                      value={formData.supplierId}
                      onValueChange={(value) => handleInputChange('supplierId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر مورد / Select supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">بدون مورد / No Supplier</SelectItem>
                        {suppliersLoading ? (
                          <div className="p-2">
                            <Skeleton className="h-4 w-full mb-2" />
                            <Skeleton className="h-4 w-full mb-2" />
                            <Skeleton className="h-4 w-full" />
                          </div>
                        ) : (
                          suppliers.map((supplier) => (
                            <SelectItem key={supplier.id} value={supplier.id.toString()}>
                              {supplier.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="description">
                      الوصف / Description
                    </Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="أدخل وصف المصروف / Enter expense description"
                      rows={4}
                    />
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="payment" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="paymentStatus">
                      حالة الدفع / Payment Status
                    </Label>
                    <Select
                      value={formData.paymentStatus}
                      onValueChange={(value) => handleInputChange('paymentStatus', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر حالة الدفع / Select payment status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PAID">مدفوع / Paid</SelectItem>
                        <SelectItem value="PENDING">معلق / Pending</SelectItem>
                        <SelectItem value="PARTIAL">جزئي / Partial</SelectItem>
                        <SelectItem value="CANCELLED">ملغي / Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">
                      طريقة الدفع / Payment Method
                    </Label>
                    <Select
                      value={formData.paymentMethod}
                      onValueChange={(value) => handleInputChange('paymentMethod', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر طريقة الدفع / Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CASH">نقدي / Cash</SelectItem>
                        <SelectItem value="BANK_TRANSFER">تحويل بنكي / Bank Transfer</SelectItem>
                        <SelectItem value="CREDIT_CARD">بطاقة ائتمان / Credit Card</SelectItem>
                        <SelectItem value="CHEQUE">شيك / Cheque</SelectItem>
                        <SelectItem value="ONLINE">دفع إلكتروني / Online Payment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="reference">
                      المرجع / Reference
                    </Label>
                    <Input
                      id="reference"
                      value={formData.reference}
                      onChange={(e) => handleInputChange('reference', e.target.value)}
                      placeholder="أدخل رقم المرجع / Enter reference number"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="dueDate">
                      تاريخ الاستحقاق / Due Date
                    </Label>
                    <DatePicker
                      value={formData.dueDate}
                      onChange={(date) => handleInputChange('dueDate', date)}
                    />
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="recurring" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Checkbox
                        id="recurring"
                        checked={formData.recurring}
                        onCheckedChange={(checked) => handleInputChange('recurring', checked)}
                      />
                      <Label htmlFor="recurring">
                        مصروف متكرر / Recurring Expense
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      حدد هذا الخيار إذا كان هذا المصروف يتكرر بشكل دوري
                      <br />
                      Check this option if this expense recurs periodically
                    </p>
                  </div>
                  
                  {formData.recurring && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="recurringPeriod">
                          فترة التكرار / Recurring Period
                        </Label>
                        <Select
                          value={formData.recurringPeriod}
                          onValueChange={(value) => handleInputChange('recurringPeriod', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="اختر فترة التكرار / Select recurring period" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DAILY">يومي / Daily</SelectItem>
                            <SelectItem value="WEEKLY">أسبوعي / Weekly</SelectItem>
                            <SelectItem value="MONTHLY">شهري / Monthly</SelectItem>
                            <SelectItem value="QUARTERLY">ربع سنوي / Quarterly</SelectItem>
                            <SelectItem value="YEARLY">سنوي / Yearly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="recurringEndDate">
                          تاريخ انتهاء التكرار / Recurring End Date
                        </Label>
                        <DatePicker
                          value={formData.recurringEndDate}
                          onChange={(date) => handleInputChange('recurringEndDate', date)}
                        />
                      </div>
                    </>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="additional" className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label>
                      العلامات / Tags
                    </Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {formData.tags.map((tag) => (
                        <div
                          key={tag}
                          className="bg-primary/10 text-primary px-2 py-1 rounded-md text-sm flex items-center"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => handleRemoveTag(tag)}
                            className="ml-1 text-primary hover:text-primary/80"
                          >
                            &times;
                          </button>
                        </div>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        id="tag"
                        placeholder="أدخل علامة / Enter tag"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddTag((e.target as HTMLInputElement).value);
                            (e.target as HTMLInputElement).value = '';
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          const input = document.getElementById('tag') as HTMLInputElement;
                          handleAddTag(input.value);
                          input.value = '';
                        }}
                      >
                        <PlusIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>
                      المرفقات / Attachments
                    </Label>
                    <div className="flex flex-col gap-2 mb-2">
                      {formData.attachments.map((attachment, index) => (
                        <div
                          key={index}
                          className="bg-secondary/50 p-2 rounded-md text-sm flex items-center justify-between"
                        >
                          <div>
                            <span className="font-medium">{attachment.fileName}</span>
                            <span className="text-muted-foreground ml-2">
                              ({Math.round(attachment.fileSize / 1024)} KB)
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveAttachment(index)}
                            className="text-destructive hover:text-destructive/80"
                          >
                            &times;
                          </button>
                        </div>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        id="attachment"
                        type="file"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            // في التطبيق الحقيقي، يجب رفع الملف إلى الخادم
                            // In a real app, you would upload the file to the server
                            handleAddAttachment({
                              fileName: file.name,
                              fileUrl: URL.createObjectURL(file),
                              fileType: file.type,
                              fileSize: file.size,
                            });
                            e.target.value = '';
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <div className="mt-6 flex justify-end space-x-2 rtl:space-x-reverse">
                <Button type="button" variant="outline" onClick={handleCancel}>
                  إلغاء / Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'جاري الحفظ... / Saving...' : 'حفظ / Save'}
                </Button>
              </div>
            </form>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
