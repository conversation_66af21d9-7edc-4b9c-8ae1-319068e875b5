'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { DataTable } from '@/components/ui/data-table';
import { IngredientsStockAlerts } from '@/components/recipes/ingredients-stock-alerts';
import { toast } from 'sonner';
import { Edit, Search, RefreshCw, Package } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  price: number;
  cost?: number;
  stock: number;
  minStock?: number;
  unit?: string;
  isComposite: boolean;
}

export default function IngredientsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const router = useRouter();

  useEffect(() => {
    fetchIngredients();
  }, []);

  const fetchIngredients = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      if (!response.ok) {
        throw new Error('فشل في جلب المكونات');
      }
      const data = await response.json();
      
      // ترتيب المنتجات حسب المخزون (الأقل أولاً)
      const sortedProducts = data.sort((a: Product, b: Product) => {
        // إذا كان المنتج له حد أدنى للمخزون والمخزون أقل من الحد الأدنى
        const aIsLow = a.minStock && a.stock < a.minStock;
        const bIsLow = b.minStock && b.stock < b.minStock;
        
        if (aIsLow && !bIsLow) return -1;
        if (!aIsLow && bIsLow) return 1;
        
        // ترتيب حسب المخزون
        return a.stock - b.stock;
      });
      
      setProducts(sortedProducts);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب المكونات:', error);
      toast.error('حدث خطأ أثناء جلب المكونات');
      setLoading(false);
    }
  };

  const handleEditProduct = (id: number) => {
    router.push(`/dashboard/products/${id}`);
  };

  const handleViewIngredient = (id: number) => {
    router.push(`/dashboard/products/${id}`);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(price);
  };

  const filteredProducts = products.filter(product => 
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const columns = [
    {
      header: 'اسم المكون',
      accessorKey: 'name',
    },
    {
      header: 'المخزون',
      accessorKey: 'stock',
      cell: ({ row }: any) => {
        const product = row.original;
        const isLowStock = product.minStock && product.stock < product.minStock;
        const isOutOfStock = product.stock <= 0;
        
        return (
          <div className={`font-medium ${isOutOfStock ? 'text-red-600' : isLowStock ? 'text-amber-600' : ''}`}>
            {product.stock} {product.unit && `(${product.unit})`}
            {isOutOfStock && <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full mr-2">نفد</span>}
            {isLowStock && !isOutOfStock && <span className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full mr-2">منخفض</span>}
          </div>
        );
      },
    },
    {
      header: 'الحد الأدنى',
      accessorKey: 'minStock',
      cell: ({ row }: any) => row.original.minStock || '-',
    },
    {
      header: 'التكلفة',
      accessorKey: 'cost',
      cell: ({ row }: any) => formatPrice(row.original.cost || 0),
    },
    {
      header: 'سعر البيع',
      accessorKey: 'price',
      cell: ({ row }: any) => formatPrice(row.original.price),
    },
    {
      header: 'الإجراءات',
      cell: ({ row }: any) => (
        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleEditProduct(row.original.id)}
            title="تعديل المكون"
          >
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Breadcrumb
          items={[
            { label: 'الرئيسية', href: '/dashboard' },
            { label: 'التقادير', href: '/dashboard/recipes' },
            { label: 'إدارة المكونات', href: '/dashboard/recipes/ingredients' },
          ]}
        />
        <Button variant="outline" onClick={fetchIngredients}>
          <RefreshCw className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
          تحديث
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
                  إدارة مخزون المكونات
                </CardTitle>
                <div className="relative w-64">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="بحث عن مكون..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center p-4">
                  <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : filteredProducts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد مكونات متطابقة مع البحث
                </div>
              ) : (
                <DataTable
                  data={filteredProducts}
                  columns={columns}
                  searchable={false}
                  pagination
                  pageSize={10}
                />
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-1">
          <IngredientsStockAlerts onViewIngredient={handleViewIngredient} />
        </div>
      </div>
    </div>
  );
}
