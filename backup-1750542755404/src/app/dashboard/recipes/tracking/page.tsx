'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON>le, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { QrCode, Printer, Download, Search, Package, Calendar, Tag } from 'lucide-react';
import Image from 'next/image';

interface Product {
  id: number;
  name: string;
  price: number;
  isComposite: boolean;
}

interface ProductionPlan {
  id: number;
  productId: number;
  quantity: number;
  batchNumber: string;
  productionDate: string;
  expiryDate: string;
  product: {
    name: string;
  };
}

export default function ProductTrackingPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [productionPlans, setProductionPlans] = useState<ProductionPlan[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');
  const [batchNumber, setBatchNumber] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('product');
  const [qrCode, setQrCode] = useState<string>('');
  const [trackingData, setTrackingData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchingData, setFetchingData] = useState<boolean>(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setFetchingData(true);
      
      // جلب المنتجات
      const productsResponse = await fetch('/api/products');
      if (!productsResponse.ok) {
        throw new Error('فشل في جلب المنتجات');
      }
      const productsData = await productsResponse.json();
      setProducts(productsData);
      
      // جلب خطط الإنتاج
      // هنا يجب إنشاء API لجلب خطط الإنتاج
      // لأغراض العرض، سنستخدم بيانات وهمية
      const mockPlans = [
        {
          id: 1,
          productId: 1,
          quantity: 10,
          batchNumber: 'BATCH-001',
          productionDate: new Date().toISOString(),
          expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          product: { name: 'منتج تجريبي 1' }
        },
        {
          id: 2,
          productId: 2,
          quantity: 20,
          batchNumber: 'BATCH-002',
          productionDate: new Date().toISOString(),
          expiryDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
          product: { name: 'منتج تجريبي 2' }
        }
      ];
      setProductionPlans(mockPlans);
      
      setFetchingData(false);
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
      toast.error('حدث خطأ أثناء جلب البيانات');
      setFetchingData(false);
    }
  };

  const generateQRCode = async () => {
    try {
      setLoading(true);
      
      let url = '/api/recipes/qr-code?';
      
      if (activeTab === 'product' && selectedProductId) {
        url += `productId=${selectedProductId}`;
      } else if (activeTab === 'batch' && batchNumber) {
        url += `batchNumber=${batchNumber}`;
      } else if (activeTab === 'plan' && selectedPlanId) {
        url += `productionPlanId=${selectedPlanId}`;
      } else {
        toast.error('الرجاء اختيار منتج أو دفعة أو خطة إنتاج');
        setLoading(false);
        return;
      }
      
      const response = await fetch(url);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إنشاء رمز QR');
      }
      
      const data = await response.json();
      setQrCode(data.qrCode);
      setTrackingData(data.data);
      
      setLoading(false);
    } catch (error) {
      console.error('خطأ في إنشاء رمز QR:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء رمز QR');
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    if (!qrCode) return;
    
    const link = document.createElement('a');
    link.href = qrCode;
    link.download = `qrcode-${activeTab}-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ar-AE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: 'تتبع المنتجات', href: '/dashboard/recipes/tracking' },
        ]}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <QrCode className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
              إنشاء رمز QR للتتبع
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="product" className="flex items-center">
                  <Package className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  منتج
                </TabsTrigger>
                <TabsTrigger value="batch" className="flex items-center">
                  <Tag className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  دفعة
                </TabsTrigger>
                <TabsTrigger value="plan" className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  خطة إنتاج
                </TabsTrigger>
              </TabsList>

              <TabsContent value="product" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="product">المنتج</Label>
                  <Select
                    value={selectedProductId}
                    onValueChange={setSelectedProductId}
                    disabled={fetchingData || loading}
                  >
                    <SelectTrigger id="product">
                      <SelectValue placeholder="اختر المنتج" />
                    </SelectTrigger>
                    <SelectContent>
                      {fetchingData ? (
                        <div className="flex justify-center p-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        </div>
                      ) : products.length === 0 ? (
                        <div className="p-2 text-center text-gray-500">لا توجد منتجات</div>
                      ) : (
                        products.map((product) => (
                          <SelectItem key={product.id} value={product.id.toString()}>
                            {product.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>

              <TabsContent value="batch" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="batchNumber">رقم الدفعة</Label>
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <Input
                      id="batchNumber"
                      value={batchNumber}
                      onChange={(e) => setBatchNumber(e.target.value)}
                      disabled={loading}
                      placeholder="مثال: BATCH-001"
                    />
                    <Button variant="outline" size="icon" disabled={loading}>
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="plan" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="productionPlan">خطة الإنتاج</Label>
                  <Select
                    value={selectedPlanId}
                    onValueChange={setSelectedPlanId}
                    disabled={fetchingData || loading}
                  >
                    <SelectTrigger id="productionPlan">
                      <SelectValue placeholder="اختر خطة الإنتاج" />
                    </SelectTrigger>
                    <SelectContent>
                      {fetchingData ? (
                        <div className="flex justify-center p-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        </div>
                      ) : productionPlans.length === 0 ? (
                        <div className="p-2 text-center text-gray-500">لا توجد خطط إنتاج</div>
                      ) : (
                        productionPlans.map((plan) => (
                          <SelectItem key={plan.id} value={plan.id.toString()}>
                            {plan.product.name} - {plan.batchNumber}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full"
              onClick={generateQRCode}
              disabled={loading || (activeTab === 'product' && !selectedProductId) || (activeTab === 'batch' && !batchNumber) || (activeTab === 'plan' && !selectedPlanId)}
            >
              {loading ? 'جاري الإنشاء...' : 'إنشاء رمز QR'}
            </Button>
          </CardFooter>
        </Card>

        <Card className="print:shadow-none">
          <CardHeader className="flex flex-row items-center justify-between print:pb-0">
            <CardTitle>رمز QR للتتبع</CardTitle>
            {qrCode && (
              <div className="flex space-x-2 rtl:space-x-reverse print:hidden">
                <Button variant="outline" size="sm" onClick={handlePrint}>
                  <Printer className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  طباعة
                </Button>
                <Button variant="outline" size="sm" onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  تنزيل
                </Button>
              </div>
            )}
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center">
            {loading ? (
              <div className="flex justify-center p-12">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            ) : qrCode ? (
              <div className="space-y-4 text-center">
                <div className="border p-4 inline-block">
                  <img src={qrCode} alt="QR Code" className="w-64 h-64" />
                </div>
                {trackingData && (
                  <div className="space-y-2 text-right rtl:text-right">
                    {trackingData.type === 'product' && (
                      <>
                        <p><strong>المنتج:</strong> {trackingData.name}</p>
                        <p><strong>السعر:</strong> {trackingData.price} درهم</p>
                        <p><strong>نوع المنتج:</strong> {trackingData.isComposite ? 'مركب' : 'بسيط'}</p>
                        {trackingData.isComposite && trackingData.ingredients && (
                          <div>
                            <p><strong>المكونات:</strong></p>
                            <ul className="list-disc list-inside">
                              {trackingData.ingredients.map((ing: any, index: number) => (
                                <li key={index}>{ing.name} - {ing.quantity} {ing.unit || ''}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </>
                    )}
                    {trackingData.type === 'batch' && (
                      <>
                        <p><strong>رقم الدفعة:</strong> {trackingData.batchNumber}</p>
                        <p><strong>المنتج:</strong> {trackingData.productName}</p>
                        <p><strong>الكمية:</strong> {trackingData.quantity}</p>
                        <p><strong>تاريخ الإنتاج:</strong> {formatDate(trackingData.productionDate)}</p>
                        <p><strong>تاريخ انتهاء الصلاحية:</strong> {formatDate(trackingData.expiryDate)}</p>
                      </>
                    )}
                    {trackingData.type === 'production_plan' && (
                      <>
                        <p><strong>خطة الإنتاج:</strong> #{trackingData.id}</p>
                        <p><strong>المنتج:</strong> {trackingData.productName}</p>
                        <p><strong>الكمية:</strong> {trackingData.quantity}</p>
                        <p><strong>رقم الدفعة:</strong> {trackingData.batchNumber || '-'}</p>
                        <p><strong>تاريخ الإنتاج:</strong> {formatDate(trackingData.productionDate)}</p>
                        <p><strong>تاريخ انتهاء الصلاحية:</strong> {formatDate(trackingData.expiryDate)}</p>
                      </>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <QrCode className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>قم باختيار منتج أو دفعة أو خطة إنتاج لإنشاء رمز QR للتتبع</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
