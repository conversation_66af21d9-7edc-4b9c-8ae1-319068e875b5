'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { CalendarIcon, Plus, Trash2, CheckCircle2, XCircle } from 'lucide-react';

interface ProductionPlan {
  id: number;
  productId: number;
  quantity: number;
  plannedDate: string;
  status: string;
  product: {
    name: string;
  };
}

interface QualityParameter {
  name: string;
  expectedValue: string;
  actualValue: string;
  isPassed: boolean;
  notes?: string;
}

export default function QualityCheckPage() {
  const [productionPlans, setProductionPlans] = useState<ProductionPlan[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');
  const [checkDate, setCheckDate] = useState<Date>(new Date());
  const [checkedBy, setCheckedBy] = useState<string>('');
  const [status, setStatus] = useState<string>('PENDING');
  const [notes, setNotes] = useState<string>('');
  const [parameters, setParameters] = useState<QualityParameter[]>([
    { name: '', expectedValue: '', actualValue: '', isPassed: false }
  ]);
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchingPlans, setFetchingPlans] = useState<boolean>(true);

  useEffect(() => {
    fetchProductionPlans();
  }, []);

  const fetchProductionPlans = async () => {
    try {
      setFetchingPlans(true);
      // هنا يجب إنشاء API لجلب خطط الإنتاج
      // لأغراض العرض، سنستخدم بيانات وهمية
      const mockPlans = [
        {
          id: 1,
          productId: 1,
          quantity: 10,
          plannedDate: new Date().toISOString(),
          status: 'SCHEDULED',
          product: { name: 'منتج تجريبي 1' }
        },
        {
          id: 2,
          productId: 2,
          quantity: 20,
          plannedDate: new Date().toISOString(),
          status: 'IN_PROGRESS',
          product: { name: 'منتج تجريبي 2' }
        }
      ];
      setProductionPlans(mockPlans);
      setFetchingPlans(false);
    } catch (error) {
      console.error('خطأ في جلب خطط الإنتاج:', error);
      toast.error('حدث خطأ أثناء جلب خطط الإنتاج');
      setFetchingPlans(false);
    }
  };

  const handleAddParameter = () => {
    setParameters([...parameters, { name: '', expectedValue: '', actualValue: '', isPassed: false }]);
  };

  const handleRemoveParameter = (index: number) => {
    const newParameters = [...parameters];
    newParameters.splice(index, 1);
    setParameters(newParameters);
  };

  const handleParameterChange = (index: number, field: keyof QualityParameter, value: any) => {
    const newParameters = [...parameters];
    newParameters[index] = { ...newParameters[index], [field]: value };
    setParameters(newParameters);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPlanId) {
      toast.error('الرجاء اختيار خطة إنتاج');
      return;
    }

    if (parameters.some(param => !param.name)) {
      toast.error('الرجاء إدخال اسم لكل معيار');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/recipes/quality-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productionPlanId: selectedPlanId,
          checkDate: checkDate.toISOString(),
          checkedBy,
          status,
          notes,
          parameters,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إنشاء فحص الجودة');
      }

      const data = await response.json();
      toast.success('تم إنشاء فحص الجودة بنجاح');
      
      // إعادة تعيين النموذج
      setSelectedPlanId('');
      setCheckDate(new Date());
      setCheckedBy('');
      setStatus('PENDING');
      setNotes('');
      setParameters([{ name: '', expectedValue: '', actualValue: '', isPassed: false }]);
      
      setLoading(false);
    } catch (error) {
      console.error('خطأ في إنشاء فحص الجودة:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء فحص الجودة');
      setLoading(false);
    }
  };

  const handleReset = () => {
    setSelectedPlanId('');
    setCheckDate(new Date());
    setCheckedBy('');
    setStatus('PENDING');
    setNotes('');
    setParameters([{ name: '', expectedValue: '', actualValue: '', isPassed: false }]);
  };

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: 'فحص الجودة', href: '/dashboard/recipes/quality-check' },
        ]}
      />

      <Card>
        <CardHeader>
          <CardTitle>إنشاء فحص جودة جديد</CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="productionPlan">خطة الإنتاج</Label>
                <Select
                  value={selectedPlanId}
                  onValueChange={setSelectedPlanId}
                  disabled={fetchingPlans || loading}
                >
                  <SelectTrigger id="productionPlan">
                    <SelectValue placeholder="اختر خطة الإنتاج" />
                  </SelectTrigger>
                  <SelectContent>
                    {fetchingPlans ? (
                      <div className="flex justify-center p-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                      </div>
                    ) : productionPlans.length === 0 ? (
                      <div className="p-2 text-center text-gray-500">لا توجد خطط إنتاج</div>
                    ) : (
                      productionPlans.map((plan) => (
                        <SelectItem key={plan.id} value={plan.id.toString()}>
                          {plan.product.name} - الكمية: {plan.quantity}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="checkDate">تاريخ الفحص</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-right"
                      disabled={loading}
                    >
                      <CalendarIcon className="ml-2 h-4 w-4" />
                      {checkDate ? format(checkDate, 'PPP', { locale: ar }) : 'اختر تاريخ'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={checkDate}
                      onSelect={(date) => date && setCheckDate(date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="checkedBy">تم الفحص بواسطة</Label>
                <Input
                  id="checkedBy"
                  value={checkedBy}
                  onChange={(e) => setCheckedBy(e.target.value)}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">حالة الفحص</Label>
                <Select
                  value={status}
                  onValueChange={setStatus}
                  disabled={loading}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="اختر حالة الفحص" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">قيد الانتظار</SelectItem>
                    <SelectItem value="PASSED">ناجح</SelectItem>
                    <SelectItem value="FAILED">فاشل</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  disabled={loading}
                  rows={3}
                />
              </div>
            </div>

            <div className="space-y-4 border-t pt-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">معايير الجودة</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddParameter}
                  disabled={loading}
                >
                  <Plus className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  إضافة معيار
                </Button>
              </div>

              {parameters.map((parameter, index) => (
                <div key={`parameter-${index}`} className="grid grid-cols-12 gap-4 items-end border p-4 rounded-md">
                  <div className="col-span-3">
                    <Label htmlFor={`name-${index}`}>اسم المعيار</Label>
                    <Input
                      id={`name-${index}`}
                      value={parameter.name}
                      onChange={(e) => handleParameterChange(index, 'name', e.target.value)}
                      disabled={loading}
                    />
                  </div>
                  <div className="col-span-3">
                    <Label htmlFor={`expectedValue-${index}`}>القيمة المتوقعة</Label>
                    <Input
                      id={`expectedValue-${index}`}
                      value={parameter.expectedValue}
                      onChange={(e) => handleParameterChange(index, 'expectedValue', e.target.value)}
                      disabled={loading}
                    />
                  </div>
                  <div className="col-span-3">
                    <Label htmlFor={`actualValue-${index}`}>القيمة الفعلية</Label>
                    <Input
                      id={`actualValue-${index}`}
                      value={parameter.actualValue}
                      onChange={(e) => handleParameterChange(index, 'actualValue', e.target.value)}
                      disabled={loading}
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor={`isPassed-${index}`} className="mb-2 block">اجتاز الفحص</Label>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Switch
                        id={`isPassed-${index}`}
                        checked={parameter.isPassed}
                        onCheckedChange={(checked) => handleParameterChange(index, 'isPassed', checked)}
                        disabled={loading}
                      />
                      {parameter.isPassed ? (
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  </div>
                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveParameter(index)}
                      disabled={parameters.length === 1 || loading}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              disabled={loading}
            >
              إعادة تعيين
            </Button>
            <Button
              type="submit"
              disabled={loading || !selectedPlanId || parameters.some(param => !param.name)}
            >
              {loading ? 'جاري الحفظ...' : 'حفظ فحص الجودة'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
