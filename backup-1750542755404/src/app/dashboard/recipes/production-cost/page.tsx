'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { Calculator, Printer, Download } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  price: number;
  isComposite: boolean;
}

interface IngredientDetail {
  id: number;
  name: string;
  quantity: number;
  unit?: string;
  unitCost: number;
  totalCost: number;
}

interface ProductionCost {
  product: {
    id: number;
    name: string;
    sellingPrice: number;
    totalSellingPrice: number;
  };
  quantity: number;
  ingredientsCost: number;
  ingredientsDetails: IngredientDetail[];
  laborCost: number;
  overheadCost: number;
  totalProductionCost: number;
  unitCost: number;
  profitMargin: number;
  profitPercentage: number;
}

export default function ProductionCostPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [includeLabor, setIncludeLabor] = useState<boolean>(false);
  const [laborCost, setLaborCost] = useState<number>(0);
  const [includeOverhead, setIncludeOverhead] = useState<boolean>(false);
  const [overheadCost, setOverheadCost] = useState<number>(0);
  const [productionCost, setProductionCost] = useState<ProductionCost | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchingProducts, setFetchingProducts] = useState<boolean>(true);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setFetchingProducts(true);
      const response = await fetch('/api/recipes');
      if (!response.ok) {
        throw new Error('فشل في جلب المنتجات المركبة');
      }
      const data = await response.json();
      setProducts(data);
      setFetchingProducts(false);
    } catch (error) {
      console.error('خطأ في جلب المنتجات المركبة:', error);
      toast.error('حدث خطأ أثناء جلب المنتجات المركبة');
      setFetchingProducts(false);
    }
  };

  const handleCalculate = async () => {
    if (!selectedProductId) {
      toast.error('الرجاء اختيار منتج');
      return;
    }

    if (quantity <= 0) {
      toast.error('الرجاء إدخال كمية صالحة');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/recipes/production-cost', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: selectedProductId,
          quantity,
          includeLabor,
          laborCost,
          includeOverhead,
          overheadCost,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حساب تكلفة الإنتاج');
      }

      const data = await response.json();
      setProductionCost(data);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في حساب تكلفة الإنتاج:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حساب تكلفة الإنتاج');
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    toast.info('جاري تصدير التقرير إلى PDF...');
    // هنا يمكن إضافة كود لتصدير التقرير إلى PDF
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(price);
  };

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: 'تقدير تكلفة الإنتاج', href: '/dashboard/recipes/production-cost' },
        ]}
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
                حساب تكلفة الإنتاج
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="product">المنتج المركب</Label>
                <Select
                  value={selectedProductId}
                  onValueChange={setSelectedProductId}
                  disabled={fetchingProducts}
                >
                  <SelectTrigger id="product">
                    <SelectValue placeholder="اختر المنتج" />
                  </SelectTrigger>
                  <SelectContent>
                    {fetchingProducts ? (
                      <div className="flex justify-center p-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                      </div>
                    ) : products.length === 0 ? (
                      <div className="p-2 text-center text-gray-500">لا توجد منتجات مركبة</div>
                    ) : (
                      products.map((product) => (
                        <SelectItem key={product.id} value={product.id.toString()}>
                          {product.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="quantity">الكمية</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                />
              </div>

              <div className="space-y-2 border-t pt-4">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Checkbox
                    id="includeLabor"
                    checked={includeLabor}
                    onCheckedChange={(checked) => setIncludeLabor(checked as boolean)}
                  />
                  <Label htmlFor="includeLabor">تضمين تكلفة العمالة</Label>
                </div>
                {includeLabor && (
                  <div className="pl-6 rtl:pr-6 rtl:pl-0">
                    <Label htmlFor="laborCost">تكلفة العمالة (للوحدة)</Label>
                    <Input
                      id="laborCost"
                      type="number"
                      min="0"
                      step="0.01"
                      value={laborCost}
                      onChange={(e) => setLaborCost(parseFloat(e.target.value) || 0)}
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2 border-t pt-4">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Checkbox
                    id="includeOverhead"
                    checked={includeOverhead}
                    onCheckedChange={(checked) => setIncludeOverhead(checked as boolean)}
                  />
                  <Label htmlFor="includeOverhead">تضمين التكاليف العامة</Label>
                </div>
                {includeOverhead && (
                  <div className="pl-6 rtl:pr-6 rtl:pl-0">
                    <Label htmlFor="overheadCost">التكاليف العامة (للوحدة)</Label>
                    <Input
                      id="overheadCost"
                      type="number"
                      min="0"
                      step="0.01"
                      value={overheadCost}
                      onChange={(e) => setOverheadCost(parseFloat(e.target.value) || 0)}
                    />
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={handleCalculate}
                disabled={loading || !selectedProductId || quantity <= 0}
              >
                {loading ? 'جاري الحساب...' : 'حساب تكلفة الإنتاج'}
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div className="md:col-span-2 print:w-full">
          {productionCost ? (
            <Card className="print:shadow-none">
              <CardHeader className="flex flex-row items-center justify-between print:pb-0">
                <div>
                  <CardTitle>تقرير تكلفة الإنتاج</CardTitle>
                  <p className="text-sm text-gray-500 mt-1">
                    {new Date().toLocaleDateString('ar-AE', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </p>
                </div>
                <div className="flex space-x-2 rtl:space-x-reverse print:hidden">
                  <Button variant="outline" size="sm" onClick={handlePrint}>
                    <Printer className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                    طباعة
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleExportPDF}>
                    <Download className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                    PDF
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4 border-b pb-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">المنتج</h3>
                    <p className="font-medium">{productionCost.product.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">الكمية</h3>
                    <p className="font-medium">{productionCost.quantity}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">سعر البيع (للوحدة)</h3>
                    <p className="font-medium">{formatPrice(productionCost.product.sellingPrice)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">إجمالي سعر البيع</h3>
                    <p className="font-medium">{formatPrice(productionCost.product.totalSellingPrice)}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">تكاليف المكونات</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="border p-2 text-right">المكون</th>
                          <th className="border p-2 text-center">الكمية</th>
                          <th className="border p-2 text-center">الوحدة</th>
                          <th className="border p-2 text-center">تكلفة الوحدة</th>
                          <th className="border p-2 text-center">التكلفة الإجمالية</th>
                        </tr>
                      </thead>
                      <tbody>
                        {productionCost.ingredientsDetails.map((ingredient) => (
                          <tr key={ingredient.id} className="hover:bg-gray-50">
                            <td className="border p-2">{ingredient.name}</td>
                            <td className="border p-2 text-center">{ingredient.quantity}</td>
                            <td className="border p-2 text-center">{ingredient.unit || '-'}</td>
                            <td className="border p-2 text-center">{formatPrice(ingredient.unitCost)}</td>
                            <td className="border p-2 text-center">{formatPrice(ingredient.totalCost)}</td>
                          </tr>
                        ))}
                        <tr className="bg-gray-100 font-medium">
                          <td colSpan={4} className="border p-2 text-left">إجمالي تكلفة المكونات</td>
                          <td className="border p-2 text-center">{formatPrice(productionCost.ingredientsCost)}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 border-t border-b py-4">
                  {includeLabor && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">تكلفة العمالة</h3>
                      <p className="font-medium">{formatPrice(productionCost.laborCost)}</p>
                    </div>
                  )}
                  {includeOverhead && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">التكاليف العامة</h3>
                      <p className="font-medium">{formatPrice(productionCost.overheadCost)}</p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">إجمالي تكلفة الإنتاج</h3>
                    <p className="font-medium text-lg">{formatPrice(productionCost.totalProductionCost)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">تكلفة الوحدة</h3>
                    <p className="font-medium text-lg">{formatPrice(productionCost.unitCost)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">هامش الربح</h3>
                    <p className="font-medium text-lg text-primary">{formatPrice(productionCost.profitMargin)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">نسبة الربح</h3>
                    <p className="font-medium text-lg text-primary">{productionCost.profitPercentage.toFixed(2)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Calculator className="h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500 text-center">
                  قم باختيار منتج وإدخال الكمية لحساب تكلفة الإنتاج
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
