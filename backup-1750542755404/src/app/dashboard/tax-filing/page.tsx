'use client';

import { useState, useEffect } from 'react';
import { formatCurrency } from '@/lib/utils';

interface TaxFiling {
  id: string;
  period: string;
  startDate: string;
  endDate: string;
  dueDate: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  totalSales: number;
  totalPurchases: number;
  totalTaxCollected: number;
  totalTaxPaid: number;
  netTaxDue: number;
  submissionDate?: string;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

export default function TaxFilingPage() {
  const [taxFilings, setTaxFilings] = useState<TaxFiling[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [currentFiling, setCurrentFiling] = useState<TaxFiling | null>(null);
  const [filter, setFilter] = useState({
    status: '',
    year: new Date().getFullYear()
  });
  
  useEffect(() => {
    // استدعاء API للحصول على الإقرارات الضريبية
    const fetchTaxFilings = async () => {
      setLoading(true);
      try {
        // في التطبيق الحقيقي، هذا سيكون استدعاء API
        const data = localStorage.getItem('taxFilings');
        if (data) {
          setTaxFilings(JSON.parse(data));
        } else {
          setTaxFilings([]);
        }
      } catch (error) {
        console.error('خطأ في جلب الإقرارات الضريبية:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchTaxFilings();
  }, []);
  
  // تصفية الإقرارات الضريبية
  const filteredFilings = taxFilings.filter(filing => {
    let match = true;
    
    if (filter.status && filing.status !== filter.status) {
      match = false;
    }
    
    if (filter.year && new Date(filing.startDate).getFullYear() !== filter.year) {
      match = false;
    }
    
    return match;
  });
  
  // إضافة إقرار ضريبي جديد
  const handleAddFiling = () => {
    // إنشاء فترة ضريبية جديدة (ربع سنوية)
    const now = new Date();
    const currentQuarter = Math.floor(now.getMonth() / 3) + 1;
    const startDate = new Date(now.getFullYear(), (currentQuarter - 1) * 3, 1);
    const endDate = new Date(now.getFullYear(), currentQuarter * 3, 0);
    const dueDate = new Date(now.getFullYear(), currentQuarter * 3, 15);
    
    const newFiling: TaxFiling = {
      id: Math.random().toString(36).substring(2, 9),
      period: `Q${currentQuarter} ${now.getFullYear()}`,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      dueDate: dueDate.toISOString().split('T')[0],
      status: 'draft',
      totalSales: 0,
      totalPurchases: 0,
      totalTaxCollected: 0,
      totalTaxPaid: 0,
      netTaxDue: 0,
      notes: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    setCurrentFiling(newFiling);
    setShowModal(true);
  };
  
  // تعديل إقرار ضريبي
  const handleEditFiling = (filing: TaxFiling) => {
    setCurrentFiling(filing);
    setShowModal(true);
  };
  
  // حذف إقرار ضريبي
  const handleDeleteFiling = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الإقرار الضريبي؟')) {
      const updatedFilings = taxFilings.filter(filing => filing.id !== id);
      setTaxFilings(updatedFilings);
      localStorage.setItem('taxFilings', JSON.stringify(updatedFilings));
    }
  };
  
  // تقديم إقرار ضريبي
  const handleSubmitFiling = (id: string) => {
    if (confirm('هل أنت متأكد من تقديم هذا الإقرار الضريبي؟')) {
      const updatedFilings = taxFilings.map(filing => 
        filing.id === id 
          ? { 
              ...filing, 
              status: 'submitted', 
              submissionDate: new Date().toISOString(),
              updatedAt: new Date().toISOString() 
            }
          : filing
      );
      setTaxFilings(updatedFilings);
      localStorage.setItem('taxFilings', JSON.stringify(updatedFilings));
    }
  };
  
  // حفظ إقرار ضريبي (إضافة أو تعديل)
  const handleSaveFiling = (formData: any) => {
    // حساب صافي الضريبة المستحقة
    const netTaxDue = formData.totalTaxCollected - formData.totalTaxPaid;
    
    if (currentFiling) {
      // تعديل إقرار موجود
      const updatedFilings = taxFilings.map(filing => 
        filing.id === currentFiling.id 
          ? { 
              ...filing, 
              ...formData, 
              netTaxDue,
              updatedAt: new Date().toISOString() 
            }
          : filing
      );
      setTaxFilings(updatedFilings);
      localStorage.setItem('taxFilings', JSON.stringify(updatedFilings));
    } else {
      // إضافة إقرار جديد
      const newFiling: TaxFiling = {
        id: Math.random().toString(36).substring(2, 9),
        ...formData,
        netTaxDue,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const updatedFilings = [...taxFilings, newFiling];
      setTaxFilings(updatedFilings);
      localStorage.setItem('taxFilings', JSON.stringify(updatedFilings));
    }
    
    setShowModal(false);
  };
  
  // الحصول على نص حالة الإقرار
  const getStatusText = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'مسودة | Draft';
      case 'submitted':
        return 'مقدم | Submitted';
      case 'approved':
        return 'موافق عليه | Approved';
      case 'rejected':
        return 'مرفوض | Rejected';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة الإقرار
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'submitted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };
  
  // التحقق مما إذا كان الإقرار متأخرًا
  const isFilingOverdue = (filing: TaxFiling): boolean => {
    if (filing.status !== 'draft') return false;
    
    const dueDate = new Date(filing.dueDate);
    const today = new Date();
    
    return today > dueDate;
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1 dark:text-white">الإقرارات الضريبية</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">Tax Filing</p>
        </div>
        <button
          onClick={handleAddFiling}
          className="px-4 py-2 bg-primary text-white rounded-md text-sm"
        >
          إقرار جديد | New Filing
        </button>
      </div>
      
      {/* فلاتر البحث */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold mb-3 dark:text-white">تصفية الإقرارات</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">الحالة</label>
            <select
              value={filter.status}
              onChange={(e) => setFilter({...filter, status: e.target.value})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="">الكل</option>
              <option value="draft">مسودة | Draft</option>
              <option value="submitted">مقدم | Submitted</option>
              <option value="approved">موافق عليه | Approved</option>
              <option value="rejected">مرفوض | Rejected</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">السنة</label>
            <select
              value={filter.year}
              onChange={(e) => setFilter({...filter, year: parseInt(e.target.value)})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
      
      {/* جدول الإقرارات الضريبية */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="p-2 text-right border dark:border-gray-600">الفترة</th>
              <th className="p-2 text-right border dark:border-gray-600">تاريخ البداية</th>
              <th className="p-2 text-right border dark:border-gray-600">تاريخ النهاية</th>
              <th className="p-2 text-right border dark:border-gray-600">تاريخ الاستحقاق</th>
              <th className="p-2 text-right border dark:border-gray-600">الحالة</th>
              <th className="p-2 text-right border dark:border-gray-600">صافي الضريبة</th>
              <th className="p-2 text-center border dark:border-gray-600">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={7} className="p-4 text-center">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </td>
              </tr>
            ) : filteredFilings.length === 0 ? (
              <tr>
                <td colSpan={7} className="p-4 text-center text-gray-500 dark:text-gray-400">
                  لا توجد إقرارات ضريبية مطابقة للفلاتر المحددة
                </td>
              </tr>
            ) : (
              filteredFilings.map(filing => (
                <tr key={filing.id} className={`border-b dark:border-gray-700 ${
                  isFilingOverdue(filing) ? 'bg-red-50 dark:bg-red-900/20' : ''
                }`}>
                  <td className="p-2 border dark:border-gray-600">{filing.period}</td>
                  <td className="p-2 border dark:border-gray-600">
                    {new Date(filing.startDate).toLocaleDateString('ar-AE')}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {new Date(filing.endDate).toLocaleDateString('ar-AE')}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    <span className={isFilingOverdue(filing) ? 'text-red-600 dark:text-red-400 font-semibold' : ''}>
                      {new Date(filing.dueDate).toLocaleDateString('ar-AE')}
                    </span>
                    {isFilingOverdue(filing) && (
                      <span className="mr-2 text-xs text-red-600 dark:text-red-400">
                        (متأخر | Overdue)
                      </span>
                    )}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(filing.status)}`}>
                      {getStatusText(filing.status)}
                    </span>
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    <span className={filing.netTaxDue > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}>
                      {formatCurrency(filing.netTaxDue)}
                    </span>
                  </td>
                  <td className="p-2 border dark:border-gray-600 text-center">
                    <div className="flex justify-center space-x-1 space-x-reverse">
                      <button
                        onClick={() => handleEditFiling(filing)}
                        className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-md text-xs"
                      >
                        تعديل
                      </button>
                      {filing.status === 'draft' && (
                        <>
                          <button
                            onClick={() => handleSubmitFiling(filing.id)}
                            className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-md text-xs"
                          >
                            تقديم
                          </button>
                          <button
                            onClick={() => handleDeleteFiling(filing.id)}
                            className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 rounded-md text-xs"
                          >
                            حذف
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* نموذج إضافة/تعديل إقرار ضريبي */}
      {showModal && (
        <TaxFilingForm
          filing={currentFiling}
          onSave={handleSaveFiling}
          onCancel={() => setShowModal(false)}
        />
      )}
    </div>
  );
}

// مكون نموذج الإقرار الضريبي
function TaxFilingForm({ filing, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    period: filing?.period || '',
    startDate: filing?.startDate || '',
    endDate: filing?.endDate || '',
    dueDate: filing?.dueDate || '',
    status: filing?.status || 'draft',
    totalSales: filing?.totalSales || 0,
    totalPurchases: filing?.totalPurchases || 0,
    totalTaxCollected: filing?.totalTaxCollected || 0,
    totalTaxPaid: filing?.totalTaxPaid || 0,
    notes: filing?.notes || ''
  });
  
  // حساب صافي الضريبة المستحقة
  const netTaxDue = formData.totalTaxCollected - formData.totalTaxPaid;
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: ['totalSales', 'totalPurchases', 'totalTaxCollected', 'totalTaxPaid'].includes(name)
        ? parseFloat(value) || 0
        : value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-screen overflow-y-auto">
        <h2 className="text-xl font-bold mb-4 dark:text-white">
          {filing ? 'تعديل إقرار ضريبي' : 'إقرار ضريبي جديد'}
        </h2>
        
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                الفترة <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="period"
                value={formData.period}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                الحالة
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                disabled={filing && filing.status !== 'draft'}
              >
                <option value="draft">مسودة | Draft</option>
                <option value="submitted">مقدم | Submitted</option>
                <option value="approved">موافق عليه | Approved</option>
                <option value="rejected">مرفوض | Rejected</option>
              </select>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                تاريخ البداية <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                تاريخ النهاية <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                تاريخ الاستحقاق <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              />
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
            <h3 className="text-lg font-semibold mb-3 dark:text-white">بيانات الضريبة</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block mb-2 text-sm font-medium dark:text-white">
                  إجمالي المبيعات <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="totalSales"
                  value={formData.totalSales}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                />
              </div>
              
              <div>
                <label className="block mb-2 text-sm font-medium dark:text-white">
                  إجمالي المشتريات <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="totalPurchases"
                  value={formData.totalPurchases}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block mb-2 text-sm font-medium dark:text-white">
                  الضريبة المحصلة <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="totalTaxCollected"
                  value={formData.totalTaxCollected}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                />
              </div>
              
              <div>
                <label className="block mb-2 text-sm font-medium dark:text-white">
                  الضريبة المدفوعة <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="totalTaxPaid"
                  value={formData.totalTaxPaid}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                />
              </div>
            </div>
            
            <div className="p-3 rounded-md bg-blue-50 dark:bg-blue-900/30 mb-4">
              <div className="flex justify-between items-center">
                <span className="font-semibold dark:text-white">صافي الضريبة المستحقة:</span>
                <span className={`font-bold text-lg ${
                  netTaxDue > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'
                }`}>
                  {formatCurrency(netTaxDue)}
                </span>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {netTaxDue > 0 
                  ? 'ضريبة مستحقة الدفع للهيئة | Tax payable to authority'
                  : 'ضريبة مستحقة الاسترداد من الهيئة | Tax refundable from authority'}
              </p>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              ملاحظات
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              rows={3}
            ></textarea>
          </div>
          
          <div className="flex justify-end space-x-2 space-x-reverse">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:text-white rounded-md"
            >
              إلغاء | Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md"
              disabled={filing && filing.status !== 'draft'}
            >
              {filing ? 'تحديث | Update' : 'إضافة | Add'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
