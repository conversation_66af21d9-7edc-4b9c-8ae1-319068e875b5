'use client';

/**
 * صفحة إعدادات الضريبة - تتيح للمستخدم تعديل إعدادات الضريبة
 * Tax Settings Page - Allows user to modify tax settings
 */

import { useState, useEffect } from 'react';
import { useI18n } from '@/lib/i18n';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { RotateCw, Save, FileText, Calculator, Check } from 'lucide-react';
import EnhancedTaxSettings, { EnhancedTaxSettings as EnhancedTaxSettingsType, DEFAULT_ENHANCED_TAX_SETTINGS } from '@/components/ui/enhanced-tax-settings';
import { getSystemSettings, saveSystemSettings } from '@/lib/settings';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { TaxType, TAXES, getTaxInfo } from '@/lib/utils';

/**
 * صفحة إعدادات الضريبة
 * Tax Settings Page
 */
export default function TaxSettingsPage() {
  const { t, language } = useI18n();
  const [activeTab, setActiveTab] = useState('settings');
  const [taxSettings, setTaxSettings] = useState<EnhancedTaxSettingsType>(DEFAULT_ENHANCED_TAX_SETTINGS);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // تحميل الإعدادات عند تحميل الصفحة
  // Load settings when page loads
  useEffect(() => {
    const settings = getSystemSettings();
    
    // إذا كانت إعدادات الضريبة المحسنة موجودة، استخدمها
    // If enhanced tax settings exist, use them
    if (settings.enhancedTaxSettings) {
      setTaxSettings(settings.enhancedTaxSettings);
    } else {
      // تحويل إعدادات الضريبة القديمة إلى الشكل الجديد إذا كانت موجودة
      // Convert old tax settings to new format if they exist
      if (settings.defaultTaxSettings) {
        const oldSettings = settings.defaultTaxSettings;
        setTaxSettings({
          ...DEFAULT_ENHANCED_TAX_SETTINGS,
          enabled: oldSettings.enabled,
          rate: oldSettings.rate,
          inclusive: oldSettings.inclusive,
          type: 'VAT' as TaxType
        });
      }
    }
    
    setIsLoading(false);
  }, []);

  // حفظ الإعدادات
  // Save settings
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      
      // حفظ إعدادات الضريبة
      // Save tax settings
      const settings = getSystemSettings();
      saveSystemSettings({
        ...settings,
        enhancedTaxSettings: taxSettings
      });
      
      toast.success(t('settings.saved') || 'Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error(t('settings.saveError') || 'Error saving settings');
    } finally {
      setIsSaving(false);
    }
  };

  // تحديث إعدادات الضريبة
  // Update tax settings
  const handleTaxSettingsChange = (newSettings: EnhancedTaxSettingsType) => {
    setTaxSettings(newSettings);
  };

  // الحصول على معلومات الضريبة الحالية
  // Get current tax information
  const currentTaxInfo = getTaxInfo(taxSettings.type);

  return (
    <div className="container py-6">
      <h1 className="text-2xl font-bold mb-6">
        {t('tax.settings') || 'إعدادات الضريبة'}
        <span className="text-lg font-normal text-muted-foreground mr-2">Tax Settings</span>
      </h1>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="settings">
            <Calculator className="h-4 w-4 ml-2" />
            {t('tax.settings') || 'الإعدادات'}
          </TabsTrigger>
          <TabsTrigger value="filing">
            <FileText className="h-4 w-4 ml-2" />
            {t('tax.filing') || 'الإقرارات الضريبية'}
          </TabsTrigger>
        </TabsList>
        
        {/* إعدادات الضريبة */}
        {/* Tax Settings */}
        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>{t('tax.settings') || 'إعدادات الضريبة'}</CardTitle>
              <CardDescription>
                {t('tax.settingsDescription') || 'إدارة إعدادات الضريبة وفقًا لمتطلبات الهيئة الاتحادية للضرائب'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isLoading ? (
                <div className="flex justify-center items-center h-40">
                  <RotateCw className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <EnhancedTaxSettings
                  initialSettings={taxSettings}
                  onChange={handleTaxSettingsChange}
                />
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                {taxSettings.enabled && (
                  <div className="text-sm">
                    <span className="font-medium">{t('tax.currentTax') || 'الضريبة الحالية'}:</span>{' '}
                    <span className="text-primary">
                      {language === 'ar' ? currentTaxInfo.nameAr : currentTaxInfo.nameEn} ({taxSettings.rate}%)
                    </span>
                  </div>
                )}
              </div>
              <Button onClick={handleSaveSettings} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                    {t('common.saving') || 'Saving...'}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t('common.save') || 'Save'}
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* الإقرارات الضريبية */}
        {/* Tax Filing */}
        <TabsContent value="filing">
          <Card>
            <CardHeader>
              <CardTitle>{t('tax.filing') || 'الإقرارات الضريبية'}</CardTitle>
              <CardDescription>
                {t('tax.filingDescription') || 'إدارة الإقرارات الضريبية وتقديمها إلى الهيئة الاتحادية للضرائب'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-amber-50 border border-amber-100 rounded-md p-4 text-amber-800">
                <h3 className="font-medium mb-2">
                  {t('tax.filingInstructions') || 'تعليمات تقديم الإقرارات الضريبية'}
                </h3>
                <p className="text-sm mb-2">
                  {t('tax.filingInstructionsDescription') || 'يجب تقديم الإقرارات الضريبية إلى الهيئة الاتحادية للضرائب وفقًا للجدول الزمني التالي:'}
                </p>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>
                    {t('tax.filingPeriod1') || 'الربع الأول (يناير - مارس): يجب التقديم بحلول 28 أبريل'}
                  </li>
                  <li>
                    {t('tax.filingPeriod2') || 'الربع الثاني (أبريل - يونيو): يجب التقديم بحلول 28 يوليو'}
                  </li>
                  <li>
                    {t('tax.filingPeriod3') || 'الربع الثالث (يوليو - سبتمبر): يجب التقديم بحلول 28 أكتوبر'}
                  </li>
                  <li>
                    {t('tax.filingPeriod4') || 'الربع الرابع (أكتوبر - ديسمبر): يجب التقديم بحلول 28 يناير'}
                  </li>
                </ul>
              </div>
              
              <Separator className="my-4" />
              
              <div className="text-center">
                <Button
                  variant="outline"
                  size="lg"
                  className="mx-auto"
                  onClick={() => window.location.href = '/dashboard/tax-filing'}
                >
                  <FileText className="mr-2 h-5 w-5" />
                  {t('tax.goToFilingPage') || 'الانتقال إلى صفحة الإقرارات الضريبية'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
