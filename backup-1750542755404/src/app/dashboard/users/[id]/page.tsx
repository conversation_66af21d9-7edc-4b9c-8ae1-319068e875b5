'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { PermissionGuard } from '@/components/auth/permission-guard'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'

export default function EditUserPage() {
    return (
        <PermissionGuard permission="MANAGE_USERS">
            <EditUserForm />
        </PermissionGuard>
    )
}

function EditUserForm() {
    const [user, setUser] = useState<any>(null)
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
        roleId: '',
        isActive: true
    })
    const [roles, setRoles] = useState([])
    const [loading, setLoading] = useState(true)
    const [submitting, setSubmitting] = useState(false)
    const router = useRouter()
    const { id } = useParams()

    useEffect(() => {
        async function fetchData() {
            try {
                setLoading(true)

                // جلب بيانات المستخدم
                const userResponse = await fetch(`/api/users/${id}`)
                if (!userResponse.ok) throw new Error('فشل في جلب بيانات المستخدم')
                const userData = await userResponse.json()

                // جلب الأدوار
                const rolesResponse = await fetch('/api/roles')
                if (!rolesResponse.ok) throw new Error('فشل في جلب الأدوار')
                const rolesData = await rolesResponse.json()

                setUser(userData)
                setRoles(rolesData)
                setFormData({
                    name: userData.name || '',
                    email: userData.email || '',
                    password: '',
                    confirmPassword: '',
                    roleId: userData.role?.id.toString() || '',
                    isActive: userData.isActive
                })
            } catch (error) {
                toast.error('فشل في جلب بيانات المستخدم')
                router.push('/dashboard/users')
            } finally {
                setLoading(false)
            }
        }

        if (id) {
            fetchData()
        }
    }, [id, router])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
    }

    const handleCheckboxChange = (checked: boolean) => {
        setFormData(prev => ({ ...prev, isActive: checked }))
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()

        // التحقق من البيانات
        if (!formData.email || !formData.roleId) {
            toast.error('يرجى ملء جميع الحقول المطلوبة')
            return
        }

        if (formData.password && formData.password !== formData.confirmPassword) {
            toast.error('كلمات المرور غير متطابقة')
            return
        }

        setSubmitting(true)

        try {
            const updateData = {
                name: formData.name,
                email: formData.email,
                roleId: formData.roleId,
                isActive: formData.isActive,
                ...(formData.password ? { password: formData.password } : {})
            }

            const response = await fetch(`/api/users/${id}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updateData)
            })

            if (!response.ok) {
                const error = await response.json()
                throw new Error(error.error || 'فشل في تحديث بيانات المستخدم')
            }

            toast.success('تم تحديث بيانات المستخدم بنجاح')
            router.push('/dashboard/users')
        } catch (error: any) {
            toast.error(error.message || 'فشل في تحديث بيانات المستخدم')
        } finally {
            setSubmitting(false)
        }
    }

    if (loading) {
        return <div className="container py-6 text-center">جاري التحميل...</div>
    }

    return (
        <div className="container py-6">
            <h1 className="mb-6 text-2xl font-bold">تعديل بيانات المستخدم</h1>

            <Card className="max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle>معلومات المستخدم</CardTitle>
                </CardHeader>
                <form onSubmit={handleSubmit}>
                    <CardContent className="space-y-4">
                        <div className="space-y-1">
                            <Label htmlFor="name">الاسم</Label>
                            <Input
                                id="name"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                placeholder="أدخل اسم المستخدم"
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="email">البريد الإلكتروني</Label>
                            <Input
                                id="email"
                                name="email"
                                type="email"
                                value={formData.email}
                                onChange={handleChange}
                                placeholder="<EMAIL>"
                                required
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="password">كلمة المرور (اتركها فارغة إذا لم ترغب في تغييرها)</Label>
                            <Input
                                id="password"
                                name="password"
                                type="password"
                                value={formData.password}
                                onChange={handleChange}
                                placeholder="********"
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                            <Input
                                id="confirmPassword"
                                name="confirmPassword"
                                type="password"
                                value={formData.confirmPassword}
                                onChange={handleChange}
                                placeholder="********"
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="roleId">الدور</Label>
                            <select
                                id="roleId"
                                name="roleId"
                                value={formData.roleId}
                                onChange={handleChange}
                                className="flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm"
                                required
                                disabled={id === '1'} // لا يمكن تغيير دور المسؤول الأول
                            >
                                <option value="">اختر الدور</option>
                                {roles.map((role: any) => (
                                    <option key={role.id} value={role.id}>
                                        {role.name} - {role.description}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div className="flex items-center space-x-2 space-x-reverse">
                            <Checkbox
                                id="isActive"
                                checked={formData.isActive}
                                onCheckedChange={handleCheckboxChange}
                                disabled={id === '1'} // لا يمكن تعطيل المسؤول الأول
                            />
                            <Label htmlFor="isActive">حساب نشط</Label>
                        </div>
                    </CardContent>
                    <CardFooter className="justify-between">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.push('/dashboard/users')}
                        >
                            إلغاء
                        </Button>
                        <Button type="submit" disabled={submitting}>
                            {submitting ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                        </Button>
                    </CardFooter>
                </form>
            </Card>
        </div>
    )
}
