'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeft, Edit, Trash2, Phone, Mail, MapPin, Building, 
  CreditCard, Star, Calendar, TrendingUp, Package, Receipt,
  User, Globe, Hash, DollarSign
} from 'lucide-react';
import { Button, Card, CardContent, CardHeader, CardTitle, Loading, StatCard } from '@/components/ui';
import { useI18n } from '@/lib/i18n';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface Supplier {
  id: number;
  name: string;
  nameEn?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  address?: string;
  city?: string;
  country?: string;
  postalCode?: string;
  taxNumber?: string;
  website?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  bankDetails?: string;
  paymentTerms?: string;
  creditLimit?: number;
  supplierType?: string;
  category?: string;
  rating?: number;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    purchases: number;
    expenses: number;
  };
  _sum?: {
    purchaseTotal: number;
    expenseTotal: number;
  };
}

export default function SupplierDetailsPage() {
  const { language } = useI18n();
  const router = useRouter();
  const params = useParams();
  const supplierId = params.id as string;
  
  const [loading, setLoading] = useState(true);
  const [supplier, setSupplier] = useState<Supplier | null>(null);

  useEffect(() => {
    const loadSupplier = async () => {
      setLoading(true);
      try {
        // محاكاة API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // بيانات وهمية للمورد
        const mockSupplier: Supplier = {
          id: parseInt(supplierId),
          name: 'شركة الإمارات للتوريدات',
          nameEn: 'Emirates Supply Company',
          email: '<EMAIL>',
          phone: '+971-4-1234567',
          mobile: '+971-50-1234567',
          address: 'شارع الشيخ زايد، مجمع دبي التجاري',
          city: 'دبي',
          country: 'الإمارات العربية المتحدة',
          postalCode: '12345',
          taxNumber: 'TRN123456789',
          website: 'www.emiratessupply.ae',
          contactPerson: 'أحمد محمد علي',
          contactPhone: '+971-50-1234567',
          contactEmail: '<EMAIL>',
          bankDetails: 'بنك الإمارات دبي الوطني\nرقم الحساب: **********\nIBAN: ********************',
          paymentTerms: '30',
          creditLimit: 50000,
          supplierType: 'vendor',
          category: 'مواد خام',
          rating: 4.5,
          notes: 'مورد موثوق مع تاريخ طويل من التعامل الجيد. يقدم خدمة عملاء ممتازة وأسعار تنافسية.',
          isActive: true,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-06-20T14:45:00Z',
          _count: {
            purchases: 25,
            expenses: 12
          },
          _sum: {
            purchaseTotal: 125000,
            expenseTotal: 15000
          }
        };

        setSupplier(mockSupplier);
      } catch (error) {
        console.error('Error loading supplier:', error);
        toast.error('حدث خطأ أثناء تحميل بيانات المورد');
      } finally {
        setLoading(false);
      }
    };

    if (supplierId) {
      loadSupplier();
    }
  }, [supplierId]);

  const handleDelete = async () => {
    if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا المورد؟' : 'Are you sure you want to delete this supplier?')) {
      return;
    }

    try {
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(language === 'ar' ? 'تم حذف المورد بنجاح' : 'Supplier deleted successfully');
      router.push('/dashboard/suppliers');
    } catch (error) {
      console.error('Error deleting supplier:', error);
      toast.error('حدث خطأ أثناء حذف المورد');
    }
  };

  if (loading) {
    return <Loading />;
  }

  if (!supplier) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-foreground mb-2">
          {language === 'ar' ? 'المورد غير موجود' : 'Supplier not found'}
        </h3>
        <p className="text-muted-foreground mb-4">
          {language === 'ar' ? 'لم يتم العثور على المورد المطلوب' : 'The requested supplier was not found'}
        </p>
        <Button onClick={() => router.push('/dashboard/suppliers')}>
          {language === 'ar' ? 'العودة للقائمة' : 'Back to List'}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {language === 'ar' ? 'رجوع' : 'Back'}
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground heading">
              {supplier.name}
            </h1>
            {supplier.nameEn && (
              <p className="text-lg text-muted-foreground mt-1">
                {supplier.nameEn}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex gap-2">
          <Link href={`/dashboard/suppliers/${supplier.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'تعديل' : 'Edit'}
            </Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'حذف' : 'Delete'}
          </Button>
        </div>
      </div>

      {/* Status Badge */}
      <div className="flex items-center gap-4">
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          supplier.isActive 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        }`}>
          {supplier.isActive 
            ? (language === 'ar' ? 'نشط' : 'Active')
            : (language === 'ar' ? 'غير نشط' : 'Inactive')
          }
        </span>
        
        {supplier.rating && (
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500 fill-current" />
            <span className="font-medium">{supplier.rating}/5</span>
          </div>
        )}
        
        <span className="text-sm text-muted-foreground">
          {language === 'ar' ? 'تم الإنشاء:' : 'Created:'} {new Date(supplier.createdAt).toLocaleDateString()}
        </span>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title={language === 'ar' ? 'إجمالي المشتريات' : 'Total Purchases'}
          value={supplier._count?.purchases?.toLocaleString() || '0'}
          icon={<Package className="h-5 w-5" />}
          color="blue"
        />
        <StatCard
          title={language === 'ar' ? 'قيمة المشتريات' : 'Purchase Value'}
          value={formatCurrency(supplier._sum?.purchaseTotal || 0, language)}
          icon={<DollarSign className="h-5 w-5" />}
          color="green"
        />
        <StatCard
          title={language === 'ar' ? 'إجمالي المصروفات' : 'Total Expenses'}
          value={supplier._count?.expenses?.toLocaleString() || '0'}
          icon={<Receipt className="h-5 w-5" />}
          color="purple"
        />
        <StatCard
          title={language === 'ar' ? 'قيمة المصروفات' : 'Expense Value'}
          value={formatCurrency(supplier._sum?.expenseTotal || 0, language)}
          icon={<TrendingUp className="h-5 w-5" />}
          color="orange"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'نوع المورد' : 'Supplier Type'}
                </label>
                <p className="text-sm mt-1 capitalize">{supplier.supplierType}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'الفئة' : 'Category'}
                </label>
                <p className="text-sm mt-1">{supplier.category || '-'}</p>
              </div>
            </div>
            
            {supplier.taxNumber && (
              <div>
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  {language === 'ar' ? 'الرقم الضريبي' : 'Tax Number'}
                </label>
                <p className="text-sm mt-1">{supplier.taxNumber}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {supplier.contactPerson && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'الشخص المسؤول' : 'Contact Person'}
                </label>
                <p className="text-sm mt-1">{supplier.contactPerson}</p>
              </div>
            )}
            
            {supplier.email && (
              <div>
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                </label>
                <p className="text-sm mt-1">
                  <a href={`mailto:${supplier.email}`} className="text-blue-600 hover:underline">
                    {supplier.email}
                  </a>
                </p>
              </div>
            )}
            
            {supplier.phone && (
              <div>
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  {language === 'ar' ? 'رقم الهاتف' : 'Phone'}
                </label>
                <p className="text-sm mt-1">
                  <a href={`tel:${supplier.phone}`} className="text-blue-600 hover:underline">
                    {supplier.phone}
                  </a>
                </p>
              </div>
            )}
            
            {supplier.mobile && (
              <div>
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  {language === 'ar' ? 'رقم الجوال' : 'Mobile'}
                </label>
                <p className="text-sm mt-1">
                  <a href={`tel:${supplier.mobile}`} className="text-blue-600 hover:underline">
                    {supplier.mobile}
                  </a>
                </p>
              </div>
            )}
            
            {supplier.website && (
              <div>
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  {language === 'ar' ? 'الموقع الإلكتروني' : 'Website'}
                </label>
                <p className="text-sm mt-1">
                  <a 
                    href={supplier.website.startsWith('http') ? supplier.website : `https://${supplier.website}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {supplier.website}
                  </a>
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              {language === 'ar' ? 'معلومات العنوان' : 'Address Information'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {supplier.address && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'العنوان' : 'Address'}
                </label>
                <p className="text-sm mt-1">{supplier.address}</p>
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-4">
              {supplier.city && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {language === 'ar' ? 'المدينة' : 'City'}
                  </label>
                  <p className="text-sm mt-1">{supplier.city}</p>
                </div>
              )}
              
              {supplier.country && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {language === 'ar' ? 'الدولة' : 'Country'}
                  </label>
                  <p className="text-sm mt-1">{supplier.country}</p>
                </div>
              )}
            </div>
            
            {supplier.postalCode && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'الرمز البريدي' : 'Postal Code'}
                </label>
                <p className="text-sm mt-1">{supplier.postalCode}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {language === 'ar' ? 'المعلومات المالية' : 'Financial Information'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'شروط الدفع' : 'Payment Terms'}
                </label>
                <p className="text-sm mt-1">
                  {supplier.paymentTerms} {language === 'ar' ? 'يوم' : 'days'}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'الحد الائتماني' : 'Credit Limit'}
                </label>
                <p className="text-sm mt-1">
                  {formatCurrency(supplier.creditLimit || 0, language)}
                </p>
              </div>
            </div>
            
            {supplier.bankDetails && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {language === 'ar' ? 'تفاصيل البنك' : 'Bank Details'}
                </label>
                <pre className="text-sm mt-1 whitespace-pre-wrap bg-muted p-3 rounded-md">
                  {supplier.bankDetails}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Notes */}
      {supplier.notes && (
        <Card>
          <CardHeader>
            <CardTitle>
              {language === 'ar' ? 'ملاحظات' : 'Notes'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">{supplier.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Link href={`/dashboard/purchases/new?supplier=${supplier.id}`}>
              <Button variant="outline" size="sm">
                <Package className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إنشاء مشتريات' : 'Create Purchase'}
              </Button>
            </Link>
            <Link href={`/dashboard/expenses/new?supplier=${supplier.id}`}>
              <Button variant="outline" size="sm">
                <Receipt className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إضافة مصروف' : 'Add Expense'}
              </Button>
            </Link>
            <Link href={`/dashboard/suppliers/${supplier.id}/reports`}>
              <Button variant="outline" size="sm">
                <TrendingUp className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'التقارير' : 'Reports'}
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
