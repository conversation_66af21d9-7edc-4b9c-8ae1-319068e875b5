'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  InventoryMovement,
  InventoryLocation,
  Product
} from '@/types/inventory';
import { 
  getInventoryMovements,
  getInventoryLocations,
  getProducts
} from '@/lib/inventory-service';

export default function InventoryMovementsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [movements, setMovements] = useState<InventoryMovement[]>([]);
  const [locations, setLocations] = useState<InventoryLocation[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');

  // تحميل البيانات
  useEffect(() => {
    const loadData = () => {
      setLoading(true);
      try {
        const movementsData = getInventoryMovements();
        const locationsData = getInventoryLocations();
        const productsData = getProducts();
        
        setMovements(movementsData);
        setLocations(locationsData);
        setProducts(productsData);
        
        // تعيين التاريخ الافتراضي (آخر 30 يوم)
        if (!dateFrom) {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          setDateFrom(thirtyDaysAgo.toISOString().split('T')[0]);
        }
        
        if (!dateTo) {
          const today = new Date();
          setDateTo(today.toISOString().split('T')[0]);
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات حركات المخزون:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [dateFrom, dateTo]);

  // الحصول على الحركات المصفاة
  const filteredMovements = movements
    .filter(movement => {
      // تصفية حسب الموقع
      if (selectedLocation && movement.locationId !== selectedLocation) return false;
      
      // تصفية حسب المنتج
      if (selectedProduct && movement.productId !== selectedProduct) return false;
      
      // تصفية حسب النوع
      if (selectedType && movement.type !== selectedType) return false;
      
      // تصفية حسب التاريخ
      if (dateFrom) {
        const movementDate = new Date(movement.createdAt);
        const fromDate = new Date(dateFrom);
        fromDate.setHours(0, 0, 0, 0);
        if (movementDate < fromDate) return false;
      }
      
      if (dateTo) {
        const movementDate = new Date(movement.createdAt);
        const toDate = new Date(dateTo);
        toDate.setHours(23, 59, 59, 999);
        if (movementDate > toDate) return false;
      }
      
      return true;
    })
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  // الحصول على اسم المنتج
  const getProductName = (productId: string): string => {
    const product = products.find(p => p.id === productId);
    return product ? product.name : 'غير معروف';
  };

  // الحصول على اسم الموقع
  const getLocationName = (locationId: string): string => {
    const location = locations.find(l => l.id === locationId);
    return location ? location.name : 'غير معروف';
  };

  // الحصول على نص نوع الحركة
  const getMovementTypeText = (type: string): string => {
    switch (type) {
      case 'in':
        return 'وارد | In';
      case 'out':
        return 'صادر | Out';
      case 'transfer':
        return 'نقل | Transfer';
      case 'adjustment':
        return 'تعديل | Adjustment';
      default:
        return type;
    }
  };

  // الحصول على نص نوع المرجع
  const getReferenceTypeText = (type: string): string => {
    switch (type) {
      case 'purchase':
        return 'شراء | Purchase';
      case 'sale':
        return 'بيع | Sale';
      case 'return':
        return 'مرتجع | Return';
      case 'transfer':
        return 'نقل | Transfer';
      case 'adjustment':
        return 'تعديل | Adjustment';
      case 'stocktake':
        return 'جرد | Stock Take';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="mr-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">حركات المخزون</h1>
          <p className="text-sm text-gray-500">Inventory Movements</p>
        </div>
        <button
          onClick={() => router.push('/dashboard/inventory')}
          className="px-4 py-2 bg-gray-600 text-white rounded-md text-sm"
        >
          العودة للمخزون | Back to Inventory
        </button>
      </div>

      {/* أدوات التصفية */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label htmlFor="location" className="block mb-2 text-sm font-medium">
            <span>الموقع</span>
            <span className="text-xs text-gray-500 mr-1">Location</span>
          </label>
          <select
            id="location"
            value={selectedLocation}
            onChange={(e) => setSelectedLocation(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">الكل | All</option>
            {locations.map(location => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="product" className="block mb-2 text-sm font-medium">
            <span>المنتج</span>
            <span className="text-xs text-gray-500 mr-1">Product</span>
          </label>
          <select
            id="product"
            value={selectedProduct}
            onChange={(e) => setSelectedProduct(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">الكل | All</option>
            {products
              .filter(product => product.type === 'physical')
              .map(product => (
                <option key={product.id} value={product.id}>
                  {product.name} ({product.sku})
                </option>
              ))}
          </select>
        </div>
        <div>
          <label htmlFor="type" className="block mb-2 text-sm font-medium">
            <span>نوع الحركة</span>
            <span className="text-xs text-gray-500 mr-1">Movement Type</span>
          </label>
          <select
            id="type"
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">الكل | All</option>
            <option value="in">وارد | In</option>
            <option value="out">صادر | Out</option>
            <option value="transfer">نقل | Transfer</option>
            <option value="adjustment">تعديل | Adjustment</option>
          </select>
        </div>
        <div>
          <label htmlFor="dateFrom" className="block mb-2 text-sm font-medium">
            <span>من تاريخ</span>
            <span className="text-xs text-gray-500 mr-1">From Date</span>
          </label>
          <input
            id="dateFrom"
            type="date"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
        <div>
          <label htmlFor="dateTo" className="block mb-2 text-sm font-medium">
            <span>إلى تاريخ</span>
            <span className="text-xs text-gray-500 mr-1">To Date</span>
          </label>
          <input
            id="dateTo"
            type="date"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
      </div>

      {/* جدول حركات المخزون */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-2 text-right border">
                <span>التاريخ</span>
                <span className="text-xs text-gray-500 mr-1">Date</span>
              </th>
              <th className="p-2 text-right border">
                <span>المنتج</span>
                <span className="text-xs text-gray-500 mr-1">Product</span>
              </th>
              <th className="p-2 text-right border">
                <span>الموقع</span>
                <span className="text-xs text-gray-500 mr-1">Location</span>
              </th>
              <th className="p-2 text-right border">
                <span>النوع</span>
                <span className="text-xs text-gray-500 mr-1">Type</span>
              </th>
              <th className="p-2 text-right border">
                <span>الكمية</span>
                <span className="text-xs text-gray-500 mr-1">Quantity</span>
              </th>
              <th className="p-2 text-right border">
                <span>الكمية السابقة</span>
                <span className="text-xs text-gray-500 mr-1">Previous</span>
              </th>
              <th className="p-2 text-right border">
                <span>الكمية الجديدة</span>
                <span className="text-xs text-gray-500 mr-1">New</span>
              </th>
              <th className="p-2 text-right border">
                <span>المرجع</span>
                <span className="text-xs text-gray-500 mr-1">Reference</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredMovements.length === 0 ? (
              <tr>
                <td colSpan={8} className="p-4 text-center text-gray-500">
                  <span>لا توجد حركات مخزون</span>
                  <br />
                  <span className="text-sm">No inventory movements</span>
                </td>
              </tr>
            ) : (
              filteredMovements.map(movement => (
                <tr key={movement.id} className="border-b">
                  <td className="p-2 border">
                    {new Date(movement.createdAt).toLocaleDateString('ar-AE')}
                    <div className="text-xs text-gray-500">
                      {new Date(movement.createdAt).toLocaleTimeString('ar-AE')}
                    </div>
                  </td>
                  <td className="p-2 border">
                    {getProductName(movement.productId)}
                  </td>
                  <td className="p-2 border">
                    {getLocationName(movement.locationId)}
                  </td>
                  <td className="p-2 border">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      movement.type === 'in' ? 'bg-green-100 text-green-800' :
                      movement.type === 'out' ? 'bg-red-100 text-red-800' :
                      movement.type === 'transfer' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {getMovementTypeText(movement.type)}
                    </span>
                  </td>
                  <td className="p-2 border">
                    {movement.quantity}
                  </td>
                  <td className="p-2 border">
                    {movement.previousQuantity}
                  </td>
                  <td className="p-2 border">
                    {movement.newQuantity}
                  </td>
                  <td className="p-2 border">
                    <div>{getReferenceTypeText(movement.referenceType)}</div>
                    <div className="text-xs text-gray-500">{movement.reference}</div>
                    {movement.notes && (
                      <div className="text-xs text-gray-500 mt-1">{movement.notes}</div>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
