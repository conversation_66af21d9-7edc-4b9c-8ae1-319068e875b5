'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Button, StatCard, Loading, Input } from '@/components/ui'
import { useRouter } from 'next/navigation'
import { Edit, Trash2, Eye, PlusCircle, Package, AlertTriangle, TrendingUp, Search, Filter, XCircle, DollarSign } from 'lucide-react'
import { Breadcrumb } from '@/components/ui/breadcrumb'
import { DataTable } from '@/components/ui/data-table'
import { toast } from 'sonner'
import { useI18n } from '@/lib/i18n'
import { formatCurrency } from '@/lib/utils'

interface Product {
    id: string
    name: string
    price: number
    sku: string
    unit: string
    quantity?: number
    category?: string
    cost?: number
    createdAt?: Date
}

export default function ProductsPage() {
    const [products, setProducts] = useState<Product[]>([])
    const [loading, setLoading] = useState(true)
    const [searchTerm, setSearchTerm] = useState('')
    const [stats, setStats] = useState({
        total: 0,
        lowStock: 0,
        outOfStock: 0,
        totalValue: 0
    })
    const router = useRouter()
    const { t, language } = useI18n()
    const canManageProducts = true // تم تعطيل التحقق من الصلاحيات مؤقتًا

    useEffect(() => {
        async function fetchProducts() {
            try {
                const response = await fetch('/api/products');
                if (!response.ok) {
                    throw new Error('Failed to fetch products');
                }
                const data = await response.json();
                const productsList = data.products || [];
                setProducts(productsList);

                // حساب الإحصائيات
                const lowStock = productsList.filter((p: Product) => p.quantity && p.quantity <= 10).length;
                const outOfStock = productsList.filter((p: Product) => !p.quantity || p.quantity === 0).length;
                const totalValue = productsList.reduce((sum: number, p: Product) => sum + (p.price * (p.quantity || 0)), 0);

                setStats({
                    total: productsList.length,
                    lowStock,
                    outOfStock,
                    totalValue
                });

                setLoading(false);
            } catch (error) {
                console.error('خطأ في جلب بيانات المنتجات:', error);
                setLoading(false);
                toast.error('حدث خطأ أثناء تحميل المنتجات');
            }
        }

        fetchProducts()
    }, [])

    const handleAddProduct = () => {
        router.push('/dashboard/products/new')
    }

    const handleEditProduct = (id: string) => {
        router.push(`/dashboard/products/${id}/edit`)
    }

    const handleDeleteProduct = async (id: string) => {
        if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            try {
                const response = await fetch(`/api/products/${id}`, {
                    method: 'DELETE',
                });

                if (!response.ok) {
                    throw new Error('Failed to delete product');
                }

                setProducts(products.filter(product => product.id !== id));
                toast.success('تم حذف المنتج بنجاح');
            } catch (error) {
                console.error('Error deleting product:', error);
                toast.error('حدث خطأ أثناء حذف المنتج');
            }
        }
    }

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(price)
    }

    // تعريف أعمدة جدول المنتجات
    const columns = [
        {
            key: 'name',
            header: 'اسم المنتج | Product Name',
            cell: (product: Product) => <span className="font-medium">{product.name}</span>,
            sortable: true,
        },
        {
            key: 'sku',
            header: 'رمز المنتج | SKU',
            cell: (product: Product) => <span>{product.sku}</span>,
            sortable: true,
        },
        {
            key: 'unit',
            header: 'الوحدة | Unit',
            cell: (product: Product) => <span>{product.unit}</span>,
            sortable: true,
        },
        {
            key: 'price',
            header: 'سعر البيع | Price',
            cell: (product: Product) => <span>{formatPrice(product.price)}</span>,
            sortable: true,
        },
        {
            key: 'quantity',
            header: 'الكمية | Quantity',
            cell: (product: Product) => (
                <span
                    className={
                        product.quantity && product.quantity <= 5
                            ? 'text-red-500 font-medium'
                            : product.quantity && product.quantity <= 10
                                ? 'text-amber-500 font-medium'
                                : ''
                    }
                >
                    {product.quantity || '-'}
                </span>
            ),
            sortable: true,
        },
        {
            key: 'category',
            header: 'التصنيف | Category',
            cell: (product: Product) => <span>{product.category || '-'}</span>,
            sortable: true,
        },
        {
            key: 'actions',
            header: 'الإجراءات | Actions',
            cell: (product: Product) => (
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="icon"
                        title="عرض | View"
                        onClick={() => router.push(`/dashboard/products/${product.id}`)}
                    >
                        <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        title="تعديل | Edit"
                        onClick={() => handleEditProduct(product.id)}
                    >
                        <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        title="حذف | Delete"
                        onClick={() => handleDeleteProduct(product.id)}
                    >
                        <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                </div>
            ),
        },
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-purple-50/30 via-white to-pink-50/30 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
            <div className="space-y-8 p-6 animate-fade-in">
                <Breadcrumb
                    segments={[
                        { title: 'الرئيسية', href: '/dashboard' },
                        { title: 'المنتجات', href: '/dashboard/products' },
                    ]}
                    className="mb-4"
                />

                {/* Enhanced Header */}
                <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 p-8 text-white shadow-strong">
                    <div className="absolute inset-0 bg-floating-shapes opacity-20" />
                    <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent" />

                    <div className="relative z-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-6">
                        <div className="space-y-3">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                                    <Package className="h-6 w-6 text-white" />
                                </div>
                                <h1 className="text-4xl font-bold text-white animate-fade-in-up">
                                    إدارة المنتجات
                                </h1>
                            </div>
                            <p className="text-purple-100 text-lg animate-fade-in-up animate-delay-200">
                                Products Management - إدارة شاملة للمخزون والمنتجات
                            </p>
                        </div>

                        {canManageProducts && (
                            <div className="animate-fade-in-up animate-delay-400">
                                <Button
                                    onClick={handleAddProduct}
                                    variant="outline"
                                    size="lg"
                                    className="bg-white text-purple-600 border-white hover:bg-purple-50"
                                >
                                    <PlusCircle className="ml-2 h-5 w-5" />
                                    إضافة منتج جديد | New Product
                                </Button>
                            </div>
                        )}
                    </div>
                </div>

                {/* Enhanced Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard
                        title="إجمالي المنتجات | Total Products"
                        value={stats.total.toLocaleString()}
                        icon={<Package className="h-6 w-6" />}
                        color="purple"
                        trend={{
                            value: 5,
                            label: 'مقارنة بالشهر الماضي | vs last month',
                            isPositive: true
                        }}
                        className="animate-fade-in-up animate-delay-500 hover:scale-105 transition-transform duration-300"
                    />
                    <StatCard
                        title="مخزون منخفض | Low Stock"
                        value={stats.lowStock.toLocaleString()}
                        icon={<AlertTriangle className="h-6 w-6" />}
                        color="yellow"
                        trend={{
                            value: 2,
                            label: 'مقارنة بالشهر الماضي | vs last month',
                            isPositive: false
                        }}
                        className="animate-fade-in-up animate-delay-600 hover:scale-105 transition-transform duration-300"
                    />
                    <StatCard
                        title="نفد المخزون | Out of Stock"
                        value={stats.outOfStock.toLocaleString()}
                        icon={<XCircle className="h-6 w-6" />}
                        color="red"
                        trend={{
                            value: 1,
                            label: 'مقارنة بالشهر الماضي | vs last month',
                            isPositive: false
                        }}
                        className="animate-fade-in-up animate-delay-700 hover:scale-105 transition-transform duration-300"
                    />
                    <StatCard
                        title="قيمة المخزون | Inventory Value"
                        value={formatPrice(stats.totalValue)}
                        icon={<DollarSign className="h-6 w-6" />}
                        color="green"
                        trend={{
                            value: 8,
                            label: 'مقارنة بالشهر الماضي | vs last month',
                            isPositive: true
                        }}
                        className="animate-fade-in-up animate-delay-800 hover:scale-105 transition-transform duration-300"
                    />
                </div>

                <Card>
                    <CardHeader>
                        <div>
                            <CardTitle>قائمة المنتجات</CardTitle>
                            <p className="text-sm text-gray-500 mt-1">Products List</p>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <div className="flex justify-center p-4">
                                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                            </div>
                        ) : products.length === 0 ? (
                            <div className="text-center py-8 text-gray-500">
                                لا توجد منتجات حتى الآن
                                <br />
                                <span className="text-sm">No products found</span>
                            </div>
                        ) : (
                            <DataTable
                                data={products}
                                columns={columns}
                                searchable
                                searchKeys={['name', 'sku', 'category']}
                                pagination
                                pageSize={10}
                            />
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
