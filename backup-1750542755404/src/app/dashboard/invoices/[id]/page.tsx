'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { formatCurrency } from "@/lib/utils";
import { Printer, CreditCard, Bell } from 'lucide-react';
import { ExportPDFButton } from '@/components/invoices/export-pdf-button';
import { EmailInvoiceDialog } from '@/components/invoices/email-invoice-dialog';
import { Button } from '@/components/ui/button';

export default function InvoiceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [invoice, setInvoice] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    async function fetchInvoice() {
      try {
        const res = await fetch(`/api/invoices/${params.id}`)
        if (res.ok) {
          const data = await res.json()
          setInvoice(data)
        } else {
          console.error('فشل جلب الفاتورة')
        }
      } catch (error) {
        console.error('خطأ:', error)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchInvoice()
    }
  }, [params.id])

  const handleStatusChange = async (newStatus: string) => {
    if (updating || !invoice) return

    setUpdating(true)
    try {
      const res = await fetch(`/api/invoices/${params.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      })

      if (res.ok) {
        const updatedInvoice = await res.json()
        setInvoice(updatedInvoice)
      } else {
        console.error('فشل تحديث حالة الفاتورة')
      }
    } catch (error) {
      console.error('خطأ:', error)
    } finally {
      setUpdating(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="ml-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="text-center py-10">
        <p className="text-lg text-gray-600">لم يتم العثور على الفاتورة | Invoice not found</p>
        <button
          onClick={() => router.push('/dashboard/invoices')}
          className="mt-4 px-4 py-2 bg-primary text-white rounded-md"
        >
          العودة للفواتير | Back to Invoices
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">تفاصيل الفاتورة</h1>
          <p className="text-sm text-gray-500">Invoice Details</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(`/dashboard/invoices/${params.id}/print`, '_blank')}
            className="flex items-center"
          >
            <Printer className="h-4 w-4 ml-1" />
            طباعة | Print
          </Button>

          <ExportPDFButton
            invoiceId={params.id as string}
            invoiceNumber={invoice.invoiceNumber || ''}
          />

          <EmailInvoiceDialog
            invoiceId={params.id as string}
            invoiceNumber={invoice.invoiceNumber || ''}
            customerEmail={invoice.customer?.email}
            customerName={invoice.customer?.name}
          />

          {invoice.status !== 'PAID' && (
            <Button
              variant="default"
              size="sm"
              className="flex items-center"
              onClick={() => handleStatusChange('PAID')}
              disabled={updating}
            >
              <CreditCard className="h-4 w-4 ml-1" />
              تسجيل كمدفوعة
            </Button>
          )}

          {invoice.status !== 'PAID' && invoice.status !== 'CANCELLED' && (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={() => handleStatusChange('OVERDUE')}
              disabled={updating}
            >
              <Bell className="h-4 w-4 ml-1" />
              تسجيل كمتأخرة
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/dashboard/invoices')}
          >
            العودة | Back
          </Button>
        </div>
      </div>

      <div className="border-t border-b py-4 my-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500 mb-1">رقم الفاتورة | Invoice No.</p>
            <p className="font-medium">{invoice.invoiceNumber || 'غير متوفر | N/A'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">التاريخ | Date</p>
            <p className="font-medium">{invoice.issueDate ? new Date(invoice.issueDate).toLocaleDateString('ar-AE') : 'غير متوفر | N/A'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">العميل | Customer</p>
            <p className="font-medium">{invoice.customer?.name || 'غير متوفر | N/A'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">الحالة | Status</p>
            <p className="font-medium">{invoice.status || 'غير متوفر | N/A'}</p>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <h2 className="text-lg font-semibold mb-3">ملخص الفاتورة | Invoice Summary</h2>
        <div className="flex justify-between py-2 border-b">
          <span className="font-medium">المجموع | Total:</span>
          <span>{invoice.total ? formatCurrency(invoice.total) : 'غير متوفر | N/A'}</span>
        </div>
      </div>
    </div>
  )
}
