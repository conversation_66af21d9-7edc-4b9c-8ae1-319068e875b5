import { CurrencyCode, DEFAULT_CURRENCY } from './utils';
import { TaxSettings, DEFAULT_TAX_SETTINGS } from '@/components/ui/tax-settings';
import { EnhancedTaxSettings, DEFAULT_ENHANCED_TAX_SETTINGS } from '@/components/ui/enhanced-tax-settings';

// نوع إعدادات العملة
// Currency settings type
export interface CurrencySettings {
  readonly defaultCurrency: CurrencyCode;
  readonly showCurrencySymbol: boolean;
  readonly showCurrencyCode: boolean;
  readonly showCurrencyName: boolean;
  readonly currencyPosition: 'before' | 'after';
  readonly decimalSeparator: '.' | ',';
  readonly thousandsSeparator: ',' | '.' | ' ' | '';
  readonly decimalPlaces: number;
}

// نوع إعدادات النظام
export interface SystemSettings {
  readonly defaultCurrency: CurrencyCode;
  readonly defaultTaxSettings: TaxSettings;
  readonly companyInfo: CompanyInfo;
  readonly invoiceSettings: InvoiceSettings;
  readonly currencySettings?: CurrencySettings;
  readonly enhancedTaxSettings?: EnhancedTaxSettings;
}

// معلومات الشركة
export interface CompanyInfo {
  readonly name: string;
  readonly nameEn: string;
  readonly address: string;
  readonly phone: string;
  readonly email: string;
  readonly website: string;
  readonly taxNumber: string;
  readonly logo?: string;
}

// إعدادات الفاتورة
export interface InvoiceSettings {
  readonly prefix: string;
  readonly nextNumber: number;
  readonly termsAndConditions: string;
  readonly footer: string;
}

// الإعدادات الافتراضية للعملة
// Default currency settings
export const DEFAULT_CURRENCY_SETTINGS: CurrencySettings = {
  defaultCurrency: DEFAULT_CURRENCY,
  showCurrencySymbol: true,
  showCurrencyCode: false,
  showCurrencyName: false,
  currencyPosition: 'after',
  decimalSeparator: '.',
  thousandsSeparator: ',',
  decimalPlaces: 2
};

// الإعدادات الافتراضية للنظام
export const DEFAULT_SYSTEM_SETTINGS: SystemSettings = {
  defaultCurrency: DEFAULT_CURRENCY,
  defaultTaxSettings: DEFAULT_TAX_SETTINGS,
  companyInfo: {
    name: 'شركة أمين بلس',
    nameEn: 'Amin Plus Company',
    address: 'دبي، الإمارات العربية المتحدة',
    phone: '+971 4 123 4567',
    email: '<EMAIL>',
    website: 'www.aminplus.com',
    taxNumber: '*********',
    logo: '/logo.png'
  },
  invoiceSettings: {
    prefix: 'INV',
    nextNumber: 1001,
    termsAndConditions: 'يرجى دفع المبلغ خلال 30 يوم من تاريخ إصدار الفاتورة.',
    footer: 'شكراً لتعاملكم معنا'
  },
  currencySettings: DEFAULT_CURRENCY_SETTINGS,
  enhancedTaxSettings: DEFAULT_ENHANCED_TAX_SETTINGS
};

// الحصول على إعدادات النظام من التخزين المحلي
export function getSystemSettings(): SystemSettings {
  if (typeof window === 'undefined') {
    return DEFAULT_SYSTEM_SETTINGS;
  }

  const savedSettings = localStorage.getItem('systemSettings');
  if (!savedSettings) {
    return DEFAULT_SYSTEM_SETTINGS;
  }

  try {
    return JSON.parse(savedSettings);
  } catch (error) {
    console.error('خطأ في قراءة إعدادات النظام:', error);
    return DEFAULT_SYSTEM_SETTINGS;
  }
}

// حفظ إعدادات النظام في التخزين المحلي
export function saveSystemSettings(settings: SystemSettings): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('systemSettings', JSON.stringify(settings));
}

// الحصول على العملة الافتراضية
export function getDefaultCurrency(): CurrencyCode {
  return getSystemSettings().defaultCurrency;
}

// الحصول على إعدادات العملة
export function getCurrencySettings(): CurrencySettings {
  const settings = getSystemSettings();
  return settings.currencySettings || DEFAULT_CURRENCY_SETTINGS;
}

// الحصول على إعدادات الضريبة الافتراضية
export function getDefaultTaxSettings(): TaxSettings {
  return getSystemSettings().defaultTaxSettings;
}

// الحصول على معلومات الشركة
export function getCompanyInfo(): CompanyInfo {
  return getSystemSettings().companyInfo;
}

// الحصول على إعدادات الفاتورة
export function getInvoiceSettings(): InvoiceSettings {
  return getSystemSettings().invoiceSettings;
}

// إنشاء رقم فاتورة جديد
export function generateInvoiceNumber(): string {
  const settings = getSystemSettings();
  const { prefix, nextNumber } = settings.invoiceSettings;

  // تحديث رقم الفاتورة التالي
  const newSettings = {
    ...settings,
    invoiceSettings: {
      ...settings.invoiceSettings,
      nextNumber: nextNumber + 1
    }
  };

  saveSystemSettings(newSettings);

  return `${prefix}-${nextNumber.toString().padStart(4, '0')}`;
}
