import { prisma } from './prisma';
import { logger } from './logger';

export type ExportFormat = 'CSV' | 'EXCEL' | 'PDF' | 'JSON';
export type ExportType = 'CUSTOMERS' | 'PRODUCTS' | 'INVOICES' | 'SALES_REPORT' | 'INVENTORY_REPORT';

interface ExportOptions {
  format: ExportFormat;
  type: ExportType;
  filters?: Record<string, any>;
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  columns?: string[];
  includeHeaders?: boolean;
  fileName?: string;
}

interface ExportResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  size?: number;
  recordCount?: number;
  error?: string;
}

/**
 * نظام تصدير البيانات المتقدم
 */
export class ExportSystem {
  
  /**
   * تصدير البيانات
   */
  async exportData(options: ExportOptions, userId: string): Promise<ExportResult> {
    const startTime = Date.now();
    
    try {
      logger.info('Export started', {
        userId,
        type: options.type,
        format: options.format,
        filters: options.filters
      });

      // الحصول على البيانات
      const data = await this.fetchData(options);
      
      if (!data || data.length === 0) {
        return {
          success: false,
          error: 'لا توجد بيانات للتصدير'
        };
      }

      // تصدير البيانات حسب التنسيق
      let result: ExportResult;
      
      switch (options.format) {
        case 'CSV':
          result = await this.exportToCSV(data, options);
          break;
        case 'EXCEL':
          result = await this.exportToExcel(data, options);
          break;
        case 'PDF':
          result = await this.exportToPDF(data, options);
          break;
        case 'JSON':
          result = await this.exportToJSON(data, options);
          break;
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      const duration = Date.now() - startTime;
      
      logger.info('Export completed', {
        userId,
        type: options.type,
        format: options.format,
        recordCount: data.length,
        duration,
        success: result.success
      });

      return {
        ...result,
        recordCount: data.length
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Export failed', error, {
        userId,
        type: options.type,
        format: options.format,
        duration
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'فشل في التصدير'
      };
    }
  }

  /**
   * جلب البيانات حسب النوع
   */
  private async fetchData(options: ExportOptions): Promise<any[]> {
    switch (options.type) {
      case 'CUSTOMERS':
        return this.fetchCustomers(options);
      case 'PRODUCTS':
        return this.fetchProducts(options);
      case 'INVOICES':
        return this.fetchInvoices(options);
      case 'SALES_REPORT':
        return this.fetchSalesReport(options);
      case 'INVENTORY_REPORT':
        return this.fetchInventoryReport(options);
      default:
        throw new Error(`Unsupported export type: ${options.type}`);
    }
  }

  /**
   * جلب بيانات العملاء
   */
  private async fetchCustomers(options: ExportOptions): Promise<any[]> {
    const where: any = {};
    
    if (options.filters?.search) {
      where.OR = [
        { name: { contains: options.filters.search } },
        { email: { contains: options.filters.search } },
        { phone: { contains: options.filters.search } }
      ];
    }

    if (options.dateRange) {
      where.createdAt = {
        gte: options.dateRange.startDate,
        lte: options.dateRange.endDate
      };
    }

    const customers = await prisma.customer.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    });

    return customers.map(customer => ({
      'الرقم': customer.id,
      'الاسم': customer.name,
      'البريد الإلكتروني': customer.email || '',
      'الهاتف': customer.phone || '',
      'العنوان': customer.address || '',
      'الرقم الضريبي': customer.taxNumber || '',
      'تاريخ الإضافة': customer.createdAt.toLocaleDateString('ar-AE')
    }));
  }

  /**
   * جلب بيانات المنتجات
   */
  private async fetchProducts(options: ExportOptions): Promise<any[]> {
    const where: any = {};
    
    if (options.filters?.categoryId) {
      where.categoryId = parseInt(options.filters.categoryId);
    }

    if (options.filters?.lowStock) {
      where.stockQty = { lte: 10 }; // أقل من أو يساوي 10
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true
      },
      orderBy: { name: 'asc' }
    });

    return products.map(product => ({
      'الرقم': product.id,
      'الاسم': product.name,
      'الوصف': product.description || '',
      'الباركود': product.barcode,
      'الفئة': product.category.name,
      'سعر البيع': product.price,
      'سعر التكلفة': product.cost,
      'الكمية': product.stockQty,
      'تاريخ الإضافة': product.createdAt.toLocaleDateString('ar-AE')
    }));
  }

  /**
   * جلب بيانات الفواتير
   */
  private async fetchInvoices(options: ExportOptions): Promise<any[]> {
    const where: any = {};
    
    if (options.filters?.customerId) {
      where.customerId = parseInt(options.filters.customerId);
    }

    if (options.filters?.paymentStatus) {
      where.paymentStatus = options.filters.paymentStatus;
    }

    if (options.dateRange) {
      where.createdAt = {
        gte: options.dateRange.startDate,
        lte: options.dateRange.endDate
      };
    }

    const invoices = await prisma.invoice.findMany({
      where,
      include: {
        customer: true,
        items: {
          include: {
            product: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return invoices.map(invoice => ({
      'رقم الفاتورة': invoice.invoiceNumber,
      'العميل': invoice.customer.name,
      'المبلغ الفرعي': invoice.subtotal,
      'الضريبة': invoice.taxAmount,
      'الإجمالي': invoice.total,
      'حالة الدفع': this.translatePaymentStatus(invoice.paymentStatus),
      'عدد الأصناف': invoice.items.length,
      'تاريخ الإنشاء': invoice.createdAt.toLocaleDateString('ar-AE'),
      'الملاحظات': invoice.notes || ''
    }));
  }

  /**
   * جلب تقرير المبيعات
   */
  private async fetchSalesReport(options: ExportOptions): Promise<any[]> {
    const where: any = {};
    
    if (options.dateRange) {
      where.createdAt = {
        gte: options.dateRange.startDate,
        lte: options.dateRange.endDate
      };
    }

    const invoices = await prisma.invoice.findMany({
      where,
      include: {
        customer: true,
        items: {
          include: {
            product: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // تجميع البيانات حسب المنتج
    const salesByProduct = new Map();
    
    invoices.forEach(invoice => {
      invoice.items.forEach(item => {
        const productId = item.productId;
        const existing = salesByProduct.get(productId) || {
          productName: item.product.name,
          totalQuantity: 0,
          totalRevenue: 0,
          invoiceCount: 0
        };
        
        existing.totalQuantity += item.quantity;
        existing.totalRevenue += item.total;
        existing.invoiceCount += 1;
        
        salesByProduct.set(productId, existing);
      });
    });

    return Array.from(salesByProduct.values()).map(item => ({
      'المنتج': item.productName,
      'الكمية المباعة': item.totalQuantity,
      'إجمالي الإيرادات': item.totalRevenue.toFixed(2),
      'عدد الفواتير': item.invoiceCount,
      'متوسط السعر': (item.totalRevenue / item.totalQuantity).toFixed(2)
    }));
  }

  /**
   * جلب تقرير المخزون
   */
  private async fetchInventoryReport(options: ExportOptions): Promise<any[]> {
    const products = await prisma.product.findMany({
      include: {
        category: true
      },
      orderBy: { stockQty: 'asc' }
    });

    return products.map(product => ({
      'الرقم': product.id,
      'الاسم': product.name,
      'الفئة': product.category.name,
      'الكمية الحالية': product.stockQty,
      'قيمة المخزون': (product.stockQty * product.cost).toFixed(2),
      'حالة المخزون': product.stockQty <= 10 ? 'منخفض' : product.stockQty <= 50 ? 'متوسط' : 'جيد',
      'آخر تحديث': product.updatedAt.toLocaleDateString('ar-AE')
    }));
  }

  /**
   * تصدير إلى CSV
   */
  private async exportToCSV(data: any[], options: ExportOptions): Promise<ExportResult> {
    try {
      const fileName = options.fileName || `${options.type}_${Date.now()}.csv`;
      const filePath = `./exports/${fileName}`;
      
      // إنشاء مجلد التصدير إذا لم يكن موجوداً
      const fs = await import('fs/promises');
      await fs.mkdir('./exports', { recursive: true });
      
      // تحويل البيانات إلى CSV
      const headers = Object.keys(data[0]);
      const csvContent = [
        headers.join(','),
        ...data.map(row => 
          headers.map(header => 
            `"${String(row[header]).replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');
      
      // كتابة الملف
      await fs.writeFile(filePath, '\ufeff' + csvContent, 'utf8'); // BOM for Arabic support
      
      const stats = await fs.stat(filePath);
      
      return {
        success: true,
        filePath,
        fileName,
        size: stats.size
      };
    } catch (error) {
      throw new Error(`فشل في تصدير CSV: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * تصدير إلى Excel
   */
  private async exportToExcel(data: any[], options: ExportOptions): Promise<ExportResult> {
    try {
      // يحتاج تثبيت مكتبة xlsx
      // npm install xlsx
      const XLSX = await import('xlsx');
      
      const fileName = options.fileName || `${options.type}_${Date.now()}.xlsx`;
      const filePath = `./exports/${fileName}`;
      
      // إنشاء مجلد التصدير
      const fs = await import('fs/promises');
      await fs.mkdir('./exports', { recursive: true });
      
      // إنشاء workbook
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(data);
      
      // إضافة الورقة
      XLSX.utils.book_append_sheet(workbook, worksheet, options.type);
      
      // كتابة الملف
      XLSX.writeFile(workbook, filePath);
      
      const stats = await fs.stat(filePath);
      
      return {
        success: true,
        filePath,
        fileName,
        size: stats.size
      };
    } catch (error) {
      throw new Error(`فشل في تصدير Excel: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * تصدير إلى PDF
   */
  private async exportToPDF(data: any[], options: ExportOptions): Promise<ExportResult> {
    try {
      // يحتاج تثبيت مكتبة puppeteer أو jsPDF
      const fileName = options.fileName || `${options.type}_${Date.now()}.pdf`;
      const filePath = `./exports/${fileName}`;
      
      // إنشاء مجلد التصدير
      const fs = await import('fs/promises');
      await fs.mkdir('./exports', { recursive: true });
      
      // إنشاء HTML للتحويل إلى PDF
      const html = this.generateHTMLTable(data, options);
      
      // هنا يمكن استخدام puppeteer لتحويل HTML إلى PDF
      // للبساطة، سنحفظ HTML فقط
      await fs.writeFile(filePath.replace('.pdf', '.html'), html, 'utf8');
      
      return {
        success: true,
        filePath: filePath.replace('.pdf', '.html'),
        fileName: fileName.replace('.pdf', '.html'),
        size: html.length
      };
    } catch (error) {
      throw new Error(`فشل في تصدير PDF: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * تصدير إلى JSON
   */
  private async exportToJSON(data: any[], options: ExportOptions): Promise<ExportResult> {
    try {
      const fileName = options.fileName || `${options.type}_${Date.now()}.json`;
      const filePath = `./exports/${fileName}`;
      
      // إنشاء مجلد التصدير
      const fs = await import('fs/promises');
      await fs.mkdir('./exports', { recursive: true });
      
      const jsonContent = JSON.stringify({
        exportType: options.type,
        exportDate: new Date().toISOString(),
        recordCount: data.length,
        data
      }, null, 2);
      
      await fs.writeFile(filePath, jsonContent, 'utf8');
      
      const stats = await fs.stat(filePath);
      
      return {
        success: true,
        filePath,
        fileName,
        size: stats.size
      };
    } catch (error) {
      throw new Error(`فشل في تصدير JSON: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * إنشاء جدول HTML
   */
  private generateHTMLTable(data: any[], options: ExportOptions): string {
    const headers = Object.keys(data[0]);
    
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير ${options.type}</title>
        <style>
          body { font-family: Arial, sans-serif; direction: rtl; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          th { background-color: #f2f2f2; font-weight: bold; }
          .header { text-align: center; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>تقرير ${options.type}</h1>
          <p>تاريخ التصدير: ${new Date().toLocaleDateString('ar-AE')}</p>
          <p>عدد السجلات: ${data.length}</p>
        </div>
        <table>
          <thead>
            <tr>
              ${headers.map(header => `<th>${header}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            ${data.map(row => 
              `<tr>${headers.map(header => `<td>${row[header]}</td>`).join('')}</tr>`
            ).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;
  }

  /**
   * ترجمة حالة الدفع
   */
  private translatePaymentStatus(status: string): string {
    const translations: Record<string, string> = {
      'PENDING': 'معلقة',
      'PAID': 'مدفوعة',
      'PARTIALLY_PAID': 'مدفوعة جزئياً',
      'CANCELLED': 'ملغية'
    };
    
    return translations[status] || status;
  }
}

// مثيل نظام التصدير العام
export const exportSystem = new ExportSystem();
