import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { logSecurityEvent, getUserSecurityLogs, detectSuspiciousActivity } from '../security-logger';

// Mock Prisma
const mockPrisma = {
  securityLog: {
    create: jest.fn().mockResolvedValue({ id: '1', type: 'LOGIN_SUCCESS' }),
    findMany: jest.fn().mockResolvedValue([]),
    count: jest.fn().mockResolvedValue(5),
    deleteMany: jest.fn().mockResolvedValue({ count: 0 }),
  },
};

jest.mock('@/lib/prisma', () => ({
  prisma: mockPrisma,
}));

describe.skip('Security Logger', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock implementations
    mockPrisma.securityLog.create.mockResolvedValue({ id: '1', type: 'LOGIN_SUCCESS' });
    mockPrisma.securityLog.findMany.mockResolvedValue([]);
    mockPrisma.securityLog.count.mockResolvedValue(10);
  });

  describe('logSecurityEvent', () => {
    it('should log a security event successfully', async () => {
      const mockEvent = {
        type: 'LOGIN_SUCCESS' as const,
        userId: 'user123',
        ip: '***********',
        details: 'User logged in successfully'
      };

      await logSecurityEvent(mockEvent);

      expect(mockPrisma.securityLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          type: mockEvent.type,
          userId: parseInt(mockEvent.userId!), // تحويل إلى رقم
          ip: mockEvent.ip,
          details: mockEvent.details,
          severity: 'LOW'
        })
      });
    });

    it('should handle critical events with proper severity', async () => {
      const mockEvent = {
        type: 'DATA_BREACH_ATTEMPT' as const,
        ip: '***********',
        details: 'Suspicious data access attempt'
      };

      await logSecurityEvent(mockEvent);

      expect(mockPrisma.securityLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          severity: 'CRITICAL'
        })
      });
    });
  });

  describe('getUserSecurityLogs', () => {
    it('should retrieve user security logs', async () => {
      const userId = 'user123';
      
      const result = await getUserSecurityLogs(userId, 10);

      expect(mockPrisma.securityLog.findMany).toHaveBeenCalledWith({
        where: { userId: parseInt(userId) }, // تحويل إلى رقم
        orderBy: { timestamp: 'desc' },
        take: 10,
        select: expect.any(Object)
      });
      expect(result).toEqual([]);
    });
  });

  describe('detectSuspiciousActivity', () => {
    it('should detect suspicious activity based on event count', async () => {
      const ip = '***********';
      mockPrisma.securityLog.count.mockResolvedValueOnce(15);
      
      const result = await detectSuspiciousActivity(ip, 15);

      expect(result).toBe(true);
      expect(mockPrisma.securityLog.count).toHaveBeenCalledWith({
        where: {
          ip,
          timestamp: {
            gte: expect.any(Date)
          },
          severity: {
            in: ['HIGH', 'CRITICAL']
          }
        }
      });
    });

    it('should return false when activity is not suspicious', async () => {
      const ip = '***********';
      mockPrisma.securityLog.count.mockResolvedValue(5);
      
      const result = await detectSuspiciousActivity(ip, 15);

      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      const ip = '***********';
      mockPrisma.securityLog.count.mockRejectedValue(new Error('Database error'));
      
      const result = await detectSuspiciousActivity(ip, 15);

      expect(result).toBe(false);
    });
  });
});
