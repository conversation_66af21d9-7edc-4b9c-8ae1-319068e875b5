/**
 * خدمة العملات - توفر وظائف موحدة للتعامل مع العملات وتحويلها
 * Currency Service - Provides unified functions for handling and converting currencies
 */

import { CurrencyCode, CurrencyInfo, CURRENCIES, DEFAULT_CURRENCY, getCurrencyInfo } from '@/lib/utils';
import { getSystemSettings, saveSystemSettings } from '@/lib/settings';

// واجهة أسعار الصرف
// Exchange rates interface
export interface ExchangeRates {
  base: CurrencyCode;
  date: string;
  rates: Record<CurrencyCode, number>;
}

// واجهة سجل التحويل
// Conversion history entry interface
export interface ConversionHistoryEntry {
  id: string;
  fromCurrency: CurrencyCode;
  toCurrency: CurrencyCode;
  fromAmount: number;
  toAmount: number;
  rate: number;
  date: string;
}

// أسعار الصرف الافتراضية (بالنسبة للدرهم الإماراتي)
// Default exchange rates (relative to AED)
const DEFAULT_EXCHANGE_RATES: Record<CurrencyCode, number> = {
  'AED': 1.0,
  'SAR': 1.02,
  'QAR': 1.01,
  'BHD': 0.102,
  'KWD': 0.082,
  'OMR': 0.105,
  'EGP': 8.52,
  'JOD': 0.194,
  'LBP': 413.22,
  'USD': 0.272,
  'EUR': 0.25,
  'GBP': 0.214
};

// مفتاح تخزين أسعار الصرف في التخزين المحلي
// Local storage key for exchange rates
const EXCHANGE_RATES_STORAGE_KEY = 'amin_plus_exchange_rates';

// مفتاح تخزين سجل التحويلات في التخزين المحلي
// Local storage key for conversion history
const CONVERSION_HISTORY_STORAGE_KEY = 'amin_plus_conversion_history';

/**
 * الحصول على العملة الافتراضية
 * Get default currency
 */
export function getDefaultCurrency(): CurrencyCode {
  return getSystemSettings().defaultCurrency;
}

/**
 * تعيين العملة الافتراضية
 * Set default currency
 */
export function setDefaultCurrency(currency: CurrencyCode): void {
  const settings = getSystemSettings();
  saveSystemSettings({
    ...settings,
    defaultCurrency: currency
  });
}

/**
 * الحصول على أسعار الصرف المخزنة
 * Get stored exchange rates
 */
export function getStoredExchangeRates(): ExchangeRates {
  try {
    // محاولة الحصول على أسعار الصرف من التخزين المحلي
    // Try to get exchange rates from local storage
    const storedRates = localStorage.getItem(EXCHANGE_RATES_STORAGE_KEY);
    if (storedRates) {
      const parsedRates = JSON.parse(storedRates) as ExchangeRates;
      
      // التحقق من أن البيانات ليست قديمة (أكثر من 24 ساعة)
      // Check if data is not stale (more than 24 hours old)
      const storedDate = new Date(parsedRates.date);
      const now = new Date();
      const hoursDiff = (now.getTime() - storedDate.getTime()) / (1000 * 60 * 60);
      
      if (hoursDiff < 24) {
        return parsedRates;
      }
    }
  } catch (error) {
    console.error('Error getting stored exchange rates:', error);
  }
  
  // إذا لم يتم العثور على أسعار صرف مخزنة أو كانت قديمة، استخدم الأسعار الافتراضية
  // If no stored rates found or they are stale, use default rates
  return {
    base: 'AED',
    date: new Date().toISOString(),
    rates: DEFAULT_EXCHANGE_RATES
  };
}

/**
 * تخزين أسعار الصرف
 * Store exchange rates
 */
export function storeExchangeRates(rates: ExchangeRates): void {
  try {
    localStorage.setItem(EXCHANGE_RATES_STORAGE_KEY, JSON.stringify(rates));
  } catch (error) {
    console.error('Error storing exchange rates:', error);
  }
}

/**
 * تحديث أسعار الصرف من مصدر خارجي
 * Update exchange rates from external source
 */
export async function updateExchangeRates(): Promise<ExchangeRates> {
  try {
    // محاولة الحصول على أسعار الصرف من واجهة برمجة التطبيقات
    // Try to get exchange rates from API
    const response = await fetch('https://open.er-api.com/v6/latest/AED');
    if (!response.ok) {
      throw new Error(`Error fetching exchange rates: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // تنسيق البيانات لتتوافق مع واجهة أسعار الصرف
    // Format data to match ExchangeRates interface
    const exchangeRates: ExchangeRates = {
      base: 'AED',
      date: new Date().toISOString(),
      rates: {}
    };
    
    // استخراج أسعار الصرف للعملات المدعومة فقط
    // Extract exchange rates for supported currencies only
    for (const currency of CURRENCIES) {
      if (data.rates[currency.code]) {
        exchangeRates.rates[currency.code] = data.rates[currency.code];
      } else {
        // إذا لم يتم العثور على سعر الصرف، استخدم السعر الافتراضي
        // If exchange rate not found, use default rate
        exchangeRates.rates[currency.code] = DEFAULT_EXCHANGE_RATES[currency.code];
      }
    }
    
    // تخزين أسعار الصرف الجديدة
    // Store new exchange rates
    storeExchangeRates(exchangeRates);
    
    return exchangeRates;
  } catch (error) {
    console.error('Error updating exchange rates:', error);
    
    // في حالة الفشل، استخدم أسعار الصرف المخزنة
    // In case of failure, use stored exchange rates
    return getStoredExchangeRates();
  }
}

/**
 * تحويل مبلغ من عملة إلى أخرى
 * Convert amount from one currency to another
 */
export function convertCurrency(
  amount: number,
  fromCurrency: CurrencyCode,
  toCurrency: CurrencyCode
): number {
  // الحصول على أسعار الصرف
  // Get exchange rates
  const exchangeRates = getStoredExchangeRates();
  
  // إذا كانت العملتان متطابقتين، أعد المبلغ كما هو
  // If currencies are the same, return amount as is
  if (fromCurrency === toCurrency) {
    return amount;
  }
  
  // حساب المبلغ بالدرهم الإماراتي أولاً (العملة الأساسية)
  // Calculate amount in AED first (base currency)
  const amountInAED = fromCurrency === 'AED' 
    ? amount 
    : amount / exchangeRates.rates[fromCurrency];
  
  // تحويل المبلغ من الدرهم الإماراتي إلى العملة المطلوبة
  // Convert amount from AED to target currency
  const convertedAmount = toCurrency === 'AED'
    ? amountInAED
    : amountInAED * exchangeRates.rates[toCurrency];
  
  // تقريب المبلغ إلى عدد المنازل العشرية المناسب للعملة المطلوبة
  // Round amount to appropriate decimal places for target currency
  const targetCurrencyInfo = getCurrencyInfo(toCurrency);
  const decimalPlaces = targetCurrencyInfo.decimalPlaces;
  
  return Number(convertedAmount.toFixed(decimalPlaces));
}

/**
 * الحصول على سعر الصرف بين عملتين
 * Get exchange rate between two currencies
 */
export function getExchangeRate(
  fromCurrency: CurrencyCode,
  toCurrency: CurrencyCode
): number {
  // الحصول على أسعار الصرف
  // Get exchange rates
  const exchangeRates = getStoredExchangeRates();
  
  // إذا كانت العملتان متطابقتين، أعد 1
  // If currencies are the same, return 1
  if (fromCurrency === toCurrency) {
    return 1;
  }
  
  // حساب سعر الصرف
  // Calculate exchange rate
  const rateFromBaseToFrom = exchangeRates.rates[fromCurrency];
  const rateFromBaseToTo = exchangeRates.rates[toCurrency];
  
  return rateFromBaseToTo / rateFromBaseToFrom;
}

/**
 * الحصول على سجل التحويلات
 * Get conversion history
 */
export function getConversionHistory(): ConversionHistoryEntry[] {
  try {
    const storedHistory = localStorage.getItem(CONVERSION_HISTORY_STORAGE_KEY);
    if (storedHistory) {
      return JSON.parse(storedHistory) as ConversionHistoryEntry[];
    }
  } catch (error) {
    console.error('Error getting conversion history:', error);
  }
  
  return [];
}

/**
 * إضافة تحويل إلى سجل التحويلات
 * Add conversion to history
 */
export function addConversionToHistory(
  fromCurrency: CurrencyCode,
  toCurrency: CurrencyCode,
  fromAmount: number,
  toAmount: number
): void {
  try {
    const history = getConversionHistory();
    
    // إنشاء إدخال جديد
    // Create new entry
    const newEntry: ConversionHistoryEntry = {
      id: Math.random().toString(36).substring(2, 15),
      fromCurrency,
      toCurrency,
      fromAmount,
      toAmount,
      rate: getExchangeRate(fromCurrency, toCurrency),
      date: new Date().toISOString()
    };
    
    // إضافة الإدخال إلى بداية المصفوفة (أحدث تحويل أولاً)
    // Add entry to beginning of array (newest conversion first)
    history.unshift(newEntry);
    
    // الاحتفاظ بآخر 50 تحويل فقط
    // Keep only last 50 conversions
    const trimmedHistory = history.slice(0, 50);
    
    // تخزين السجل المحدث
    // Store updated history
    localStorage.setItem(CONVERSION_HISTORY_STORAGE_KEY, JSON.stringify(trimmedHistory));
  } catch (error) {
    console.error('Error adding conversion to history:', error);
  }
}

/**
 * مسح سجل التحويلات
 * Clear conversion history
 */
export function clearConversionHistory(): void {
  try {
    localStorage.removeItem(CONVERSION_HISTORY_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing conversion history:', error);
  }
}

/**
 * تنسيق مبلغ بعملة معينة
 * Format amount in specific currency
 */
export function formatCurrencyAmount(
  amount: number,
  currency: CurrencyCode = DEFAULT_CURRENCY,
  language: string = 'ar',
  options: Intl.NumberFormatOptions = {}
): string {
  try {
    const currencyInfo = getCurrencyInfo(currency);
    
    // تحديد اللغة المناسبة للتنسيق
    // Determine appropriate locale for formatting
    const locale = language === 'ar' ? currencyInfo.locale : currencyInfo.locale.replace('ar-', 'en-');
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      maximumFractionDigits: currencyInfo.decimalPlaces,
      ...options
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency amount:', error);
    
    // في حالة حدوث خطأ، نعود إلى التنسيق البسيط
    // In case of error, return simple format
    const currencyInfo = getCurrencyInfo(currency);
    return `${amount.toFixed(currencyInfo.decimalPlaces)} ${currencyInfo.symbol}`;
  }
}

/**
 * الحصول على رمز العملة
 * Get currency symbol
 */
export function getCurrencySymbol(
  currency: CurrencyCode = DEFAULT_CURRENCY,
  language: string = 'ar'
): string {
  try {
    const currencyInfo = getCurrencyInfo(currency);
    
    // تحديد اللغة المناسبة للتنسيق
    // Determine appropriate locale for formatting
    const locale = language === 'ar' ? currencyInfo.locale : currencyInfo.locale.replace('ar-', 'en-');
    
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      currencyDisplay: 'symbol',
    });
    
    return formatter.formatToParts(0).find(part => part.type === 'currency')?.value ?? currencyInfo.symbol;
  } catch (error) {
    console.error('Error getting currency symbol:', error);
    return getCurrencyInfo(currency).symbol;
  }
}

/**
 * الحصول على اسم العملة
 * Get currency name
 */
export function getCurrencyName(
  currency: CurrencyCode = DEFAULT_CURRENCY,
  language: string = 'ar'
): string {
  const currencyInfo = getCurrencyInfo(currency);
  return language === 'ar' ? currencyInfo.nameAr : currencyInfo.nameEn;
}

/**
 * تحويل مبلغ إلى كلمات
 * Convert amount to words
 */
export function amountToWords(
  amount: number,
  currency: CurrencyCode = DEFAULT_CURRENCY,
  language: string = 'ar'
): string {
  // استخدام مكتبة تحويل الأرقام إلى كلمات
  // Use number to words library
  const { numberToWords } = require('@/lib/number-to-words');
  
  const currencyInfo = getCurrencyInfo(currency);
  const currencyName = language === 'ar' ? currencyInfo.nameAr : currencyInfo.nameEn;
  
  return numberToWords(amount, currencyName, currencyName);
}

// تصدير الوظائف والأنواع
// Export functions and types
export default {
  getDefaultCurrency,
  setDefaultCurrency,
  getStoredExchangeRates,
  updateExchangeRates,
  convertCurrency,
  getExchangeRate,
  getConversionHistory,
  addConversionToHistory,
  clearConversionHistory,
  formatCurrencyAmount,
  getCurrencySymbol,
  getCurrencyName,
  amountToWords
};
