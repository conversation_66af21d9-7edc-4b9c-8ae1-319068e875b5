import { prisma } from '../db/prisma'
// استيراد وظيفة محددة
import { getCustomers } from '@/lib/services/customer-service';

// أو استيراد كل الوظائف
import customerService from '@/lib/services/customer-service';

/**
 * الحصول على قائمة العملاء
 * Get a list of all customers
 *
 * @returns {Promise<Array>} قائمة العملاء مع البيانات الأساسية
 */
export async function getCustomers() {
  try {
    const customers = await prisma.customer.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
      },
    })
    return customers
  } catch (error) {
    console.error('Failed to fetch customers:', error)
    throw error
  }
}

/**
 * الحصول على بيانات عميل محدد
 * Get data for a specific customer
 *
 * @param {string} id معرّف العميل
 * @returns {Promise<Object|null>} بيانات العميل أو null إذا لم يتم العثور عليه
 */
export async function getCustomerById(id: string) {
  try {
    const customer = await prisma.customer.findUnique({
      where: { id },
    })
    return customer
  } catch (error) {
    console.error(`Failed to fetch customer with ID ${id}:`, error)
    throw error
  }
}

export default {
  getCustomers,
  getCustomerById,
}
