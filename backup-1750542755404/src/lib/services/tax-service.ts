/**
 * خدمة الضرائب - توفر وظائف لحساب الضرائب وإدارتها
 * Tax Service - Provides functions for calculating and managing taxes
 */

import { EnhancedTaxSettings, DEFAULT_ENHANCED_TAX_SETTINGS } from '@/components/ui/enhanced-tax-settings';
import { getSystemSettings } from '@/lib/settings';
import { TaxType, getTaxInfo } from '@/lib/utils';

/**
 * واجهة معلومات الضريبة المحسوبة
 * Calculated tax information interface
 */
export interface CalculatedTax {
  readonly taxableAmount: number;
  readonly taxAmount: number;
  readonly totalWithTax: number;
  readonly taxRate: number;
  readonly taxType: TaxType;
  readonly taxTypeName: string;
  readonly taxTypeNameAr: string;
  readonly taxTypeNameEn: string;
  readonly isInclusive: boolean;
  readonly isExempt: boolean;
  readonly isZeroRated: boolean;
}

/**
 * الحصول على إعدادات الضريبة المحسنة
 * Get enhanced tax settings
 */
export function getEnhancedTaxSettings(): EnhancedTaxSettings {
  const settings = getSystemSettings();
  return settings.enhancedTaxSettings || DEFAULT_ENHANCED_TAX_SETTINGS;
}

/**
 * التحقق مما إذا كانت الضريبة مفعلة
 * Check if tax is enabled
 */
export function isTaxEnabled(): boolean {
  return getEnhancedTaxSettings().enabled;
}

/**
 * التحقق مما إذا كانت الفئة معفاة من الضريبة
 * Check if category is tax exempt
 */
export function isCategoryExempt(category: string): boolean {
  const settings = getEnhancedTaxSettings();
  return settings.exemptionCategories.includes(category);
}

/**
 * التحقق مما إذا كانت الفئة ذات نسبة صفرية
 * Check if category is zero-rated
 */
export function isCategoryZeroRated(category: string): boolean {
  const settings = getEnhancedTaxSettings();
  return settings.zeroRatedCategories.includes(category);
}

/**
 * التحقق مما إذا كانت الفئة ذات نسبة قياسية
 * Check if category is standard-rated
 */
export function isCategoryStandardRated(category: string): boolean {
  const settings = getEnhancedTaxSettings();
  return settings.standardRatedCategories.includes(category);
}

/**
 * حساب الضريبة على مبلغ معين
 * Calculate tax on a specific amount
 * 
 * @param amount المبلغ الأساسي - Base amount
 * @param category فئة المنتج أو الخدمة (اختياري) - Product or service category (optional)
 * @param isExemptOverride تجاوز حالة الإعفاء (اختياري) - Override exempt status (optional)
 * @returns معلومات الضريبة المحسوبة - Calculated tax information
 */
export function calculateTax(
  amount: number,
  category?: string,
  isExemptOverride?: boolean
): CalculatedTax {
  // الحصول على إعدادات الضريبة
  // Get tax settings
  const settings = getEnhancedTaxSettings();
  
  // إذا كانت الضريبة غير مفعلة، أعد ضريبة صفرية
  // If tax is not enabled, return zero tax
  if (!settings.enabled) {
    return createZeroTaxResult(amount, settings.type);
  }
  
  // التحقق من حالة الإعفاء
  // Check exemption status
  const isExempt = isExemptOverride !== undefined 
    ? isExemptOverride 
    : (category ? isCategoryExempt(category) : false);
  
  // إذا كان معفى من الضريبة، أعد ضريبة صفرية
  // If tax exempt, return zero tax
  if (isExempt) {
    return createZeroTaxResult(amount, settings.type, true, false);
  }
  
  // التحقق من حالة النسبة الصفرية
  // Check zero-rated status
  const isZeroRated = category ? isCategoryZeroRated(category) : false;
  
  // إذا كان ذو نسبة صفرية، أعد ضريبة صفرية مع الإشارة إلى أنه ذو نسبة صفرية
  // If zero-rated, return zero tax with indication that it's zero-rated
  if (isZeroRated) {
    return createZeroTaxResult(amount, settings.type, false, true);
  }
  
  // الحصول على معلومات نوع الضريبة
  // Get tax type information
  const taxInfo = getTaxInfo(settings.type);
  
  // حساب الضريبة بناءً على ما إذا كانت شاملة أم لا
  // Calculate tax based on whether it's inclusive or not
  let taxableAmount: number;
  let taxAmount: number;
  let totalWithTax: number;
  
  if (settings.inclusive) {
    // إذا كانت الضريبة مضمنة في السعر
    // If tax is included in the price
    taxableAmount = amount / (1 + (settings.rate / 100));
    taxAmount = amount - taxableAmount;
    totalWithTax = amount;
  } else {
    // إذا كانت الضريبة غير مضمنة في السعر
    // If tax is not included in the price
    taxableAmount = amount;
    taxAmount = amount * (settings.rate / 100);
    totalWithTax = amount + taxAmount;
  }
  
  // إعادة نتيجة الضريبة المحسوبة
  // Return calculated tax result
  return {
    taxableAmount,
    taxAmount,
    totalWithTax,
    taxRate: settings.rate,
    taxType: settings.type,
    taxTypeName: language === 'ar' ? taxInfo.nameAr : taxInfo.nameEn,
    taxTypeNameAr: taxInfo.nameAr,
    taxTypeNameEn: taxInfo.nameEn,
    isInclusive: settings.inclusive,
    isExempt: false,
    isZeroRated: false
  };
}

/**
 * إنشاء نتيجة ضريبة صفرية
 * Create zero tax result
 */
function createZeroTaxResult(
  amount: number,
  taxType: TaxType,
  isExempt: boolean = false,
  isZeroRated: boolean = false
): CalculatedTax {
  const taxInfo = getTaxInfo(taxType);
  
  return {
    taxableAmount: amount,
    taxAmount: 0,
    totalWithTax: amount,
    taxRate: 0,
    taxType,
    taxTypeName: language === 'ar' ? taxInfo.nameAr : taxInfo.nameEn,
    taxTypeNameAr: taxInfo.nameAr,
    taxTypeNameEn: taxInfo.nameEn,
    isInclusive: false,
    isExempt,
    isZeroRated
  };
}

/**
 * الحصول على رقم التسجيل الضريبي
 * Get tax registration number
 */
export function getTaxRegistrationNumber(): string {
  return getEnhancedTaxSettings().registrationNumber || '';
}

/**
 * الحصول على نسبة الضريبة الحالية
 * Get current tax rate
 */
export function getCurrentTaxRate(): number {
  return getEnhancedTaxSettings().rate;
}

/**
 * التحقق مما إذا كانت الأسعار شاملة للضريبة
 * Check if prices include tax
 */
export function arePricesInclusive(): boolean {
  return getEnhancedTaxSettings().inclusive;
}

// متغير لتخزين اللغة الحالية
// Variable to store current language
let language = 'ar';

/**
 * تعيين اللغة الحالية
 * Set current language
 */
export function setLanguage(lang: string): void {
  language = lang;
}

/**
 * تنسيق مبلغ الضريبة كنص
 * Format tax amount as text
 */
export function formatTaxAmount(tax: CalculatedTax): string {
  if (tax.isExempt) {
    return language === 'ar' ? 'معفى من الضريبة' : 'Tax Exempt';
  }
  
  if (tax.isZeroRated) {
    return language === 'ar' ? 'نسبة صفرية (0%)' : 'Zero-Rated (0%)';
  }
  
  return `${tax.taxTypeNameAr} / ${tax.taxTypeNameEn} (${tax.taxRate}%)`;
}

/**
 * إنشاء بيانات رمز الاستجابة السريعة للفاتورة الضريبية
 * Create QR code data for tax invoice
 * 
 * وفقًا لمتطلبات الهيئة الاتحادية للضرائب في الإمارات
 * According to UAE Federal Tax Authority requirements
 */
export function createTaxInvoiceQRData(
  sellerName: string,
  sellerTRN: string,
  invoiceDate: string,
  invoiceTotal: number,
  taxAmount: number
): string {
  // تنسيق البيانات وفقًا لمتطلبات الهيئة الاتحادية للضرائب
  // Format data according to Federal Tax Authority requirements
  const data = [
    sellerName,
    sellerTRN,
    invoiceDate,
    invoiceTotal.toFixed(2),
    taxAmount.toFixed(2)
  ];
  
  // إرجاع البيانات كسلسلة نصية مفصولة بفواصل
  // Return data as comma-separated string
  return data.join(',');
}
