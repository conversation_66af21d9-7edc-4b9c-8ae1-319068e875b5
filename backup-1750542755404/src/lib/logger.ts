import fs from 'fs/promises';
import path from 'path';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  userId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  stack?: string;
}

interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  filePath?: string;
  maxFileSize: number; // in bytes
  maxFiles: number;
  enableStructured: boolean;
}

/**
 * نظام تسجيل متقدم ومرن
 */
export class Logger {
  private config: LoggerConfig;
  private logLevels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4
  };

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: (process.env.LOG_LEVEL as LogLevel) || 'info',
      enableConsole: process.env.NODE_ENV !== 'production',
      enableFile: true,
      filePath: process.env.LOG_FILE_PATH || './logs/app.log',
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      enableStructured: true,
      ...config
    };

    this.ensureLogDirectory();
  }

  /**
   * تسجيل رسالة debug
   */
  debug(message: string, context?: Record<string, any>): void {
    this.log('debug', message, context);
  }

  /**
   * تسجيل رسالة info
   */
  info(message: string, context?: Record<string, any>): void {
    this.log('info', message, context);
  }

  /**
   * تسجيل رسالة تحذير
   */
  warn(message: string, context?: Record<string, any>): void {
    this.log('warn', message, context);
  }

  /**
   * تسجيل رسالة خطأ
   */
  error(message: string, error?: Error | any, context?: Record<string, any>): void {
    const logContext = { ...context };
    
    if (error) {
      if (error instanceof Error) {
        logContext.error = {
          name: error.name,
          message: error.message,
          stack: error.stack
        };
      } else {
        logContext.error = error;
      }
    }

    this.log('error', message, logContext, error?.stack);
  }

  /**
   * تسجيل رسالة خطأ فادح
   */
  fatal(message: string, error?: Error | any, context?: Record<string, any>): void {
    const logContext = { ...context };
    
    if (error) {
      if (error instanceof Error) {
        logContext.error = {
          name: error.name,
          message: error.message,
          stack: error.stack
        };
      } else {
        logContext.error = error;
      }
    }

    this.log('fatal', message, logContext, error?.stack);
  }

  /**
   * تسجيل طلب HTTP
   */
  logRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    userId?: string,
    ip?: string,
    userAgent?: string
  ): void {
    const level: LogLevel = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
    
    this.log(level, `${method} ${url} ${statusCode} - ${responseTime}ms`, {
      type: 'http_request',
      method,
      url,
      statusCode,
      responseTime,
      userId,
      ip,
      userAgent
    });
  }

  /**
   * تسجيل عملية قاعدة البيانات
   */
  logDatabaseOperation(
    operation: string,
    table: string,
    duration: number,
    success: boolean,
    error?: string
  ): void {
    const level: LogLevel = success ? 'debug' : 'error';
    const message = `DB ${operation} on ${table} - ${duration}ms ${success ? 'SUCCESS' : 'FAILED'}`;
    
    this.log(level, message, {
      type: 'database_operation',
      operation,
      table,
      duration,
      success,
      error
    });
  }

  /**
   * تسجيل عملية أعمال
   */
  logBusinessOperation(
    operation: string,
    entityType: string,
    entityId: string,
    userId: string,
    success: boolean,
    details?: Record<string, any>
  ): void {
    const level: LogLevel = success ? 'info' : 'warn';
    const message = `Business operation: ${operation} on ${entityType}:${entityId} ${success ? 'SUCCESS' : 'FAILED'}`;
    
    this.log(level, message, {
      type: 'business_operation',
      operation,
      entityType,
      entityId,
      userId,
      success,
      ...details
    });
  }

  /**
   * الدالة الأساسية للتسجيل
   */
  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    stack?: string
  ): void {
    // فحص مستوى التسجيل
    if (this.logLevels[level] < this.logLevels[this.config.level]) {
      return;
    }

    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      stack
    };

    // إضافة معلومات إضافية من السياق
    if (context) {
      logEntry.userId = context.userId;
      logEntry.requestId = context.requestId;
      logEntry.ip = context.ip;
      logEntry.userAgent = context.userAgent;
    }

    // تسجيل في Console
    if (this.config.enableConsole) {
      this.logToConsole(logEntry);
    }

    // تسجيل في الملف
    if (this.config.enableFile) {
      this.logToFile(logEntry);
    }
  }

  /**
   * تسجيل في Console
   */
  private logToConsole(entry: LogEntry): void {
    const { timestamp, level, message, context } = entry;
    const coloredLevel = this.colorizeLevel(level);
    const formattedTime = new Date(timestamp).toLocaleTimeString();
    
    let logMessage = `[${formattedTime}] ${coloredLevel} ${message}`;
    
    if (context && Object.keys(context).length > 0) {
      logMessage += ` ${JSON.stringify(context, null, 2)}`;
    }

    switch (level) {
      case 'debug':
        console.debug(logMessage);
        break;
      case 'info':
        console.info(logMessage);
        break;
      case 'warn':
        console.warn(logMessage);
        break;
      case 'error':
      case 'fatal':
        console.error(logMessage);
        if (entry.stack) {
          console.error(entry.stack);
        }
        break;
    }
  }

  /**
   * تسجيل في الملف
   */
  private async logToFile(entry: LogEntry): Promise<void> {
    try {
      const logLine = this.config.enableStructured
        ? JSON.stringify(entry) + '\n'
        : this.formatPlainText(entry) + '\n';

      await this.writeToFile(logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  /**
   * كتابة في الملف مع إدارة الحجم
   */
  private async writeToFile(content: string): Promise<void> {
    if (!this.config.filePath) return;

    try {
      // فحص حجم الملف
      const stats = await fs.stat(this.config.filePath).catch(() => null);
      
      if (stats && stats.size >= this.config.maxFileSize) {
        await this.rotateLogFile();
      }

      await fs.appendFile(this.config.filePath, content, 'utf8');
    } catch (error) {
      console.error('Error writing to log file:', error);
    }
  }

  /**
   * تدوير ملفات السجل
   */
  private async rotateLogFile(): Promise<void> {
    if (!this.config.filePath) return;

    try {
      const logDir = path.dirname(this.config.filePath);
      const logName = path.basename(this.config.filePath, path.extname(this.config.filePath));
      const logExt = path.extname(this.config.filePath);

      // نقل الملفات الموجودة
      for (let i = this.config.maxFiles - 1; i > 0; i--) {
        const oldFile = path.join(logDir, `${logName}.${i}${logExt}`);
        const newFile = path.join(logDir, `${logName}.${i + 1}${logExt}`);
        
        try {
          await fs.rename(oldFile, newFile);
        } catch (error) {
          // الملف غير موجود، تجاهل
        }
      }

      // نقل الملف الحالي
      const rotatedFile = path.join(logDir, `${logName}.1${logExt}`);
      await fs.rename(this.config.filePath, rotatedFile);

      // حذف الملفات القديمة
      const oldFile = path.join(logDir, `${logName}.${this.config.maxFiles + 1}${logExt}`);
      try {
        await fs.unlink(oldFile);
      } catch (error) {
        // الملف غير موجود، تجاهل
      }
    } catch (error) {
      console.error('Error rotating log file:', error);
    }
  }

  /**
   * تنسيق النص العادي
   */
  private formatPlainText(entry: LogEntry): string {
    const { timestamp, level, message, context, userId, requestId, ip } = entry;
    
    let formatted = `[${timestamp}] ${level.toUpperCase()} ${message}`;
    
    if (userId) formatted += ` [User: ${userId}]`;
    if (requestId) formatted += ` [Request: ${requestId}]`;
    if (ip) formatted += ` [IP: ${ip}]`;
    
    if (context && Object.keys(context).length > 0) {
      formatted += ` Context: ${JSON.stringify(context)}`;
    }
    
    return formatted;
  }

  /**
   * تلوين مستوى السجل
   */
  private colorizeLevel(level: LogLevel): string {
    const colors = {
      debug: '\x1b[36m', // Cyan
      info: '\x1b[32m',  // Green
      warn: '\x1b[33m',  // Yellow
      error: '\x1b[31m', // Red
      fatal: '\x1b[35m'  // Magenta
    };
    
    const reset = '\x1b[0m';
    return `${colors[level]}${level.toUpperCase()}${reset}`;
  }

  /**
   * التأكد من وجود مجلد السجلات
   */
  private async ensureLogDirectory(): Promise<void> {
    if (!this.config.filePath) return;

    try {
      const logDir = path.dirname(this.config.filePath);
      await fs.mkdir(logDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  /**
   * الحصول على إحصائيات السجلات
   */
  async getLogStats(): Promise<{
    totalSize: number;
    fileCount: number;
    oldestLog: Date | null;
    newestLog: Date | null;
  }> {
    if (!this.config.filePath) {
      return { totalSize: 0, fileCount: 0, oldestLog: null, newestLog: null };
    }

    try {
      const logDir = path.dirname(this.config.filePath);
      const files = await fs.readdir(logDir);
      const logFiles = files.filter(file => file.includes(path.basename(this.config.filePath, path.extname(this.config.filePath))));
      
      let totalSize = 0;
      let oldestLog: Date | null = null;
      let newestLog: Date | null = null;

      for (const file of logFiles) {
        const filePath = path.join(logDir, file);
        const stats = await fs.stat(filePath);
        
        totalSize += stats.size;
        
        if (!oldestLog || stats.birthtime < oldestLog) {
          oldestLog = stats.birthtime;
        }
        
        if (!newestLog || stats.mtime > newestLog) {
          newestLog = stats.mtime;
        }
      }

      return {
        totalSize,
        fileCount: logFiles.length,
        oldestLog,
        newestLog
      };
    } catch (error) {
      console.error('Error getting log stats:', error);
      return { totalSize: 0, fileCount: 0, oldestLog: null, newestLog: null };
    }
  }
}

// مثيل Logger العام
export const logger = new Logger();
