'use client';

import { useState, useEffect } from 'react';

type Language = 'ar' | 'en';

interface Translations {
  [key: string]: {
    [key: string]: string;
  };
}

const translations: Translations = {
  ar: {
    // Dashboard
    'dashboard.title': 'لوحة التحكم',
    'dashboard.customers': 'العملاء',
    'dashboard.invoices': 'الفواتير',
    'dashboard.products': 'المنتجات',
    'dashboard.revenue': 'الإيرادات',
    'dashboard.recentInvoices': 'أحدث الفواتير',
    'dashboard.topProducts': 'أكثر المنتجات مبيعًا',
    
    // Navigation
    'nav.dashboard': 'لوحة التحكم',
    'nav.customers': 'العملاء',
    'nav.products': 'المنتجات',
    'nav.invoices': 'الفواتير',
    'nav.reports': 'التقارير',
    'nav.settings': 'الإعدادات',
    
    // Common
    'common.save': 'حفظ',
    'common.cancel': 'إلغاء',
    'common.delete': 'حذف',
    'common.edit': 'تعديل',
    'common.add': 'إضافة',
    'common.search': 'بحث',
    'common.loading': 'جاري التحميل...',
    'common.error': 'حدث خطأ',
    'common.success': 'تم بنجاح',
    
    // Auth
    'auth.login': 'تسجيل الدخول',
    'auth.logout': 'تسجيل الخروج',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
  },
  en: {
    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.customers': 'Customers',
    'dashboard.invoices': 'Invoices',
    'dashboard.products': 'Products',
    'dashboard.revenue': 'Revenue',
    'dashboard.recentInvoices': 'Recent Invoices',
    'dashboard.topProducts': 'Top Products',
    
    // Navigation
    'nav.dashboard': 'Dashboard',
    'nav.customers': 'Customers',
    'nav.products': 'Products',
    'nav.invoices': 'Invoices',
    'nav.reports': 'Reports',
    'nav.settings': 'Settings',
    
    // Common
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.add': 'Add',
    'common.search': 'Search',
    'common.loading': 'Loading...',
    'common.error': 'Error occurred',
    'common.success': 'Success',
    
    // Auth
    'auth.login': 'Login',
    'auth.logout': 'Logout',
    'auth.email': 'Email',
    'auth.password': 'Password',
  }
};

export function useI18n() {
  const [language, setLanguage] = useState<Language>('ar');

  useEffect(() => {
    // Get language from localStorage or default to Arabic
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
      setLanguage(savedLanguage);
    }
  }, []);

  const t = (key: string): string => {
    const keys = key.split('.');
    let value: any = translations[language];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  const changeLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
  };

  return {
    language,
    t,
    changeLanguage,
    isRTL: language === 'ar'
  };
}

// Export useTranslation for compatibility
export function useTranslation() {
  const { t, language, changeLanguage } = useI18n();
  return {
    t,
    i18n: {
      language,
      changeLanguage
    }
  };
}
