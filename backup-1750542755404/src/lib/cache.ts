type CacheItem<T> = {
    data: T;
    timestamp: number;
};

type CacheOptions = {
    ttl?: number; // مدة صلاحية العنصر بالمللي ثانية
};

/**
 * نظام تخزين مؤقت بسيط للبيانات
 */
class SimpleCache<T = any> { // Make the class generic
    private readonly cache: Map<string, CacheItem<T>> = new Map(); // Mark as readonly
    private readonly defaultTTL: number; // Mark as readonly

    constructor(defaultTTL = 60000) { // افتراضيًا دقيقة واحدة
        if (defaultTTL <= 0) {
            throw new Error("defaultTTL must be a positive number.");
        }
        this.defaultTTL = defaultTTL;
    }

    /**
     * تخزين قيمة في الذاكرة المؤقتة
     */
    set(key: string, data: T, options?: CacheOptions): void { // Use generic type T
        const ttl = options?.ttl ?? this.defaultTTL; // Use nullish coalescing operator
        this.cache.set(key, {
            data,
            timestamp: Date.now() + ttl,
        });
    }

    /**
     * الحصول على قيمة من الذاكرة المؤقتة
     */
    get(key: string): T | null { // Use generic type T
        const item = this.cache.get(key);

        // التحقق من وجود العنصر وصلاحيته
        if (item && item.timestamp > Date.now()) {
            return item.data;
        }

        // إزالة العنصر منتهي الصلاحية
        if (item) {
            this.cache.delete(key);
        }

        return null;
    }

    /**
     * حذف عنصر من الذاكرة المؤقتة
     */
    delete(key: string): boolean {
        return this.cache.delete(key);
    }

    /**
     * مسح جميع العناصر منتهية الصلاحية
     */
    cleanup(): void {
        const now = Date.now();
        for (const [key, item] of Array.from(this.cache.entries())) { // Use Array.from for compatibility
            if (item.timestamp < now) {
                this.cache.delete(key);
            }
        }
    }

    /**
     * حذف جميع العناصر من الذاكرة المؤقتة
     */
    clear(): void {
        this.cache.clear();
    }
}

// إنشاء مثيل واحد من نظام التخزين المؤقت للاستخدام في التطبيق
export const appCache = new SimpleCache<any>(); // Explicitly specify the type as any

// تنظيف العناصر منتهية الصلاحية كل 5 دقائق
if (typeof window !== 'undefined') {
    setInterval(() => {
        appCache.cleanup();
    }, 5 * 60 * 1000);
}