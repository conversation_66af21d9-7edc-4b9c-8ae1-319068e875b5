import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || '';
const TOKEN_EXPIRY = '1d'; // صلاحية لمدة يوم واحد

/**
 * إنشاء جلسة جديدة للمستخدم
 */
export async function createSession(user: { 
  id: string; 
  name: string; 
  email: string; 
  role: string;
}) {
  // التحقق من وجود مفتاح السر
  if (!JWT_SECRET) {
    throw new Error('JWT_SECRET غير معرّف في متغيرات البيئة');
  }

  // إنشاء رمز JWT
  const token = await new SignJWT({
    id: user.id,
    name: user.name,
    email: user.email,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    // إضافة عشوائية لمنع استخدام الرمز مرتين
    nonce: Math.random().toString(36).substring(2, 15),
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(TOKEN_EXPIRY)
    .setNotBefore(Math.floor(Date.now() / 1000)) // تحديد وقت بدء صلاحية الرمز
    .sign(new TextEncoder().encode(JWT_SECRET));

  // تعيين كوكي الجلسة
  cookies().set({
    name: 'auth_token',
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 24 * 60 * 60, // يوم واحد بالثواني
  });

  return token;
}

/**
 * إنهاء جلسة المستخدم
 */
export function destroySession() {
  cookies().set({
    name: 'auth_token',
    value: '',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    expires: new Date(0),
  });
}

/**
 * التحقق من رمز الجلسة
 */
export async function verifySession() {
  try {
    const token = cookies().get('auth_token')?.value;
    
    if (!token || !JWT_SECRET) {
      return null;
    }
    
    const verified = await jwtVerify(token, new TextEncoder().encode(JWT_SECRET));
    
    return {
      id: verified.payload.id as string,
      name: verified.payload.name as string,
      email: verified.payload.email as string,
      role: verified.payload.role as string,
    };
  } catch (error) {
    // انتهاء صلاحية الرمز أو تلاعب به
    destroySession();
    return null;
  }
}