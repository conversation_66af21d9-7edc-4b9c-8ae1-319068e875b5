import { prisma } from './prisma';
import fs from 'fs/promises';
import { performance } from 'perf_hooks';

export interface HealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  details?: string;
  timestamp: Date;
}

export interface SystemHealth {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  checks: HealthCheck[];
  uptime: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
}

/**
 * فحص صحة النظام الشامل
 */
export async function performHealthCheck(): Promise<SystemHealth> {
  const startTime = performance.now();
  const checks: HealthCheck[] = [];

  // فحص قاعدة البيانات
  checks.push(await checkDatabase());

  // فحص مساحة القرص
  checks.push(await checkDiskSpace());

  // فحص الذاكرة
  checks.push(await checkMemoryUsage());

  // فحص ملفات النظام المهمة
  checks.push(await checkCriticalFiles());

  // فحص الاتصال بالخدمات الخارجية
  checks.push(await checkExternalServices());

  // تحديد الحالة العامة
  const unhealthyChecks = checks.filter(check => check.status === 'unhealthy');
  const degradedChecks = checks.filter(check => check.status === 'degraded');

  let overall: 'healthy' | 'unhealthy' | 'degraded';
  if (unhealthyChecks.length > 0) {
    overall = 'unhealthy';
  } else if (degradedChecks.length > 0) {
    overall = 'degraded';
  } else {
    overall = 'healthy';
  }

  // معلومات النظام
  const memoryUsage = process.memoryUsage();
  const uptime = process.uptime();

  return {
    overall,
    checks,
    uptime,
    memory: {
      used: memoryUsage.heapUsed,
      total: memoryUsage.heapTotal,
      percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
    },
    disk: await getDiskUsage()
  };
}

/**
 * فحص قاعدة البيانات
 */
async function checkDatabase(): Promise<HealthCheck> {
  const startTime = performance.now();
  
  try {
    // اختبار الاتصال بقاعدة البيانات
    await prisma.$queryRaw`SELECT 1`;
    
    // اختبار عملية قراءة بسيطة
    const userCount = await prisma.user.count();
    
    const responseTime = performance.now() - startTime;
    
    return {
      service: 'database',
      status: responseTime < 1000 ? 'healthy' : 'degraded',
      responseTime,
      details: `Connected successfully. Users: ${userCount}`,
      timestamp: new Date()
    };
  } catch (error) {
    return {
      service: 'database',
      status: 'unhealthy',
      responseTime: performance.now() - startTime,
      details: error instanceof Error ? error.message : 'Database connection failed',
      timestamp: new Date()
    };
  }
}

/**
 * فحص مساحة القرص
 */
async function checkDiskSpace(): Promise<HealthCheck> {
  try {
    const stats = await fs.stat(process.cwd());
    const diskUsage = await getDiskUsage();
    
    let status: 'healthy' | 'unhealthy' | 'degraded';
    if (diskUsage.percentage > 90) {
      status = 'unhealthy';
    } else if (diskUsage.percentage > 80) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }
    
    return {
      service: 'disk_space',
      status,
      details: `Disk usage: ${diskUsage.percentage.toFixed(1)}%`,
      timestamp: new Date()
    };
  } catch (error) {
    return {
      service: 'disk_space',
      status: 'unhealthy',
      details: error instanceof Error ? error.message : 'Failed to check disk space',
      timestamp: new Date()
    };
  }
}

/**
 * فحص استخدام الذاكرة
 */
async function checkMemoryUsage(): Promise<HealthCheck> {
  const memoryUsage = process.memoryUsage();
  const percentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
  
  let status: 'healthy' | 'unhealthy' | 'degraded';
  if (percentage > 90) {
    status = 'unhealthy';
  } else if (percentage > 80) {
    status = 'degraded';
  } else {
    status = 'healthy';
  }
  
  return {
    service: 'memory',
    status,
    details: `Memory usage: ${percentage.toFixed(1)}% (${formatBytes(memoryUsage.heapUsed)}/${formatBytes(memoryUsage.heapTotal)})`,
    timestamp: new Date()
  };
}

/**
 * فحص الملفات المهمة
 */
async function checkCriticalFiles(): Promise<HealthCheck> {
  const criticalFiles = [
    'package.json',
    'prisma/schema.prisma',
    '.env',
    'src/lib/prisma.ts'
  ];
  
  try {
    for (const file of criticalFiles) {
      await fs.access(file);
    }
    
    return {
      service: 'critical_files',
      status: 'healthy',
      details: 'All critical files are accessible',
      timestamp: new Date()
    };
  } catch (error) {
    return {
      service: 'critical_files',
      status: 'unhealthy',
      details: error instanceof Error ? error.message : 'Critical file missing',
      timestamp: new Date()
    };
  }
}

/**
 * فحص الخدمات الخارجية
 */
async function checkExternalServices(): Promise<HealthCheck> {
  // هذا مثال - يمكن إضافة فحوصات للخدمات الخارجية الفعلية
  try {
    // مثال: فحص API خارجي
    // const response = await fetch('https://api.example.com/health');
    // if (!response.ok) throw new Error('External API unhealthy');
    
    return {
      service: 'external_services',
      status: 'healthy',
      details: 'All external services are accessible',
      timestamp: new Date()
    };
  } catch (error) {
    return {
      service: 'external_services',
      status: 'degraded',
      details: 'Some external services may be unavailable',
      timestamp: new Date()
    };
  }
}

/**
 * الحصول على معلومات استخدام القرص
 */
async function getDiskUsage(): Promise<{ used: number; total: number; percentage: number }> {
  try {
    // هذا مثال مبسط - في الإنتاج يجب استخدام مكتبة مخصصة
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);
    
    const { stdout } = await execAsync('df -h .');
    const lines = stdout.split('\n');
    const diskInfo = lines[1].split(/\s+/);
    
    const total = parseFloat(diskInfo[1]) * 1024 * 1024; // تحويل إلى بايت
    const used = parseFloat(diskInfo[2]) * 1024 * 1024;
    const percentage = (used / total) * 100;
    
    return { used, total, percentage };
  } catch (error) {
    // في حالة فشل الحصول على معلومات القرص، إرجاع قيم افتراضية
    return { used: 0, total: 1, percentage: 0 };
  }
}

/**
 * تنسيق البايتات
 */
function formatBytes(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * مراقبة الأداء المستمرة
 */
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  
  /**
   * تسجيل مقياس أداء
   */
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // الاحتفاظ بآخر 100 قيمة فقط
    if (values.length > 100) {
      values.shift();
    }
  }
  
  /**
   * الحصول على إحصائيات المقياس
   */
  getMetricStats(name: string): {
    count: number;
    average: number;
    min: number;
    max: number;
    latest: number;
  } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) return null;
    
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);
    const average = sum / count;
    const min = Math.min(...values);
    const max = Math.max(...values);
    const latest = values[values.length - 1];
    
    return { count, average, min, max, latest };
  }
  
  /**
   * الحصول على جميع المقاييس
   */
  getAllMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [name, values] of this.metrics.entries()) {
      result[name] = this.getMetricStats(name);
    }
    
    return result;
  }
  
  /**
   * مسح المقاييس
   */
  clearMetrics(): void {
    this.metrics.clear();
  }
}

// مثيل مراقب الأداء العام
export const performanceMonitor = new PerformanceMonitor();
