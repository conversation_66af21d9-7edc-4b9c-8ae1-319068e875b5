import { SessionUser } from './auth-middleware';

// تعريف الصلاحيات المتاحة
export const PERMISSIONS = {
  // صلاحيات لوحة التحكم
  VIEW_DASHBOARD: 'view_dashboard',
  
  // صلاحيات المنتجات
  VIEW_PRODUCTS: 'view_products',
  CREATE_PRODUCT: 'create_product',
  EDIT_PRODUCT: 'edit_product',
  DELETE_PRODUCT: 'delete_product',
  
  // صلاحيات العملاء
  VIEW_CUSTOMERS: 'view_customers',
  CREATE_CUSTOMER: 'create_customer',
  EDIT_CUSTOMER: 'edit_customer',
  DELETE_CUSTOMER: 'delete_customer',
  
  // صلاحيات الفواتير
  VIEW_INVOICES: 'view_invoices',
  CREATE_INVOICE: 'create_invoice',
  EDIT_INVOICE: 'edit_invoice',
  DELETE_INVOICE: 'delete_invoice',
  
  // صلاحيات التقارير
  VIEW_REPORTS: 'view_reports',
  
  // صلاحيات إدارية
  MANAGE_USERS: 'manage_users',
  MANAGE_SETTINGS: 'manage_settings'
};

// تعريف الصلاحيات حسب الدور
const ROLE_PERMISSIONS: Record<string, string[]> = {
  admin: Object.values(PERMISSIONS),
  
  manager: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_PRODUCTS, PERMISSIONS.CREATE_PRODUCT, PERMISSIONS.EDIT_PRODUCT,
    PERMISSIONS.VIEW_CUSTOMERS, PERMISSIONS.CREATE_CUSTOMER, PERMISSIONS.EDIT_CUSTOMER,
    PERMISSIONS.VIEW_INVOICES, PERMISSIONS.CREATE_INVOICE, PERMISSIONS.EDIT_INVOICE,
    PERMISSIONS.VIEW_REPORTS
  ],
  
  accountant: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_PRODUCTS,
    PERMISSIONS.VIEW_CUSTOMERS, 
    PERMISSIONS.VIEW_INVOICES, PERMISSIONS.CREATE_INVOICE, PERMISSIONS.EDIT_INVOICE,
    PERMISSIONS.VIEW_REPORTS
  ],
  
  user: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_PRODUCTS,
    PERMISSIONS.VIEW_CUSTOMERS,
    PERMISSIONS.VIEW_INVOICES, PERMISSIONS.CREATE_INVOICE
  ]
};

/**
 * التحقق من صلاحيات المستخدم
 */
export function hasPermission(user: SessionUser, permission: string): boolean {
  if (!user) return false;
  
  // المدير لديه جميع الصلاحيات
  if (user.role === 'admin') return true;
  
  // التحقق من صلاحيات الدور
  const permissions = ROLE_PERMISSIONS[user.role] || [];
  return permissions.includes(permission);
}