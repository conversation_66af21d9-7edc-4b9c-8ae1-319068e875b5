import { prisma } from './prisma';
import { logger } from './logger';

export type NotificationType = 
  | 'INVOICE_CREATED'
  | 'INVOICE_PAID'
  | 'INVOICE_OVERDUE'
  | 'LOW_STOCK'
  | 'CUSTOMER_ADDED'
  | 'SYSTEM_ALERT'
  | 'BACKUP_COMPLETED'
  | 'BACKUP_FAILED'
  | 'SECURITY_ALERT'
  | 'MAINTENANCE';

export type NotificationPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

export type NotificationChannel = 'IN_APP' | 'EMAIL' | 'SMS' | 'PUSH' | 'WEBHOOK';

interface NotificationData {
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  channels: NotificationChannel[];
  recipients: string[]; // User IDs or email addresses
  data?: Record<string, any>;
  scheduledFor?: Date;
  expiresAt?: Date;
}

interface NotificationTemplate {
  type: NotificationType;
  titleTemplate: string;
  messageTemplate: string;
  defaultPriority: NotificationPriority;
  defaultChannels: NotificationChannel[];
}

/**
 * نظام إشعارات شامل ومتقدم
 */
export class NotificationSystem {
  private templates: Map<NotificationType, NotificationTemplate> = new Map();

  constructor() {
    this.initializeTemplates();
  }

  /**
   * إرسال إشعار
   */
  async sendNotification(data: NotificationData): Promise<void> {
    try {
      // إنشاء الإشعار في قاعدة البيانات
      const notification = await prisma.notification.create({
        data: {
          title: data.title,
          message: data.message,
          type: data.type,
          relatedId: data.data?.relatedId,
          relatedType: data.data?.relatedType,
          isRead: false
        }
      });

      // إرسال عبر القنوات المختلفة
      for (const channel of data.channels) {
        await this.sendViaChannel(channel, data);
      }

      logger.info('Notification sent successfully', {
        notificationId: notification.id,
        type: data.type,
        channels: data.channels,
        recipients: data.recipients.length
      });

    } catch (error) {
      logger.error('Failed to send notification', error, {
        type: data.type,
        channels: data.channels
      });
      throw error;
    }
  }

  /**
   * إرسال إشعار باستخدام قالب
   */
  async sendTemplatedNotification(
    type: NotificationType,
    recipients: string[],
    templateData: Record<string, any>,
    options?: {
      priority?: NotificationPriority;
      channels?: NotificationChannel[];
      scheduledFor?: Date;
      expiresAt?: Date;
    }
  ): Promise<void> {
    const template = this.templates.get(type);
    if (!template) {
      throw new Error(`Template not found for notification type: ${type}`);
    }

    const title = this.interpolateTemplate(template.titleTemplate, templateData);
    const message = this.interpolateTemplate(template.messageTemplate, templateData);

    await this.sendNotification({
      type,
      title,
      message,
      priority: options?.priority || template.defaultPriority,
      channels: options?.channels || template.defaultChannels,
      recipients,
      data: templateData,
      scheduledFor: options?.scheduledFor,
      expiresAt: options?.expiresAt
    });
  }

  /**
   * إرسال عبر قناة محددة
   */
  private async sendViaChannel(
    channel: NotificationChannel,
    data: NotificationData
  ): Promise<void> {
    switch (channel) {
      case 'IN_APP':
        await this.sendInAppNotification(data);
        break;
      case 'EMAIL':
        await this.sendEmailNotification(data);
        break;
      case 'SMS':
        await this.sendSMSNotification(data);
        break;
      case 'PUSH':
        await this.sendPushNotification(data);
        break;
      case 'WEBHOOK':
        await this.sendWebhookNotification(data);
        break;
    }
  }

  /**
   * إرسال إشعار داخل التطبيق
   */
  private async sendInAppNotification(data: NotificationData): Promise<void> {
    // الإشعار موجود بالفعل في قاعدة البيانات
    // يمكن إضافة WebSocket هنا للإشعارات الفورية
    logger.debug('In-app notification sent', { type: data.type });
  }

  /**
   * إرسال إشعار بالبريد الإلكتروني
   */
  private async sendEmailNotification(data: NotificationData): Promise<void> {
    // تكامل مع خدمة البريد الإلكتروني
    // يمكن استخدام SendGrid, AWS SES, أو SMTP
    
    logger.info('Email notification sent', {
      type: data.type,
      recipients: data.recipients.length,
      subject: data.title
    });

    // مثال على التكامل مع SMTP
    if (process.env.EMAIL_SERVER_HOST) {
      try {
        // هنا يمكن إضافة كود إرسال البريد الإلكتروني الفعلي
        console.log(`📧 Email sent: ${data.title} to ${data.recipients.join(', ')}`);
      } catch (error) {
        logger.error('Failed to send email notification', error);
      }
    }
  }

  /**
   * إرسال إشعار SMS
   */
  private async sendSMSNotification(data: NotificationData): Promise<void> {
    // تكامل مع خدمة SMS مثل Twilio أو AWS SNS
    
    logger.info('SMS notification sent', {
      type: data.type,
      recipients: data.recipients.length
    });

    // مثال على التكامل
    console.log(`📱 SMS sent: ${data.message} to ${data.recipients.join(', ')}`);
  }

  /**
   * إرسال إشعار Push
   */
  private async sendPushNotification(data: NotificationData): Promise<void> {
    // تكامل مع خدمة Push مثل Firebase Cloud Messaging
    
    logger.info('Push notification sent', {
      type: data.type,
      recipients: data.recipients.length
    });

    console.log(`🔔 Push notification sent: ${data.title}`);
  }

  /**
   * إرسال إشعار Webhook
   */
  private async sendWebhookNotification(data: NotificationData): Promise<void> {
    // إرسال إلى webhook URLs محددة
    const webhookUrl = process.env.NOTIFICATION_WEBHOOK_URL;
    
    if (webhookUrl) {
      try {
        const response = await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: data.type,
            title: data.title,
            message: data.message,
            priority: data.priority,
            timestamp: new Date().toISOString(),
            data: data.data
          })
        });

        if (!response.ok) {
          throw new Error(`Webhook failed with status: ${response.status}`);
        }

        logger.info('Webhook notification sent', {
          type: data.type,
          url: webhookUrl,
          status: response.status
        });

      } catch (error) {
        logger.error('Failed to send webhook notification', error);
      }
    }
  }

  /**
   * استبدال المتغيرات في القالب
   */
  private interpolateTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match;
    });
  }

  /**
   * تهيئة قوالب الإشعارات
   */
  private initializeTemplates(): void {
    this.templates.set('INVOICE_CREATED', {
      type: 'INVOICE_CREATED',
      titleTemplate: 'فاتورة جديدة تم إنشاؤها',
      messageTemplate: 'تم إنشاء فاتورة جديدة رقم {{invoiceNumber}} للعميل {{customerName}} بمبلغ {{amount}} درهم',
      defaultPriority: 'MEDIUM',
      defaultChannels: ['IN_APP', 'EMAIL']
    });

    this.templates.set('INVOICE_PAID', {
      type: 'INVOICE_PAID',
      titleTemplate: 'تم دفع الفاتورة',
      messageTemplate: 'تم دفع الفاتورة رقم {{invoiceNumber}} بمبلغ {{amount}} درهم',
      defaultPriority: 'MEDIUM',
      defaultChannels: ['IN_APP', 'EMAIL']
    });

    this.templates.set('INVOICE_OVERDUE', {
      type: 'INVOICE_OVERDUE',
      titleTemplate: 'فاتورة متأخرة الدفع',
      messageTemplate: 'الفاتورة رقم {{invoiceNumber}} للعميل {{customerName}} متأخرة {{daysPastDue}} يوم',
      defaultPriority: 'HIGH',
      defaultChannels: ['IN_APP', 'EMAIL', 'SMS']
    });

    this.templates.set('LOW_STOCK', {
      type: 'LOW_STOCK',
      titleTemplate: 'تنبيه مخزون منخفض',
      messageTemplate: 'المنتج {{productName}} وصل إلى مستوى مخزون منخفض ({{currentStock}} متبقي)',
      defaultPriority: 'HIGH',
      defaultChannels: ['IN_APP', 'EMAIL']
    });

    this.templates.set('CUSTOMER_ADDED', {
      type: 'CUSTOMER_ADDED',
      titleTemplate: 'عميل جديد',
      messageTemplate: 'تم إضافة عميل جديد: {{customerName}}',
      defaultPriority: 'LOW',
      defaultChannels: ['IN_APP']
    });

    this.templates.set('SYSTEM_ALERT', {
      type: 'SYSTEM_ALERT',
      titleTemplate: 'تنبيه نظام',
      messageTemplate: '{{alertMessage}}',
      defaultPriority: 'HIGH',
      defaultChannels: ['IN_APP', 'EMAIL', 'WEBHOOK']
    });

    this.templates.set('BACKUP_COMPLETED', {
      type: 'BACKUP_COMPLETED',
      titleTemplate: 'تم إنشاء النسخة الاحتياطية',
      messageTemplate: 'تم إنشاء نسخة احتياطية بنجاح بحجم {{backupSize}}',
      defaultPriority: 'LOW',
      defaultChannels: ['IN_APP']
    });

    this.templates.set('BACKUP_FAILED', {
      type: 'BACKUP_FAILED',
      titleTemplate: 'فشل في إنشاء النسخة الاحتياطية',
      messageTemplate: 'فشل في إنشاء النسخة الاحتياطية: {{errorMessage}}',
      defaultPriority: 'HIGH',
      defaultChannels: ['IN_APP', 'EMAIL', 'WEBHOOK']
    });

    this.templates.set('SECURITY_ALERT', {
      type: 'SECURITY_ALERT',
      titleTemplate: 'تنبيه أمني',
      messageTemplate: 'تم رصد نشاط أمني مشبوه: {{alertDetails}}',
      defaultPriority: 'URGENT',
      defaultChannels: ['IN_APP', 'EMAIL', 'SMS', 'WEBHOOK']
    });

    this.templates.set('MAINTENANCE', {
      type: 'MAINTENANCE',
      titleTemplate: 'صيانة النظام',
      messageTemplate: 'سيتم إجراء صيانة للنظام من {{startTime}} إلى {{endTime}}',
      defaultPriority: 'MEDIUM',
      defaultChannels: ['IN_APP', 'EMAIL']
    });
  }

  /**
   * الحصول على الإشعارات غير المقروءة للمستخدم
   */
  async getUnreadNotifications(userId: string, limit: number = 20): Promise<any[]> {
    try {
      const notifications = await prisma.notification.findMany({
        where: {
          isRead: false
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit
      });

      return notifications;
    } catch (error) {
      logger.error('Failed to get unread notifications', error, { userId });
      return [];
    }
  }

  /**
   * تحديد الإشعار كمقروء
   */
  async markAsRead(notificationId: number, userId: string): Promise<void> {
    try {
      await prisma.notification.update({
        where: { id: notificationId },
        data: { isRead: true }
      });

      logger.debug('Notification marked as read', {
        notificationId,
        userId
      });
    } catch (error) {
      logger.error('Failed to mark notification as read', error, {
        notificationId,
        userId
      });
    }
  }

  /**
   * تحديد جميع الإشعارات كمقروءة
   */
  async markAllAsRead(userId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: { isRead: false },
        data: { isRead: true }
      });

      logger.info('All notifications marked as read', { userId });
    } catch (error) {
      logger.error('Failed to mark all notifications as read', error, { userId });
    }
  }
}

// مثيل نظام الإشعارات العام
export const notificationSystem = new NotificationSystem();
