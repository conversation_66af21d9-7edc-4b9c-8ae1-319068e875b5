import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { prisma } from '@/lib/prisma';
import { compare } from 'bcryptjs';
import { logSecurityEvent } from '@/lib/security-logger';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: "البريد الإلكتروني", type: "email" },
        password: { label: "كلمة المرور", type: "password" }
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          await logSecurityEvent({
            type: 'LOGIN_FAILURE',
            ip: req?.headers?.['x-forwarded-for'] as string || 'unknown',
            details: 'Missing credentials'
          });
          return null;
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
            include: {
              role: {
                include: {
                  rolePermissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          });

          if (!user) {
            await logSecurityEvent({
              type: 'LOGIN_FAILURE',
              ip: req?.headers?.['x-forwarded-for'] as string || 'unknown',
              details: `User not found: ${credentials.email}`
            });
            return null;
          }

          // التحقق من كلمة المرور
          const isPasswordValid = await compare(credentials.password, user.password);

          if (!isPasswordValid) {
            await logSecurityEvent({
              type: 'LOGIN_FAILURE',
              userId: user.id,
              ip: req?.headers?.['x-forwarded-for'] as string || 'unknown',
              details: 'Invalid password'
            });
            return null;
          }

          // تسجيل نجاح تسجيل الدخول
          await logSecurityEvent({
            type: 'LOGIN_SUCCESS',
            userId: user.id,
            ip: req?.headers?.['x-forwarded-for'] as string || 'unknown'
          });

          const permissions = user.role?.rolePermissions.map(p => p.permission.permission) || [];

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role?.name || 'user',
            permissions
          };
        } catch (error) {
          await logSecurityEvent({
            type: 'LOGIN_FAILURE',
            ip: req?.headers?.['x-forwarded-for'] as string || 'unknown',
            details: `Login error: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
          return null;
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
    updateAge: 60 * 60, // 1 hour
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.permissions = user.permissions;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.permissions = token.permissions as string[];
      }
      return session;
    }
  },
  events: {
    async signOut({ token }) {
      if (token?.sub) {
        await logSecurityEvent({
          type: 'LOGOUT',
          userId: token.sub
        });
      }
    }
  }
};
