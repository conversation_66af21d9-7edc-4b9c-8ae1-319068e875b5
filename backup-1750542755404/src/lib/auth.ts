'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * Hook to protect client-side routes
 */
export function useAuth(options?: {
    requiredRole?: string,
    requiredPermissions?: string[]
}) {
    const { data: session, status } = useSession();
    const router = useRouter();
    const isLoading = status === 'loading';
    const isAuthenticated = status === 'authenticated';

    useEffect(() => {
        if (!isLoading && !isAuthenticated) {
            router.push('/auth/login');
            return;
        }

        if (isAuthenticated && session?.user) {
            // Check required role
            if (options?.requiredRole && session.user.role !== options?.requiredRole) {
                router.push('/unauthorized');
                return;
            }

            // Check required permissions
            if (options?.requiredPermissions && options.requiredPermissions.length > 0) {
                const hasAllPermissions = options.requiredPermissions.every(
                    permission => session.user.permissions?.includes(permission as any)
                );

                if (!hasAllPermissions) {
                    router.push('/unauthorized');
                    return;
                }
            }
        }
    }, [isLoading, isAuthenticated, session, router, options]);

    return {
        session,
        isLoading,
        isAuthenticated,
        user: session?.user
    };
}

/**
 * Check if the current user has a specific permission
 */
export function hasPermission(session: any, permission: string): boolean {
    if (!session?.user?.permissions) {
        return false;
    }
    return session.user.permissions.includes(permission);
}