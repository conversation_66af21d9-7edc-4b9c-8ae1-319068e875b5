import { createDatabaseBackup } from './backup-system';
import { cleanupOldData } from './database-optimizer';
import { cleanupOldSecurityLogs } from './security-logger';
import { logSecurityEvent } from './security-logger';

interface ScheduledTask {
  name: string;
  interval: number; // بالميلي ثانية
  lastRun?: Date;
  nextRun?: Date;
  isRunning: boolean;
  enabled: boolean;
  handler: () => Promise<void>;
}

/**
 * مدير المهام المجدولة
 */
export class TaskScheduler {
  private tasks: Map<string, ScheduledTask> = new Map();
  private timers: Map<string, NodeJS.Timeout> = new Map();

  /**
   * إضافة مهمة مجدولة
   */
  addTask(task: Omit<ScheduledTask, 'isRunning' | 'nextRun'>): void {
    const fullTask: ScheduledTask = {
      ...task,
      isRunning: false,
      nextRun: new Date(Date.now() + task.interval)
    };

    this.tasks.set(task.name, fullTask);

    if (task.enabled) {
      this.scheduleTask(task.name);
    }
  }

  /**
   * جدولة مهمة
   */
  private scheduleTask(taskName: string): void {
    const task = this.tasks.get(taskName);
    if (!task || !task.enabled) return;

    const timer = setTimeout(async () => {
      await this.runTask(taskName);
    }, task.interval);

    this.timers.set(taskName, timer);
  }

  /**
   * تشغيل مهمة
   */
  private async runTask(taskName: string): Promise<void> {
    const task = this.tasks.get(taskName);
    if (!task || task.isRunning) return;

    task.isRunning = true;
    task.lastRun = new Date();

    try {
      console.log(`Running scheduled task: ${taskName}`);
      await task.handler();
      console.log(`Completed scheduled task: ${taskName}`);
    } catch (error) {
      console.error(`Error in scheduled task ${taskName}:`, error);
      
      await logSecurityEvent({
        type: 'SYSTEM',
        details: `Scheduled task failed: ${taskName} - ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'MEDIUM'
      });
    } finally {
      task.isRunning = false;
      task.nextRun = new Date(Date.now() + task.interval);
      
      // جدولة التشغيل التالي
      if (task.enabled) {
        this.scheduleTask(taskName);
      }
    }
  }

  /**
   * تفعيل مهمة
   */
  enableTask(taskName: string): void {
    const task = this.tasks.get(taskName);
    if (task) {
      task.enabled = true;
      this.scheduleTask(taskName);
    }
  }

  /**
   * تعطيل مهمة
   */
  disableTask(taskName: string): void {
    const task = this.tasks.get(taskName);
    if (task) {
      task.enabled = false;
      const timer = this.timers.get(taskName);
      if (timer) {
        clearTimeout(timer);
        this.timers.delete(taskName);
      }
    }
  }

  /**
   * الحصول على حالة المهام
   */
  getTasksStatus(): Array<{
    name: string;
    enabled: boolean;
    isRunning: boolean;
    lastRun?: Date;
    nextRun?: Date;
    interval: number;
  }> {
    return Array.from(this.tasks.values()).map(task => ({
      name: task.name,
      enabled: task.enabled,
      isRunning: task.isRunning,
      lastRun: task.lastRun,
      nextRun: task.nextRun,
      interval: task.interval
    }));
  }

  /**
   * إيقاف جميع المهام
   */
  stopAll(): void {
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    this.timers.clear();
    
    for (const task of this.tasks.values()) {
      task.enabled = false;
    }
  }
}

// مثيل مدير المهام العام
export const taskScheduler = new TaskScheduler();

/**
 * تهيئة المهام المجدولة الافتراضية
 */
export function initializeScheduledTasks(): void {
  // النسخ الاحتياطية التلقائية - كل 24 ساعة
  taskScheduler.addTask({
    name: 'auto_backup',
    interval: 24 * 60 * 60 * 1000, // 24 ساعة
    enabled: process.env.BACKUP_ENABLED === 'true',
    handler: async () => {
      const result = await createDatabaseBackup({
        compressionEnabled: true,
        encryptionEnabled: true
      });

      if (result.success) {
        await logSecurityEvent({
          type: 'BACKUP_CREATED',
          details: `Automatic backup created successfully: ${result.filePath}`,
          severity: 'LOW'
        });
      } else {
        await logSecurityEvent({
          type: 'BACKUP_FAILED',
          details: `Automatic backup failed: ${result.error}`,
          severity: 'HIGH'
        });
      }
    }
  });

  // تنظيف البيانات القديمة - كل أسبوع
  taskScheduler.addTask({
    name: 'cleanup_old_data',
    interval: 7 * 24 * 60 * 60 * 1000, // أسبوع
    enabled: true,
    handler: async () => {
      const result = await cleanupOldData({
        securityLogsDays: 90,
        dryRun: false
      });

      if (result.success) {
        const totalCleaned = Object.values(result.cleaned).reduce((sum, count) => sum + count, 0);
        await logSecurityEvent({
          type: 'DATA_CLEANUP',
          details: `Cleaned up ${totalCleaned} old records`,
          severity: 'LOW'
        });
      }
    }
  });

  // تنظيف سجلات الأمان القديمة - كل شهر
  taskScheduler.addTask({
    name: 'cleanup_security_logs',
    interval: 30 * 24 * 60 * 60 * 1000, // شهر
    enabled: true,
    handler: async () => {
      await cleanupOldSecurityLogs(90); // الاحتفاظ بـ 90 يوم
    }
  });

  // فحص صحة النظام - كل ساعة
  taskScheduler.addTask({
    name: 'health_check',
    interval: 60 * 60 * 1000, // ساعة
    enabled: true,
    handler: async () => {
      const { performHealthCheck } = await import('./health-monitor');
      const health = await performHealthCheck();
      
      if (health.overall === 'unhealthy') {
        await logSecurityEvent({
          type: 'SYSTEM',
          details: `System health check failed: ${health.checks.filter(c => c.status === 'unhealthy').map(c => c.service).join(', ')}`,
          severity: 'HIGH'
        });
      }
    }
  });

  // تحليل أداء قاعدة البيانات - كل 6 ساعات
  taskScheduler.addTask({
    name: 'database_analysis',
    interval: 6 * 60 * 60 * 1000, // 6 ساعات
    enabled: true,
    handler: async () => {
      const { analyzeDatabasePerformance } = await import('./database-optimizer');
      const analysis = await analyzeDatabasePerformance();
      
      const criticalSuggestions = analysis.suggestions.filter(s => s.priority === 'critical');
      if (criticalSuggestions.length > 0) {
        await logSecurityEvent({
          type: 'SYSTEM',
          details: `Critical database performance issues detected: ${criticalSuggestions.length} suggestions`,
          severity: 'HIGH'
        });
      }
    }
  });

  console.log('Scheduled tasks initialized');
}

/**
 * تشغيل مهمة فورية
 */
export async function runTaskNow(taskName: string): Promise<{ success: boolean; error?: string }> {
  const task = taskScheduler['tasks'].get(taskName);
  
  if (!task) {
    return { success: false, error: 'Task not found' };
  }

  if (task.isRunning) {
    return { success: false, error: 'Task is already running' };
  }

  try {
    await taskScheduler['runTask'](taskName);
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * الحصول على إحصائيات المهام
 */
export function getTaskStatistics(): {
  totalTasks: number;
  enabledTasks: number;
  runningTasks: number;
  tasksWithErrors: number;
} {
  const tasks = taskScheduler.getTasksStatus();
  
  return {
    totalTasks: tasks.length,
    enabledTasks: tasks.filter(t => t.enabled).length,
    runningTasks: tasks.filter(t => t.isRunning).length,
    tasksWithErrors: 0 // يمكن تحسينه لتتبع الأخطاء
  };
}
