/**
 * مكتبة Prisma - توفر اتصالًا بقاعدة البيانات
 * Prisma library - Provides database connection
 */
import { PrismaClient } from '@prisma/client';

// تجنب إنشاء مثيلات متعددة من Prisma خلال التطوير
// Avoid creating multiple Prisma instances during development
const globalForPrisma = global as unknown as {
  prisma: PrismaClient | undefined
};

// إنشاء مثيل واحد من PrismaClient واستخدامه في جميع أنحاء التطبيق
// Create a single PrismaClient instance and use it throughout the app
export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  });

// حفظ المثيل في الكائن العالمي خلال التطوير
// Save the instance in the global object during development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}
