import { NextRequest, NextResponse } from 'next/server';
import { verify } from 'jsonwebtoken';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth-options';
import { rateLimit, RATE_LIMITS } from '@/lib/rate-limit';
import { validateInput } from '@/lib/validation';
import { logSecurityEvent } from '@/lib/security-logger';

// إعادة تصدير RATE_LIMITS للاستخدام في ملفات أخرى
export { RATE_LIMITS };

interface ApiUser {
  id: string;
  name: string;
  email: string;
  permissions: string[];
  role?: string;
}

/**
 * التحقق من صحة رمز API
 * @param request طلب Next.js
 * @returns بيانات المستخدم إذا كان الرمز صالحًا، وإلا يعيد null
 */
export function verifyApiToken(request: NextRequest): ApiUser | null {
  try {
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.split(' ')[1];
    const secret = process.env.API_TOKEN_SECRET ?? process.env.NEXTAUTH_SECRET;

    if (!secret) {
      console.error('API_TOKEN_SECRET or NEXTAUTH_SECRET is not defined');
      return null;
    }

    const decoded = verify(token, secret) as ApiUser;
    return decoded;
  } catch (error) {
    console.error('API token verification error:', error);
    return null;
  }
}

/**
 * حماية مسارات API مع تحسينات الأمان
 * Protect API routes with enhanced security
 */
export function protectApiRoute(
  handler: (req: NextRequest, user: any) => Promise<NextResponse>,
  options?: {
    requiredPermissions?: string | string[];
    requiredRole?: string;
    rateLimit?: { requests: number; windowMs: number };
    validateInput?: boolean;
  }
) {
  return async (req: NextRequest) => {
    try {
      // تطبيق Rate Limiting
      if (options?.rateLimit) {
        const rateLimitResult = await rateLimit(req, options.rateLimit);
        if (!rateLimitResult.success) {
          await logSecurityEvent({
            type: 'RATE_LIMIT_EXCEEDED',
            ip: getClientIP(req),
            userAgent: req.headers.get('user-agent') || '',
            details: `Rate limit exceeded: ${rateLimitResult.remaining} requests remaining`
          });

          return NextResponse.json(
            { error: 'تم تجاوز الحد المسموح من الطلبات' },
            { status: 429, headers: { 'Retry-After': '60' } }
          );
        }
      }

      // التحقق من الجلسة
      const session = await getServerSession(authOptions);

      if (!session || !session.user) {
        await logSecurityEvent({
          type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
          ip: getClientIP(req),
          userAgent: req.headers.get('user-agent') || '',
          path: req.nextUrl.pathname
        });

        return NextResponse.json(
          { error: 'غير مصرح لك بالوصول' },
          { status: 401 }
        );
      }

      // التحقق من الدور المطلوب
      if (options?.requiredRole) {
        const userRole = session.user.role;
        if (userRole !== options.requiredRole && userRole !== 'admin') {
          await logSecurityEvent({
            type: 'INSUFFICIENT_ROLE',
            userId: session.user.id,
            ip: getClientIP(req),
            details: `Required role: ${options.requiredRole}, User role: ${userRole}`
          });

          return NextResponse.json(
            { error: 'ليس لديك الدور المطلوب للوصول' },
            { status: 403 }
          );
        }
      }

      // التحقق من الصلاحيات إذا كانت مطلوبة
      if (options?.requiredPermissions) {
        const userPermissions = session.user?.permissions || [];

        if (Array.isArray(options.requiredPermissions)) {
          const hasAnyPermission = options.requiredPermissions.some(
            permission => userPermissions.includes(permission)
          );

          if (!hasAnyPermission) {
            await logSecurityEvent({
              type: 'INSUFFICIENT_PERMISSIONS',
              userId: session.user.id,
              ip: getClientIP(req),
              details: `Required permissions: ${options.requiredPermissions.join(', ')}`
            });

            return NextResponse.json(
              { error: 'ليس لديك الصلاحيات المطلوبة' },
              { status: 403 }
            );
          }
        } else {
          if (!userPermissions.includes(options.requiredPermissions)) {
            await logSecurityEvent({
              type: 'INSUFFICIENT_PERMISSIONS',
              userId: session.user.id,
              ip: getClientIP(req),
              details: `Required permission: ${options.requiredPermissions}`
            });

            return NextResponse.json(
              { error: 'ليس لديك الصلاحيات المطلوبة' },
              { status: 403 }
            );
          }
        }
      }

      // التحقق من صحة المدخلات إذا كان مطلوباً
      if (options?.validateInput && req.method !== 'GET') {
        const body = await req.json().catch(() => null);
        if (body) {
          const validationResult = validateInput(body, req.nextUrl.pathname);
          if (!validationResult.isValid) {
            await logSecurityEvent({
              type: 'INVALID_INPUT',
              userId: session.user.id,
              ip: getClientIP(req),
              details: `Validation errors: ${validationResult.errors.join(', ')}`
            });

            return NextResponse.json(
              { error: 'بيانات غير صالحة', details: validationResult.errors },
              { status: 400 }
            );
          }
        }
      }

      // تسجيل الوصول الناجح
      await logSecurityEvent({
        type: 'API_ACCESS',
        userId: session.user.id,
        ip: getClientIP(req),
        path: req.nextUrl.pathname,
        method: req.method
      });

      // تنفيذ المعالج
      return handler(req, session.user);
    } catch (error) {
      await logSecurityEvent({
        type: 'API_ERROR',
        ip: getClientIP(req),
        details: error instanceof Error ? error.message : 'Unknown error',
        path: req.nextUrl.pathname
      });

      console.error('خطأ في حماية مسار API:', error);
      return NextResponse.json(
        { error: 'حدث خطأ أثناء التحقق من الصلاحيات' },
        { status: 500 }
      );
    }
  };
}

/**
 * الحصول على عنوان IP الخاص بالعميل
 */
function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const real = req.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (real) {
    return real.trim();
  }

  return 'unknown';
}
