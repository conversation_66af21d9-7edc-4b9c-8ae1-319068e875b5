/**
 * نظام النسخ الاحتياطي التلقائي
 * Automatic Backup System
 */

import { SecureBackupManager } from './secure-backup';
import { logSecurityEvent } from './security-logger';
import { prisma } from './prisma';

/**
 * مدير النسخ الاحتياطي التلقائي
 * Automatic Backup Manager
 */
export class AutoBackupManager {
  private static instance: AutoBackupManager;
  private backupInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // إعدادات افتراضية
  private readonly DEFAULT_SETTINGS = {
    enabled: true,
    fullBackupInterval: 24 * 60 * 60 * 1000, // 24 hours
    incrementalBackupInterval: 4 * 60 * 60 * 1000, // 4 hours
    maxBackupsToKeep: 30,
    maxBackupAge: 90 * 24 * 60 * 60 * 1000, // 90 days
    compressionLevel: 6,
    encryptionEnabled: true
  };

  private constructor() {}

  static getInstance(): AutoBackupManager {
    if (!this.instance) {
      this.instance = new AutoBackupManager();
    }
    return this.instance;
  }

  /**
   * بدء نظام النسخ الاحتياطي التلقائي
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Auto backup system is already running');
      return;
    }

    try {
      const settings = await this.getBackupSettings();
      
      if (!settings.enabled) {
        console.log('Auto backup is disabled');
        return;
      }

      // بدء النسخ الاحتياطي التلقائي
      this.scheduleBackups(settings);
      
      // بدء تنظيف النسخ القديمة
      this.scheduleCleanup(settings);
      
      this.isRunning = true;
      
      await logSecurityEvent({
        type: 'AUTO_BACKUP_STARTED',
        details: 'Automatic backup system started',
        severity: 'LOW'
      });

      console.log('Auto backup system started successfully');
    } catch (error) {
      console.error('Failed to start auto backup system:', error);
      throw error;
    }
  }

  /**
   * إيقاف نظام النسخ الاحتياطي التلقائي
   */
  async stop(): Promise<void> {
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
      this.backupInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.isRunning = false;

    await logSecurityEvent({
      type: 'AUTO_BACKUP_STOPPED',
      details: 'Automatic backup system stopped',
      severity: 'LOW'
    });

    console.log('Auto backup system stopped');
  }

  /**
   * جدولة النسخ الاحتياطية
   */
  private scheduleBackups(settings: any): void {
    // جدولة النسخ الاحتياطية الكاملة
    this.backupInterval = setInterval(async () => {
      await this.performScheduledBackup(settings);
    }, settings.incrementalBackupInterval);

    // تنفيذ نسخة احتياطية فورية عند البدء
    setTimeout(() => {
      this.performScheduledBackup(settings);
    }, 5000); // بعد 5 ثوان من البدء
  }

  /**
   * جدولة تنظيف النسخ القديمة
   */
  private scheduleCleanup(settings: any): void {
    // تنظيف يومي للنسخ القديمة
    this.cleanupInterval = setInterval(async () => {
      await this.cleanupOldBackups(settings);
    }, 24 * 60 * 60 * 1000); // كل 24 ساعة

    // تنظيف فوري عند البدء
    setTimeout(() => {
      this.cleanupOldBackups(settings);
    }, 10000); // بعد 10 ثوان من البدء
  }

  /**
   * تنفيذ النسخ الاحتياطي المجدول
   */
  private async performScheduledBackup(settings: any): Promise<void> {
    try {
      const now = new Date();
      const lastFullBackup = await this.getLastFullBackup();
      
      // تحديد نوع النسخة الاحتياطية المطلوبة
      const shouldCreateFullBackup = !lastFullBackup || 
        (now.getTime() - lastFullBackup.createdAt.getTime()) >= settings.fullBackupInterval;

      if (shouldCreateFullBackup) {
        await this.createFullBackup();
      } else {
        await this.createIncrementalBackup(lastFullBackup.createdAt);
      }

    } catch (error) {
      console.error('Scheduled backup failed:', error);
      await logSecurityEvent({
        type: 'AUTO_BACKUP_FAILED',
        details: `Scheduled backup failed: ${error.message}`,
        severity: 'HIGH'
      });
    }
  }

  /**
   * إنشاء نسخة احتياطية كاملة
   */
  private async createFullBackup(): Promise<void> {
    try {
      const result = await SecureBackupManager.createFullBackup('system');
      
      await logSecurityEvent({
        type: 'AUTO_BACKUP_CREATED',
        details: `Automatic full backup created: ${result.filename} (${this.formatBytes(result.size)})`,
        severity: 'LOW'
      });

      console.log(`Automatic full backup created: ${result.filename}`);
    } catch (error) {
      console.error('Failed to create automatic full backup:', error);
      throw error;
    }
  }

  /**
   * إنشاء نسخة احتياطية تزايدية
   */
  private async createIncrementalBackup(lastBackupDate: Date): Promise<void> {
    try {
      const result = await SecureBackupManager.createIncrementalBackup('system', lastBackupDate);
      
      await logSecurityEvent({
        type: 'AUTO_INCREMENTAL_BACKUP_CREATED',
        details: `Automatic incremental backup created: ${result.filename} (${this.formatBytes(result.size)})`,
        severity: 'LOW'
      });

      console.log(`Automatic incremental backup created: ${result.filename}`);
    } catch (error) {
      // إذا لم تكن هناك تغييرات، لا نعتبر هذا خطأ
      if (error.message.includes('No changes found')) {
        console.log('No changes found since last backup - skipping incremental backup');
        return;
      }
      
      console.error('Failed to create automatic incremental backup:', error);
      throw error;
    }
  }

  /**
   * تنظيف النسخ الاحتياطية القديمة
   */
  private async cleanupOldBackups(settings: any): Promise<void> {
    try {
      const cutoffDate = new Date(Date.now() - settings.maxBackupAge);
      
      // حذف النسخ القديمة
      const oldBackups = await prisma.backup.findMany({
        where: {
          createdAt: { lt: cutoffDate },
          type: { not: 'RESTORE_POINT' } // الاحتفاظ بنقاط الاستعادة
        },
        orderBy: { createdAt: 'asc' }
      });

      for (const backup of oldBackups) {
        await SecureBackupManager.deleteBackup(backup.id.toString(), 'system');
      }

      // الاحتفاظ بعدد محدود من النسخ الحديثة
      const recentBackups = await prisma.backup.findMany({
        where: {
          type: { not: 'RESTORE_POINT' }
        },
        orderBy: { createdAt: 'desc' },
        skip: settings.maxBackupsToKeep
      });

      for (const backup of recentBackups) {
        await SecureBackupManager.deleteBackup(backup.id.toString(), 'system');
      }

      if (oldBackups.length > 0 || recentBackups.length > 0) {
        await logSecurityEvent({
          type: 'AUTO_BACKUP_CLEANUP',
          details: `Cleaned up ${oldBackups.length + recentBackups.length} old backups`,
          severity: 'LOW'
        });

        console.log(`Cleaned up ${oldBackups.length + recentBackups.length} old backups`);
      }

    } catch (error) {
      console.error('Failed to cleanup old backups:', error);
      await logSecurityEvent({
        type: 'AUTO_BACKUP_CLEANUP_FAILED',
        details: `Failed to cleanup old backups: ${error.message}`,
        severity: 'MEDIUM'
      });
    }
  }

  /**
   * الحصول على آخر نسخة احتياطية كاملة
   */
  private async getLastFullBackup(): Promise<any | null> {
    return await prisma.backup.findFirst({
      where: { type: 'FULL' },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * الحصول على إعدادات النسخ الاحتياطي
   */
  private async getBackupSettings(): Promise<any> {
    try {
      const settings = await prisma.setting.findMany({
        where: {
          key: {
            startsWith: 'backup_'
          }
        }
      });

      const backupSettings = { ...this.DEFAULT_SETTINGS };

      settings.forEach(setting => {
        const key = setting.key.replace('backup_', '');
        let value: any = setting.value;

        // تحويل القيم حسب النوع
        if (key.includes('Interval') || key.includes('Age')) {
          value = parseInt(value) || backupSettings[key as keyof typeof backupSettings];
        } else if (key.includes('enabled') || key.includes('Enabled')) {
          value = value === 'true';
        } else if (key.includes('Level') || key.includes('Keep')) {
          value = parseInt(value) || backupSettings[key as keyof typeof backupSettings];
        }

        (backupSettings as any)[key] = value;
      });

      return backupSettings;
    } catch (error) {
      console.error('Failed to load backup settings, using defaults:', error);
      return this.DEFAULT_SETTINGS;
    }
  }

  /**
   * تحديث إعدادات النسخ الاحتياطي
   */
  async updateBackupSettings(newSettings: Partial<typeof this.DEFAULT_SETTINGS>): Promise<void> {
    try {
      for (const [key, value] of Object.entries(newSettings)) {
        await prisma.setting.upsert({
          where: { key: `backup_${key}` },
          update: { value: String(value) },
          create: {
            key: `backup_${key}`,
            value: String(value),
            category: 'backup'
          }
        });
      }

      // إعادة تشغيل النظام بالإعدادات الجديدة
      if (this.isRunning) {
        await this.stop();
        await this.start();
      }

      await logSecurityEvent({
        type: 'BACKUP_SETTINGS_UPDATED',
        details: `Backup settings updated: ${Object.keys(newSettings).join(', ')}`,
        severity: 'LOW'
      });

    } catch (error) {
      console.error('Failed to update backup settings:', error);
      throw error;
    }
  }

  /**
   * الحصول على حالة النظام
   */
  getStatus(): {
    isRunning: boolean;
    nextBackup: Date | null;
    lastBackup: Date | null;
  } {
    return {
      isRunning: this.isRunning,
      nextBackup: null, // يمكن حسابها بناءً على الجدولة
      lastBackup: null  // يمكن الحصول عليها من قاعدة البيانات
    };
  }

  /**
   * تنسيق حجم الملف
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// تصدير مثيل واحد للاستخدام العام
export const autoBackupManager = AutoBackupManager.getInstance();
