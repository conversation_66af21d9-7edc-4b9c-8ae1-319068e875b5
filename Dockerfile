# ملف Dockerfile لمشروع أمين بلس
# Dockerfile for Amin Plus project

# استخدام صورة Node.js الرسمية
# Use official Node.js image
FROM node:18-alpine AS base

# تعيين دليل العمل
# Set working directory
WORKDIR /app

# تثبيت التبعيات الأساسية
# Install basic dependencies
RUN apk add --no-cache libc6-compat
RUN npm install -g pnpm

# مرحلة التبعيات
# Dependencies stage
FROM base AS deps
COPY package.json pnpm-lock.yaml* ./
RUN pnpm install --frozen-lockfile

# مرحلة البناء
# Build stage
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# بناء التطبيق
# Build the app
ENV NEXT_TELEMETRY_DISABLED 1
RUN pnpm build

# مرحلة الإنتاج
# Production stage
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# إنشاء مستخدم غير جذري
# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# نسخ الملفات اللازمة
# Copy necessary files
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# تعريض المنفذ
# Expose port
EXPOSE 3000

# تعيين متغيرات البيئة
# Set environment variables
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# تشغيل التطبيق
# Run the app
CMD ["node", "server.js"]
