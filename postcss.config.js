// postcss.config.js

/**
 * إعداد PostCSS الرسمي
 * هذا الملف يُستخدم لمعالجة CSS باستخدام TailwindCSS و Autoprefixer
 * ويمكن توسيعه لإضافة دعم مستقبلية مثل postcss-preset-env
 */

module.exports = {
  plugins: {
    tailwindcss: {}, // معالجة TailwindCSS
    autoprefixer: {}, // إضافة بادئات تلقائية للتوافق مع المتصفحات
    // 'postcss-preset-env': {},  // (اختياري) لدعم ميزات CSS المستقبلية
  },
};
/**
 * ملاحظات:
 * - تأكد من تثبيت الحزم المطلوبة باستخدام npm أو yarn.
 * - يمكنك إضافة المزيد من الإضافات حسب الحاجة.
 * - تأكد من أن لديك ملف tailwind.config.js في مشروعك لتخصيص إعدادات TailwindCSS.
 */
