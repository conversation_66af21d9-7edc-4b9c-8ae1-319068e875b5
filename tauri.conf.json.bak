{"build": {"beforeBuildCommand": "npm run build:full", "beforeDevCommand": "npm run dev", "devPath": "http://localhost:3000", "distDir": "../out"}, "package": {"productName": "<PERSON>in Plus | أمين بلس", "version": "1.0.0"}, "tauri": {"windows": [{"title": "أمين بلس | Amin Plus - نظام إدارة المبيعات والمخازن | Integrated Sales & Inventory Management System", "width": 1280, "height": 800, "resizable": true, "fullscreen": false, "center": true}], "security": {"csp": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline';"}, "bundle": {"active": true, "identifier": "com.mnylyza.aminplus", "icon": ["icons/icon.icns", "icons/icon.ico", "icons/icon.png"], "resources": [], "targets": ["msi", "dmg", "appimage"], "windows": {"publisher": "MNYLYZA", "installerIcon": "icons/icon.ico", "requestedExecutionLevel": "requireAdministrator"}}, "allowlist": {"all": true}}}