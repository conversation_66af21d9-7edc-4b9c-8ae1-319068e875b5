# Dependencies
node_modules/
.pnpm-store/

# Build outputs
.next/
dist/
build/
out/

# TypeScript files (تجاهل مؤقت)
**/*.ts
**/*.tsx
**/*.d.ts

# Config files
*.config.js
*.config.ts
*.config.mjs
next.config.js
next.config.mjs
tailwind.config.js
tailwind.config.ts
jest.config.js
jest.config.ts

# Generated files
.env*
.vercel/
coverage/
.nyc_output/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Prisma
prisma/migrations/
prisma/dev.db*

# Backups
backups/
