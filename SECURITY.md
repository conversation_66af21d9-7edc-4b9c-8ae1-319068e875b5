# سياسة الأمان

## الإبلاغ عن ثغرة أمنية

نحن نأخذ أمان تطبيق أمين بلس على محمل الجد. إذا اكتشفت ثغرة أمنية، يرجى إبلاغنا بها عبر البريد الإلكتروني على العنوان التالي:

```
<EMAIL>
```

يرجى تضمين المعلومات التالية في تقريرك:

1. نوع الثغرة
2. المسار أو الموقع الذي تم اكتشاف الثغرة فيه
3. أي خطوات للاستنساخ أو إثبات المفهوم
4. التأثير المحتمل للثغرة

## سياسة الإفصاح المسؤول

نحن نتبع سياسة الإفصاح المسؤول:

1. سنؤكد استلام تقريرك في غضون 48 ساعة.
2. سنتحقق من التقرير ونتواصل معك للحصول على مزيد من المعلومات إذا لزم الأمر.
3. سنعمل على إصلاح الثغرة في أقرب وقت ممكن.
4. بمجرد إصلاح الثغرة، سنقوم بإصدار تحديث للتطبيق.
5. سنقوم بالاعتراف بمساهمتك في اكتشاف الثغرة (ما لم تفضل عدم ذكر اسمك).

## ممارسات الأمان المتبعة

نحن نتبع ممارسات الأمان التالية في تطوير تطبيق أمين بلس:

### أمان التطبيق

1. **المصادقة والتفويض**:
   - استخدام NextAuth.js للمصادقة الآمنة.
   - تنفيذ نظام أدوار وصلاحيات متعدد المستويات.
   - استخدام JWT مع توقيع آمن.

2. **أمان البيانات**:
   - تشفير البيانات الحساسة في قاعدة البيانات.
   - استخدام HTTPS لجميع الاتصالات.
   - تنفيذ آليات للنسخ الاحتياطي واستعادة البيانات.

3. **أمان الكود**:
   - فحص الكود بانتظام بحثًا عن الثغرات الأمنية.
   - استخدام أدوات التحليل الثابت للكود.
   - تحديث التبعيات بانتظام.

4. **أمان تطبيق سطح المكتب**:
   - تنفيذ آليات للتحقق من سلامة التطبيق.
   - استخدام التوقيع الرقمي للتطبيق.
   - تنفيذ آليات للتحديث الآمن.

### أمان البنية التحتية

1. **أمان الخادم**:
   - تحديث نظام التشغيل والبرامج بانتظام.
   - تنفيذ جدار حماية وإعدادات أمان مناسبة.
   - مراقبة الخادم بانتظام بحثًا عن النشاط المشبوه.

2. **أمان قاعدة البيانات**:
   - تقييد الوصول إلى قاعدة البيانات.
   - تنفيذ آليات للنسخ الاحتياطي واستعادة البيانات.
   - تشفير البيانات الحساسة.

3. **أمان الشبكة**:
   - استخدام HTTPS لجميع الاتصالات.
   - تنفيذ جدار حماية وإعدادات أمان مناسبة.
   - مراقبة الشبكة بانتظام بحثًا عن النشاط المشبوه.

## الإصدارات المدعومة

نحن نقدم الدعم الأمني للإصدارات التالية من تطبيق أمين بلس:

| الإصدار | الدعم            |
| ------- | ---------------- |
| 1.x.x   | ✅ مدعوم         |
| < 1.0.0 | ❌ غير مدعوم     |

## الاتصال

إذا كان لديك أي أسئلة أو استفسارات حول سياسة الأمان، يرجى التواصل معنا عبر البريد الإلكتروني على العنوان التالي:

```
<EMAIL>
```
