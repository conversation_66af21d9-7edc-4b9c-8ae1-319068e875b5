/**
 * إعدادات Jest لمشروع أمين بلس
 * Jest configuration for Amin Plus project
 */
const nextJest = require('next/jest');

// Disable Console Ninja to avoid warnings
process.env.CONSOLE_NINJA_DISABLE = 'true';

const createJestConfig = nextJest({
  // مسار تطبيق Next.js لتحميل ملفات next.config.js و .env في بيئة الاختبار
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// إضافة أي إعدادات مخصصة لـ Jest
// Add any custom config to be passed to Jest
const customJestConfig = {
  // ملفات الإعداد التي يتم تحميلها بعد إعداد بيئة الاختبار
  // Setup files that are loaded after the test environment is set up
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],

  // بيئة الاختبار
  // Test environment
  testEnvironment: 'jest-environment-jsdom',

  // تعيين أسماء الوحدات
  // Module name mapping
  moduleNameMapper: {
    // التعامل مع أسماء الوحدات المستعارة
    // Handle module aliases
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    '^@/utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@/styles/(.*)$': '<rootDir>/src/styles/$1',
    '^@/public/(.*)$': '<rootDir>/public/$1',
    '^@/tests/(.*)$': '<rootDir>/tests/$1',
  },

  // تم نقل هذا الإعداد إلى أسفل الملف
  // This setting has been moved to the bottom of the file

  // تحويل ملفات الاختبار
  // Transform test files
  transform: {
    // استخدام babel-jest لتحويل الاختبارات باستخدام next/babel
    // Use babel-jest to transpile tests with the next/babel preset
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },

  // أنماط التحويل التي يتم تجاهلها
  // Transform patterns to ignore
  transformIgnorePatterns: ['/node_modules/', '^.+\\.module\\.(css|sass|scss)$'],

  // جمع تغطية الاختبارات
  // Collect test coverage
  collectCoverage: false,

  // الملفات التي يتم جمع تغطية الاختبارات منها
  // Files to collect coverage from
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/*.test.{js,jsx,ts,tsx}',
    '!src/**/*.spec.{js,jsx,ts,tsx}',
    '!src/**/__tests__/**',
    '!src/**/__mocks__/**',
    '!**/node_modules/**',
    '!**/.next/**',
  ],

  // حد أدنى للتغطية
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 10,
      functions: 10,
      lines: 10,
      statements: 10,
    },
  },

  // مراقبة التغييرات
  // Watch for changes
  // watchPlugins: ['jest-watch-typeahead/filename', 'jest-watch-typeahead/testname'],

  // الوقت المستغرق للاختبار
  // Test timeout
  testTimeout: 30000,

  // تجاهل الاختبارات
  // Skip tests
  testPathIgnorePatterns: [
    '/node_modules/',
    '/.next/',
    '/out/',
    '/coverage/',
    '/public/',
    '/cypress/',
    '/tests/e2e/', // استبعاد اختبارات Playwright
  ],

  // أنماط الاختبارات
  // Test patterns
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
