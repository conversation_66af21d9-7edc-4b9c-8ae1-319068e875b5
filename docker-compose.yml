# ملف docker-compose.yml لمشروع أمين بلس
# docker-compose.yml file for Amin Plus project

version: '3.8'

services:
  # خدمة التطبيق
  # App service
  app:
    container_name: amin-plus-app
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    depends_on:
      - db
    networks:
      - amin-plus-network

  # خدمة قاعدة البيانات
  # Database service
  db:
    container_name: amin-plus-db
    image: postgres:14-alpine
    restart: always
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-aminplus}
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - amin-plus-network

  # خدمة Adminer (واجهة إدارة قاعدة البيانات)
  # Adminer service (database management interface)
  adminer:
    container_name: amin-plus-adminer
    image: adminer:latest
    restart: always
    ports:
      - "8080:8080"
    depends_on:
      - db
    networks:
      - amin-plus-network

# تعريف الشبكات
# Define networks
networks:
  amin-plus-network:
    driver: bridge

# تعريف المجلدات
# Define volumes
volumes:
  postgres-data:
