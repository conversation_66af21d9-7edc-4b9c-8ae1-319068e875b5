# 🚀 نظام أمين بلس للمحاسبة - الإصدار المتقدم

<div align="center">
  <h1>🇦🇪 أمين بلس - Amin Plus</h1>
  <h3>نظام محاسبة شامل مع قاعدة بيانات حقيقية</h3>
  <h3>Comprehensive Accounting System with Real Database</h3>

  [![Next.js](https://img.shields.io/badge/Next.js-15.3.1-black)](https://nextjs.org/)
  [![Prisma](https://img.shields.io/badge/Prisma-Latest-blue)](https://prisma.io/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://typescriptlang.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.0-38B2AC)](https://tailwindcss.com/)
</div>

## 🎯 الميزات الجديدة والمتقدمة

### 💾 **قاعدة البيانات الحقيقية**
- ✅ **Prisma ORM** مع PostgreSQL/SQLite
- ✅ **Migration System** للتحديثات الآمنة
- ✅ **Data Validation** على مستوى قاعدة البيانات
- ✅ **Backup تلقائي** وحماية البيانات
- ✅ **Performance Optimization** مع Indexing

### 🔐 **نظام مصادقة متقدم**
- ✅ **NextAuth.js** مع قاعدة البيانات
- ✅ **Role-Based Access Control (RBAC)**
- ✅ **Password Hashing** مع bcrypt
- ✅ **Session Management** محكم
- ✅ **Permission System** مرن

### 📊 **إدارة شاملة ومحسنة**
- ✅ **إدارة العملاء**: CRUD كامل مع البحث والتصفية
- ✅ **إدارة المنتجات**: كتالوج متقدم مع SKU تلقائي
- ✅ **إدارة الفواتير**: نظام فواتير احترافي مع ترقيم تلقائي
- ✅ **التقارير والإحصائيات**: تقارير مالية مفصلة ورسوم بيانية
- ✅ **Pagination** و **Search** متقدم

### 🎨 **واجهة المستخدم المحسنة**
- ✅ تصميم عصري ومتجاوب
- ✅ دعم اللغة العربية والإنجليزية
- ✅ **Loading States** و **Error Handling**
- ✅ **Toast Notifications** للتفاعل
- ✅ **Form Validation** شامل

## 🛠️ التقنيات المستخدمة

### **Frontend**
- **Next.js 15.3.1** (أحدث إصدار)
- **React 18** مع TypeScript
- **Tailwind CSS** للتصميم
- **Lucide React** للأيقونات

### **Backend & Database**
- **Prisma ORM** لإدارة قاعدة البيانات
- **PostgreSQL** (للإنتاج) / **SQLite** (للتطوير)
- **NextAuth.js** للمصادقة
- **bcryptjs** لتشفير كلمات المرور

### **DevOps & Tools**
- **Docker** للنشر
- **TypeScript** للأمان
- **ESLint** و **Prettier** لجودة الكود

## 🚀 التثبيت والإعداد السريع

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd amin-plus
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env.local
```

قم بتحديث الملف `.env.local`:
```env
# قاعدة البيانات
DATABASE_URL="file:./prisma/dev.db"

# المصادقة
NEXTAUTH_SECRET="your-super-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# API
API_URL="http://localhost:3000/api"
```

### 4. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات والجداول
npx prisma migrate dev

# إدخال البيانات التجريبية
npm run db:seed

# (اختياري) فتح Prisma Studio لإدارة البيانات
npm run db:studio
```

### 5. تشغيل النظام
```bash
npm run dev
```

🎉 **النظام جاهز على**: http://localhost:3000

## 🔑 بيانات تسجيل الدخول

### 👨‍💼 **المدير (صلاحيات كاملة)**
- **البريد**: `<EMAIL>`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: جميع العمليات

### 👤 **المستخدم العادي (صلاحيات محدودة)**
- **البريد**: `<EMAIL>`
- **كلمة المرور**: `user123`
- **الصلاحيات**: عرض + إنشاء فواتير

## 📁 هيكل المشروع المحدث

```
amin-plus/
├── 📁 src/
│   ├── 📁 app/                    # Next.js App Router
│   │   ├── 📁 api/               # API Routes محدثة
│   │   │   ├── 📁 customers/     # APIs العملاء
│   │   │   ├── 📁 products/      # APIs المنتجات
│   │   │   ├── 📁 invoices/      # APIs الفواتير
│   │   │   └── 📁 auth/          # APIs المصادقة
│   │   ├── 📁 auth/              # صفحات المصادقة
│   │   └── 📁 dashboard/         # لوحة التحكم
│   ├── 📁 components/            # مكونات React
│   ├── 📁 hooks/                 # React Hooks
│   ├── 📁 lib/                   # مكتبات مساعدة
│   │   ├── 📄 prisma.ts          # إعداد Prisma
│   │   └── 📄 auth-config.ts     # إعداد المصادقة
│   └── 📁 types/                 # تعريفات TypeScript
├── 📁 prisma/
│   ├── 📄 schema.prisma          # مخطط قاعدة البيانات
│   ├── 📁 migrations/            # ملفات الهجرة
│   └── 📄 seed.ts               # البيانات التجريبية
├── 📄 docker-compose.yml        # إعداد Docker
└── 📄 package.json              # التبعيات والأوامر
```

## 🎯 الأوامر المتاحة

### **التطوير**
```bash
npm run dev          # تشغيل النظام في وضع التطوير
npm run build        # بناء النظام للإنتاج
npm start           # تشغيل النظام في وضع الإنتاج
```

### **قاعدة البيانات**
```bash
npm run db:generate  # إنشاء Prisma Client
npm run db:migrate   # تطبيق التغييرات على قاعدة البيانات
npm run db:seed      # إدخال البيانات التجريبية
npm run db:studio    # فتح Prisma Studio
```

### **جودة الكود**
```bash
npm run lint         # فحص الكود
npm run format       # تنسيق الكود
npm run type-check   # فحص TypeScript
```

## 🐳 النشر مع Docker

### **تشغيل PostgreSQL محلياً**
```bash
docker-compose up -d postgres
```

### **بناء ونشر النظام**
```bash
# بناء الصورة
docker build -t amin-plus .

# تشغيل النظام
docker run -p 3000:3000 amin-plus
```

## 📊 إحصائيات النظام

### **قاعدة البيانات**
- 📋 **15+ جدول** مترابط
- 🔗 **Foreign Keys** و **Constraints**
- 📈 **Indexes** للأداء العالي
- 🔄 **Migration History** كامل

### **APIs**
- 🌐 **20+ API Endpoint**
- 🔐 **Authentication** على جميع APIs
- 📄 **Pagination** و **Search**
- ✅ **Error Handling** شامل

### **الأمان**
- 🔒 **Password Hashing** مع bcrypt
- 🎫 **JWT Sessions** آمنة
- 🛡️ **RBAC** متقدم
- 📝 **Input Validation** شامل

## 🎉 الميزات القادمة

### **المرحلة التالية**
- 📱 **تطبيق الهاتف المحمول** (React Native)
- 💳 **نظام الدفع الإلكتروني** (PayFort, Network International)
- 🤖 **الذكاء الاصطناعي** للتنبؤات المالية
- 🌐 **التكامل الحكومي** (هيئة الضرائب، البنك المركزي)

## 🆘 الدعم والمساعدة

### **المشاكل الشائعة**
```bash
# إعادة تعيين قاعدة البيانات
npx prisma migrate reset

# إعادة إنشاء Prisma Client
npx prisma generate

# فحص حالة قاعدة البيانات
npx prisma db pull
```

### **التواصل**
- 📧 **البريد**: <EMAIL>
- 🐛 **المشاكل**: GitHub Issues
- 💬 **المناقشات**: GitHub Discussions

## 📜 الترخيص

هذا المشروع مرخص تحت **رخصة MIT** - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

<div align="center">

**🇦🇪 نظام أمين بلس - حلول محاسبية متطورة للشركات الإماراتية**

*تم تطويره بـ ❤️ باستخدام أحدث التقنيات*

</div>
