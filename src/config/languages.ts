/**
 * تكوين اللغات المدعومة في التطبيق
 * Configuration for supported languages in the application
 */

export type SupportedLanguage = 'ar' | 'en';

export interface Language {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  direction: 'rtl' | 'ltr';
  flag: string;
  dateFormat: string;
  timeFormat: string;
  currency: {
    code: string;
    symbol: string;
    name: string;
    namePlural: string;
    fraction: {
      name: string;
      namePlural: string;
    };
  };
}

/**
 * قائمة اللغات المدعومة في التطبيق
 * List of supported languages in the application
 */
export const SUPPORTED_LANGUAGES: Record<SupportedLanguage, Language> = {
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    direction: 'rtl',
    flag: '🇦🇪',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'hh:mm A',
    currency: {
      code: 'AED',
      symbol: 'د.إ',
      name: 'درهم إماراتي',
      namePlural: 'دراهم إماراتية',
      fraction: {
        name: 'فلس',
        namePlural: 'فلوس'
      }
    }
  },
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
    flag: '🇬🇧',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: 'hh:mm A',
    currency: {
      code: 'AED',
      symbol: 'AED',
      name: 'UAE Dirham',
      namePlural: 'UAE Dirhams',
      fraction: {
        name: 'fils',
        namePlural: 'fils'
      }
    }
  }
};

/**
 * اللغة الافتراضية للتطبيق
 * Default language for the application
 */
export const DEFAULT_LANGUAGE: SupportedLanguage = 'ar';

/**
 * الحصول على اللغة الحالية من localStorage أو الافتراضية
 * Get current language from localStorage or default
 */
export function getCurrentLanguage(): SupportedLanguage {
  if (typeof window === 'undefined') {
    return DEFAULT_LANGUAGE;
  }
  
  const savedLanguage = localStorage.getItem('language') as SupportedLanguage;
  return savedLanguage && SUPPORTED_LANGUAGES[savedLanguage] ? savedLanguage : DEFAULT_LANGUAGE;
}

/**
 * تغيير اللغة الحالية وحفظها في localStorage
 * Change current language and save it to localStorage
 */
export function changeLanguage(language: SupportedLanguage): void {
  if (!SUPPORTED_LANGUAGES[language]) {
    console.error(`Language ${language} is not supported`);
    return;
  }
  
  if (typeof window === 'undefined') {
    return;
  }
  
  localStorage.setItem('language', language);
  document.documentElement.lang = language;
  document.documentElement.dir = SUPPORTED_LANGUAGES[language].direction;
}

/**
 * الحصول على اتجاه اللغة الحالية
 * Get direction of current language
 */
export function getLanguageDirection(language: SupportedLanguage = getCurrentLanguage()): 'rtl' | 'ltr' {
  return SUPPORTED_LANGUAGES[language]?.direction || 'rtl';
}

/**
 * الحصول على معلومات العملة للغة الحالية
 * Get currency information for current language
 */
export function getCurrencyInfo(language: SupportedLanguage = getCurrentLanguage()) {
  return SUPPORTED_LANGUAGES[language]?.currency;
}
