"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Invoice } from "@prisma/client"
import { Button } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"

export const columns: ColumnDef<Invoice & { customer: { name: string } }>[] = [
  {
    accessorKey: "number",
    header: "رقم الفاتورة",
  },
  {
    accessorKey: "customer.name",
    header: "العميل",
  },
  {
    accessorKey: "date",
    header: "التاريخ",
    cell: ({ row }) => {
      return new Date(row.getValue("date")).toLocaleDateString("ar-SA")
    },
  },
  {
    accessorKey: "total",
    header: "المبلغ",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("total"))
      return new Intl.NumberFormat("ar-AE", {
        style: "currency",
        currency: "AED",
      }).format(amount)
    },
  },
  {
    accessorKey: "status",
    header: "الحالة",
    cell: ({ row }) => {
      const status = row.getValue("status")
      return (
        <span
          className={`px-2 py-1 rounded-full text-xs ${status === "paid"
              ? "bg-green-100 text-green-800"
              : "bg-yellow-100 text-yellow-800"
            }`}
        >
          {status === "paid" ? "مدفوعة" : "غير مدفوعة"}
        </span>
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      return (
        <Button variant="ghost" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      )
    },
  },
]
