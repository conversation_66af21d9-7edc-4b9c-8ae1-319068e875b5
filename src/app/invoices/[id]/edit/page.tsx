'use client';

import { useEffect, useState, use } from 'react';
import { useRouter } from 'next/navigation';
import InvoiceForm from '@/components/invoices/invoice-form';

interface InvoiceData {
  id: string;
  number: string;
  date: string;
  dueDate: string | null;
  customerId: string;
  status: string;
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
  }[];
}

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  
  const router = useRouter();
  const [invoice, setInvoice] = useState<InvoiceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoice = async () => {
      try {
        const response = await fetch(`/api/invoices/${resolvedParams.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('الفاتورة غير موجودة');
          }
          throw new Error('فشل في جلب بيانات الفاتورة');
        }

        const data = await response.json();

        // تنسيق البيانات لتتناسب مع نموذج الفاتورة
        setInvoice({
          id: data.id,
          number: data.number,
          date: new Date(data.date).toISOString().split('T')[0],
          dueDate: data.dueDate ? new Date(data.dueDate).toISOString().split('T')[0] : null,
          customerId: data.customer.id,
          status: data.status,
          items: data.items.map((item: any) => ({
            id: item.id,
            name: item.name,
            quantity: item.quantity,
            price: item.price,
          })),
        });

        setLoading(false);
      } catch (err: any) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [resolvedParams.id]);

  if (loading) return <div className="text-center py-10">جاري التحميل...</div>;
  if (error) return <div className="text-center text-red-500 py-10">{error}</div>;
  if (!invoice) return <div className="text-center py-10">لا توجد بيانات للفاتورة</div>;

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">تعديل الفاتورة</h1>
      <InvoiceForm initialData={invoice} isEditing={true} />
    </div>
  );
}
