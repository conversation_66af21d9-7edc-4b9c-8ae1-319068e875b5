@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* نظام ألوان متقدم ومحسن للوضع النهاري */
    /* Advanced and improved color system for light mode */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* نظام ألوان أساسي متدرج */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-50: 221 83% 98%;
    --primary-100: 221 83% 95%;
    --primary-200: 221 83% 88%;
    --primary-300: 221 83% 78%;
    --primary-400: 221 83% 65%;
    --primary-500: 221 83% 53%;
    --primary-600: 221 83% 45%;
    --primary-700: 221 83% 38%;
    --primary-800: 221 83% 32%;
    --primary-900: 221 83% 26%;

    /* ألوان ثانوية محسنة */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    /* أ<PERSON>و<PERSON> محايدة متدرجة */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    /* ألوان الحالة المحسنة */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;

    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* عناصر الواجهة */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;

    /* الظلال والتأثيرات */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
  }

  .dark {
    /* نظام ألوان متقدم ومحسن للوضع الليلي */
    /* Advanced and improved color system for dark mode */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* نظام ألوان أساسي متدرج للوضع الليلي */
    --primary: 217 91% 60%;
    --primary-foreground: 222 84% 4.9%;
    --primary-50: 217 91% 5%;
    --primary-100: 217 91% 10%;
    --primary-200: 217 91% 20%;
    --primary-300: 217 91% 30%;
    --primary-400: 217 91% 45%;
    --primary-500: 217 91% 60%;
    --primary-600: 217 91% 70%;
    --primary-700: 217 91% 80%;
    --primary-800: 217 91% 88%;
    --primary-900: 217 91% 95%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    /* ألوان الحالة المحسنة للوضع الليلي */
    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;

    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 217 91% 60%;

    /* الظلال والتأثيرات للوضع الليلي */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* استيراد الخطوط العربية والإنجليزية */
/* Import Arabic and English fonts */

/* خطوط عربية محسنة */
/* Improved Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* خطوط إنجليزية محسنة */
/* Improved English fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* تحسينات متقدمة للحركات والتأثيرات */
/* Advanced animations and effects improvements */
@layer utilities {

  /* حركات سلسة ومحسنة */
  /* Smooth and improved animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.4s ease-out;
  }

  .animate-fade-in-down {
    animation: fadeInDown 0.4s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  /* تأثيرات التحويل السلسة */
  /* Smooth transition effects */
  .transition-all-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-colors-smooth {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  }

  .transition-transform-smooth {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-shadow-smooth {
    transition: box-shadow 0.2s ease-in-out;
  }

  /* تأثيرات التفاعل */
  /* Interactive effects */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
  }

  .hover-scale {
    transition: transform 0.2s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  .hover-glow {
    transition: box-shadow 0.2s ease-in-out;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* تأثيرات التركيز المحسنة */
  /* Enhanced focus effects */
  .focus-ring-enhanced {
    transition: box-shadow 0.15s ease-in-out;
  }

  .focus-ring-enhanced:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 0 0 1px rgba(59, 130, 246, 0.2);
  }

  /* تأثيرات التحميل */
  /* Loading effects */
  .loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .dark .loading-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }

  .loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
  }

  /* تأثيرات تأخير الحركة المحسنة */
  /* Enhanced Animation delay effects */
  .animate-delay-100 { animation-delay: 0.1s; }
  .animate-delay-200 { animation-delay: 0.2s; }
  .animate-delay-300 { animation-delay: 0.3s; }
  .animate-delay-400 { animation-delay: 0.4s; }
  .animate-delay-500 { animation-delay: 0.5s; }
  .animate-delay-600 { animation-delay: 0.6s; }
  .animate-delay-700 { animation-delay: 0.7s; }
  .animate-delay-800 { animation-delay: 0.8s; }
  .animate-delay-900 { animation-delay: 0.9s; }
  .animate-delay-1000 { animation-delay: 1s; }

  /* تأثيرات التدرج المتقدمة */
  /* Advanced gradient effects */
  .gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .gradient-warning {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .gradient-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .gradient-purple {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-ocean {
    background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
  }

  .gradient-sunset {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  }

  .gradient-forest {
    background: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
  }

  .gradient-royal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* تدرجات خلفية متقدمة */
  /* Advanced background gradients */
  .bg-gradient-radial {
    background: radial-gradient(circle at center, var(--tw-gradient-stops));
  }

  .bg-gradient-conic {
    background: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
  }

  .bg-mesh-gradient {
    background:
      radial-gradient(at 40% 20%, hsla(28,100%,74%,1) 0px, transparent 50%),
      radial-gradient(at 80% 0%, hsla(189,100%,56%,1) 0px, transparent 50%),
      radial-gradient(at 0% 50%, hsla(355,100%,93%,1) 0px, transparent 50%),
      radial-gradient(at 80% 50%, hsla(340,100%,76%,1) 0px, transparent 50%),
      radial-gradient(at 0% 100%, hsla(22,100%,77%,1) 0px, transparent 50%),
      radial-gradient(at 80% 100%, hsla(242,100%,70%,1) 0px, transparent 50%),
      radial-gradient(at 0% 0%, hsla(343,100%,76%,1) 0px, transparent 50%);
  }

/* تحسينات للغة العربية والإنجليزية */
/* Improvements for Arabic and English languages */

  /* تحسين عرض النصوص العربية */
  /* Improved Arabic text display */
  .arabic-text {
    font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', 'Almarai', 'IBM Plex Sans Arabic', sans-serif;
    letter-spacing: 0;
    line-height: 1.6;
    font-feature-settings: "kern", "liga", "calt";
  }

  /* تحسين عرض النصوص العربية - خط أنيق */
  /* Improved Arabic text display - elegant font */
  .arabic-text-elegant {
    font-family: 'Tajawal', 'Almarai', 'Cairo', sans-serif;
    letter-spacing: 0;
    line-height: 1.6;
    font-feature-settings: "kern", "liga", "calt";
  }

  /* تحسين عرض النصوص العربية - خط حديث */
  /* Improved Arabic text display - modern font */
  .arabic-text-modern {
    font-family: 'IBM Plex Sans Arabic', 'Noto Sans Arabic', 'Cairo', sans-serif;
    letter-spacing: 0;
    line-height: 1.6;
    font-feature-settings: "kern", "liga", "calt";
  }

  /* تحسين عرض النصوص الإنجليزية */
  /* Improved English text display */
  .english-text {
    font-family: 'Poppins', 'Inter', 'IBM Plex Sans', 'Roboto', system-ui, sans-serif;
    letter-spacing: -0.01em;
    line-height: 1.5;
  }

  /* تحسين عرض النصوص الإنجليزية - خط أنيق */
  /* Improved English text display - elegant font */
  .english-text-elegant {
    font-family: 'Poppins', 'Inter', system-ui, sans-serif;
    letter-spacing: -0.01em;
    line-height: 1.5;
  }

  /* تحسين عرض النصوص الإنجليزية - خط حديث */
  /* Improved English text display - modern font */
  .english-text-modern {
    font-family: 'IBM Plex Sans', 'Roboto', 'Inter', system-ui, sans-serif;
    letter-spacing: -0.01em;
    line-height: 1.5;
  }

  /* تحسين محاذاة العناصر في RTL */
  .rtl-grid {
    direction: rtl;
    text-align: right;
  }

  /* تحسين محاذاة العناصر في LTR */
  .ltr-grid {
    direction: ltr;
    text-align: left;
  }

  /* تحسين الهوامش والحشوات في RTL */
  .rtl-spacing {
    margin-right: 0;
    margin-left: auto;
    padding-right: 0;
    padding-left: 1rem;
  }

  /* تحسين الهوامش والحشوات في LTR */
  .ltr-spacing {
    margin-left: 0;
    margin-right: auto;
    padding-left: 0;
    padding-right: 1rem;
  }

  /* تحسين قراءة النصوص في الوضع الليلي */
  .dark .text-enhance {
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
    font-weight: 400;
  }

  /* تحسين قراءة النصوص في الوضع النهاري */
  .text-enhance {
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.05);
    font-weight: 400;
  }

  /* تحسين عرض الأرقام */
  .tabular-nums {
    font-variant-numeric: tabular-nums;
  }

  /* تحسين عرض العناوين */
  .heading {
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.2;
  }

  /* تحسين عرض الأزرار */
  .button-text {
    font-weight: 500;
    letter-spacing: 0;
  }

  /* تحسين textarea auto-resize */
  .textarea-auto-resize {
    resize: none;
  }

  .textarea-auto-resize[data-auto-resize="true"] {
    resize: none;
    overflow: hidden;
  }
}

/* تطبيق الخطوط على كامل التطبيق */
/* Apply fonts to the entire application */
html {
  font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', 'Poppins', 'Inter', system-ui, sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

/* تحسين عرض النصوص حسب اللغة */
/* Improve text display based on language */
html[lang="ar"] {
  font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', 'Almarai', 'IBM Plex Sans Arabic', system-ui, sans-serif;
  letter-spacing: 0;
  line-height: 1.6;
}

html[lang="en"] {
  font-family: 'Poppins', 'Inter', 'IBM Plex Sans', 'Roboto', system-ui, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1.5;
}

/* تحسين عرض النصوص في الوضع الليلي */
/* Improve text display in dark mode */
.dark body {
  font-weight: 350;
  /* خط أخف قليلاً في الوضع الليلي لتحسين القراءة */
}

/* تحسين عرض العناوين */
/* Improve headings display */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* تحسين عرض الأزرار */
/* Improve buttons display */
button,
.button {
  font-weight: 500;
  letter-spacing: 0;
}

/* تعريف الحركات المخصصة محسنة للأداء */
/* Custom animations keyframes optimized for performance */
@keyframes fadeIn {
  from {
    opacity: 0;
    will-change: opacity;
  }
  to {
    opacity: 1;
    will-change: auto;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

/* تحسينات الأداء للحركات */
/* Performance optimizations for animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* تقليل الحركات للمستخدمين الذين يفضلون ذلك */
/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Animation delay utilities */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }
.animate-delay-1000 { animation-delay: 1s; }

/* ===== تأثيرات بصرية متقدمة ===== */
/* ===== Advanced Visual Effects ===== */

/* تأثيرات الظلال المتقدمة */
/* Advanced shadow effects */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-medium {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-strong {
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 8px -2px rgba(0, 0, 0, 0.05);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
}

.shadow-colored-blue {
  box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.3);
}

.shadow-colored-purple {
  box-shadow: 0 8px 25px -8px rgba(139, 92, 246, 0.3);
}

.shadow-colored-green {
  box-shadow: 0 8px 25px -8px rgba(34, 197, 94, 0.3);
}

.shadow-inner-soft {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

/* تأثيرات الحدود المتقدمة */
/* Advanced border effects */
.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #667eea, #764ba2) border-box;
}

.border-animated {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, transparent, #667eea, transparent) border-box;
  animation: borderRotate 3s linear infinite;
}

/* تأثيرات النصوص المتقدمة */
/* Advanced text effects */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.text-glow-purple {
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

/* تأثيرات الخلفية المتحركة */
/* Animated background effects */
.bg-animated-gradient {
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

.bg-floating-shapes {
  position: relative;
  overflow: hidden;
}

.bg-floating-shapes::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: floatingShapes 20s ease-in-out infinite;
  z-index: -1;
}

/* تأثيرات الزجاج المصقول */
/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* تأثيرات التمرير */
/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* ===== حركات متقدمة ===== */
/* ===== Advanced Animations ===== */

/* حركات الظهور المتقدمة */
/* Advanced entrance animations */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.animate-pulse-soft {
  animation: pulseSoft 2s ease-in-out infinite;
}

/* حركات متقدمة جديدة */
/* New advanced animations */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes floatingShapes {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(30px) rotate(240deg); }
}

@keyframes borderRotate {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes flipIn {
  from {
    opacity: 0;
    transform: perspective(400px) rotateY(90deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateY(0deg);
  }
}

@keyframes rubberBand {
  from { transform: scale3d(1, 1, 1); }
  30% { transform: scale3d(1.25, 0.75, 1); }
  40% { transform: scale3d(0.75, 1.25, 1); }
  50% { transform: scale3d(1.15, 0.85, 1); }
  65% { transform: scale3d(0.95, 1.05, 1); }
  75% { transform: scale3d(1.05, 0.95, 1); }
  to { transform: scale3d(1, 1, 1); }
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

@keyframes wobble {
  from { transform: translate3d(0, 0, 0); }
  15% { transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }
  30% { transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }
  45% { transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }
  60% { transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }
  75% { transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }
  to { transform: translate3d(0, 0, 0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  from { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  to { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
}

/* فئات الحركات الجديدة */
/* New animation classes */
.animate-slide-in-top {
  animation: slideInFromTop 0.6s ease-out;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.6s ease-out;
}

.animate-zoom-in {
  animation: zoomIn 0.6s ease-out;
}

.animate-flip-in {
  animation: flipIn 0.8s ease-out;
}

.animate-rubber-band {
  animation: rubberBand 1s ease-out;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-wobble {
  animation: wobble 1s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* ===== تحسينات الأداء والتفاعل ===== */
/* ===== Performance and Interaction Enhancements ===== */

/* تحسين الانتقالات */
/* Enhanced transitions */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors-smooth {
  transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform-smooth {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-opacity-smooth {
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين التركيز */
/* Enhanced focus states */
.focus-ring-enhanced {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
}

.focus-ring-primary {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
}

.focus-ring-success {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:ring-offset-2;
}

/* تحسين التمرير */
/* Enhanced scrolling */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* تحسين النصوص */
/* Enhanced typography */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* تحسين الشبكة */
/* Enhanced grid layouts */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* تحسين الفليكس */
/* Enhanced flex layouts */
.flex-center {
  @apply flex items-center justify-center;
}

.flex-between {
  @apply flex items-center justify-between;
}

.flex-start {
  @apply flex items-center justify-start;
}

.flex-end {
  @apply flex items-center justify-end;
}

/* تحسين المساحات */
/* Enhanced spacing */
.space-y-fluid > * + * {
  margin-top: clamp(0.5rem, 2vw, 1.5rem);
}

.space-x-fluid > * + * {
  margin-left: clamp(0.5rem, 2vw, 1.5rem);
}

/* تحسين الحاويات */
/* Enhanced containers */
.container-fluid {
  width: 100%;
  max-width: none;
  padding-left: clamp(1rem, 5vw, 3rem);
  padding-right: clamp(1rem, 5vw, 3rem);
}

.container-narrow {
  max-width: 65ch;
  margin-left: auto;
  margin-right: auto;
}

/* تحسين الاستجابة */
/* Enhanced responsiveness */
.responsive-text {
  font-size: clamp(0.875rem, 2.5vw, 1.125rem);
}

.responsive-heading {
  font-size: clamp(1.5rem, 5vw, 3rem);
}

.responsive-subheading {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
}

/* تحسين الحالات */
/* Enhanced states */
.loading-shimmer {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%);
  background-size: 200% 100%;
}

/* تحسين الأزرار */
/* Enhanced buttons */
.btn-magnetic {
  transition: transform 0.2s ease;
}

.btn-magnetic:hover {
  transform: translateY(-2px);
}

.btn-magnetic:active {
  transform: translateY(0);
}

/* تحسين البطاقات */
/* Enhanced cards */
.card-hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* تحسين النماذج */
/* Enhanced forms */
.form-floating {
  position: relative;
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.85);
  color: hsl(var(--primary));
}

.form-floating label {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  transition: all 0.2s ease;
  pointer-events: none;
  color: hsl(var(--muted-foreground));
}

/* تحسين الإشعارات */
/* Enhanced notifications */
.notification-slide-in {
  animation: slideInFromRight 0.3s ease-out;
}

.notification-slide-out {
  animation: slideOutToRight 0.3s ease-in;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}