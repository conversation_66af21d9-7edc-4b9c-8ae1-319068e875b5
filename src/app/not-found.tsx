'use client';

import Link from 'next/link';
import { Button } from '@/components/ui';
import { Home, ArrowLeft, Search, RefreshCw } from 'lucide-react';
import { useI18n } from '@/lib/i18n';

export default function NotFound() {
  const { language } = useI18n();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4">
      <div className="max-w-2xl w-full text-center">
        {/* 404 Animation */}
        <div className="mb-8 animate-bounce">
          <div className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mb-4">
            404
          </div>
          <div className="w-32 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
        </div>

        {/* Content */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-white/20">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
            {language === 'ar' ? 'الصفحة غير موجودة' : 'Page Not Found'}
          </h1>

          <p className="text-gray-600 mb-2">
            {language === 'ar'
              ? 'عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.'
              : 'Sorry, the page you are looking for does not exist or has been moved.'
            }
          </p>

          <p className="text-sm text-gray-500 mb-8">
            {language === 'ar' ? 'Page Not Found' : 'الصفحة غير موجودة'}
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button variant="gradient" className="w-full sm:w-auto">
                <Home className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'العودة إلى الرئيسية' : 'Back to Home'}
              </Button>
            </Link>

            <Button
              variant="outline"
              onClick={() => window.history.back()}
              className="w-full sm:w-auto"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'الصفحة السابقة' : 'Go Back'}
            </Button>
          </div>

          {/* Quick Links */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-500 mb-4">
              {language === 'ar' ? 'روابط سريعة:' : 'Quick Links:'}
            </p>
            <div className="flex flex-wrap gap-2 justify-center">
              <Link href="/dashboard/invoices">
                <Button variant="ghost" size="sm">
                  {language === 'ar' ? 'الفواتير' : 'Invoices'}
                </Button>
              </Link>
              <Link href="/dashboard/products">
                <Button variant="ghost" size="sm">
                  {language === 'ar' ? 'المنتجات' : 'Products'}
                </Button>
              </Link>
              <Link href="/dashboard/customers">
                <Button variant="ghost" size="sm">
                  {language === 'ar' ? 'العملاء' : 'Customers'}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
