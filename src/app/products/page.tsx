'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * صفحة إعادة توجيه للمنتجات
 * Redirect page for products
 */
export default function ProductsRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // إعادة توجيه المستخدم إلى صفحة المنتجات في لوحة التحكم
    router.replace('/dashboard/products');
  }, [router]);

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-t-4 border-gray-200 border-t-primary mx-auto mb-4"></div>
        <p className="text-lg">جاري التوجيه إلى صفحة المنتجات...</p>
        <p className="text-sm text-gray-500">Redirecting to products page...</p>
      </div>
    </div>
  );
}