import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { prisma, calculatePagination, createPaginatedResult, createSearchFilter, generateProductSKU } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const { skip } = calculatePagination({ page, limit });

    // بناء شروط البحث
    const where: any = {};

    // إضافة البحث النصي
    if (search) {
      const searchFilter = createSearchFilter(search, ['name', 'nameEn', 'description', 'sku']);
      Object.assign(where, searchFilter);
    }

    // إضافة فلتر الحالة
    if (status) {
      where.status = status;
    }

    // إضافة فلتر الفئة
    if (category) {
      where.category = category;
    }

    // جلب المنتجات مع العد الإجمالي
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          productCategory: true,
        },
      }),
      prisma.product.count({ where }),
    ]);

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const formattedProducts = products.map(product => ({
      id: product.id.toString(),
      name: product.name,
      nameEn: product.nameEn || '',
      description: product.description || '',
      descriptionEn: product.descriptionEn || '',
      sku: product.sku || '',
      barcode: product.barcode || '',
      price: product.price,
      cost: product.cost || 0,
      wholesalePrice: product.wholesalePrice || 0,
      category: product.category || '',
      categoryEn: product.categoryEn || '',
      unit: product.unit || 'قطعة',
      unitEn: product.unitEn || 'Unit',
      taxRate: product.taxRate,
      status: product.status,
      trackInventory: product.trackInventory,
      currentStock: product.currentStock,
      minStock: product.minStock,
      maxStock: product.maxStock,
      isComposite: product.isComposite,
      imageUrl: product.imageUrl || '',
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString(),
    }));

    const paginatedResult = createPaginatedResult(formattedProducts, total, page, limit);

    return NextResponse.json({
      products: paginatedResult.data,
      pagination: paginatedResult.pagination
    });
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    // التحقق من صحة البيانات
    if (!body.name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 })
    }

    // التحقق من عدم تكرار SKU (إذا تم توفيره)
    if (body.sku) {
      const existingProduct = await prisma.product.findFirst({
        where: { sku: body.sku }
      });

      if (existingProduct) {
        return NextResponse.json({ error: 'Product with this SKU already exists' }, { status: 409 });
      }
    }

    // إنشاء SKU تلقائياً إذا لم يتم توفيره
    const sku = body.sku || await generateProductSKU();

    // إنشاء المنتج الجديد
    const newProduct = await prisma.product.create({
      data: {
        name: body.name,
        nameEn: body.nameEn || null,
        description: body.description || null,
        descriptionEn: body.descriptionEn || null,
        sku,
        barcode: body.barcode || null,
        price: parseFloat(body.price) || 0,
        cost: parseFloat(body.cost) || 0,
        wholesalePrice: parseFloat(body.wholesalePrice) || 0,
        category: body.category || null,
        categoryEn: body.categoryEn || null,
        unit: body.unit || 'قطعة',
        unitEn: body.unitEn || 'Unit',
        taxRate: parseFloat(body.taxRate) || 5,
        status: body.status || 'active',
        trackInventory: Boolean(body.trackInventory),
        currentStock: parseInt(body.currentStock) || 0,
        minStock: parseInt(body.minStock) || 0,
        maxStock: parseInt(body.maxStock) || 0,
        isComposite: Boolean(body.isComposite),
        imageUrl: body.imageUrl || null,
        categoryId: body.categoryId ? parseInt(body.categoryId) : null,
      },
    });

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const formattedProduct = {
      id: newProduct.id.toString(),
      name: newProduct.name,
      nameEn: newProduct.nameEn || '',
      description: newProduct.description || '',
      descriptionEn: newProduct.descriptionEn || '',
      sku: newProduct.sku || '',
      barcode: newProduct.barcode || '',
      price: newProduct.price,
      cost: newProduct.cost || 0,
      wholesalePrice: newProduct.wholesalePrice || 0,
      category: newProduct.category || '',
      categoryEn: newProduct.categoryEn || '',
      unit: newProduct.unit || 'قطعة',
      unitEn: newProduct.unitEn || 'Unit',
      taxRate: newProduct.taxRate,
      status: newProduct.status,
      trackInventory: newProduct.trackInventory,
      currentStock: newProduct.currentStock,
      minStock: newProduct.minStock,
      maxStock: newProduct.maxStock,
      isComposite: newProduct.isComposite,
      imageUrl: newProduct.imageUrl || '',
      createdAt: newProduct.createdAt.toISOString(),
      updatedAt: newProduct.updatedAt.toISOString(),
    };

    return NextResponse.json(formattedProduct, { status: 201 })
  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
