import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET(request: Request) {
  try {
    // استخراج نطاق التاريخ من الاستعلام
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate') as string) : new Date(new Date().getFullYear(), 0, 1);
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate') as string) : new Date();
    
    // جلب البيانات من قاعدة البيانات
    const [
      invoices,
      paidInvoicesCount,
      unpaidInvoicesCount,
      overdueInvoicesCount
    ] = await Promise.all([
      // جميع الفواتير ضمن نطاق التاريخ
      prisma.invoice.findMany({
        where: {
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          customer: true,
        },
      }),
      // عدد الفواتير المدفوعة
      prisma.invoice.count({
        where: {
          status: 'paid',
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      // عدد الفواتير غير المدفوعة
      prisma.invoice.count({
        where: {
          status: 'sent',
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      // عدد الفواتير المتأخرة
      prisma.invoice.count({
        where: {
          status: 'overdue',
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
    ]);

    // حساب إجمالي الإيرادات
    const totalRevenue = invoices.reduce((sum, invoice) => {
      return invoice.status === 'paid' ? sum + invoice.total : sum;
    }, 0);

    // تجميع الإيرادات حسب الشهر
    const months = Array.from({ length: 12 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      return {
        month: date.toLocaleDateString('ar-EG', { month: 'short' }),
        year: date.getFullYear(),
        amount: 0,
      };
    }).reverse();

    invoices.forEach((invoice) => {
      const invoiceDate = new Date(invoice.date);
      const month = invoiceDate.toLocaleDateString('ar-EG', { month: 'short' });
      const year = invoiceDate.getFullYear();
      
      const monthIndex = months.findIndex((m) => m.month === month && m.year === year);
      if (monthIndex !== -1 && invoice.status === 'paid') {
        months[monthIndex].amount += invoice.total;
      }
    });

    // تجميع أفضل العملاء
    const customers: Record<string, { id: string; name: string; totalSpent: number; invoicesCount: number }> = {};
    
    invoices.forEach((invoice) => {
      const { customer } = invoice;
      if (!customers[customer.id]) {
        customers[customer.id] = {
          id: customer.id,
          name: customer.name,
          totalSpent: 0,
          invoicesCount: 0,
        };
      }
      
      customers[customer.id].invoicesCount++;
      if (invoice.status === 'paid') {
        customers[customer.id].totalSpent += invoice.total;
      }
    });

    const topCustomers = Object.values(customers)
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 5);

    // تجهيز بيانات التقرير
    const reportData = {
      totalRevenue,
      paidInvoices: paidInvoicesCount,
      unpaidInvoices: unpaidInvoicesCount,
      overdueInvoices: overdueInvoicesCount,
      revenueByMonth: months.map(m => ({
        month: m.month,
        amount: m.amount,
      })),
      topCustomers,
    };

    return NextResponse.json(reportData);
  } catch (error) {
    console.error("Error generating report:", error);
    return NextResponse.json(
      { error: "حدث خطأ أثناء إنشاء التقرير" },
      { status: 500 }
    );
  }
}