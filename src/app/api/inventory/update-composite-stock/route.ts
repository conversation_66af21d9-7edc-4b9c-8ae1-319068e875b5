import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * تحديث مخزون المكونات عند بيع منتج مركب
 * Update ingredients stock when selling a composite product
 */
export async function POST(request: NextRequest) {
  try {
    const { productId, quantity, operation = 'decrease' } = await request.json();

    if (!productId || !quantity) {
      return NextResponse.json(
        { error: 'معرف المنتج والكمية مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من أن المنتج مركب
    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) },
      select: { isComposite: true }
    });

    if (!product || !product.isComposite) {
      return NextResponse.json(
        { error: 'المنتج غير موجود أو ليس منتجًا مركبًا' },
        { status: 400 }
      );
    }

    // الحصول على مقادير المنتج
    const recipeItems = await prisma.recipeItem.findMany({
      where: { productId: parseInt(productId) },
      include: { ingredient: true }
    });

    if (recipeItems.length === 0) {
      return NextResponse.json(
        { error: 'المنتج لا يحتوي على مقادير' },
        { status: 400 }
      );
    }

    // تحديث مخزون كل مكون
    const updates = [];
    for (const item of recipeItems) {
      const ingredientQuantity = item.quantity * quantity;
      const currentStock = item.ingredient.stock || 0;

      let newStock;
      if (operation === 'decrease') {
        newStock = Math.max(0, currentStock - ingredientQuantity);
      } else {
        newStock = currentStock + ingredientQuantity;
      }

      updates.push(
        prisma.product.update({
          where: { id: item.ingredientId },
          data: { stock: newStock }
        })
      );
    }

    // تنفيذ التحديثات في معاملة واحدة
    await prisma.$transaction(updates);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث مخزون المكونات بنجاح'
    });
  } catch (error) {
    console.error('خطأ في تحديث مخزون المكونات:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث مخزون المكونات' },
      { status: 500 }
    );
  }
}
