import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * التحقق من مستوى مخزون المكونات وإنشاء تنبيهات
 * Check ingredients stock levels and create alerts
 */
export async function GET(request: NextRequest) {
  try {
    // الحصول على جميع المنتجات المركبة مع مقاديرها
    const compositeProducts = await prisma.product.findMany({
      where: { isComposite: true },
      include: {
        recipeItems: {
          include: {
            ingredient: true
          }
        }
      }
    });

    // تجميع المكونات التي تحتاج إلى تنبيه
    const lowStockIngredients = [];
    const outOfStockIngredients = [];

    // فحص مستوى المخزون لكل مكون
    for (const product of compositeProducts) {
      for (const item of product.recipeItems) {
        const ingredient = item.ingredient;
        
        // التحقق من وجود حد أدنى للمخزون
        if (ingredient.minStock && ingredient.stock !== null) {
          if (ingredient.stock <= 0) {
            // المكون نفد من المخزون
            if (!outOfStockIngredients.some(i => i.id === ingredient.id)) {
              outOfStockIngredients.push({
                id: ingredient.id,
                name: ingredient.name,
                stock: ingredient.stock,
                minStock: ingredient.minStock,
                usedIn: [{ id: product.id, name: product.name }]
              });
            } else {
              // إضافة المنتج إلى قائمة المنتجات التي تستخدم هذا المكون
              const existingIngredient = outOfStockIngredients.find(i => i.id === ingredient.id);
              if (!existingIngredient.usedIn.some(p => p.id === product.id)) {
                existingIngredient.usedIn.push({ id: product.id, name: product.name });
              }
            }
          } else if (ingredient.stock <= ingredient.minStock) {
            // المكون منخفض في المخزون
            if (!lowStockIngredients.some(i => i.id === ingredient.id)) {
              lowStockIngredients.push({
                id: ingredient.id,
                name: ingredient.name,
                stock: ingredient.stock,
                minStock: ingredient.minStock,
                usedIn: [{ id: product.id, name: product.name }]
              });
            } else {
              // إضافة المنتج إلى قائمة المنتجات التي تستخدم هذا المكون
              const existingIngredient = lowStockIngredients.find(i => i.id === ingredient.id);
              if (!existingIngredient.usedIn.some(p => p.id === product.id)) {
                existingIngredient.usedIn.push({ id: product.id, name: product.name });
              }
            }
          }
        }
      }
    }

    return NextResponse.json({
      lowStockIngredients,
      outOfStockIngredients,
      totalAlerts: lowStockIngredients.length + outOfStockIngredients.length
    });
  } catch (error) {
    console.error('خطأ في التحقق من مستوى المخزون:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء التحقق من مستوى المخزون' },
      { status: 500 }
    );
  }
}
