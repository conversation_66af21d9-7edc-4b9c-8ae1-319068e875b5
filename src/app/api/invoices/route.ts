import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma, calculatePagination, createPaginatedResult, createSearchFilter, generateInvoiceNumber } from '@/lib/prisma';

// GET: جلب الفواتير
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const customerId = searchParams.get('customerId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const { skip } = calculatePagination({ page, limit });

    // بناء شروط البحث
    const where: any = {};

    // إضافة البحث النصي
    if (search) {
      const searchFilter = createSearchFilter(search, ['number', 'notes']);
      Object.assign(where, searchFilter);
    }

    // إضافة فلتر العميل
    if (customerId) {
      where.customerId = parseInt(customerId);
    }

    // إضافة فلتر الحالة
    if (status) {
      where.status = status;
    }

    // جلب الفواتير مع العد الإجمالي
    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          customer: true,
          items: {
            include: {
              product: true
            }
          }
        }
      }),
      prisma.invoice.count({ where }),
    ]);

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const formattedInvoices = invoices.map(invoice => ({
      id: invoice.id.toString(),
      number: invoice.number,
      customer: {
        id: invoice.customer.id.toString(),
        name: invoice.customer.name,
        nameEn: invoice.customer.nameEn || '',
        email: invoice.customer.email || '',
        phone: invoice.customer.phone || '',
      },
      issueDate: invoice.issueDate,
      dueDate: invoice.dueDate,
      status: invoice.status,
      subtotal: invoice.subtotal,
      taxAmount: invoice.taxAmount,
      discountAmount: invoice.discountAmount,
      total: invoice.total,
      currency: invoice.currency,
      notes: invoice.notes || '',
      terms: invoice.terms || '',
      paymentStatus: invoice.paymentStatus,
      paidAt: invoice.paidAt?.toISOString() || null,
      items: invoice.items.map(item => ({
        id: item.id.toString(),
        productId: item.productId?.toString() || null,
        name: item.name,
        nameEn: item.nameEn || '',
        description: item.description || '',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        taxRate: item.taxRate,
        taxAmount: item.taxAmount,
        total: item.total,
        notes: item.notes || '',
      })),
      createdAt: invoice.createdAt.toISOString(),
      updatedAt: invoice.updatedAt.toISOString(),
    }));

    const paginatedResult = createPaginatedResult(formattedInvoices, total, page, limit);

    return NextResponse.json({
      invoices: paginatedResult.data,
      pagination: paginatedResult.pagination
    });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST: إنشاء فاتورة جديدة
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.customerId || !body.items || body.items.length === 0) {
      return NextResponse.json({ error: 'Customer and items are required' }, { status: 400 });
    }

    // التحقق من وجود العميل
    const customer = await prisma.customer.findUnique({
      where: { id: parseInt(body.customerId) }
    });

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 });
    }

    // حساب المجاميع
    let subtotal = 0;
    let totalTaxAmount = 0;

    const items = body.items.map((item: any) => {
      const quantity = parseFloat(item.quantity) || 0;
      const unitPrice = parseFloat(item.unitPrice) || 0;
      const taxRate = parseFloat(item.taxRate) || 5;

      const itemSubtotal = quantity * unitPrice;
      const itemTaxAmount = itemSubtotal * (taxRate / 100);
      const itemTotal = itemSubtotal + itemTaxAmount;

      subtotal += itemSubtotal;
      totalTaxAmount += itemTaxAmount;

      return {
        productId: item.productId ? parseInt(item.productId) : null,
        name: item.name || '',
        nameEn: item.nameEn || '',
        description: item.description || '',
        quantity,
        unitPrice,
        taxRate,
        taxAmount: itemTaxAmount,
        total: itemTotal,
        notes: item.notes || '',
      };
    });

    const discountAmount = parseFloat(body.discountAmount) || 0;
    const total = subtotal + totalTaxAmount - discountAmount;

    // إنشاء رقم فاتورة فريد
    const invoiceNumber = await generateInvoiceNumber();

    // إنشاء الفاتورة
    const newInvoice = await prisma.invoice.create({
      data: {
        number: invoiceNumber,
        customerId: parseInt(body.customerId),
        issueDate: body.issueDate || new Date().toISOString().split('T')[0],
        dueDate: body.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: body.status || 'draft',
        subtotal,
        taxAmount: totalTaxAmount,
        discountAmount,
        total,
        currency: body.currency || 'AED',
        notes: body.notes || '',
        terms: body.terms || '',
        paymentStatus: body.paymentStatus || 'pending',
        items: {
          create: items
        }
      },
      include: {
        customer: true,
        items: true
      }
    });

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const formattedInvoice = {
      id: newInvoice.id.toString(),
      number: newInvoice.number,
      customer: {
        id: newInvoice.customer.id.toString(),
        name: newInvoice.customer.name,
        nameEn: newInvoice.customer.nameEn || '',
        email: newInvoice.customer.email || '',
        phone: newInvoice.customer.phone || '',
      },
      issueDate: newInvoice.issueDate,
      dueDate: newInvoice.dueDate,
      status: newInvoice.status,
      subtotal: newInvoice.subtotal,
      taxAmount: newInvoice.taxAmount,
      discountAmount: newInvoice.discountAmount,
      total: newInvoice.total,
      currency: newInvoice.currency,
      notes: newInvoice.notes || '',
      terms: newInvoice.terms || '',
      paymentStatus: newInvoice.paymentStatus,
      items: newInvoice.items.map(item => ({
        id: item.id.toString(),
        productId: item.productId?.toString() || null,
        name: item.name,
        nameEn: item.nameEn || '',
        description: item.description || '',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        taxRate: item.taxRate,
        taxAmount: item.taxAmount,
        total: item.total,
        notes: item.notes || '',
      })),
      createdAt: newInvoice.createdAt.toISOString(),
      updatedAt: newInvoice.updatedAt.toISOString(),
    };

    return NextResponse.json(formattedInvoice, { status: 201 });
  } catch (error) {
    console.error('Error creating invoice:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE: حذف فاتورة
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Invoice ID is required' }, { status: 400 });
    }

    // التحقق من وجود الفاتورة
    const invoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) }
    });

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // حذف الفاتورة وعناصرها (Cascade delete)
    await prisma.invoice.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting invoice:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
