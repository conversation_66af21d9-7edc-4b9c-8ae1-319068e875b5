import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Schema للتحقق من صحة البيانات
const supplierSchema = z.object({
  name: z.string().min(1, 'اسم المورد مطلوب'),
  nameEn: z.string().optional(),
  email: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().default('الإمارات العربية المتحدة'),
  postalCode: z.string().optional(),
  taxNumber: z.string().optional(),
  website: z.string().optional(),
  contactPerson: z.string().optional(),
  contactPhone: z.string().optional(),
  contactEmail: z.string().email().optional().or(z.literal('')),
  bankDetails: z.string().optional(),
  paymentTerms: z.string().default('30'),
  creditLimit: z.number().default(0),
  supplierType: z.enum(['vendor', 'distributor', 'manufacturer', 'service']).default('vendor'),
  category: z.string().optional(),
  rating: z.number().min(0).max(5).default(0),
  notes: z.string().optional(),
  isActive: z.boolean().default(true)
});

// GET - جلب جميع الموردين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') || '';
    const isActive = searchParams.get('isActive');

    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { nameEn: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { contactPerson: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (type) {
      where.supplierType = type;
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    // جلب الموردين مع العد
    const [suppliers, total] = await Promise.all([
      prisma.supplier.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              purchases: true,
              expenses: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.supplier.count({ where })
    ]);

    // حساب الإحصائيات
    const stats = await prisma.supplier.aggregate({
      _count: {
        id: true
      },
      _avg: {
        rating: true
      },
      where: { isActive: true }
    });

    const activeCount = await prisma.supplier.count({
      where: { isActive: true }
    });

    const inactiveCount = await prisma.supplier.count({
      where: { isActive: false }
    });

    return NextResponse.json({
      suppliers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: {
        total: stats._count.id,
        active: activeCount,
        inactive: inactiveCount,
        averageRating: stats._avg.rating || 0
      }
    });

  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات الموردين' },
      { status: 500 }
    );
  }
}

// POST - إنشاء مورد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // التحقق من صحة البيانات
    const validatedData = supplierSchema.parse(body);

    // التحقق من عدم تكرار البريد الإلكتروني
    if (validatedData.email) {
      const existingSupplier = await prisma.supplier.findFirst({
        where: {
          email: validatedData.email
        }
      });

      if (existingSupplier) {
        return NextResponse.json(
          { error: 'البريد الإلكتروني مستخدم بالفعل' },
          { status: 400 }
        );
      }
    }

    // إنشاء المورد
    const supplier = await prisma.supplier.create({
      data: validatedData,
      include: {
        _count: {
          select: {
            purchases: true,
            expenses: true
          }
        }
      }
    });

    return NextResponse.json(supplier, { status: 201 });

  } catch (error) {
    console.error('Error creating supplier:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء المورد' },
      { status: 500 }
    );
  }
}

// PUT - تحديث مورد
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المورد مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من صحة البيانات
    const validatedData = supplierSchema.partial().parse(updateData);

    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingSupplier) {
      return NextResponse.json(
        { error: 'المورد غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    if (validatedData.email && validatedData.email !== existingSupplier.email) {
      const emailExists = await prisma.supplier.findFirst({
        where: {
          email: validatedData.email,
          id: { not: parseInt(id) }
        }
      });

      if (emailExists) {
        return NextResponse.json(
          { error: 'البريد الإلكتروني مستخدم بالفعل' },
          { status: 400 }
        );
      }
    }

    // تحديث المورد
    const supplier = await prisma.supplier.update({
      where: { id: parseInt(id) },
      data: validatedData,
      include: {
        _count: {
          select: {
            purchases: true,
            expenses: true
          }
        }
      }
    });

    return NextResponse.json(supplier);

  } catch (error) {
    console.error('Error updating supplier:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث المورد' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مورد
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المورد مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: {
            purchases: true,
            expenses: true
          }
        }
      }
    });

    if (!existingSupplier) {
      return NextResponse.json(
        { error: 'المورد غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود معاملات مرتبطة
    if (existingSupplier._count.purchases > 0 || existingSupplier._count.expenses > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف المورد لوجود معاملات مرتبطة به' },
        { status: 400 }
      );
    }

    // حذف المورد
    await prisma.supplier.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'تم حذف المورد بنجاح' });

  } catch (error) {
    console.error('Error deleting supplier:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف المورد' },
      { status: 500 }
    );
  }
}
