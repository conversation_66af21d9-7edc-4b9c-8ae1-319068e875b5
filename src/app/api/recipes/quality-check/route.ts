import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * إنشاء فحص جودة جديد
 * Create a new quality check
 */
export async function POST(request: NextRequest) {
  try {
    const { productionPlanId, checkDate, checkedBy, status, notes, parameters } = await request.json();

    if (!productionPlanId || !checkDate || !status || !parameters || !Array.isArray(parameters)) {
      return NextResponse.json(
        { error: 'بيانات غير صالحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود خطة الإنتاج
    const productionPlan = await prisma.productionPlan.findUnique({
      where: { id: parseInt(productionPlanId) }
    });

    if (!productionPlan) {
      return NextResponse.json(
        { error: 'خطة الإنتاج غير موجودة' },
        { status: 404 }
      );
    }

    // إنشاء فحص الجودة
    const qualityCheck = await prisma.qualityCheck.create({
      data: {
        productionPlanId: parseInt(productionPlanId),
        checkDate: new Date(checkDate),
        checkedBy,
        status,
        notes
      }
    });

    // إنشاء معايير الجودة
    const qualityParameters = [];
    for (const param of parameters) {
      const qualityParameter = await prisma.qualityParameter.create({
        data: {
          qualityCheckId: qualityCheck.id,
          name: param.name,
          expectedValue: param.expectedValue,
          actualValue: param.actualValue,
          isPassed: param.isPassed,
          notes: param.notes
        }
      });
      qualityParameters.push(qualityParameter);
    }

    // تحديث حالة خطة الإنتاج إذا كانت جميع المعايير ناجحة
    const allPassed = parameters.every(param => param.isPassed);
    if (status === 'PASSED' && allPassed) {
      await prisma.productionPlan.update({
        where: { id: parseInt(productionPlanId) },
        data: {
          status: 'COMPLETED',
          actualDate: new Date(),
          productionDate: new Date(),
          // حساب تاريخ انتهاء الصلاحية (مثال: 30 يوم من تاريخ الإنتاج)
          expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          // إنشاء رقم دفعة فريد
          batchNumber: `BATCH-${Date.now()}-${productionPlan.productId}`
        }
      });
    }

    return NextResponse.json({
      success: true,
      qualityCheck,
      qualityParameters,
      allPassed
    });
  } catch (error) {
    console.error('خطأ في إنشاء فحص الجودة:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء فحص الجودة' },
      { status: 500 }
    );
  }
}

/**
 * الحصول على فحوصات الجودة لخطة إنتاج معينة
 * Get quality checks for a specific production plan
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const productionPlanId = searchParams.get('productionPlanId');

    if (!productionPlanId) {
      return NextResponse.json(
        { error: 'معرف خطة الإنتاج مطلوب' },
        { status: 400 }
      );
    }

    const qualityChecks = await prisma.qualityCheck.findMany({
      where: {
        productionPlanId: parseInt(productionPlanId)
      },
      include: {
        parameters: true
      },
      orderBy: {
        checkDate: 'desc'
      }
    });

    return NextResponse.json(qualityChecks);
  } catch (error) {
    console.error('خطأ في جلب فحوصات الجودة:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب فحوصات الجودة' },
      { status: 500 }
    );
  }
}
