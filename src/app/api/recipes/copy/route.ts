import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * نسخ التقادير من منتج إلى آخر
 * Copy recipe items from one product to another
 */
export async function POST(request: NextRequest) {
  try {
    const { sourceProductId, targetProductId } = await request.json();

    if (!sourceProductId || !targetProductId) {
      return NextResponse.json(
        { error: 'معرف المنتج المصدر والمنتج الهدف مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من وجود المنتجين
    const sourceProduct = await prisma.product.findUnique({
      where: { id: parseInt(sourceProductId) }
    });

    const targetProduct = await prisma.product.findUnique({
      where: { id: parseInt(targetProductId) }
    });

    if (!sourceProduct || !targetProduct) {
      return NextResponse.json(
        { error: 'المنتج المصدر أو المنتج الهدف غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من أن المنتج المصدر مركب
    const sourceRecipeItems = await prisma.recipeItem.findMany({
      where: { productId: parseInt(sourceProductId) }
    });

    if (sourceRecipeItems.length === 0) {
      return NextResponse.json(
        { error: 'المنتج المصدر لا يحتوي على مقادير' },
        { status: 400 }
      );
    }

    // حذف المقادير الحالية للمنتج الهدف إن وجدت
    await prisma.recipeItem.deleteMany({
      where: { productId: parseInt(targetProductId) }
    });

    // نسخ المقادير من المنتج المصدر إلى المنتج الهدف
    const newRecipeItems = [];
    for (const item of sourceRecipeItems) {
      const newItem = await prisma.recipeItem.create({
        data: {
          productId: parseInt(targetProductId),
          ingredientId: item.ingredientId,
          quantity: item.quantity,
          unit: item.unit,
          notes: item.notes
        }
      });
      newRecipeItems.push(newItem);
    }

    // تحديث المنتج الهدف ليكون مركبًا
    await prisma.product.update({
      where: { id: parseInt(targetProductId) },
      data: { isComposite: true }
    });

    return NextResponse.json({
      success: true,
      message: 'تم نسخ المقادير بنجاح',
      recipeItems: newRecipeItems
    });
  } catch (error) {
    console.error('خطأ في نسخ المقادير:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء نسخ المقادير' },
      { status: 500 }
    );
  }
}
