import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * حساب تكلفة الإنتاج للمنتجات المركبة
 * Calculate production cost for composite products
 */
export async function POST(request: NextRequest) {
  try {
    const { productId, quantity = 1, includeLabor = false, laborCost = 0, includeOverhead = false, overheadCost = 0 } = await request.json();

    if (!productId) {
      return NextResponse.json(
        { error: 'معرف المنتج مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المنتج
    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) },
      include: {
        recipeItems: {
          include: {
            ingredient: true
          }
        }
      }
    });

    if (!product) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      );
    }

    if (!product.isComposite || product.recipeItems.length === 0) {
      return NextResponse.json(
        { error: 'المنتج ليس منتجًا مركبًا أو لا يحتوي على مقادير' },
        { status: 400 }
      );
    }

    // حساب تكلفة المكونات
    let ingredientsCost = 0;
    const ingredientsDetails = [];

    for (const item of product.recipeItems) {
      const ingredient = item.ingredient;
      const itemQuantity = item.quantity * quantity;
      const itemCost = (ingredient.cost || 0) * itemQuantity;

      ingredientsCost += itemCost;

      ingredientsDetails.push({
        id: ingredient.id,
        name: ingredient.name,
        quantity: itemQuantity,
        unit: item.unit || ingredient.unit,
        unitCost: ingredient.cost || 0,
        totalCost: itemCost
      });
    }

    // حساب تكلفة العمالة إذا تم تضمينها
    const totalLaborCost = includeLabor ? laborCost * quantity : 0;

    // حساب التكاليف العامة إذا تم تضمينها
    const totalOverheadCost = includeOverhead ? overheadCost * quantity : 0;

    // حساب إجمالي تكلفة الإنتاج
    const totalProductionCost = ingredientsCost + totalLaborCost + totalOverheadCost;

    // حساب تكلفة الوحدة
    const unitCost = totalProductionCost / quantity;

    // حساب هامش الربح
    const sellingPrice = product.price * quantity;
    const profitMargin = sellingPrice - totalProductionCost;
    const profitPercentage = (profitMargin / sellingPrice) * 100;

    return NextResponse.json({
      product: {
        id: product.id,
        name: product.name,
        sellingPrice: product.price,
        totalSellingPrice: sellingPrice
      },
      quantity,
      ingredientsCost,
      ingredientsDetails,
      laborCost: totalLaborCost,
      overheadCost: totalOverheadCost,
      totalProductionCost,
      unitCost,
      profitMargin,
      profitPercentage
    });
  } catch (error) {
    console.error('خطأ في حساب تكلفة الإنتاج:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حساب تكلفة الإنتاج' },
      { status: 500 }
    );
  }
}
