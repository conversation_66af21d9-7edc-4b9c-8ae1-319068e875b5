import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import QRCode from 'qrcode';

/**
 * إنشاء رمز QR للمنتج أو دفعة الإنتاج
 * Create QR code for a product or production batch
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get('productId');
    const batchNumber = searchParams.get('batchNumber');
    const productionPlanId = searchParams.get('productionPlanId');

    if (!productId && !batchNumber && !productionPlanId) {
      return NextResponse.json(
        { error: 'يجب توفير معرف المنتج أو رقم الدفعة أو معرف خطة الإنتاج' },
        { status: 400 }
      );
    }

    let data: any = {};
    let qrData: string = '';

    // إذا تم توفير معرف خطة الإنتاج
    if (productionPlanId) {
      const productionPlan = await prisma.productionPlan.findUnique({
        where: { id: parseInt(productionPlanId) },
        include: {
          product: true
        }
      });

      if (!productionPlan) {
        return NextResponse.json(
          { error: 'خطة الإنتاج غير موجودة' },
          { status: 404 }
        );
      }

      data = {
        type: 'production_plan',
        id: productionPlan.id,
        productId: productionPlan.productId,
        productName: productionPlan.product.name,
        quantity: productionPlan.quantity,
        batchNumber: productionPlan.batchNumber,
        productionDate: productionPlan.productionDate,
        expiryDate: productionPlan.expiryDate
      };

      qrData = JSON.stringify(data);
    }
    // إذا تم توفير رقم الدفعة
    else if (batchNumber) {
      const productionPlan = await prisma.productionPlan.findFirst({
        where: { batchNumber },
        include: {
          product: true
        }
      });

      if (!productionPlan) {
        return NextResponse.json(
          { error: 'رقم الدفعة غير موجود' },
          { status: 404 }
        );
      }

      data = {
        type: 'batch',
        batchNumber: productionPlan.batchNumber,
        productId: productionPlan.productId,
        productName: productionPlan.product.name,
        quantity: productionPlan.quantity,
        productionDate: productionPlan.productionDate,
        expiryDate: productionPlan.expiryDate
      };

      qrData = JSON.stringify(data);
    }
    // إذا تم توفير معرف المنتج
    else if (productId) {
      const product = await prisma.product.findUnique({
        where: { id: parseInt(productId) },
        include: {
          recipeItems: {
            include: {
              ingredient: true
            }
          }
        }
      });

      if (!product) {
        return NextResponse.json(
          { error: 'المنتج غير موجود' },
          { status: 404 }
        );
      }

      data = {
        type: 'product',
        id: product.id,
        name: product.name,
        price: product.price,
        isComposite: product.isComposite,
        ingredients: product.recipeItems.map(item => ({
          id: item.ingredient.id,
          name: item.ingredient.name,
          quantity: item.quantity,
          unit: item.unit || item.ingredient.unit
        }))
      };

      qrData = JSON.stringify(data);
    }

    // إنشاء رمز QR
    const qrCodeDataURL = await QRCode.toDataURL(qrData);

    return NextResponse.json({
      success: true,
      data,
      qrCode: qrCodeDataURL
    });
  } catch (error) {
    console.error('خطأ في إنشاء رمز QR:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء رمز QR' },
      { status: 500 }
    );
  }
}
