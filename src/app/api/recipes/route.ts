import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// الحصول على جميع المنتجات المركبة (التي تحتوي على مقادير)
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get('productId');

    // إذا تم تحديد معرف منتج، قم بإرجاع مقادير هذا المنتج فقط
    if (productId) {
      const recipeItems = await prisma.recipeItem.findMany({
        where: {
          productId: parseInt(productId),
        },
        include: {
          ingredient: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      return NextResponse.json(recipeItems);
    }

    // إرجاع جميع المنتجات المركبة مع مقاديرها
    const compositeProducts = await prisma.product.findMany({
      where: {
        isComposite: true,
      },
      include: {
        recipeItems: {
          include: {
            ingredient: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(compositeProducts);
  } catch (error) {
    console.error('خطأ في جلب المقادير:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب المقادير' },
      { status: 500 }
    );
  }
}

// إضافة أو تحديث مقادير منتج
export async function POST(request: NextRequest) {
  try {
    const { productId, ingredients } = await request.json();

    if (!productId || !ingredients || !Array.isArray(ingredients)) {
      return NextResponse.json(
        { error: 'بيانات غير صالحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود المنتج
    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      );
    }

    // تحديث المنتج ليكون مركبًا
    await prisma.product.update({
      where: { id: parseInt(productId) },
      data: { isComposite: true },
    });

    // حذف المقادير الحالية للمنتج
    await prisma.recipeItem.deleteMany({
      where: { productId: parseInt(productId) },
    });

    // إضافة المقادير الجديدة
    const recipeItems = [];
    for (const ingredient of ingredients) {
      const recipeItem = await prisma.recipeItem.create({
        data: {
          productId: parseInt(productId),
          ingredientId: parseInt(ingredient.ingredientId),
          quantity: parseFloat(ingredient.quantity),
          unit: ingredient.unit,
          notes: ingredient.notes,
        },
      });
      recipeItems.push(recipeItem);
    }

    return NextResponse.json({
      success: true,
      message: 'تم حفظ المقادير بنجاح',
      recipeItems,
    });
  } catch (error) {
    console.error('خطأ في حفظ المقادير:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حفظ المقادير' },
      { status: 500 }
    );
  }
}

// حذف مقادير منتج
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get('productId');

    if (!productId) {
      return NextResponse.json(
        { error: 'معرف المنتج مطلوب' },
        { status: 400 }
      );
    }

    // حذف جميع مقادير المنتج
    await prisma.recipeItem.deleteMany({
      where: { productId: parseInt(productId) },
    });

    // تحديث المنتج ليكون غير مركب
    await prisma.product.update({
      where: { id: parseInt(productId) },
      data: { isComposite: false },
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف المقادير بنجاح',
    });
  } catch (error) {
    console.error('خطأ في حذف المقادير:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف المقادير' },
      { status: 500 }
    );
  }
}
