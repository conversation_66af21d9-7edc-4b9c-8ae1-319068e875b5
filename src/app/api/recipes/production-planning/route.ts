import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * تخطيط الإنتاج للمنتجات المركبة
 * Production planning for composite products
 */
export async function POST(request: NextRequest) {
  try {
    const { productId, quantity, plannedDate } = await request.json();

    if (!productId || !quantity || !plannedDate) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة: معرف المنتج، الكمية، تاريخ الإنتاج المخطط' },
        { status: 400 }
      );
    }

    // التحقق من وجود المنتج
    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) },
      include: {
        recipeItems: {
          include: {
            ingredient: true
          }
        }
      }
    });

    if (!product) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      );
    }

    if (!product.isComposite || product.recipeItems.length === 0) {
      return NextResponse.json(
        { error: 'المنتج ليس منتجًا مركبًا أو لا يحتوي على مقادير' },
        { status: 400 }
      );
    }

    // التحقق من توفر المكونات
    const insufficientIngredients = [];
    for (const item of product.recipeItems) {
      const requiredQuantity = item.quantity * quantity;
      const availableQuantity = item.ingredient.stock || 0;

      if (availableQuantity < requiredQuantity) {
        insufficientIngredients.push({
          id: item.ingredient.id,
          name: item.ingredient.name,
          required: requiredQuantity,
          available: availableQuantity,
          shortage: requiredQuantity - availableQuantity,
          unit: item.unit || item.ingredient.unit
        });
      }
    }

    // إنشاء خطة الإنتاج
    const productionPlan = await prisma.productionPlan.create({
      data: {
        productId: parseInt(productId),
        quantity: parseInt(quantity),
        plannedDate: new Date(plannedDate),
        status: insufficientIngredients.length > 0 ? 'PENDING' : 'SCHEDULED',
        notes: insufficientIngredients.length > 0 ? 'نقص في المكونات' : ''
      }
    });

    // إنشاء تفاصيل خطة الإنتاج
    const productionPlanDetails = [];
    for (const item of product.recipeItems) {
      const detail = await prisma.productionPlanDetail.create({
        data: {
          productionPlanId: productionPlan.id,
          ingredientId: item.ingredientId,
          requiredQuantity: item.quantity * quantity,
          unit: item.unit || item.ingredient.unit,
          availableQuantity: item.ingredient.stock || 0
        }
      });
      productionPlanDetails.push(detail);
    }

    return NextResponse.json({
      success: true,
      productionPlan,
      productionPlanDetails,
      insufficientIngredients,
      canProceed: insufficientIngredients.length === 0
    });
  } catch (error) {
    console.error('خطأ في تخطيط الإنتاج:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تخطيط الإنتاج' },
      { status: 500 }
    );
  }
}
