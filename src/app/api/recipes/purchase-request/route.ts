import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * إنشاء طلب شراء للمكونات الناقصة
 * Create a purchase request for missing ingredients
 */
export async function POST(request: NextRequest) {
  try {
    const { productionPlanId, items } = await request.json();

    if (!productionPlanId || !items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'بيانات غير صالحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود خطة الإنتاج
    const productionPlan = await prisma.productionPlan.findUnique({
      where: { id: parseInt(productionPlanId) },
      include: {
        product: true
      }
    });

    if (!productionPlan) {
      return NextResponse.json(
        { error: 'خطة الإنتاج غير موجودة' },
        { status: 404 }
      );
    }

    // إنشاء طلب الشراء
    const purchaseRequest = await prisma.purchaseRequest.create({
      data: {
        title: `طلب مكونات لإنتاج ${productionPlan.product.name}`,
        status: 'PENDING',
        notes: `طلب مكونات لخطة الإنتاج رقم ${productionPlanId}`,
        requestedBy: 'النظام',
        requestDate: new Date()
      }
    });

    // إنشاء عناصر طلب الشراء
    const purchaseItems = [];
    for (const item of items) {
      const purchaseItem = await prisma.purchaseRequestItem.create({
        data: {
          purchaseRequestId: purchaseRequest.id,
          productId: parseInt(item.productId),
          quantity: parseFloat(item.quantity),
          unit: item.unit,
          estimatedPrice: item.estimatedPrice || 0,
          notes: item.notes
        }
      });
      purchaseItems.push(purchaseItem);
    }

    // تحديث حالة خطة الإنتاج
    await prisma.productionPlan.update({
      where: { id: parseInt(productionPlanId) },
      data: {
        notes: `${productionPlan.notes || ''} | تم إنشاء طلب شراء رقم ${purchaseRequest.id}`.trim()
      }
    });

    return NextResponse.json({
      success: true,
      purchaseRequest,
      purchaseItems
    });
  } catch (error) {
    console.error('خطأ في إنشاء طلب الشراء:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء طلب الشراء' },
      { status: 500 }
    );
  }
}
