// تم تعديل هذا الملف لتعطيل التحقق من كلمة المرور بناءً على طلب المستخدم
// This file has been modified to disable password verification as per user request

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import jwt from 'jsonwebtoken';


export async function POST(request: NextRequest) {
  try {
    let requestData = { email: '<EMAIL>' };

    try {
      requestData = await request.json();
    } catch (e) {
      // استخدام القيمة الافتراضية إذا لم يتم توفير بيانات
      console.log('No request body provided, using default email');
    }

    const email = requestData.email || '<EMAIL>';

    // Validation
    if (!email) {
      return NextResponse.json(
        { error: 'الرجاء إدخال البريد الإلكتروني' },
        { status: 400 }
      );
    }

    // Log login attempt
    console.log(`Login attempt for email: ${email}`);

    const user = await prisma.user.findUnique({
      where: {
        email,
        isActive: true // Only allow active users
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      // إذا لم يتم العثور على المستخدم، استخدم المستخدم التجريبي
      console.log(`User not found: ${email}, using mock user`);

      // إنشاء مستخدم افتراضي في الذاكرة
      const mockUser = {
        id: '1',
        name: 'مستخدم النظام',
        email: '<EMAIL>',
        role: {
          name: 'admin',
          rolePermissions: [
            { permission: { permission: 'MANAGE_USERS' } },
            { permission: { permission: 'MANAGE_CUSTOMERS' } },
            { permission: { permission: 'MANAGE_INVOICES' } },
            { permission: { permission: 'MANAGE_PRODUCTS' } },
            { permission: { permission: 'MANAGE_SETTINGS' } },
            { permission: { permission: 'VIEW_REPORTS' } }
          ]
        }
      };

      const permissions = mockUser.role.rolePermissions.map(rp => rp.permission.permission);

      // إنشاء توكن JWT للمستخدم الافتراضي
      const jwtSecret = process.env.JWT_SECRET ?? 'your-jwt-secret-key-here';
      const jwtExpiresIn = process.env.JWT_EXPIRES_IN ?? '30d';

      // @ts-ignore - Ignoring type issues with jwt.sign
      const token = jwt.sign(
        {
          id: mockUser.id,
          email: mockUser.email,
          name: mockUser.name,
          role: mockUser.role.name,
          permissions
        },
        jwtSecret,
        { expiresIn: jwtExpiresIn }
      );

      return NextResponse.json({
        user: {
          id: mockUser.id,
          name: mockUser.name,
          email: mockUser.email,
          role: mockUser.role.name,
          permissions
        },
        token
      });
    }

    // تم تعطيل التحقق من كلمة المرور بناءً على طلب المستخدم
    // Password verification has been disabled as per user request

    const permissions = user.role?.rolePermissions.map(rp => rp.permission.permission) ?? [];

    // Ensure the secret key and expiration are properly typed
    const jwtSecret = process.env.JWT_SECRET ?? 'your-jwt-secret-key-here';
    const jwtExpiresIn = process.env.JWT_EXPIRES_IN ?? '30d';

    // Generate JWT token for API clients
    const payload = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role?.name ?? 'user',
      permissions
    };

    // @ts-ignore - Ignoring type issues with jwt.sign
    const token = jwt.sign(
      payload,
      jwtSecret,
      { expiresIn: jwtExpiresIn }
    );

    // Return user data and token
    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role?.name ?? 'user',
        permissions
      },
      token
    });
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تسجيل الدخول' },
      { status: 500 }
    );
  }
}
