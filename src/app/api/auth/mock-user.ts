import { hash } from 'bcryptjs';

// هذا الملف يستخدم فقط في بيئة التطوير لإنشاء مستخدم تجريبي
// لا تستخدمه في بيئة الإنتاج

export async function createMockUser() {
  try {
    // إنشاء كلمة مرور مشفرة
    const hashedPassword = await hash('password123', 10);

    // إنشاء مستخدم تجريبي
    const mockUser = {
      id: '1',
      name: 'مستخدم تجريبي',
      email: '<EMAIL>',
      password: hashedPassword,
      isActive: true,
      role: {
        id: '1',
        name: 'مدير',
        rolePermissions: [
          { permission: { permission: 'MANAGE_USERS' } },
          { permission: { permission: 'MANAGE_CUSTOMERS' } },
          { permission: { permission: 'MANAGE_INVOICES' } },
          { permission: { permission: 'MANAGE_PRODUCTS' } },
          { permission: { permission: '<PERSON><PERSON>GE_SETTINGS' } },
          { permission: { permission: 'VIEW_REPORTS' } }
        ]
      }
    };

    return mockUser;
  } catch (error) {
    console.error('Error creating mock user:', error);
    throw error;
  }
}
