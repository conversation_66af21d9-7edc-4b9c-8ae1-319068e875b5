import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // استخدام مستخدم افتراضي للدخول التلقائي
    let requestData = { email: '<EMAIL>' };

    try {
      requestData = await request.json();
    } catch (e) {
      // استخدام القيمة الافتراضية إذا لم يتم توفير بيانات
      }

    const defaultEmail = requestData.email || '<EMAIL>';

    // البحث عن المستخدم في قاعدة البيانات
    const user = await prisma.user.findUnique({
      where: {
        email: defaultEmail,
        isActive: true
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });

    // إذا لم يتم العثور على المستخدم، حاول استخدام المستخدم التجريبي
    if (!user) {
      const testUser = await prisma.user.findUnique({
        where: {
          email: '<EMAIL>',
        },
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      });

      if (!testUser) {
        // إذا لم يتم العثور على أي مستخدم، قم بإنشاء مستخدم افتراضي في الذاكرة
        const mockUser = {
          id: '1',
          name: 'مستخدم النظام',
          email: '<EMAIL>',
          role: {
            name: 'admin',
            rolePermissions: [
              { permission: { permission: 'MANAGE_USERS' } },
              { permission: { permission: 'MANAGE_CUSTOMERS' } },
              { permission: { permission: 'MANAGE_INVOICES' } },
              { permission: { permission: 'MANAGE_PRODUCTS' } },
              { permission: { permission: 'MANAGE_SETTINGS' } },
              { permission: { permission: 'VIEW_REPORTS' } }
            ]
          }
        };

        // إنشاء توكن JWT للمستخدم الافتراضي
        const permissions = mockUser.role.rolePermissions.map(rp => rp.permission.permission);
        const token = jwt.sign(
          {
            id: mockUser.id,
            email: mockUser.email,
            name: mockUser.name,
            role: mockUser.role.name,
            permissions
          },
          process.env.JWT_SECRET ?? 'your-jwt-secret-key-here',
          { expiresIn: process.env.JWT_EXPIRES_IN ?? '30d' }
        );

        // تعيين كوكي الجلسة
        cookies().set('auth-token', token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 30 * 24 * 60 * 60, // 30 days
          path: '/',
        });

        // إرجاع بيانات المستخدم والتوكن
        return NextResponse.json({
          user: {
            id: mockUser.id,
            name: mockUser.name,
            email: mockUser.email,
            role: mockUser.role.name,
            permissions
          },
          token
        });
      }

      // استخدام المستخدم التجريبي
      const permissions = testUser.role?.rolePermissions.map(rp => rp.permission.permission) || [];

      // إنشاء توكن JWT للمستخدم التجريبي
      const token = jwt.sign(
        {
          id: testUser.id,
          email: testUser.email,
          name: testUser.name,
          role: testUser.role?.name ?? 'user',
          permissions
        },
        process.env.JWT_SECRET ?? 'your-jwt-secret-key-here',
        { expiresIn: process.env.JWT_EXPIRES_IN ?? '30d' } // Ensure expiresIn is a string
      );

      // تعيين كوكي الجلسة
      cookies().set('auth-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60, // 30 days
        path: '/',
      });

      // إرجاع بيانات المستخدم والتوكن
      return NextResponse.json({
        user: {
          id: testUser.id,
          name: testUser.name,
          email: testUser.email,
          role: testUser.role?.name ?? 'user',
          permissions
        },
        token
      });
    }

    // استخدام المستخدم الافتراضي
    const permissions = user.role?.rolePermissions.map(rp => rp.permission.permission) || [];

    // إنشاء توكن JWT للمستخدم الافتراضي
    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role?.name ?? 'user',
        permissions
      },
      process.env.JWT_SECRET ?? 'your-jwt-secret-key-here',
      { expiresIn: process.env.JWT_EXPIRES_IN ?? '30d' } // Ensure expiresIn is a string
    );

    // تعيين كوكي الجلسة
    cookies().set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/',
    });

    // إرجاع بيانات المستخدم والتوكن
    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role?.name ?? 'user',
        permissions
      },
      token
    });
  } catch (error) {
    console.error('Auto login error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء محاولة تسجيل الدخول التلقائي' },
      { status: 500 }
    );
  }
}
