import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma, calculatePagination, createPaginatedResult, createSearchFilter } from '@/lib/prisma';

// GET - جلب جميع العملاء
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const { skip } = calculatePagination({ page, limit });

    // بناء شروط البحث
    const where: any = {};

    // إضافة البحث النصي
    if (search) {
      const searchFilter = createSearchFilter(search, ['name', 'nameEn', 'email', 'phone']);
      Object.assign(where, searchFilter);
    }

    // إضافة فلتر الحالة
    if (status) {
      where.status = status;
    }

    // جلب العملاء مع العد الإجمالي
    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.customer.count({ where }),
    ]);

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const formattedCustomers = customers.map(customer => ({
      id: customer.id.toString(),
      name: customer.name,
      nameEn: customer.nameEn || '',
      email: customer.email || '',
      phone: customer.phone || '',
      address: customer.address || '',
      city: customer.city || '',
      country: customer.country || '',
      taxNumber: customer.taxNumber || '',
      contactPerson: customer.contactPerson || '',
      contactPhone: customer.contactPhone || '',
      contactEmail: customer.contactEmail || '',
      status: customer.status,
      customerType: customer.customerType || 'company',
      createdAt: customer.createdAt.toISOString(),
      updatedAt: customer.updatedAt.toISOString(),
    }));

    const paginatedResult = createPaginatedResult(formattedCustomers, total, page, limit);

    return NextResponse.json({
      customers: paginatedResult.data,
      pagination: paginatedResult.pagination
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST - إنشاء عميل جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // التحقق من صحة البيانات
    if (!body.name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    // التحقق من عدم تكرار البريد الإلكتروني (إذا تم توفيره)
    if (body.email) {
      const existingCustomer = await prisma.customer.findFirst({
        where: { email: body.email }
      });

      if (existingCustomer) {
        return NextResponse.json({ error: 'Customer with this email already exists' }, { status: 409 });
      }
    }

    // إنشاء العميل الجديد
    const newCustomer = await prisma.customer.create({
      data: {
        name: body.name,
        nameEn: body.nameEn || null,
        email: body.email || null,
        phone: body.phone || null,
        address: body.address || null,
        city: body.city || null,
        country: body.country || 'الإمارات العربية المتحدة',
        taxNumber: body.taxNumber || null,
        contactPerson: body.contactPerson || null,
        contactPhone: body.contactPhone || null,
        contactEmail: body.contactEmail || null,
        status: body.status || 'active',
        customerType: body.customerType || 'company',
        notes: body.notes || null,
      },
    });

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const formattedCustomer = {
      id: newCustomer.id.toString(),
      name: newCustomer.name,
      nameEn: newCustomer.nameEn || '',
      email: newCustomer.email || '',
      phone: newCustomer.phone || '',
      address: newCustomer.address || '',
      city: newCustomer.city || '',
      country: newCustomer.country || '',
      taxNumber: newCustomer.taxNumber || '',
      contactPerson: newCustomer.contactPerson || '',
      contactPhone: newCustomer.contactPhone || '',
      contactEmail: newCustomer.contactEmail || '',
      status: newCustomer.status,
      customerType: newCustomer.customerType || 'company',
      notes: newCustomer.notes || '',
      createdAt: newCustomer.createdAt.toISOString(),
      updatedAt: newCustomer.updatedAt.toISOString(),
    };

    return NextResponse.json(formattedCustomer, { status: 201 });
  } catch (error) {
    console.error('Error creating customer:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
