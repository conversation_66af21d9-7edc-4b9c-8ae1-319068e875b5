import { NextResponse } from "next/server";
// تصحيح مسار الاستيراد
import { prisma } from "@/lib/db";

export async function GET(
    request: Request,
    { params }: { params: { id: string; }; }
): Promise<NextResponse<any>> {
    try {
        const customer = await prisma.customer.findUnique({
            where: {
                id: parseInt(params.id),
            },
        });

        if (!customer) {
            return NextResponse.json(
                { error: "العميل غير موجود" },
                { status: 404 }
            );
        }

        return NextResponse.json(customer);
    } catch (error) {
        return NextResponse.json(
            { error: "حدث خطأ أثناء جلب بيانات العميل" },
            { status: 500 }
        );
    }
}
