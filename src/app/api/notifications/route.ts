import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * الحصول على الإشعارات
 * Get notifications
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    const whereClause: any = {};
    if (type) {
      whereClause.type = type;
    }

    const notifications = await prisma.notification.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    const totalCount = await prisma.notification.count({
      where: whereClause
    });

    return NextResponse.json({
      notifications,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('خطأ في جلب الإشعارات:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب الإشعارات' },
      { status: 500 }
    );
  }
}

/**
 * إنشاء إشعار جديد
 * Create a new notification
 */
export async function POST(request: NextRequest) {
  try {
    const { title, message, type, relatedId, relatedType, isRead = false } = await request.json();

    if (!title || !message || !type) {
      return NextResponse.json(
        { error: 'العنوان والرسالة والنوع مطلوبة' },
        { status: 400 }
      );
    }

    const notification = await prisma.notification.create({
      data: {
        title,
        message,
        type,
        relatedId: relatedId?.toString(),
        relatedType,
        isRead
      }
    });

    return NextResponse.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('خطأ في إنشاء إشعار:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء إشعار' },
      { status: 500 }
    );
  }
}

/**
 * تحديث حالة قراءة الإشعار
 * Update notification read status
 */
export async function PATCH(request: NextRequest) {
  try {
    const { id, isRead } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الإشعار مطلوب' },
        { status: 400 }
      );
    }

    const notification = await prisma.notification.update({
      where: { id: parseInt(id) },
      data: { isRead }
    });

    return NextResponse.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('خطأ في تحديث الإشعار:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث الإشعار' },
      { status: 500 }
    );
  }
}
