'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AutoLoginPage() {
  const [status, setStatus] = useState('جاري تسجيل الدخول تلقائيًا...');
  const router = useRouter();

  useEffect(() => {
    const autoLogin = async () => {
      try {
        // محاولة تسجيل الدخول باستخدام المستخدم المسؤول
        const response = await fetch('/api/auth/auto-login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        });

        const data = await response.json();

        if (!response.ok) {
          setStatus(`فشل تسجيل الدخول: ${data.error}`);
          return;
        }

        // تم تسجيل الدخول بنجاح، قم بالتوجيه إلى لوحة التحكم
        setStatus('تم تسجيل الدخول بنجاح! جاري التوجيه إلى لوحة التحكم...');
        
        // انتظر لحظة قبل التوجيه
        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);
      } catch (error) {
        setStatus(`حدث خطأ أثناء تسجيل الدخول: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    };

    autoLogin();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6 text-center">تسجيل الدخول التلقائي</h1>
        <div className="text-center">
          <div className="animate-pulse mb-4">
            <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
          <p className="text-gray-600">{status}</p>
        </div>
      </div>
    </div>
  );
}
