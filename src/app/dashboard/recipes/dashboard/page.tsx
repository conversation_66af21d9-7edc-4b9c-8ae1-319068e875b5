'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { 
  <PERSON><PERSON>hart, 
  PieChart, 
  LineChart, 
  Package, 
  AlertTriangle, 
  CheckCircle2, 
  Calendar, 
  TrendingUp, 
  TrendingDown,
  ArrowRight
} from 'lucide-react';

export default function ProductionDashboardPage() {
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState({
    totalProducts: 0,
    compositeProducts: 0,
    pendingProduction: 0,
    completedProduction: 0,
    lowStockIngredients: 0,
    qualityChecks: {
      passed: 0,
      failed: 0,
      pending: 0
    },
    wastagePercentage: 0,
    productionEfficiency: 0
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      // هنا يجب إنشاء API لجلب بيانات لوحة المعلومات
      // لأغراض العرض، سنستخدم بيانات وهمية
      
      // محاكاة تأخير الشبكة
      setTimeout(() => {
        setStats({
          totalProducts: 120,
          compositeProducts: 45,
          pendingProduction: 8,
          completedProduction: 32,
          lowStockIngredients: 12,
          qualityChecks: {
            passed: 28,
            failed: 3,
            pending: 5
          },
          wastagePercentage: 4.2,
          productionEfficiency: 87.5
        });
        setLoading(false);
      }, 1500);
    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة المعلومات:', error);
      toast.error('حدث خطأ أثناء جلب بيانات لوحة المعلومات');
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: 'لوحة معلومات الإنتاج', href: '/dashboard/recipes/dashboard' },
        ]}
      />

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">إجمالي المنتجات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-500 mr-3 rtl:ml-3 rtl:mr-0" />
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 animate-pulse rounded bg-gray-200"></div>
                  ) : (
                    stats.totalProducts
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  منها {loading ? '...' : stats.compositeProducts} منتج مركب
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">خطط الإنتاج</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-purple-500 mr-3 rtl:ml-3 rtl:mr-0" />
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 animate-pulse rounded bg-gray-200"></div>
                  ) : (
                    stats.pendingProduction + stats.completedProduction
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  {loading ? '...' : stats.pendingProduction} قيد التنفيذ | {loading ? '...' : stats.completedProduction} مكتملة
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">فحوصات الجودة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <CheckCircle2 className="h-8 w-8 text-green-500 mr-3 rtl:ml-3 rtl:mr-0" />
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 animate-pulse rounded bg-gray-200"></div>
                  ) : (
                    stats.qualityChecks.passed + stats.qualityChecks.failed + stats.qualityChecks.pending
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  {loading ? '...' : stats.qualityChecks.passed} ناجح | {loading ? '...' : stats.qualityChecks.failed} فاشل | {loading ? '...' : stats.qualityChecks.pending} معلق
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">المكونات منخفضة المخزون</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-amber-500 mr-3 rtl:ml-3 rtl:mr-0" />
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 animate-pulse rounded bg-gray-200"></div>
                  ) : (
                    stats.lowStockIngredients
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  بحاجة إلى إعادة طلب
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" />
              كفاءة الإنتاج
            </CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-500">
                  {loading ? (
                    <div className="h-9 w-24 animate-pulse rounded bg-gray-200"></div>
                  ) : (
                    `${stats.productionEfficiency}%`
                  )}
                </div>
                <p className="text-sm text-gray-500">
                  معدل الكفاءة الشهري
                </p>
              </div>
              <div className="h-16 w-32 bg-gray-100 rounded-md flex items-center justify-center">
                <LineChart className="h-8 w-8 text-gray-400" />
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="ghost" size="sm" className="w-full justify-between">
              عرض التفاصيل
              <ArrowRight className="h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingDown className="h-5 w-5 text-red-500 mr-2 rtl:ml-2 rtl:mr-0" />
              نسبة الهدر
            </CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-red-500">
                  {loading ? (
                    <div className="h-9 w-24 animate-pulse rounded bg-gray-200"></div>
                  ) : (
                    `${stats.wastagePercentage}%`
                  )}
                </div>
                <p className="text-sm text-gray-500">
                  معدل الهدر الشهري
                </p>
              </div>
              <div className="h-16 w-32 bg-gray-100 rounded-md flex items-center justify-center">
                <PieChart className="h-8 w-8 text-gray-400" />
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="ghost" size="sm" className="w-full justify-between">
              عرض التفاصيل
              <ArrowRight className="h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>الإنتاج الشهري</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-100 rounded-md flex items-center justify-center">
              <BarChart className="h-12 w-12 text-gray-400" />
              <span className="text-gray-500 mr-2">مخطط الإنتاج الشهري</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
