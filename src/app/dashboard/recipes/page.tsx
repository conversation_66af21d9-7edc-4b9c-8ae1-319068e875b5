'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { DataTable } from '@/components/ui/data-table';
import { toast } from 'sonner';
import { Edit, Trash2, Eye, PlusCircle } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  unit?: string;
  recipeItems: RecipeItem[];
}

interface RecipeItem {
  id: number;
  productId: number;
  ingredientId: number;
  quantity: number;
  unit?: string;
  notes?: string;
  ingredient: {
    id: number;
    name: string;
    price: number;
    unit?: string;
  };
}

export default function RecipesPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/recipes');
      if (!response.ok) {
        throw new Error('فشل في جلب المنتجات المركبة');
      }
      const data = await response.json();
      setProducts(data);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب المنتجات المركبة:', error);
      toast.error('حدث خطأ أثناء جلب المنتجات المركبة');
      setLoading(false);
    }
  };

  const handleAddRecipe = () => {
    router.push('/dashboard/recipes/new');
  };

  const handleEditRecipe = (id: number) => {
    router.push(`/dashboard/recipes/${id}`);
  };

  const handleViewRecipe = (id: number) => {
    router.push(`/dashboard/recipes/${id}/view`);
  };

  const handleDeleteRecipe = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه المقادير؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/recipes?productId=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('فشل في حذف المقادير');
      }

      toast.success('تم حذف المقادير بنجاح');
      fetchProducts();
    } catch (error) {
      console.error('خطأ في حذف المقادير:', error);
      toast.error('حدث خطأ أثناء حذف المقادير');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(price);
  };

  const columns = [
    {
      header: 'اسم المنتج',
      accessorKey: 'name',
    },
    {
      header: 'السعر',
      accessorKey: 'price',
      cell: ({ row }: any) => formatPrice(row.original.price),
    },
    {
      header: 'عدد المقادير',
      accessorKey: 'recipeItems',
      cell: ({ row }: any) => row.original.recipeItems.length,
    },
    {
      header: 'الإجراءات',
      cell: ({ row }: any) => (
        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleViewRecipe(row.original.id)}
            title="عرض المقادير"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleEditRecipe(row.original.id)}
            title="تعديل المقادير"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleDeleteRecipe(row.original.id)}
            title="حذف المقادير"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Breadcrumb
          items={[
            { label: 'الرئيسية', href: '/dashboard' },
            { label: 'التقادير', href: '/dashboard/recipes' },
          ]}
        />
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/recipes/dashboard')}>
            لوحة المعلومات
          </Button>
          <Button variant="outline" onClick={() => router.push('/dashboard/recipes/ingredients')}>
            إدارة المكونات
          </Button>
          <Button variant="outline" onClick={() => router.push('/dashboard/recipes/production-cost')}>
            تكلفة الإنتاج
          </Button>
          <Button variant="outline" onClick={() => router.push('/dashboard/recipes/production-planning')}>
            تخطيط الإنتاج
          </Button>
          <Button variant="outline" onClick={() => router.push('/dashboard/recipes/quality-check')}>
            فحص الجودة
          </Button>
          <Button variant="outline" onClick={() => router.push('/dashboard/recipes/analytics')}>
            تحليلات الإنتاج
          </Button>
          <Button variant="outline" onClick={() => router.push('/dashboard/recipes/tracking')}>
            تتبع المنتجات
          </Button>
          <Button onClick={handleAddRecipe}>
            <PlusCircle className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            إضافة تقادير جديدة
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div>
            <CardTitle>قائمة التقادير</CardTitle>
            <p className="text-sm text-gray-500 mt-1">Recipes List</p>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center p-4">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              لا توجد تقادير حتى الآن
              <br />
              <span className="text-sm">No recipes found</span>
            </div>
          ) : (
            <DataTable
              data={products}
              columns={columns}
              searchable
              searchKeys={['name']}
              pagination
              pageSize={10}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
