'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { Edit, ArrowLeft } from 'lucide-react';
import { CopyRecipeDialog } from '@/components/recipes/copy-recipe-dialog';

interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  unit?: string;
}

interface RecipeItem {
  id: number;
  productId: number;
  ingredientId: number;
  quantity: number;
  unit?: string;
  notes?: string;
  ingredient: Product;
}

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  
  const [product, setProduct] = useState<Product | null>(null);
  const [recipeItems, setRecipeItems] = useState<RecipeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetchRecipe();
  }, [resolvedParams.id]);

  const fetchRecipe = async () => {
    try {
      setLoading(true);

      // جلب المنتج
      const productResponse = await fetch(`/api/products/${resolvedParams.id}`);
      if (!productResponse.ok) {
        throw new Error('فشل في جلب المنتج');
      }
      const productData = await productResponse.json();
      setProduct(productData);

      // جلب المقادير
      const recipeResponse = await fetch(`/api/recipes?productId=${resolvedParams.id}`);
      if (!recipeResponse.ok) {
        throw new Error('فشل في جلب المقادير');
      }
      const recipeData = await recipeResponse.json();
      setRecipeItems(recipeData);

      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب التقادير:', error);
      toast.error('حدث خطأ أثناء جلب التقادير');
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(price);
  };

  const handleEdit = () => {
    router.push(`/dashboard/recipes/${resolvedParams.id}`);
  };

  const handleBack = () => {
    router.push('/dashboard/recipes');
  };

  // حساب التكلفة الإجمالية للمقادير
  const calculateTotalCost = () => {
    return recipeItems.reduce((total, item) => {
      const cost = item.ingredient.cost || item.ingredient.price;
      return total + (cost * item.quantity);
    }, 0);
  };

  // حساب نسبة هامش الربح
  const calculateProfitPercentage = () => {
    const totalCost = calculateTotalCost();
    if (totalCost === 0) return 0;
    return ((product.price - totalCost) / product.price) * 100;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-8">
        <p className="text-lg text-red-500">المنتج غير موجود</p>
        <Button variant="outline" className="mt-4" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
          العودة
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: product.name, href: `/dashboard/recipes/${resolvedParams.id}/view` },
        ]}
      />

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">تفاصيل التقادير</h1>
        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            العودة
          </Button>
          <Button onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            تعديل
          </Button>
          {recipeItems.length > 0 && (
            <CopyRecipeDialog
              sourceProductId={parseInt(resolvedParams.id)}
              onCopySuccess={() => toast.success('تم نسخ التقادير بنجاح')}
            />
          )}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>معلومات المنتج</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">اسم المنتج</p>
              <p className="font-medium">{product.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">السعر</p>
              <p className="font-medium">{formatPrice(product.price)}</p>
            </div>
            {product.unit && (
              <div>
                <p className="text-sm text-gray-500">الوحدة</p>
                <p className="font-medium">{product.unit}</p>
              </div>
            )}
            {product.description && (
              <div className="col-span-2">
                <p className="text-sm text-gray-500">الوصف</p>
                <p className="font-medium">{product.description}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>المقادير</CardTitle>
            <div className="text-sm text-gray-500">
              عدد المقادير: <span className="font-medium">{recipeItems.length}</span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {recipeItems.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              لا توجد مقادير لهذا المنتج
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-12 gap-4 font-medium text-gray-500 pb-2 border-b">
                <div className="col-span-5">المكون</div>
                <div className="col-span-2">الكمية</div>
                <div className="col-span-2">الوحدة</div>
                <div className="col-span-3">التكلفة</div>
              </div>
              {recipeItems.map((item) => (
                <div key={item.id} className="grid grid-cols-12 gap-4 py-2 border-b">
                  <div className="col-span-5">{item.ingredient.name}</div>
                  <div className="col-span-2">{item.quantity}</div>
                  <div className="col-span-2">{item.unit || item.ingredient.unit || '-'}</div>
                  <div className="col-span-3">{formatPrice(item.ingredient.price * item.quantity)}</div>
                </div>
              ))}
              <div className="grid grid-cols-12 gap-4 py-2 font-medium">
                <div className="col-span-9 text-left rtl:text-right">إجمالي التكلفة:</div>
                <div className="col-span-3">{formatPrice(calculateTotalCost())}</div>
              </div>
              <div className="grid grid-cols-12 gap-4 py-2 font-medium text-primary">
                <div className="col-span-9 text-left rtl:text-right">هامش الربح:</div>
                <div className="col-span-3">
                  {formatPrice(product.price - calculateTotalCost())}
                  <span className="text-sm mr-2">({calculateProfitPercentage().toFixed(2)}%)</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
