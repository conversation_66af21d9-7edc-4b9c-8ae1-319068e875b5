'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { Trash2, Plus } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  price: number;
  unit?: string;
}

interface RecipeItem {
  id: number;
  productId: number;
  ingredientId: number;
  quantity: number;
  unit?: string;
  notes?: string;
  ingredient: Product;
}

interface IngredientInput {
  ingredientId: number;
  quantity: number;
  unit?: string;
  notes?: string;
}

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  
  const [product, setProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [ingredients, setIngredients] = useState<IngredientInput[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchingData, setFetchingData] = useState(true);
  const [totalCost, setTotalCost] = useState<number>(0);
  const router = useRouter();

  useEffect(() => {
    fetchData();
  }, [resolvedParams.id]);

  // حساب التكلفة الإجمالية للمنتج المركب
  useEffect(() => {
    calculateTotalCost();
  }, [ingredients, products]);

  // دالة حساب التكلفة الإجمالية
  const calculateTotalCost = () => {
    let cost = 0;

    ingredients.forEach(ingredient => {
      if (ingredient.ingredientId > 0) {
        const product = products.find(p => p.id === ingredient.ingredientId);
        if (product && product.cost) {
          cost += product.cost * ingredient.quantity;
        }
      }
    });

    setTotalCost(cost);
  };

  const fetchData = async () => {
    try {
      setFetchingData(true);

      // جلب جميع المنتجات
      const productsResponse = await fetch('/api/products');
      if (!productsResponse.ok) {
        throw new Error('فشل في جلب المنتجات');
      }
      const productsData = await productsResponse.json();
      setProducts(productsData);

      // جلب المنتج الحالي
      const productResponse = await fetch(`/api/products/${resolvedParams.id}`);
      if (!productResponse.ok) {
        throw new Error('فشل في جلب المنتج');
      }
      const productData = await productResponse.json();
      setProduct(productData);

      // جلب المقادير
      const recipeResponse = await fetch(`/api/recipes?productId=${resolvedParams.id}`);
      if (!recipeResponse.ok) {
        throw new Error('فشل في جلب المقادير');
      }
      const recipeData = await recipeResponse.json();

      // تحويل البيانات إلى الصيغة المطلوبة
      const ingredientsData = recipeData.map((item: RecipeItem) => ({
        ingredientId: item.ingredient.id,
        quantity: item.quantity,
        unit: item.unit,
        notes: item.notes
      }));

      setIngredients(ingredientsData.length > 0 ? ingredientsData : [{ ingredientId: 0, quantity: 1 }]);

      setFetchingData(false);
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
      toast.error('حدث خطأ أثناء جلب البيانات');
      setFetchingData(false);
    }
  };

  const handleAddIngredient = () => {
    setIngredients([...ingredients, { ingredientId: 0, quantity: 1 }]);
  };

  const handleRemoveIngredient = (index: number) => {
    const newIngredients = [...ingredients];
    newIngredients.splice(index, 1);
    setIngredients(newIngredients);
  };

  const handleIngredientChange = (index: number, field: keyof IngredientInput, value: any) => {
    const newIngredients = [...ingredients];
    newIngredients[index] = { ...newIngredients[index], [field]: value };
    setIngredients(newIngredients);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!product) {
      toast.error('المنتج غير موجود');
      return;
    }

    if (ingredients.some(ing => ing.ingredientId === 0 || ing.quantity <= 0)) {
      toast.error('الرجاء إدخال جميع المقادير بشكل صحيح');
      return;
    }

    // التحقق من عدم استخدام المنتج كمكون في نفسه
    if (ingredients.some(ing => ing.ingredientId === product.id)) {
      toast.error('لا يمكن استخدام المنتج كمكون في نفسه');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/recipes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: product.id,
          ingredients,
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في حفظ المقادير');
      }

      toast.success('تم حفظ المقادير بنجاح');
      router.push('/dashboard/recipes');
    } catch (error) {
      console.error('خطأ في حفظ المقادير:', error);
      toast.error('حدث خطأ أثناء حفظ المقادير');
      setLoading(false);
    }
  };

  const getProductUnit = (productId: number) => {
    const product = products.find(p => p.id === productId);
    return product?.unit || '';
  };

  if (fetchingData) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-8">
        <p className="text-lg text-red-500">المنتج غير موجود</p>
        <Button variant="outline" className="mt-4" onClick={() => router.push('/dashboard/recipes')}>
          العودة
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: 'تعديل التقادير', href: `/dashboard/recipes/${resolvedParams.id}` },
        ]}
      />

      <Card>
        <CardHeader>
          <CardTitle>تعديل تقادير {product.name}</CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>المنتج</Label>
                <p className="p-2 border rounded-md bg-gray-50">{product.name}</p>
              </div>
              <div>
                <Label>السعر</Label>
                <p className="p-2 border rounded-md bg-gray-50">
                  {new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(product.price)}
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">المقادير</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddIngredient}
                >
                  <Plus className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  إضافة مكون
                </Button>
              </div>

              {ingredients.map((ingredient, index) => (
                <div key={`ingredient-${index}`} className="grid grid-cols-12 gap-4 items-end border p-4 rounded-md">
                  <div className="col-span-5">
                    <Label htmlFor={`ingredient-${index}`}>المكون</Label>
                    <Select
                      value={ingredient.ingredientId.toString()}
                      onValueChange={(value) => handleIngredientChange(index, 'ingredientId', parseInt(value))}
                    >
                      <SelectTrigger id={`ingredient-${index}`}>
                        <SelectValue placeholder="اختر المكون" />
                      </SelectTrigger>
                      <SelectContent>
                        {products
                          .filter(p => p.id !== product.id) // استبعاد المنتج الحالي
                          .map((p) => (
                            <SelectItem key={p.id} value={p.id.toString()}>
                              {p.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor={`quantity-${index}`}>الكمية</Label>
                    <Input
                      id={`quantity-${index}`}
                      type="number"
                      min="0.01"
                      step="0.01"
                      value={ingredient.quantity}
                      onChange={(e) => handleIngredientChange(index, 'quantity', parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor={`unit-${index}`}>الوحدة</Label>
                    <Input
                      id={`unit-${index}`}
                      value={ingredient.unit ?? getProductUnit(ingredient.ingredientId)}
                      onChange={(e) => handleIngredientChange(index, 'unit', e.target.value)}
                      placeholder="قطعة، كجم، لتر..."
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor={`notes-${index}`}>ملاحظات</Label>
                    <Input
                      id={`notes-${index}`}
                      value={ingredient.notes ?? ''}
                      onChange={(e) => handleIngredientChange(index, 'notes', e.target.value)}
                    />
                  </div>
                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveIngredient(index)}
                      disabled={ingredients.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              {/* عرض التكلفة الإجمالية */}
              {ingredients.some(ing => ing.ingredientId > 0) && (
                <div className="mt-6 p-4 border rounded-md bg-gray-50">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium">التكلفة الإجمالية للمقادير:</h3>
                    <p className="font-bold text-lg">
                      {new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(totalCost)}
                    </p>
                  </div>

                  {product && (
                    <>
                      <div className="flex justify-between items-center mt-2">
                        <h3 className="font-medium">سعر البيع:</h3>
                        <p className="font-bold text-lg">
                          {new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(product.price)}
                        </p>
                      </div>
                      <div className="flex justify-between items-center mt-2 text-primary">
                        <h3 className="font-medium">هامش الربح:</h3>
                        <p className="font-bold text-lg">
                          {new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(product.price - totalCost)}
                        </p>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/dashboard/recipes')}
            >
              إلغاء
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'جاري الحفظ...' : 'حفظ المقادير'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
