'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { CalendarIcon, AlertTriangle, CheckCircle2 } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  price: number;
  isComposite: boolean;
}

interface InsufficientIngredient {
  id: number;
  name: string;
  required: number;
  available: number;
  shortage: number;
  unit?: string;
}

interface ProductionPlanResponse {
  success: boolean;
  productionPlan: {
    id: number;
    productId: number;
    quantity: number;
    plannedDate: string;
    status: string;
  };
  insufficientIngredients: InsufficientIngredient[];
  canProceed: boolean;
}

export default function ProductionPlanningPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [plannedDate, setPlannedDate] = useState<Date>(new Date());
  const [planningResult, setPlanningResult] = useState<ProductionPlanResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchingProducts, setFetchingProducts] = useState<boolean>(true);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setFetchingProducts(true);
      const response = await fetch('/api/recipes');
      if (!response.ok) {
        throw new Error('فشل في جلب المنتجات المركبة');
      }
      const data = await response.json();
      setProducts(data);
      setFetchingProducts(false);
    } catch (error) {
      console.error('خطأ في جلب المنتجات المركبة:', error);
      toast.error('حدث خطأ أثناء جلب المنتجات المركبة');
      setFetchingProducts(false);
    }
  };

  const handlePlanProduction = async () => {
    if (!selectedProductId) {
      toast.error('الرجاء اختيار منتج');
      return;
    }

    if (quantity <= 0) {
      toast.error('الرجاء إدخال كمية صالحة');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/recipes/production-planning', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: selectedProductId,
          quantity,
          plannedDate: plannedDate.toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في تخطيط الإنتاج');
      }

      const data = await response.json();
      setPlanningResult(data);
      
      if (data.canProceed) {
        toast.success('تم إنشاء خطة الإنتاج بنجاح');
      } else {
        toast.warning('تم إنشاء خطة الإنتاج ولكن هناك نقص في المكونات');
      }
      
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تخطيط الإنتاج:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تخطيط الإنتاج');
      setLoading(false);
    }
  };

  const handleReset = () => {
    setSelectedProductId('');
    setQuantity(1);
    setPlannedDate(new Date());
    setPlanningResult(null);
  };

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: 'تخطيط الإنتاج', href: '/dashboard/recipes/production-planning' },
        ]}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>تخطيط الإنتاج</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="product">المنتج المركب</Label>
              <Select
                value={selectedProductId}
                onValueChange={setSelectedProductId}
                disabled={fetchingProducts || loading}
              >
                <SelectTrigger id="product">
                  <SelectValue placeholder="اختر المنتج" />
                </SelectTrigger>
                <SelectContent>
                  {fetchingProducts ? (
                    <div className="flex justify-center p-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    </div>
                  ) : products.length === 0 ? (
                    <div className="p-2 text-center text-gray-500">لا توجد منتجات مركبة</div>
                  ) : (
                    products.map((product) => (
                      <SelectItem key={product.id} value={product.id.toString()}>
                        {product.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quantity">الكمية</Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="plannedDate">تاريخ الإنتاج المخطط</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-right"
                    disabled={loading}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {plannedDate ? format(plannedDate, 'PPP', { locale: ar }) : 'اختر تاريخ'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={plannedDate}
                    onSelect={(date) => date && setPlannedDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={handleReset} disabled={loading}>
              إعادة تعيين
            </Button>
            <Button onClick={handlePlanProduction} disabled={loading || !selectedProductId || quantity <= 0}>
              {loading ? 'جاري التخطيط...' : 'تخطيط الإنتاج'}
            </Button>
          </CardFooter>
        </Card>

        {planningResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {planningResult.canProceed ? (
                  <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 rtl:ml-2 rtl:mr-0" />
                )}
                نتيجة تخطيط الإنتاج
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 border-b pb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">رقم الخطة</h3>
                  <p className="font-medium">#{planningResult.productionPlan.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">الحالة</h3>
                  <p className="font-medium">
                    {planningResult.productionPlan.status === 'SCHEDULED' ? 'مجدولة' : 'معلقة'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">المنتج</h3>
                  <p className="font-medium">
                    {products.find(p => p.id === parseInt(selectedProductId))?.name}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">الكمية</h3>
                  <p className="font-medium">{planningResult.productionPlan.quantity}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">تاريخ الإنتاج المخطط</h3>
                  <p className="font-medium">
                    {format(new Date(planningResult.productionPlan.plannedDate), 'PPP', { locale: ar })}
                  </p>
                </div>
              </div>

              {planningResult.insufficientIngredients.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-amber-600 flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    مكونات غير كافية
                  </h3>
                  <div className="space-y-2">
                    {planningResult.insufficientIngredients.map((ingredient) => (
                      <div key={ingredient.id} className="border rounded-md p-3 bg-amber-50">
                        <h4 className="font-medium">{ingredient.name}</h4>
                        <div className="grid grid-cols-3 gap-2 mt-2 text-sm">
                          <div>
                            <span className="text-gray-500">المطلوب:</span>{' '}
                            <span className="font-medium">
                              {ingredient.required} {ingredient.unit}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">المتوفر:</span>{' '}
                            <span className="font-medium">
                              {ingredient.available} {ingredient.unit}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">النقص:</span>{' '}
                            <span className="font-medium text-red-600">
                              {ingredient.shortage} {ingredient.unit}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {planningResult.canProceed && (
                <div className="bg-green-50 p-4 rounded-md border border-green-200">
                  <p className="text-green-700 flex items-center">
                    <CheckCircle2 className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    جميع المكونات متوفرة ويمكن البدء في الإنتاج
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                variant={planningResult.canProceed ? 'default' : 'outline'}
                onClick={() => toast.info('سيتم تنفيذ هذه الميزة قريبًا')}
              >
                {planningResult.canProceed ? 'بدء الإنتاج' : 'طلب المكونات الناقصة'}
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}
