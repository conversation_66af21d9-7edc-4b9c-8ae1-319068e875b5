'use client';

import {useState} from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import { ar } from 'date-fns/locale';
import { CalendarIcon, BarChart, PieChart, LineChart, Download, Printer } from 'lucide-react';

export default function ProductionAnalyticsPage() {
  const [startDate, setStartDate] = useState<Date>(startOfMonth(subMonths(new Date(), 1)));
  const [endDate, setEndDate] = useState<Date>(endOfMonth(new Date()));
  const [reportType, setReportType] = useState<string>('production');
  const [loading, setLoading] = useState<boolean>(false);

  const handleGenerateReport = () => {
    setLoading(true);
    // هنا يمكن إضافة كود لجلب البيانات وإنشاء التقرير
    setTimeout(() => {
      setLoading(false);
      toast.success('تم إنشاء التقرير بنجاح');
    }, 1500);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    toast.info('جاري تصدير التقرير إلى PDF...');
    // هنا يمكن إضافة كود لتصدير التقرير إلى PDF
  };

  const handleExportExcel = () => {
    toast.info('جاري تصدير التقرير إلى Excel...');
    // هنا يمكن إضافة كود لتصدير التقرير إلى Excel
  };

  return (
    <div className="space-y-4">
      <Breadcrumb
        items={[
          { label: 'الرئيسية', href: '/dashboard' },
          { label: 'التقادير', href: '/dashboard/recipes' },
          { label: 'تحليلات الإنتاج', href: '/dashboard/recipes/analytics' },
        ]}
      />

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>إعدادات التقرير</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="reportType">نوع التقرير</Label>
              <Select
                value={reportType}
                onValueChange={setReportType}
                disabled={loading}
              >
                <SelectTrigger id="reportType">
                  <SelectValue placeholder="اختر نوع التقرير" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="production">تقرير الإنتاج</SelectItem>
                  <SelectItem value="quality">تقرير الجودة</SelectItem>
                  <SelectItem value="wastage">تقرير الهدر</SelectItem>
                  <SelectItem value="cost">تقرير التكلفة</SelectItem>
                  <SelectItem value="efficiency">تقرير الكفاءة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate">تاريخ البداية</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-right"
                    disabled={loading}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {startDate ? format(startDate, 'PPP', { locale: ar }) : 'اختر تاريخ'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => date && setStartDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">تاريخ النهاية</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-right"
                    disabled={loading}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {endDate ? format(endDate, 'PPP', { locale: ar }) : 'اختر تاريخ'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <Button
              className="w-full"
              onClick={handleGenerateReport}
              disabled={loading}
            >
              {loading ? 'جاري إنشاء التقرير...' : 'إنشاء التقرير'}
            </Button>
          </CardContent>
        </Card>

        <div className="md:col-span-3 space-y-4 print:w-full">
          <Card className="print:shadow-none">
            <CardHeader className="flex flex-row items-center justify-between print:pb-0">
              <div>
                <CardTitle>
                  {reportType === 'production' && 'تقرير الإنتاج'}
                  {reportType === 'quality' && 'تقرير الجودة'}
                  {reportType === 'wastage' && 'تقرير الهدر'}
                  {reportType === 'cost' && 'تقرير التكلفة'}
                  {reportType === 'efficiency' && 'تقرير الكفاءة'}
                </CardTitle>
                <p className="text-sm text-gray-500 mt-1">
                  {format(startDate, 'PPP', { locale: ar })} - {format(endDate, 'PPP', { locale: ar })}
                </p>
              </div>
              <div className="flex space-x-2 rtl:space-x-reverse print:hidden">
                <Button variant="outline" size="sm" onClick={handlePrint}>
                  <Printer className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  طباعة
                </Button>
                <Button variant="outline" size="sm" onClick={handleExportPDF}>
                  <FileText className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  PDF
                </Button>
                <Button variant="outline" size="sm" onClick={handleExportExcel}>
                  <Download className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  Excel
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center p-12">
                  <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : (
                <div className="space-y-8">
                  {/* مخطط الإنتاج */}
                  {reportType === 'production' && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">إجمالي الإنتاج حسب المنتج</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <BarChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط الإنتاج</span>
                      </div>

                      <h3 className="text-lg font-medium mt-6">الإنتاج الشهري</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <LineChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط الإنتاج الشهري</span>
                      </div>
                    </div>
                  )}

                  {/* مخطط الجودة */}
                  {reportType === 'quality' && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">نسبة نجاح فحوصات الجودة</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <PieChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط نسبة النجاح</span>
                      </div>

                      <h3 className="text-lg font-medium mt-6">معدل نجاح المعايير</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <BarChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط معدل نجاح المعايير</span>
                      </div>
                    </div>
                  )}

                  {/* مخطط الهدر */}
                  {reportType === 'wastage' && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">نسبة الهدر حسب المكون</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <PieChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط نسبة الهدر</span>
                      </div>

                      <h3 className="text-lg font-medium mt-6">تكلفة الهدر الشهرية</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <LineChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط تكلفة الهدر</span>
                      </div>
                    </div>
                  )}

                  {/* مخطط التكلفة */}
                  {reportType === 'cost' && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">توزيع التكاليف</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <PieChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط توزيع التكاليف</span>
                      </div>

                      <h3 className="text-lg font-medium mt-6">تطور التكلفة الشهرية</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <LineChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط تطور التكلفة</span>
                      </div>
                    </div>
                  )}

                  {/* مخطط الكفاءة */}
                  {reportType === 'efficiency' && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">كفاءة الإنتاج حسب المنتج</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <BarChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط كفاءة الإنتاج</span>
                      </div>

                      <h3 className="text-lg font-medium mt-6">تطور الكفاءة الشهرية</h3>
                      <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                        <LineChart className="h-12 w-12 text-gray-400" />
                        <span className="text-gray-500 mr-2">مخطط تطور الكفاءة</span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
