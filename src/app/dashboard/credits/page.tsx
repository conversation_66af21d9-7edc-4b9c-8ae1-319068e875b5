'use client';

import { useState, useEffect } from 'react';
import { formatCurrency } from '@/lib/utils';

interface Credit {
  id: string;
  customerId: string;
  customerName: string;
  amount: number;
  date: string;
  dueDate: string;
  description: string;
  status: 'pending' | 'partial' | 'paid';
  paidAmount: number;
  lastPaymentDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
}

export default function CreditsPage() {
  const [credits, setCredits] = useState<Credit[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [currentCredit, setCurrentCredit] = useState<Credit | null>(null);
  const [filter, setFilter] = useState({
    status: '',
    customer: '',
    startDate: '',
    endDate: ''
  });
  
  useEffect(() => {
    // استدعاء API للحصول على الديون والعملاء
    const fetchData = async () => {
      setLoading(true);
      try {
        // في التطبيق الحقيقي، هذا سيكون استدعاء API
        const creditsData = localStorage.getItem('credits');
        if (creditsData) {
          setCredits(JSON.parse(creditsData));
        } else {
          setCredits([]);
        }
        
        const customersData = localStorage.getItem('customers');
        if (customersData) {
          setCustomers(JSON.parse(customersData));
        } else {
          setCustomers([]);
        }
      } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // حساب إجمالي الديون
  const totalCredits = credits.reduce((total, credit) => total + (credit.amount - credit.paidAmount), 0);
  
  // حساب الديون المستحقة (تاريخ الاستحقاق قبل اليوم)
  const overdueCredits = credits.filter(credit => 
    credit.status !== 'paid' && new Date(credit.dueDate) < new Date()
  ).reduce((total, credit) => total + (credit.amount - credit.paidAmount), 0);
  
  // تصفية الديون
  const filteredCredits = credits.filter(credit => {
    let match = true;
    
    if (filter.status && credit.status !== filter.status) {
      match = false;
    }
    
    if (filter.customer && credit.customerId !== filter.customer) {
      match = false;
    }
    
    if (filter.startDate && new Date(credit.date) < new Date(filter.startDate)) {
      match = false;
    }
    
    if (filter.endDate && new Date(credit.date) > new Date(filter.endDate)) {
      match = false;
    }
    
    return match;
  });
  
  // إضافة دين جديد
  const handleAddCredit = () => {
    setCurrentCredit(null);
    setShowModal(true);
  };
  
  // تعديل دين
  const handleEditCredit = (credit: Credit) => {
    setCurrentCredit(credit);
    setShowModal(true);
  };
  
  // حذف دين
  const handleDeleteCredit = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الدين؟')) {
      const updatedCredits = credits.filter(credit => credit.id !== id);
      setCredits(updatedCredits);
      localStorage.setItem('credits', JSON.stringify(updatedCredits));
    }
  };
  
  // فتح نموذج الدفع
  const handleOpenPaymentModal = (credit: Credit) => {
    setCurrentCredit(credit);
    setShowPaymentModal(true);
  };
  
  // حفظ الدين (إضافة أو تعديل)
  const handleSaveCredit = (formData: any) => {
    if (currentCredit) {
      // تعديل دين موجود
      const updatedCredits = credits.map(credit => 
        credit.id === currentCredit.id 
          ? { ...credit, ...formData, updatedAt: new Date().toISOString() }
          : credit
      );
      setCredits(updatedCredits);
      localStorage.setItem('credits', JSON.stringify(updatedCredits));
    } else {
      // إضافة دين جديد
      const newCredit: Credit = {
        id: Math.random().toString(36).substring(2, 9),
        ...formData,
        paidAmount: 0,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const updatedCredits = [...credits, newCredit];
      setCredits(updatedCredits);
      localStorage.setItem('credits', JSON.stringify(updatedCredits));
    }
    
    setShowModal(false);
  };
  
  // تسجيل دفعة
  const handleRecordPayment = (paymentData: { amount: number }) => {
    if (!currentCredit) return;
    
    const newPaidAmount = currentCredit.paidAmount + paymentData.amount;
    let newStatus: 'pending' | 'partial' | 'paid' = 'pending';
    
    if (newPaidAmount >= currentCredit.amount) {
      newStatus = 'paid';
    } else if (newPaidAmount > 0) {
      newStatus = 'partial';
    }
    
    const updatedCredits = credits.map(credit => 
      credit.id === currentCredit.id 
        ? { 
            ...credit, 
            paidAmount: newPaidAmount, 
            status: newStatus, 
            lastPaymentDate: new Date().toISOString(),
            updatedAt: new Date().toISOString() 
          }
        : credit
    );
    
    setCredits(updatedCredits);
    localStorage.setItem('credits', JSON.stringify(updatedCredits));
    setShowPaymentModal(false);
  };
  
  // الحصول على اسم العميل من المعرف
  const getCustomerName = (customerId: string): string => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'غير معروف';
  };
  
  // الحصول على نص حالة الدين
  const getCreditStatusText = (status: string): string => {
    switch (status) {
      case 'pending':
        return 'قيد الانتظار | Pending';
      case 'partial':
        return 'مدفوع جزئياً | Partial';
      case 'paid':
        return 'مدفوع | Paid';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة الدين
  const getCreditStatusColor = (status: string): string => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'partial':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1 dark:text-white">الديون والائتمان</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">Credits & Debts</p>
        </div>
        <button
          onClick={handleAddCredit}
          className="px-4 py-2 bg-primary text-white rounded-md text-sm"
        >
          إضافة دين جديد | Add Credit
        </button>
      </div>
      
      {/* ملخص الديون */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
          <h3 className="text-sm text-blue-500 dark:text-blue-300">إجمالي الديون المستحقة</h3>
          <p className="text-2xl font-bold text-blue-700 dark:text-blue-100">
            {formatCurrency(totalCredits)}
          </p>
        </div>
        
        <div className="bg-red-50 dark:bg-red-900 p-4 rounded-lg">
          <h3 className="text-sm text-red-500 dark:text-red-300">الديون المتأخرة</h3>
          <p className="text-2xl font-bold text-red-700 dark:text-red-100">
            {formatCurrency(overdueCredits)}
          </p>
        </div>
        
        <div className="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
          <h3 className="text-sm text-purple-500 dark:text-purple-300">عدد الديون النشطة</h3>
          <p className="text-2xl font-bold text-purple-700 dark:text-purple-100">
            {credits.filter(credit => credit.status !== 'paid').length}
          </p>
        </div>
      </div>
      
      {/* فلاتر البحث */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold mb-3 dark:text-white">تصفية الديون</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">الحالة</label>
            <select
              value={filter.status}
              onChange={(e) => setFilter({...filter, status: e.target.value})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="">الكل</option>
              <option value="pending">قيد الانتظار</option>
              <option value="partial">مدفوع جزئياً</option>
              <option value="paid">مدفوع</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">العميل</label>
            <select
              value={filter.customer}
              onChange={(e) => setFilter({...filter, customer: e.target.value})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="">الكل</option>
              {customers.map(customer => (
                <option key={customer.id} value={customer.id}>{customer.name}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">من تاريخ</label>
            <input
              type="date"
              value={filter.startDate}
              onChange={(e) => setFilter({...filter, startDate: e.target.value})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">إلى تاريخ</label>
            <input
              type="date"
              value={filter.endDate}
              onChange={(e) => setFilter({...filter, endDate: e.target.value})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>
      </div>
      
      {/* جدول الديون */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="p-2 text-right border dark:border-gray-600">العميل</th>
              <th className="p-2 text-right border dark:border-gray-600">التاريخ</th>
              <th className="p-2 text-right border dark:border-gray-600">تاريخ الاستحقاق</th>
              <th className="p-2 text-right border dark:border-gray-600">المبلغ</th>
              <th className="p-2 text-right border dark:border-gray-600">المدفوع</th>
              <th className="p-2 text-right border dark:border-gray-600">المتبقي</th>
              <th className="p-2 text-right border dark:border-gray-600">الحالة</th>
              <th className="p-2 text-center border dark:border-gray-600">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={8} className="p-4 text-center">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </td>
              </tr>
            ) : filteredCredits.length === 0 ? (
              <tr>
                <td colSpan={8} className="p-4 text-center text-gray-500 dark:text-gray-400">
                  لا توجد ديون مطابقة للفلاتر المحددة
                </td>
              </tr>
            ) : (
              filteredCredits.map(credit => (
                <tr key={credit.id} className="border-b dark:border-gray-700">
                  <td className="p-2 border dark:border-gray-600">
                    {getCustomerName(credit.customerId)}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {new Date(credit.date).toLocaleDateString('ar-AE')}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {new Date(credit.dueDate).toLocaleDateString('ar-AE')}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {formatCurrency(credit.amount)}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {formatCurrency(credit.paidAmount)}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {formatCurrency(credit.amount - credit.paidAmount)}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    <span className={`px-2 py-1 rounded-full text-xs ${getCreditStatusColor(credit.status)}`}>
                      {getCreditStatusText(credit.status)}
                    </span>
                  </td>
                  <td className="p-2 border dark:border-gray-600 text-center">
                    <div className="flex justify-center space-x-1 space-x-reverse">
                      {credit.status !== 'paid' && (
                        <button
                          onClick={() => handleOpenPaymentModal(credit)}
                          className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-md text-xs"
                        >
                          دفع
                        </button>
                      )}
                      <button
                        onClick={() => handleEditCredit(credit)}
                        className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-md text-xs"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleDeleteCredit(credit.id)}
                        className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 rounded-md text-xs"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* نموذج إضافة/تعديل دين */}
      {showModal && (
        <CreditForm
          credit={currentCredit}
          customers={customers}
          onSave={handleSaveCredit}
          onCancel={() => setShowModal(false)}
        />
      )}
      
      {/* نموذج تسجيل دفعة */}
      {showPaymentModal && currentCredit && (
        <PaymentForm
          credit={currentCredit}
          customerName={getCustomerName(currentCredit.customerId)}
          onSave={handleRecordPayment}
          onCancel={() => setShowPaymentModal(false)}
        />
      )}
    </div>
  );
}

// مكون نموذج الدين
function CreditForm({ credit, customers, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    customerId: credit?.customerId || (customers.length > 0 ? customers[0].id : ''),
    amount: credit?.amount || '',
    date: credit?.date || new Date().toISOString().split('T')[0],
    dueDate: credit?.dueDate || '',
    description: credit?.description || ''
  });
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' ? parseFloat(value) || '' : value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4 dark:text-white">
          {credit ? 'تعديل دين' : 'إضافة دين جديد'}
        </h2>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              العميل <span className="text-red-500">*</span>
            </label>
            <select
              name="customerId"
              value={formData.customerId}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            >
              {customers.length === 0 ? (
                <option value="">لا يوجد عملاء</option>
              ) : (
                customers.map(customer => (
                  <option key={customer.id} value={customer.id}>{customer.name}</option>
                ))
              )}
            </select>
          </div>
          
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              المبلغ <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>
          
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              تاريخ الدين <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>
          
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              تاريخ الاستحقاق <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              name="dueDate"
              value={formData.dueDate}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>
          
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              الوصف
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              rows={3}
            ></textarea>
          </div>
          
          <div className="flex justify-end space-x-2 space-x-reverse">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:text-white rounded-md"
            >
              إلغاء | Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md"
            >
              {credit ? 'تحديث | Update' : 'إضافة | Add'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// مكون نموذج الدفع
function PaymentForm({ credit, customerName, onSave, onCancel }) {
  const [amount, setAmount] = useState('');
  const remainingAmount = credit.amount - credit.paidAmount;
  
  const handleSubmit = (e) => {
    e.preventDefault();
    const paymentAmount = parseFloat(amount);
    
    if (isNaN(paymentAmount) || paymentAmount <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }
    
    if (paymentAmount > remainingAmount) {
      alert(`المبلغ المدخل أكبر من المبلغ المتبقي (${formatCurrency(remainingAmount)})`);
      return;
    }
    
    onSave({ amount: paymentAmount });
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4 dark:text-white">
          تسجيل دفعة
        </h2>
        
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
          <p className="text-sm dark:text-gray-300">
            <span className="font-semibold">العميل:</span> {customerName}
          </p>
          <p className="text-sm dark:text-gray-300">
            <span className="font-semibold">المبلغ الإجمالي:</span> {formatCurrency(credit.amount)}
          </p>
          <p className="text-sm dark:text-gray-300">
            <span className="font-semibold">المبلغ المدفوع:</span> {formatCurrency(credit.paidAmount)}
          </p>
          <p className="text-sm font-semibold text-primary">
            <span>المبلغ المتبقي:</span> {formatCurrency(remainingAmount)}
          </p>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              مبلغ الدفعة <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              step="0.01"
              min="0"
              max={remainingAmount}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>
          
          <div className="flex justify-end space-x-2 space-x-reverse">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:text-white rounded-md"
            >
              إلغاء | Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md"
            >
              تسجيل الدفعة | Record Payment
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
