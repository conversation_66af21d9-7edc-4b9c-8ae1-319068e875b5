'use client';

import { useState, useEffect, use } from 'react';
import { useParams } from 'next/navigation';

export default function InvoicePrintPage() {
  const params = useParams();
  const [invoice, setInvoice] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const mockInvoice = {
      id: resolvedParams.id,
      number: 'INV-001',
      issueDate: new Date().toISOString(),
      customer: {
        name: 'عميل تجريبي',
        nameEn: 'Test Customer',
        address: 'دبي، الإمارات العربية المتحدة'
      },
      items: [
        {
          id: 1,
          description: 'منتج تجريبي',
          quantity: 2,
          unitPrice: 100,
          total: 200
        }
      ],
      subtotal: 200,
      taxRate: 5,
      taxAmount: 10,
      total: 210
    };

    setTimeout(() => {
      setInvoice(mockInvoice);
      setLoading(false);
    }, 1000);
  }, [resolvedParams.id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="mr-3">جاري تحميل الفاتورة...</span>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-bold text-gray-600">الفاتورة غير موجودة</h2>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="print:hidden mb-4 flex justify-end">
        <button
          onClick={() => window.print()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          طباعة الفاتورة
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
        <div className="border-b-2 border-blue-600 pb-6 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-blue-800 mb-2">فاتورة ضريبية</h1>
              <h2 className="text-xl text-gray-600">TAX INVOICE</h2>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-800">شركة أمين بلس</div>
              <div className="text-lg text-gray-600">Amin Plus Company</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-bold mb-4 text-blue-800">معلومات العميل</h3>
            <div className="space-y-2">
              <div>
                <span className="font-medium">الاسم:</span> {invoice.customer.name}
              </div>
              <div>
                <span className="font-medium">العنوان:</span> {invoice.customer.address}
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-4 text-blue-800">تفاصيل الفاتورة</h3>
            <div className="space-y-2">
              <div>
                <span className="font-medium">رقم الفاتورة:</span> {invoice.number}
              </div>
              <div>
                <span className="font-medium">التاريخ:</span> {new Date(invoice.issueDate).toLocaleDateString('ar-AE')}
              </div>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-blue-50">
                <th className="border border-gray-300 p-3 text-right">الوصف</th>
                <th className="border border-gray-300 p-3 text-right">الكمية</th>
                <th className="border border-gray-300 p-3 text-right">السعر</th>
                <th className="border border-gray-300 p-3 text-right">الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              {invoice.items.map((item: any) => (
                <tr key={item.id}>
                  <td className="border border-gray-300 p-3 text-right">{item.description}</td>
                  <td className="border border-gray-300 p-3 text-right">{item.quantity}</td>
                  <td className="border border-gray-300 p-3 text-right">{item.unitPrice} درهم</td>
                  <td className="border border-gray-300 p-3 text-right font-bold">{item.total} درهم</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex justify-end mb-8">
          <div className="w-80">
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span>{invoice.subtotal} درهم</span>
              </div>
              <div className="flex justify-between">
                <span>الضريبة ({invoice.taxRate}%):</span>
                <span>{invoice.taxAmount} درهم</span>
              </div>
              <div className="border-t pt-2 flex justify-between font-bold text-lg">
                <span>الإجمالي:</span>
                <span className="text-blue-800">{invoice.total} درهم</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t pt-6 text-center text-gray-600">
          <p>شكراً لتعاملكم معنا | Thank you for your business</p>
        </div>
      </div>
    </div>
  );
}
