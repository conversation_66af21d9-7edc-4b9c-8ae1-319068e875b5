'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';

interface Customer {
  id: string;
  name: string;
  nameEn: string;
  email: string;
  phone: string;
  address: string;
  taxNumber: string;
}

interface Product {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  price: number;
  taxRate: number;
  unit: string;
}

interface InvoiceItem {
  id: string;
  productId: string;
  name: string;
  nameEn: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  taxAmount: number;
  total: number;
}

interface InvoiceFormData {
  customerId: string;
  issueDate: string;
  dueDate: string;
  status: 'draft' | 'pending' | 'sent' | 'paid';
  items: InvoiceItem[];
  notes: string;
  terms: string;
  discountAmount: number;
}

export default function EditInvoicePage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  
  const [formData, setFormData] = useState<InvoiceFormData>({
    customerId: '',
    issueDate: '',
    dueDate: '',
    status: 'draft',
    items: [],
    notes: '',
    terms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    discountAmount: 0
  });

  const [errors, setErrors] = useState<any>({});

  // تحميل البيانات
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [invoiceRes, customersRes, productsRes] = await Promise.all([
          fetch(`/api/invoices/${params.id}`),
          fetch('/api/customers'),
          fetch('/api/products')
        ]);

        if (!invoiceRes.ok) {
          throw new Error('Failed to fetch invoice');
        }

        const invoice = await invoiceRes.json();
        
        setFormData({
          customerId: invoice.customerId,
          issueDate: invoice.issueDate,
          dueDate: invoice.dueDate,
          status: invoice.status,
          items: invoice.items || [],
          notes: invoice.notes || '',
          terms: invoice.terms || 'الدفع خلال 30 يوم من تاريخ الفاتورة',
          discountAmount: invoice.discountAmount || 0
        });

        setSelectedCustomer(invoice.customer);

        if (customersRes.ok) {
          const customersData = await customersRes.json();
          setCustomers(customersData.customers || []);
        }

        if (productsRes.ok) {
          const productsData = await productsRes.json();
          setProducts(productsData.products || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'خطأ في تحميل البيانات',
          description: 'حدث خطأ أثناء تحميل بيانات الفاتورة',
          variant: 'destructive'
        });
        router.push('/dashboard/invoices');
      } finally {
        setFetchLoading(false);
      }
    };

    if (params.id) {
      fetchData();
    }
  }, [params.id, router, toast]);

  // حساب المجاميع
  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const taxAmount = formData.items.reduce((sum, item) => sum + item.taxAmount, 0);
    const total = subtotal + taxAmount - formData.discountAmount;

    return { subtotal, taxAmount, total };
  };

  const { subtotal, taxAmount, total } = calculateTotals();

  const validateForm = (): boolean => {
    const newErrors: any = {};

    if (!formData.customerId) {
      newErrors.customerId = 'يجب اختيار العميل';
    }

    if (!formData.issueDate) {
      newErrors.issueDate = 'تاريخ الإصدار مطلوب';
    }

    if (!formData.dueDate) {
      newErrors.dueDate = 'تاريخ الاستحقاق مطلوب';
    }

    if (formData.items.length === 0) {
      newErrors.items = 'يجب إضافة عنصر واحد على الأقل';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/invoices/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          customer: selectedCustomer,
          subtotal,
          taxAmount,
          total
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء تحديث الفاتورة');
      }

      const updatedInvoice = await response.json();
      
      toast({
        title: 'تم تحديث الفاتورة بنجاح',
        description: `تم تحديث الفاتورة ${updatedInvoice.number} بنجاح`,
        variant: 'success'
      });

      router.push('/dashboard/invoices');
    } catch (error) {
      console.error('Error updating invoice:', error);
      toast({
        title: 'خطأ في تحديث الفاتورة',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerChange = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    setSelectedCustomer(customer || null);
    setFormData(prev => ({ ...prev, customerId }));
    
    if (errors.customerId) {
      setErrors((prev: any) => ({ ...prev, customerId: undefined }));
    }
  };

  const updateItem = (itemId: string, field: keyof InvoiceItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === itemId) {
          const updatedItem = { ...item, [field]: value };
          
          // إعادة حساب المبالغ
          if (field === 'quantity' || field === 'unitPrice' || field === 'taxRate') {
            const subtotal = updatedItem.quantity * updatedItem.unitPrice;
            updatedItem.taxAmount = (subtotal * updatedItem.taxRate) / 100;
            updatedItem.total = subtotal + updatedItem.taxAmount;
          }
          
          return updatedItem;
        }
        return item;
      })
    }));
  };

  if (fetchLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="mr-3">جاري تحميل بيانات الفاتورة...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">تعديل الفاتورة</h1>
          <p className="text-sm text-gray-500">Edit Invoice</p>
        </div>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          العودة / Back
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* معلومات الفاتورة الأساسية */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium mb-2">
              العميل <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.customerId}
              onChange={(e) => handleCustomerChange(e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.customerId ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">اختر العميل</option>
              {customers.map(customer => (
                <option key={customer.id} value={customer.id}>
                  {customer.name} - {customer.nameEn}
                </option>
              ))}
            </select>
            {errors.customerId && <p className="text-red-500 text-sm mt-1">{errors.customerId}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              تاريخ الإصدار <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              value={formData.issueDate}
              onChange={(e) => setFormData(prev => ({ ...prev, issueDate: e.target.value }))}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.issueDate ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.issueDate && <p className="text-red-500 text-sm mt-1">{errors.issueDate}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              تاريخ الاستحقاق <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.dueDate ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.dueDate && <p className="text-red-500 text-sm mt-1">{errors.dueDate}</p>}
          </div>
        </div>

        {/* حالة الفاتورة */}
        <div>
          <label className="block text-sm font-medium mb-2">حالة الفاتورة</label>
          <select
            value={formData.status}
            onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="draft">مسودة</option>
            <option value="pending">قيد الانتظار</option>
            <option value="sent">مرسلة</option>
            <option value="paid">مدفوعة</option>
          </select>
        </div>

        {/* عناصر الفاتورة */}
        <div>
          <h3 className="text-lg font-semibold mb-4">عناصر الفاتورة</h3>
          {errors.items && <p className="text-red-500 text-sm mb-4">{errors.items}</p>}

          <div className="space-y-4">
            {formData.items.map((item, index) => (
              <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium mb-1">المنتج</label>
                    <input
                      type="text"
                      value={item.name}
                      onChange={(e) => updateItem(item.id, 'name', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="اسم المنتج"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">الكمية</label>
                    <input
                      type="number"
                      min="1"
                      value={item.quantity}
                      onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">سعر الوحدة</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">الإجمالي</label>
                    <input
                      type="text"
                      value={item.total.toFixed(2)}
                      readOnly
                      className="w-full p-2 border border-gray-300 rounded-md bg-gray-50"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* ملخص الفاتورة */}
        {formData.items.length > 0 && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-4">ملخص الفاتورة</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span>{subtotal.toFixed(2)} درهم</span>
              </div>
              <div className="flex justify-between">
                <span>الضريبة:</span>
                <span>{taxAmount.toFixed(2)} درهم</span>
              </div>
              <div className="flex justify-between">
                <span>الخصم:</span>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.discountAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, discountAmount: parseFloat(e.target.value) || 0 }))}
                  className="w-24 p-1 border border-gray-300 rounded text-right"
                />
              </div>
              <div className="border-t pt-2 flex justify-between font-bold text-lg">
                <span>الإجمالي:</span>
                <span className="text-blue-600">{total.toFixed(2)} درهم</span>
              </div>
            </div>
          </div>
        )}

        {/* أزرار الحفظ */}
        <div className="flex justify-end space-x-4 rtl:space-x-reverse pt-6 border-t">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            إلغاء
          </button>
          <button
            type="submit"
            disabled={loading || formData.items.length === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'جاري التحديث...' : 'تحديث الفاتورة'}
          </button>
        </div>
      </form>
    </div>
  );
}
