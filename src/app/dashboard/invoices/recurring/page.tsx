'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { DatePicker } from '@/components/ui/date-picker';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Calendar, Edit, Trash2, Copy, AlertCircle, CheckCircle2, PauseCircle } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

// نموذج بيانات للفواتير المتكررة
interface RecurringInvoice {
  id: string;
  name: string;
  customer: {
    id: string;
    name: string;
  };
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  nextDate: string;
  amount: number;
  status: 'active' | 'paused' | 'completed';
  createdAt: string;
  totalGenerated: number;
}

export default function RecurringInvoicesPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('active');
  const [recurringInvoices, setRecurringInvoices] = useState<RecurringInvoice[]>([]);
  const [customers, setCustomers] = useState<{ id: string; name: string }[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState<RecurringInvoice | null>(null);

  // نموذج بيانات للفاتورة المتكررة الجديدة
  const [newInvoice, setNewInvoice] = useState({
    name: '',
    customerId: '',
    frequency: 'monthly',
    startDate: new Date(),
    endDate: undefined as Date | undefined,
    amount: '',
    description: '',
    autoSend: false,
  });

  useEffect(() => {
    // محاكاة جلب بيانات العملاء
    setCustomers([
      { id: '1', name: 'شركة الأمل' },
      { id: '2', name: 'مؤسسة النور' },
      { id: '3', name: 'شركة الإبداع' },
      { id: '4', name: 'مؤسسة التقدم' },
    ]);

    // محاكاة جلب بيانات الفواتير المتكررة
    const mockRecurringInvoices: RecurringInvoice[] = [
      { id: '1', name: 'اشتراك شهري - شركة الأمل', customer: { id: '1', name: 'شركة الأمل' }, frequency: 'monthly', nextDate: '2023-04-01', amount: 1200, status: 'active', createdAt: '2023-01-01', totalGenerated: 3 },
      { id: '2', name: 'صيانة ربع سنوية - مؤسسة النور', customer: { id: '2', name: 'مؤسسة النور' }, frequency: 'quarterly', nextDate: '2023-05-15', amount: 3500, status: 'active', createdAt: '2023-01-15', totalGenerated: 1 },
      { id: '3', name: 'خدمات أسبوعية - شركة الإبداع', customer: { id: '3', name: 'شركة الإبداع' }, frequency: 'weekly', nextDate: '2023-03-27', amount: 500, status: 'paused', createdAt: '2023-02-01', totalGenerated: 8 },
      { id: '4', name: 'اشتراك سنوي - مؤسسة التقدم', customer: { id: '4', name: 'مؤسسة التقدم' }, frequency: 'yearly', nextDate: '2024-01-01', amount: 12000, status: 'active', createdAt: '2023-01-01', totalGenerated: 1 },
      { id: '5', name: 'خدمات شهرية - شركة الأمل', customer: { id: '1', name: 'شركة الأمل' }, frequency: 'monthly', nextDate: '2023-04-15', amount: 1800, status: 'active', createdAt: '2023-01-15', totalGenerated: 3 },
    ];
    
    setRecurringInvoices(mockRecurringInvoices);
    setLoading(false);
  }, []);

  const handleCreateRecurringInvoice = () => {
    // التحقق من صحة البيانات
    if (!newInvoice.name || !newInvoice.customerId || !newInvoice.amount || !newInvoice.startDate) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    // محاكاة إنشاء فاتورة متكررة جديدة
    const newRecurringInvoice: RecurringInvoice = {
      id: `${recurringInvoices.length + 1}`,
      name: newInvoice.name,
      customer: {
        id: newInvoice.customerId,
        name: customers.find(c => c.id === newInvoice.customerId)?.name || '',
      },
      frequency: newInvoice.frequency as any,
      nextDate: newInvoice.startDate.toISOString().split('T')[0],
      amount: parseFloat(newInvoice.amount),
      status: 'active',
      createdAt: new Date().toISOString().split('T')[0],
      totalGenerated: 0,
    };

    setRecurringInvoices([...recurringInvoices, newRecurringInvoice]);
    setOpenDialog(false);
    
    // إعادة تعيين نموذج الفاتورة المتكررة الجديدة
    setNewInvoice({
      name: '',
      customerId: '',
      frequency: 'monthly',
      startDate: new Date(),
      endDate: undefined,
      amount: '',
      description: '',
      autoSend: false,
    });
  };

  const handleEditRecurringInvoice = (invoice: RecurringInvoice) => {
    setEditingInvoice(invoice);
    setNewInvoice({
      name: invoice.name,
      customerId: invoice.customer.id,
      frequency: invoice.frequency,
      startDate: new Date(invoice.nextDate),
      endDate: undefined,
      amount: invoice.amount.toString(),
      description: '',
      autoSend: false,
    });
    setOpenDialog(true);
  };

  const handleDeleteRecurringInvoice = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة المتكررة؟')) {
      setRecurringInvoices(recurringInvoices.filter(invoice => invoice.id !== id));
    }
  };

  const handleChangeStatus = (id: string, status: 'active' | 'paused' | 'completed') => {
    setRecurringInvoices(recurringInvoices.map(invoice => 
      invoice.id === id ? { ...invoice, status } : invoice
    ));
  };

  const handleGenerateNow = (invoice: RecurringInvoice) => {
    if (confirm(`هل تريد إنشاء فاتورة جديدة الآن لـ "${invoice.name}"؟`)) {
      // محاكاة إنشاء فاتورة جديدة
      alert(`تم إنشاء فاتورة جديدة لـ "${invoice.name}" بنجاح!`);
      
      // تحديث عدد الفواتير المنشأة
      setRecurringInvoices(recurringInvoices.map(inv => 
        inv.id === invoice.id ? { ...inv, totalGenerated: inv.totalGenerated + 1 } : inv
      ));
    }
  };

  // تصفية الفواتير المتكررة حسب الحالة
  const filteredInvoices = recurringInvoices.filter(invoice => {
    if (activeTab === 'active') return invoice.status === 'active';
    if (activeTab === 'paused') return invoice.status === 'paused';
    if (activeTab === 'completed') return invoice.status === 'completed';
    return true;
  });

  // ترجمة تكرار الفاتورة
  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'weekly': return 'أسبوعي';
      case 'monthly': return 'شهري';
      case 'quarterly': return 'ربع سنوي';
      case 'yearly': return 'سنوي';
      default: return frequency;
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">الفواتير المتكررة</h1>
          <p className="text-sm text-gray-500">Recurring Invoices</p>
        </div>
        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="ml-2 h-4 w-4" />
              إنشاء فاتورة متكررة
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{editingInvoice ? 'تعديل فاتورة متكررة' : 'إنشاء فاتورة متكررة جديدة'}</DialogTitle>
              <DialogDescription>
                قم بإعداد الفاتورة المتكررة وسيتم إنشاء الفواتير تلقائياً حسب الجدول الزمني المحدد.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">اسم الفاتورة المتكررة</Label>
                  <Input
                    id="name"
                    value={newInvoice.name}
                    onChange={(e) => setNewInvoice({ ...newInvoice, name: e.target.value })}
                    placeholder="مثال: اشتراك شهري"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="customer">العميل</Label>
                  <Select
                    value={newInvoice.customerId}
                    onValueChange={(value) => setNewInvoice({ ...newInvoice, customerId: value })}
                  >
                    <SelectTrigger id="customer">
                      <SelectValue placeholder="اختر العميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="frequency">تكرار الفاتورة</Label>
                  <Select
                    value={newInvoice.frequency}
                    onValueChange={(value) => setNewInvoice({ ...newInvoice, frequency: value })}
                  >
                    <SelectTrigger id="frequency">
                      <SelectValue placeholder="اختر التكرار" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekly">أسبوعي</SelectItem>
                      <SelectItem value="monthly">شهري</SelectItem>
                      <SelectItem value="quarterly">ربع سنوي</SelectItem>
                      <SelectItem value="yearly">سنوي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="amount">المبلغ</Label>
                  <Input
                    id="amount"
                    type="number"
                    value={newInvoice.amount}
                    onChange={(e) => setNewInvoice({ ...newInvoice, amount: e.target.value })}
                    placeholder="0.00"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="startDate">تاريخ البدء</Label>
                  <DatePicker
                    date={newInvoice.startDate}
                    setDate={(date) => setNewInvoice({ ...newInvoice, startDate: date || new Date() })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="endDate">تاريخ الانتهاء (اختياري)</Label>
                  <DatePicker
                    date={newInvoice.endDate}
                    setDate={(date) => setNewInvoice({ ...newInvoice, endDate: date })}
                  />
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="description">وصف الفاتورة (اختياري)</Label>
                <Input
                  id="description"
                  value={newInvoice.description}
                  onChange={(e) => setNewInvoice({ ...newInvoice, description: e.target.value })}
                  placeholder="وصف الخدمات أو المنتجات"
                />
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="autoSend"
                  checked={newInvoice.autoSend}
                  onCheckedChange={(checked) => setNewInvoice({ ...newInvoice, autoSend: checked })}
                />
                <Label htmlFor="autoSend">إرسال الفواتير تلقائياً بعد إنشائها</Label>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleCreateRecurringInvoice}>
                {editingInvoice ? 'تحديث الفاتورة المتكررة' : 'إنشاء فاتورة متكررة'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-4 w-full md:w-1/2 mb-4">
          <TabsTrigger value="all" className="flex items-center">
            <span>الكل</span>
            <span className="mr-1 text-xs bg-gray-200 text-gray-800 rounded-full px-2 py-0.5">
              {recurringInvoices.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="active" className="flex items-center">
            <CheckCircle2 className="h-4 w-4 ml-1 text-green-500" />
            <span>نشطة</span>
            <span className="mr-1 text-xs bg-green-100 text-green-800 rounded-full px-2 py-0.5">
              {recurringInvoices.filter(i => i.status === 'active').length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="paused" className="flex items-center">
            <PauseCircle className="h-4 w-4 ml-1 text-amber-500" />
            <span>متوقفة</span>
            <span className="mr-1 text-xs bg-amber-100 text-amber-800 rounded-full px-2 py-0.5">
              {recurringInvoices.filter(i => i.status === 'paused').length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center">
            <AlertCircle className="h-4 w-4 ml-1 text-blue-500" />
            <span>مكتملة</span>
            <span className="mr-1 text-xs bg-blue-100 text-blue-800 rounded-full px-2 py-0.5">
              {recurringInvoices.filter(i => i.status === 'completed').length}
            </span>
          </TabsTrigger>
        </TabsList>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {loading ? (
            <p className="col-span-full text-center py-10">جاري التحميل...</p>
          ) : filteredInvoices.length === 0 ? (
            <p className="col-span-full text-center py-10 text-gray-500">
              لا توجد فواتير متكررة {activeTab === 'active' ? 'نشطة' : activeTab === 'paused' ? 'متوقفة' : activeTab === 'completed' ? 'مكتملة' : ''}
            </p>
          ) : (
            filteredInvoices.map((invoice) => (
              <Card key={invoice.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{invoice.name}</CardTitle>
                    <div className={`px-2 py-1 rounded-full text-xs ${
                      invoice.status === 'active' ? 'bg-green-100 text-green-800' :
                      invoice.status === 'paused' ? 'bg-amber-100 text-amber-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {invoice.status === 'active' ? 'نشطة' :
                       invoice.status === 'paused' ? 'متوقفة' : 'مكتملة'}
                    </div>
                  </div>
                  <CardDescription>
                    {invoice.customer.name} - {getFrequencyText(invoice.frequency)}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">المبلغ:</span>
                      <span className="font-medium">{formatCurrency(invoice.amount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">الفاتورة التالية:</span>
                      <span className="font-medium flex items-center">
                        <Calendar className="h-4 w-4 ml-1 text-blue-500" />
                        {new Date(invoice.nextDate).toLocaleDateString('ar-AE')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">الفواتير المنشأة:</span>
                      <span className="font-medium">{invoice.totalGenerated}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2 flex justify-between">
                  <div className="flex space-x-2 space-x-reverse">
                    <Button variant="outline" size="sm" onClick={() => handleEditRecurringInvoice(invoice)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDeleteRecurringInvoice(invoice.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    {invoice.status === 'active' && (
                      <Button variant="outline" size="sm" onClick={() => handleGenerateNow(invoice)}>
                        <Copy className="h-4 w-4 ml-1" />
                        إنشاء الآن
                      </Button>
                    )}
                    {invoice.status === 'active' ? (
                      <Button variant="outline" size="sm" onClick={() => handleChangeStatus(invoice.id, 'paused')}>
                        <PauseCircle className="h-4 w-4 ml-1" />
                        إيقاف
                      </Button>
                    ) : invoice.status === 'paused' && (
                      <Button variant="outline" size="sm" onClick={() => handleChangeStatus(invoice.id, 'active')}>
                        <CheckCircle2 className="h-4 w-4 ml-1" />
                        تنشيط
                      </Button>
                    )}
                  </div>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
      </Tabs>
    </div>
  );
}
