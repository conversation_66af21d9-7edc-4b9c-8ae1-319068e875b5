'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { formatCurrency, CurrencyCode } from '@/lib/utils';
import CurrencySelector from '@/components/ui/currency-selector';
import TaxSettings, { TaxSettings as TaxSettingsType } from '@/components/ui/tax-settings';
import {
  getDefaultCurrency,
  getDefaultTaxSettings,
  getCompanyInfo,
  generateInvoiceNumber,
  getSystemSettings
} from '@/lib/settings';

interface Customer {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  sku: string;
}

interface InvoiceItem {
  id: string;
  productId: string;
  name: string;
  quantity: number;
  price: number;
}

export default function NewInvoicePage() {
  const router = useRouter();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [invoiceDate, setInvoiceDate] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [notes, setNotes] = useState('');
  const [currency, setCurrency] = useState<CurrencyCode>(getDefaultCurrency());
  const [taxSettings, setTaxSettings] = useState<TaxSettingsType>(getDefaultTaxSettings());
  const [invoiceNumber, setInvoiceNumber] = useState<string>('');

  // إضافة عنصر جديد للفاتورة
  const addItem = () => {
    if (products.length === 0) return;

    const newItem: InvoiceItem = {
      id: Math.random().toString(36).substring(2, 9),
      productId: '',
      name: '',
      quantity: 1,
      price: 0
    };

    setItems([...items, newItem]);
  };

  // حذف عنصر من الفاتورة
  const removeItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  // تحديث عنصر في الفاتورة
  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        if (field === 'productId' && value) {
          const product = products.find(p => p.id === value);
          if (product) {
            return {
              ...item,
              [field]: value,
              name: product.name,
              price: product.price
            };
          }
        }
        return { ...item, [field]: value };
      }
      return item;
    }));
  };

  // حساب المجموع الفرعي
  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  // حساب الضريبة
  const calculateTax = () => {
    if (!taxSettings.enabled) return 0;

    const subtotal = calculateSubtotal();

    if (taxSettings.inclusive) {
      // إذا كانت الأسعار شاملة الضريبة، نحسب قيمة الضريبة من المجموع
      return subtotal - (subtotal / (1 + (taxSettings.rate / 100)));
    } else {
      // إذا كانت الأسعار غير شاملة الضريبة، نحسب قيمة الضريبة كنسبة من المجموع
      return subtotal * (taxSettings.rate / 100);
    }
  };

  // حساب المجموع الكلي
  const calculateTotal = () => {
    const subtotal = calculateSubtotal();

    if (!taxSettings.enabled) return subtotal;

    if (taxSettings.inclusive) {
      // إذا كانت الأسعار شاملة الضريبة، فالمجموع هو نفسه المجموع الفرعي
      return subtotal;
    } else {
      // إذا كانت الأسعار غير شاملة الضريبة، نضيف قيمة الضريبة إلى المجموع الفرعي
      return subtotal + calculateTax();
    }
  };

  // إرسال الفاتورة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCustomer || !invoiceDate || items.length === 0) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setSubmitting(true);

    try {
      // في التطبيق الحقيقي: إرسال البيانات إلى API
      console.log({
        invoiceNumber,
        customerId: selectedCustomer,
        issueDate: invoiceDate,
        dueDate,
        items,
        notes,
        currency,
        taxSettings,
        subtotal: calculateSubtotal(),
        tax: calculateTax(),
        total: calculateTotal(),
        companyInfo: getCompanyInfo()
      });

      // محاكاة استجابة API
      await new Promise(resolve => setTimeout(resolve, 1000));

      alert('تم إنشاء الفاتورة بنجاح');
      router.push('/dashboard/invoices');
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة:', error);
      alert('حدث خطأ أثناء إنشاء الفاتورة');
    } finally {
      setSubmitting(false);
    }
  };

  // جلب بيانات العملاء والمنتجات وتهيئة الفاتورة
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // في التطبيق الحقيقي: جلب البيانات من API

        // محاكاة جلب بيانات العملاء
        const mockCustomers: Customer[] = [
          { id: '1', name: 'شركة الأمل للتجارة' },
          { id: '2', name: 'مؤسسة النور' },
          { id: '3', name: 'شركة الإبداع' }
        ];

        // محاكاة جلب بيانات المنتجات
        const mockProducts: Product[] = [
          { id: '1', name: 'لابتوب HP ProBook', price: 3500, sku: 'HP-PB-001' },
          { id: '2', name: 'آيفون 15 برو', price: 4500, sku: 'IP-15P-001' },
          { id: '3', name: 'سماعات سوني', price: 1200, sku: 'SN-WH-001' }
        ];

        // تعيين البيانات
        setCustomers(mockCustomers);
        setProducts(mockProducts);

        // تعيين تاريخ الفاتورة إلى اليوم الحالي
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        setInvoiceDate(formattedDate);

        // تعيين تاريخ الاستحقاق إلى 30 يوم من اليوم الحالي
        const dueDate = new Date();
        dueDate.setDate(today.getDate() + 30);
        const formattedDueDate = dueDate.toISOString().split('T')[0];
        setDueDate(formattedDueDate);

        // إنشاء رقم فاتورة جديد
        setInvoiceNumber(generateInvoiceNumber());

      } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="ml-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">إنشاء فاتورة جديدة</h1>
          <p className="text-sm text-gray-500">Create New Invoice</p>
        </div>
        <button
          onClick={() => router.push('/dashboard/invoices')}
          className="px-3 py-1 border border-gray-300 rounded-md text-sm"
        >
          إلغاء | Cancel
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold">
                  <span>فاتورة جديدة</span>
                  <span className="text-xs text-gray-500 mr-1">New Invoice</span>
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  <span>رقم الفاتورة:</span> <strong>{invoiceNumber}</strong>
                </p>
              </div>
              <div className="text-sm text-gray-500">
                <p>
                  <span>{getCompanyInfo().name}</span>
                  <span className="mx-1">|</span>
                  <span>{getCompanyInfo().nameEn}</span>
                </p>
                <p className="mt-1">{getCompanyInfo().taxNumber} :الرقم الضريبي</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="customer" className="block mb-2 text-sm font-medium">
              <span>العميل</span>
              <span className="text-red-500">*</span>
              <span className="text-xs text-gray-500 mr-1">Customer</span>
            </label>
            <select
              id="customer"
              value={selectedCustomer}
              onChange={(e) => setSelectedCustomer(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              required
            >
              <option value="">اختر العميل | Select Customer</option>
              {customers.map(customer => (
                <option key={customer.id} value={customer.id}>
                  {customer.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="invoiceDate" className="block mb-2 text-sm font-medium">
              <span>تاريخ الفاتورة</span>
              <span className="text-red-500">*</span>
              <span className="text-xs text-gray-500 mr-1">Invoice Date</span>
            </label>
            <input
              id="invoiceDate"
              type="date"
              value={invoiceDate}
              onChange={(e) => setInvoiceDate(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              required
            />
          </div>

          <div>
            <label htmlFor="dueDate" className="block mb-2 text-sm font-medium">
              <span>تاريخ الاستحقاق</span>
              <span className="text-xs text-gray-500 mr-1">Due Date</span>
            </label>
            <input
              id="dueDate"
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label htmlFor="currency" className="block mb-2 text-sm font-medium">
              <span>العملة</span>
              <span className="text-xs text-gray-500 mr-1">Currency</span>
            </label>
            <CurrencySelector
              value={currency}
              onChange={setCurrency}
              className="w-full"
            />
          </div>
        </div>

        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-semibold">
              <span>عناصر الفاتورة</span>
              <span className="text-xs text-gray-500 mr-1">Invoice Items</span>
            </h2>
            <button
              type="button"
              onClick={addItem}
              className="px-3 py-1 bg-primary text-white rounded-md text-sm"
            >
              إضافة عنصر | Add Item
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="p-2 text-right border">
                    <span>المنتج</span>
                    <span className="text-xs text-gray-500 mr-1">Product</span>
                  </th>
                  <th className="p-2 text-right border">
                    <span>الكمية</span>
                    <span className="text-xs text-gray-500 mr-1">Quantity</span>
                  </th>
                  <th className="p-2 text-right border">
                    <span>السعر</span>
                    <span className="text-xs text-gray-500 mr-1">Price</span>
                  </th>
                  <th className="p-2 text-right border">
                    <span>المجموع</span>
                    <span className="text-xs text-gray-500 mr-1">Total</span>
                  </th>
                  <th className="p-2 text-center border">
                    <span>الإجراءات</span>
                    <span className="text-xs text-gray-500 mr-1">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {items.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="p-4 text-center text-gray-500">
                      <span>لا توجد عناصر في الفاتورة</span>
                      <br />
                      <span className="text-sm">No items in invoice</span>
                    </td>
                  </tr>
                ) : (
                  items.map(item => (
                    <tr key={item.id} className="border-b">
                      <td className="p-2 border">
                        <select
                          value={item.productId}
                          onChange={(e) => updateItem(item.id, 'productId', e.target.value)}
                          className="w-full p-1 border border-gray-300 rounded-md"
                        >
                          <option value="">اختر منتج | Select Product</option>
                          {products.map(product => (
                            <option key={product.id} value={product.id}>
                              {product.name} ({formatCurrency(product.price, currency)})
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="p-2 border">
                        <input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                          className="w-full p-1 border border-gray-300 rounded-md"
                        />
                      </td>
                      <td className="p-2 border">
                        {formatCurrency(item.price, currency)}
                      </td>
                      <td className="p-2 border">
                        {formatCurrency(item.price * item.quantity, currency)}
                      </td>
                      <td className="p-2 border text-center">
                        <button
                          type="button"
                          onClick={() => removeItem(item.id)}
                          className="px-2 py-1 bg-red-500 text-white rounded-md text-xs"
                        >
                          حذف | Delete
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
              <tfoot>
                <tr className="bg-gray-50">
                  <td colSpan={3} className="p-2 text-left border font-medium">
                    <span>المجموع الفرعي</span>
                    <span className="text-xs text-gray-500 mr-1">Subtotal</span>
                  </td>
                  <td colSpan={2} className="p-2 text-right border">
                    {formatCurrency(calculateSubtotal(), currency)}
                  </td>
                </tr>
                {taxSettings.enabled && (
                  <tr className="bg-gray-50">
                    <td colSpan={3} className="p-2 text-left border font-medium">
                      <span>
                        {taxSettings.inclusive
                          ? `ضريبة القيمة المضافة (${taxSettings.rate}%) (مضمنة)`
                          : `ضريبة القيمة المضافة (${taxSettings.rate}%)`}
                      </span>
                      <span className="text-xs text-gray-500 mr-1">
                        {taxSettings.inclusive
                          ? `VAT (${taxSettings.rate}%) (Included)`
                          : `VAT (${taxSettings.rate}%)`}
                      </span>
                    </td>
                    <td colSpan={2} className="p-2 text-right border">
                      {formatCurrency(calculateTax(), currency)}
                    </td>
                  </tr>
                )}
                <tr className="bg-gray-50">
                  <td colSpan={3} className="p-2 text-left border font-medium">
                    <span>المجموع الكلي</span>
                    <span className="text-xs text-gray-500 mr-1">Total</span>
                  </td>
                  <td colSpan={2} className="p-2 text-right border font-bold">
                    {formatCurrency(calculateTotal(), currency)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        <div className="mb-6">
          <label htmlFor="notes" className="block mb-2 text-sm font-medium">
            <span>ملاحظات</span>
            <span className="text-xs text-gray-500 mr-1">Notes</span>
          </label>
          <textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={3}
          ></textarea>
        </div>

        <div className="mb-6">
          <TaxSettings
            initialSettings={taxSettings}
            onChange={setTaxSettings}
          />
        </div>

        <div className="mb-6">
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h3 className="text-md font-semibold mb-2">
              <span>الشروط والأحكام</span>
              <span className="text-xs text-gray-500 mr-1">Terms & Conditions</span>
            </h3>
            <p className="text-sm text-gray-600">{getSystemSettings().invoiceSettings.termsAndConditions}</p>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="text-sm text-center text-gray-500">{getSystemSettings().invoiceSettings.footer}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={submitting}
            className="px-4 py-2 bg-primary text-white rounded-md"
          >
            {submitting ? 'جاري الحفظ... | Saving...' : 'إنشاء الفاتورة | Create Invoice'}
          </button>
        </div>
      </form>
    </div>
  );
}
