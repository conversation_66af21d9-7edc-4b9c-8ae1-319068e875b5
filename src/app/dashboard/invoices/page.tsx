'use client';

import { Suspense, useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusCircle, FileText, DollarSign, Clock, CheckCircle, Search, Filter } from 'lucide-react';
import { Button, StatCard, Loading, Input } from '@/components/ui';
import InvoicesList from '@/components/invoices/invoices-list';
import { useI18n } from '@/lib/i18n';
import { formatCurrency } from '@/lib/utils';

export default function InvoicesPage() {
    const { t, language } = useI18n();
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [stats, setStats] = useState({
        total: 0,
        pending: 0,
        paid: 0,
        overdue: 0,
        totalAmount: 0
    });

    // محاكاة تحميل البيانات
    useEffect(() => {
        const loadStats = async () => {
            setLoading(true);
            await new Promise(resolve => setTimeout(resolve, 1000));
            setStats({
                total: 156,
                pending: 23,
                paid: 128,
                overdue: 5,
                totalAmount: 125000
            });
            setLoading(false);
        };
        loadStats();
    }, []);

    if (loading) {
        return <Loading />;
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-green-50/30 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
            <div className="space-y-8 p-6 animate-fade-in">
                {/* Enhanced Header */}
                <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 p-8 text-white shadow-strong">
                    <div className="absolute inset-0 bg-floating-shapes opacity-20" />
                    <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent" />

                    <div className="relative z-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-6">
                        <div className="space-y-3">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                                    <FileText className="h-6 w-6 text-white" />
                                </div>
                                <h1 className="text-4xl font-bold text-white animate-fade-in-up">
                                    {t('invoices.title')}
                                </h1>
                            </div>
                            <p className="text-green-100 text-lg animate-fade-in-up animate-delay-200">
                                {language === 'ar' ? 'إدارة الفواتير والمدفوعات بكفاءة عالية' : 'Efficient Invoice and Payment Management'}
                            </p>
                        </div>

                        <div className="flex gap-3 animate-fade-in-up animate-delay-400">
                            <Button variant="glass" size="sm" className="text-white border-white/30 hover:bg-white/20">
                                <Filter className="h-4 w-4 mr-2" />
                                {language === 'ar' ? 'تصفية' : 'Filter'}
                            </Button>
                            <Link href="/dashboard/invoices/new">
                                <Button variant="outline" size="sm" className="bg-white text-green-600 border-white hover:bg-green-50">
                                    <PlusCircle className="h-4 w-4 mr-2" />
                                    {t('invoices.create')}
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Enhanced Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    <StatCard
                        title={language === 'ar' ? 'إجمالي الفواتير' : 'Total Invoices'}
                        value={stats.total.toLocaleString()}
                        icon={<FileText className="h-6 w-6" />}
                        color="blue"
                        trend={{
                            value: 8,
                            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                            isPositive: true
                        }}
                        className="animate-fade-in-up animate-delay-500 hover:scale-105 transition-transform duration-300"
                    />
                    <StatCard
                        title={language === 'ar' ? 'في الانتظار' : 'Pending'}
                        value={stats.pending.toLocaleString()}
                        icon={<Clock className="h-6 w-6" />}
                        color="yellow"
                        trend={{
                            value: 5,
                            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                            isPositive: false
                        }}
                        className="animate-fade-in-up animate-delay-600 hover:scale-105 transition-transform duration-300"
                    />
                    <StatCard
                        title={language === 'ar' ? 'مدفوعة' : 'Paid'}
                        value={stats.paid.toLocaleString()}
                        icon={<CheckCircle className="h-6 w-6" />}
                        color="green"
                        trend={{
                            value: 12,
                            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                            isPositive: true
                        }}
                        className="animate-fade-in-up animate-delay-700 hover:scale-105 transition-transform duration-300"
                    />
                    <StatCard
                        title={language === 'ar' ? 'متأخرة' : 'Overdue'}
                        value={stats.overdue.toLocaleString()}
                        icon={<Clock className="h-6 w-6" />}
                        color="red"
                        trend={{
                            value: 3,
                            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                            isPositive: false
                        }}
                        className="animate-fade-in-up animate-delay-800 hover:scale-105 transition-transform duration-300"
                    />
                    <StatCard
                        title={language === 'ar' ? 'إجمالي المبلغ' : 'Total Amount'}
                        value={formatCurrency(stats.totalAmount, language)}
                        icon={<DollarSign className="h-6 w-6" />}
                        color="purple"
                        trend={{
                            value: 18,
                            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                            isPositive: true
                        }}
                        className="animate-fade-in-up animate-delay-900 hover:scale-105 transition-transform duration-300"
                    />
                </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder={language === 'ar' ? 'البحث في الفواتير...' : 'Search invoices...'}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
            </div>

            {/* Invoices List */}
            <div className="bg-card rounded-xl border border-border overflow-hidden">
                <Suspense fallback={
                    <div className="p-8 text-center">
                        <Loading />
                    </div>
                }>
                    <InvoicesList searchTerm={searchTerm} />
                </Suspense>
            </div>
        </div>
    );
}
