'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { PermissionGuard } from '@/components/auth/permission-guard'
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'

export default function NewUserPage() {
    return (
        <PermissionGuard permission="MANAGE_USERS">
            <NewUserForm />
        </PermissionGuard>
    )
}

function NewUserForm() {
    const router = useRouter()
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
        roleId: '',
    })
    const [roles, setRoles] = useState([])
    const [loading, setLoading] = useState(true)
    const [submitting, setSubmitting] = useState(false)

    useEffect(() => {
        async function fetchRoles() {
            try {
                const response = await fetch('/api/roles')
                if (!response.ok) throw new Error('Failed to fetch roles')
                const data = await response.json()
                setRoles(data)
            } catch (error) {
                console.error('Error fetching roles:', error)
                toast.error('فشل في جلب الأدوار')
            } finally {
                setLoading(false)
            }
        }

        fetchRoles()
    }, [])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()

        // التحقق من البيانات
        if (!formData.name || !formData.email || !formData.password || !formData.roleId) {
            toast.error('يرجى ملء جميع الحقول المطلوبة')
            return
        }

        if (formData.password !== formData.confirmPassword) {
            toast.error('كلمات المرور غير متطابقة')
            return
        }

        setSubmitting(true)
        try {
            const response = await fetch('/api/users', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData),
            })

            if (!response.ok) {
                const error = await response.json()
                throw new Error(error.message || 'فشل في إنشاء المستخدم')
            }

            toast.success('تم إنشاء المستخدم بنجاح')
            router.push('/dashboard/users')
        } catch (error: any) {
            toast.error(error.message || 'فشل في إنشاء المستخدم')
        } finally {
            setSubmitting(false)
        }
    }

    return (
        <div className="container py-6">
            <h1 className="mb-6 text-2xl font-bold">إضافة مستخدم جديد</h1>

            <Card className="max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle>معلومات المستخدم</CardTitle>
                </CardHeader>
                <form onSubmit={handleSubmit}>
                    <CardContent className="space-y-4">
                        <div className="space-y-1">
                            <Label htmlFor="name">الاسم</Label>
                            <Input
                                id="name"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                placeholder="أدخل اسم المستخدم"
                                required
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="email">البريد الإلكتروني</Label>
                            <Input
                                id="email"
                                name="email"
                                type="email"
                                value={formData.email}
                                onChange={handleChange}
                                placeholder="<EMAIL>"
                                required
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="password">كلمة المرور</Label>
                            <Input
                                id="password"
                                name="password"
                                type="password"
                                value={formData.password}
                                onChange={handleChange}
                                placeholder="********"
                                required
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                            <Input
                                id="confirmPassword"
                                name="confirmPassword"
                                type="password"
                                value={formData.confirmPassword}
                                onChange={handleChange}
                                placeholder="********"
                                required
                            />
                        </div>

                        <div className="space-y-1">
                            <Label htmlFor="roleId">الدور</Label>
                            <select
                                id="roleId"
                                name="roleId"
                                value={formData.roleId}
                                onChange={handleChange}
                                className="flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm"
                                required
                                disabled={loading}
                            >
                                <option value="">اختر الدور</option>
                                {roles.map((role: any) => (
                                    <option key={role.id} value={role.id}>
                                        {role.name} - {role.description}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </CardContent>
                    <CardFooter className="justify-between">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.push('/dashboard/users')}
                        >
                            إلغاء
                        </Button>
                        <Button type="submit" disabled={submitting}>
                            {submitting ? 'جاري الإنشاء...' : 'إنشاء المستخدم'}
                        </Button>
                    </CardFooter>
                </form>
            </Card>
        </div>
    )
}
