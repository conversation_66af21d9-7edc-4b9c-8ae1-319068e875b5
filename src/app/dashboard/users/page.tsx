'use client';

import { useState, useEffect } from 'react';
import { User, UserRole } from '@/types/user';
import {
  getUsers,
  addUser,
  updateUser,
  deleteUser
} from '@/lib/user-service';

export default function UsersPage() {
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showModal, setShowModal] = useState(false);

  // نموذج المستخدم
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    fullName: '',
    role: 'viewer' as UserRole,
    active: true
  });

  // تحميل المستخدمين
  useEffect(() => {
    const loadUsers = () => {
      setLoading(true);
      try {
        const usersData = getUsers();
        setUsers(usersData);
      } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, []);

  // تحديث بيانات النموذج
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // فتح نموذج إضافة مستخدم جديد
  const handleAddUser = () => {
    setIsEditing(false);
    setSelectedUser(null);
    setFormData({
      username: '',
      email: '',
      fullName: '',
      role: 'viewer',
      active: true
    });
    setShowModal(true);
  };

  // فتح نموذج تعديل مستخدم
  const handleEditUser = (user: User) => {
    setIsEditing(true);
    setSelectedUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      role: user.role,
      active: user.active
    });
    setShowModal(true);
  };

  // حذف مستخدم
  const handleDeleteUser = (userId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      try {
        const success = deleteUser(userId);

        if (success) {
          setUsers(prev => prev.filter(user => user.id !== userId));
          alert('تم حذف المستخدم بنجاح');
        } else {
          alert('لا يمكن حذف المستخدم الافتراضي');
        }
      } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        alert('حدث خطأ أثناء حذف المستخدم');
      }
    }
  };

  // حفظ المستخدم (إضافة أو تعديل)
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (isEditing && selectedUser) {
        // تعديل مستخدم
        const updatedUser = updateUser(selectedUser.id, formData);

        if (updatedUser) {
          setUsers(prev => prev.map(user => user.id === updatedUser.id ? updatedUser : user));
          alert('تم تحديث المستخدم بنجاح');
        }
      } else {
        // إضافة مستخدم جديد
        const newUser = addUser(formData);
        setUsers(prev => [...prev, newUser]);
        alert('تم إضافة المستخدم بنجاح');
      }

      setShowModal(false);
    } catch (error: any) {
      console.error('خطأ في حفظ المستخدم:', error);
      alert(`حدث خطأ أثناء حفظ المستخدم: ${error.message}`);
    }
  };

  // الحصول على نص الدور
  const getRoleText = (role: UserRole): string => {
    const roles: Record<UserRole, string> = {
      admin: 'مدير النظام | Admin',
      manager: 'مدير | Manager',
      accountant: 'محاسب | Accountant',
      sales: 'مبيعات | Sales',
      inventory: 'مخزون | Inventory',
      viewer: 'مشاهد | Viewer'
    };

    return roles[role] || role;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="mr-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1 dark:text-white">إدارة المستخدمين</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">User Management</p>
        </div>
        <button
          onClick={handleAddUser}
          className="px-4 py-2 bg-primary text-white rounded-md text-sm"
        >
          إضافة مستخدم | Add User
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="p-2 text-right border dark:border-gray-600">
                <span>اسم المستخدم</span>
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Username</span>
              </th>
              <th className="p-2 text-right border dark:border-gray-600">
                <span>الاسم الكامل</span>
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Full Name</span>
              </th>
              <th className="p-2 text-right border dark:border-gray-600">
                <span>البريد الإلكتروني</span>
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Email</span>
              </th>
              <th className="p-2 text-right border dark:border-gray-600">
                <span>الدور</span>
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Role</span>
              </th>
              <th className="p-2 text-right border dark:border-gray-600">
                <span>الحالة</span>
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Status</span>
              </th>
              <th className="p-2 text-center border dark:border-gray-600">
                <span>الإجراءات</span>
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {users.length === 0 ? (
              <tr>
                <td colSpan={6} className="p-4 text-center text-gray-500 dark:text-gray-400">
                  <span>لا يوجد مستخدمين</span>
                  <br />
                  <span className="text-sm">No users found</span>
                </td>
              </tr>
            ) : (
              users.map(user => (
                <tr key={user.id} className="border-b dark:border-gray-700">
                  <td className="p-2 border dark:border-gray-600">
                    {user.username}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {user.fullName}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {user.email}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {getRoleText(user.role)}
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    <span className={`px-2 py-1 rounded-full text-xs ${user.active
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      }`}>
                      {user.active ? 'نشط | Active' : 'غير نشط | Inactive'}
                    </span>
                  </td>
                  <td className="p-2 border dark:border-gray-600 text-center">
                    <div className="flex justify-center space-x-1 space-x-reverse">
                      <button
                        onClick={() => handleEditUser(user)}
                        className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-md text-xs"
                      >
                        تعديل | Edit
                      </button>
                      {user.id !== '1' && (
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 rounded-md text-xs"
                        >
                          حذف | Delete
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* نموذج إضافة/تعديل مستخدم */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-bold mb-4 dark:text-white">
              {isEditing ? 'تعديل مستخدم | Edit User' : 'إضافة مستخدم | Add User'}
            </h2>

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label htmlFor="username" className="block mb-2 text-sm font-medium dark:text-white">
                  <span>اسم المستخدم</span>
                  <span className="text-red-500">*</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Username</span>
                </label>
                <input
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                  disabled={isEditing && selectedUser?.id === '1'}
                />
              </div>

              <div className="mb-4">
                <label htmlFor="fullName" className="block mb-2 text-sm font-medium dark:text-white">
                  <span>الاسم الكامل</span>
                  <span className="text-red-500">*</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Full Name</span>
                </label>
                <input
                  id="fullName"
                  name="fullName"
                  type="text"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                />
              </div>

              <div className="mb-4">
                <label htmlFor="email" className="block mb-2 text-sm font-medium dark:text-white">
                  <span>البريد الإلكتروني</span>
                  <span className="text-red-500">*</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Email</span>
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                />
              </div>

              <div className="mb-4">
                <label htmlFor="role" className="block mb-2 text-sm font-medium dark:text-white">
                  <span>الدور</span>
                  <span className="text-red-500">*</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Role</span>
                </label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  required
                  disabled={isEditing && selectedUser?.id === '1'}
                >
                  <option value="admin">مدير النظام | Admin</option>
                  <option value="manager">مدير | Manager</option>
                  <option value="accountant">محاسب | Accountant</option>
                  <option value="sales">مبيعات | Sales</option>
                  <option value="inventory">مخزون | Inventory</option>
                  <option value="viewer">مشاهد | Viewer</option>
                </select>
              </div>

              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="active"
                    checked={formData.active}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                    disabled={isEditing && selectedUser?.id === '1'}
                  />
                  <span className="mr-2 text-sm dark:text-white">
                    <span>نشط</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Active</span>
                  </span>
                </label>
              </div>

              <div className="flex justify-end space-x-2 space-x-reverse">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:text-white rounded-md"
                >
                  إلغاء | Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary text-white rounded-md"
                >
                  {isEditing ? 'تحديث | Update' : 'إضافة | Add'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
