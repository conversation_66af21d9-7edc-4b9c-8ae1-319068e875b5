'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON>Chart, Pie, Cell } from 'recharts';
import { Download, Filter, FileText, PieChart as PieChartIcon, BarChart as BarChartIcon } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

// نموذج بيانات للتقارير
interface InvoiceReportData {
  id: string;
  invoiceNumber: string;
  issueDate: string;
  dueDate?: string;
  customer: {
    id: string;
    name: string;
  };
  status: string;
  total: number;
  taxAmount: number;
  subtotal: number;
}

export default function InvoiceReportsPage() {
  const [activeTab, setActiveTab] = useState('summary');
  const [period, setPeriod] = useState('month');
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().setMonth(new Date().getMonth() - 1)));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [statusFilter, setStatusFilter] = useState('all');
  const [customerFilter, setCustomerFilter] = useState('all');
  const [minAmount, setMinAmount] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [invoices, setInvoices] = useState<InvoiceReportData[]>([]);
  const [customers, setCustomers] = useState<{ id: string; name: string }[]>([]);

  // بيانات نموذجية للرسوم البيانية
  const [chartData, setChartData] = useState<any[]>([]);
  const [pieData, setPieData] = useState<any[]>([]);

  useEffect(() => {
    // محاكاة جلب بيانات العملاء
    setCustomers([
      { id: '1', name: 'شركة الأمل' },
      { id: '2', name: 'مؤسسة النور' },
      { id: '3', name: 'شركة الإبداع' },
      { id: '4', name: 'مؤسسة التقدم' },
    ]);

    // محاكاة جلب بيانات الفواتير
    const mockInvoices: InvoiceReportData[] = [
      { id: '1', invoiceNumber: 'INV-001', issueDate: '2023-01-05', customer: { id: '1', name: 'شركة الأمل' }, status: 'PAID', total: 1200, taxAmount: 60, subtotal: 1140 },
      { id: '2', invoiceNumber: 'INV-002', issueDate: '2023-01-12', customer: { id: '2', name: 'مؤسسة النور' }, status: 'PAID', total: 850, taxAmount: 42.5, subtotal: 807.5 },
      { id: '3', invoiceNumber: 'INV-003', issueDate: '2023-01-18', customer: { id: '1', name: 'شركة الأمل' }, status: 'OVERDUE', total: 1500, taxAmount: 75, subtotal: 1425 },
      { id: '4', invoiceNumber: 'INV-004', issueDate: '2023-01-25', customer: { id: '3', name: 'شركة الإبداع' }, status: 'PAID', total: 2000, taxAmount: 100, subtotal: 1900 },
      { id: '5', invoiceNumber: 'INV-005', issueDate: '2023-02-03', customer: { id: '4', name: 'مؤسسة التقدم' }, status: 'DRAFT', total: 750, taxAmount: 37.5, subtotal: 712.5 },
      { id: '6', invoiceNumber: 'INV-006', issueDate: '2023-02-10', customer: { id: '2', name: 'مؤسسة النور' }, status: 'PAID', total: 1350, taxAmount: 67.5, subtotal: 1282.5 },
      { id: '7', invoiceNumber: 'INV-007', issueDate: '2023-02-17', customer: { id: '3', name: 'شركة الإبداع' }, status: 'CANCELLED', total: 500, taxAmount: 25, subtotal: 475 },
      { id: '8', invoiceNumber: 'INV-008', issueDate: '2023-02-24', customer: { id: '1', name: 'شركة الأمل' }, status: 'PAID', total: 1800, taxAmount: 90, subtotal: 1710 },
      { id: '9', invoiceNumber: 'INV-009', issueDate: '2023-03-03', customer: { id: '4', name: 'مؤسسة التقدم' }, status: 'SENT', total: 950, taxAmount: 47.5, subtotal: 902.5 },
      { id: '10', invoiceNumber: 'INV-010', issueDate: '2023-03-10', customer: { id: '2', name: 'مؤسسة النور' }, status: 'PAID', total: 1100, taxAmount: 55, subtotal: 1045 },
    ];
    
    setInvoices(mockInvoices);
    
    // إعداد بيانات الرسم البياني الشريطي
    const monthlyData = [
      { name: 'يناير', total: 5550, paid: 4050, overdue: 1500 },
      { name: 'فبراير', total: 4400, paid: 3150, overdue: 0 },
      { name: 'مارس', total: 2050, paid: 1100, overdue: 0 },
    ];
    
    setChartData(monthlyData);
    
    // إعداد بيانات الرسم البياني الدائري
    const statusData = [
      { name: 'مدفوعة', value: 8300, color: '#4CAF50' },
      { name: 'متأخرة', value: 1500, color: '#FF5722' },
      { name: 'مسودة', value: 750, color: '#9E9E9E' },
      { name: 'مرسلة', value: 950, color: '#2196F3' },
      { name: 'ملغاة', value: 500, color: '#F44336' },
    ];
    
    setPieData(statusData);
  }, []);

  const handleFilterChange = () => {
    setLoading(true);
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const handleExportReport = (format: string) => {
    // محاكاة تصدير التقرير
    alert(`تم تصدير التقرير بتنسيق ${format}`);
  };

  // حساب إجماليات التقرير
  const totalAmount = invoices.reduce((sum, invoice) => sum + invoice.total, 0);
  const paidAmount = invoices.filter(inv => inv.status === 'PAID').reduce((sum, invoice) => sum + invoice.total, 0);
  const overdueAmount = invoices.filter(inv => inv.status === 'OVERDUE').reduce((sum, invoice) => sum + invoice.total, 0);
  const totalTax = invoices.reduce((sum, invoice) => sum + invoice.taxAmount, 0);
  const invoiceCount = invoices.length;
  const paidCount = invoices.filter(inv => inv.status === 'PAID').length;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">تقارير الفواتير</h1>
          <p className="text-sm text-gray-500">Invoice Reports</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline" size="sm" onClick={() => handleExportReport('excel')}>
            <Download className="h-4 w-4 ml-1" />
            تصدير Excel
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExportReport('pdf')}>
            <FileText className="h-4 w-4 ml-1" />
            تصدير PDF
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center mb-4">
          <Filter className="h-5 w-5 ml-2 text-gray-500" />
          <h2 className="text-lg font-semibold">تصفية التقرير</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <Label htmlFor="period">الفترة الزمنية</Label>
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger id="period">
                <SelectValue placeholder="اختر الفترة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="week">هذا الأسبوع</SelectItem>
                <SelectItem value="month">هذا الشهر</SelectItem>
                <SelectItem value="quarter">هذا الربع</SelectItem>
                <SelectItem value="year">هذه السنة</SelectItem>
                <SelectItem value="custom">فترة مخصصة</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {period === 'custom' && (
            <>
              <div>
                <Label htmlFor="startDate">من تاريخ</Label>
                <DatePicker date={startDate} setDate={setStartDate} />
              </div>
              <div>
                <Label htmlFor="endDate">إلى تاريخ</Label>
                <DatePicker date={endDate} setDate={setEndDate} />
              </div>
            </>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <Label htmlFor="status">حالة الفاتورة</Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger id="status">
                <SelectValue placeholder="اختر الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="PAID">مدفوعة</SelectItem>
                <SelectItem value="OVERDUE">متأخرة</SelectItem>
                <SelectItem value="DRAFT">مسودة</SelectItem>
                <SelectItem value="SENT">مرسلة</SelectItem>
                <SelectItem value="CANCELLED">ملغاة</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="customer">العميل</Label>
            <Select value={customerFilter} onValueChange={setCustomerFilter}>
              <SelectTrigger id="customer">
                <SelectValue placeholder="اختر العميل" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع العملاء</SelectItem>
                {customers.map(customer => (
                  <SelectItem key={customer.id} value={customer.id}>
                    {customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="minAmount">الحد الأدنى</Label>
              <Input
                id="minAmount"
                type="number"
                placeholder="0"
                value={minAmount}
                onChange={(e) => setMinAmount(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="maxAmount">الحد الأقصى</Label>
              <Input
                id="maxAmount"
                type="number"
                placeholder="10000"
                value={maxAmount}
                onChange={(e) => setMaxAmount(e.target.value)}
              />
            </div>
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button onClick={handleFilterChange} disabled={loading}>
            {loading ? 'جاري التحميل...' : 'تطبيق الفلتر'}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 w-full md:w-1/2 mb-4">
          <TabsTrigger value="summary" className="flex items-center">
            <PieChartIcon className="h-4 w-4 ml-1" />
            ملخص
          </TabsTrigger>
          <TabsTrigger value="charts" className="flex items-center">
            <BarChartIcon className="h-4 w-4 ml-1" />
            رسوم بيانية
          </TabsTrigger>
          <TabsTrigger value="details" className="flex items-center">
            <FileText className="h-4 w-4 ml-1" />
            تفاصيل
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="summary" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">إجمالي الفواتير</CardTitle>
                <CardDescription>مجموع قيم جميع الفواتير</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-primary">{formatCurrency(totalAmount)}</div>
                <p className="text-sm text-gray-500">عدد الفواتير: {invoiceCount}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">الفواتير المدفوعة</CardTitle>
                <CardDescription>مجموع قيم الفواتير المدفوعة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{formatCurrency(paidAmount)}</div>
                <p className="text-sm text-gray-500">عدد الفواتير: {paidCount}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">الفواتير المتأخرة</CardTitle>
                <CardDescription>مجموع قيم الفواتير المتأخرة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{formatCurrency(overdueAmount)}</div>
                <p className="text-sm text-gray-500">عدد الفواتير: {invoices.filter(inv => inv.status === 'OVERDUE').length}</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>توزيع الفواتير حسب الحالة</CardTitle>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات إضافية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">إجمالي الضرائب</h3>
                    <p className="text-xl font-semibold">{formatCurrency(totalTax)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">متوسط قيمة الفاتورة</h3>
                    <p className="text-xl font-semibold">{formatCurrency(totalAmount / (invoiceCount || 1))}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">نسبة التحصيل</h3>
                    <p className="text-xl font-semibold">{((paidAmount / totalAmount) * 100 || 0).toFixed(1)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="charts">
          <Card>
            <CardHeader>
              <CardTitle>تحليل الفواتير الشهري</CardTitle>
            </CardHeader>
            <CardContent className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Legend />
                  <Bar dataKey="total" name="إجمالي الفواتير" fill="#8884d8" />
                  <Bar dataKey="paid" name="المدفوع" fill="#4CAF50" />
                  <Bar dataKey="overdue" name="المتأخر" fill="#FF5722" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>تفاصيل الفواتير</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="p-2 text-right border">رقم الفاتورة</th>
                      <th className="p-2 text-right border">التاريخ</th>
                      <th className="p-2 text-right border">العميل</th>
                      <th className="p-2 text-right border">الحالة</th>
                      <th className="p-2 text-right border">المبلغ</th>
                      <th className="p-2 text-right border">الضريبة</th>
                      <th className="p-2 text-right border">الإجمالي</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoices.map((invoice) => (
                      <tr key={invoice.id} className="hover:bg-gray-50">
                        <td className="p-2 border">{invoice.invoiceNumber}</td>
                        <td className="p-2 border">{new Date(invoice.issueDate).toLocaleDateString('ar-AE')}</td>
                        <td className="p-2 border">{invoice.customer.name}</td>
                        <td className="p-2 border">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            invoice.status === 'PAID' ? 'bg-green-100 text-green-800' :
                            invoice.status === 'OVERDUE' ? 'bg-red-100 text-red-800' :
                            invoice.status === 'DRAFT' ? 'bg-gray-100 text-gray-800' :
                            invoice.status === 'SENT' ? 'bg-blue-100 text-blue-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {
                              invoice.status === 'PAID' ? 'مدفوعة' :
                              invoice.status === 'OVERDUE' ? 'متأخرة' :
                              invoice.status === 'DRAFT' ? 'مسودة' :
                              invoice.status === 'SENT' ? 'مرسلة' :
                              invoice.status === 'CANCELLED' ? 'ملغاة' : invoice.status
                            }
                          </span>
                        </td>
                        <td className="p-2 border">{formatCurrency(invoice.subtotal)}</td>
                        <td className="p-2 border">{formatCurrency(invoice.taxAmount)}</td>
                        <td className="p-2 border font-bold">{formatCurrency(invoice.total)}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-100 font-bold">
                      <td className="p-2 border" colSpan={4}>الإجمالي</td>
                      <td className="p-2 border">{formatCurrency(invoices.reduce((sum, inv) => sum + inv.subtotal, 0))}</td>
                      <td className="p-2 border">{formatCurrency(totalTax)}</td>
                      <td className="p-2 border">{formatCurrency(totalAmount)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
