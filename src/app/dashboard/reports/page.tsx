'use client';

import { useState, useEffect } from 'react';
import {Card, Button, StatCard, Loading} from '@/components/ui';
import {FileText, DollarSign, Users, Package, Download, Filter, RefreshCw} from 'lucide-react';
import { useI18n } from '@/lib/i18n';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface ReportData {
  totalCustomers: number;
  totalProducts: number;
  totalInvoices: number;
  totalRevenue: number;
  monthlyRevenue: Array<{
    month: string;
    revenue: number;
  }>;
  topProducts: Array<{
    id: string;
    name: string;
    sales: number;
    revenue: number;
  }>;
  topCustomers: Array<{
    id: string;
    name: string;
    totalSpent: number;
    invoiceCount: number;
  }>;
}

export default function ReportsPage() {
  const { t, language } = useI18n();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState<ReportData>({
    totalCustomers: 0,
    totalProducts: 0,
    totalInvoices: 0,
    totalRevenue: 0,
    monthlyRevenue: [],
    topProducts: [],
    topCustomers: []
  });

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);

      // محاكاة بيانات التقارير
      const mockData: ReportData = {
        totalCustomers: 3,
        totalProducts: 5,
        totalInvoices: 3,
        totalRevenue: 45675,
        monthlyRevenue: [
          { month: 'يناير', revenue: 12000 },
          { month: 'فبراير', revenue: 15000 },
          { month: 'مارس', revenue: 18675 },
        ],
        topProducts: [
          { id: '1', name: 'خدمات استشارية تقنية', sales: 40, revenue: 20000 },
          { id: '2', name: 'تطوير تطبيقات الويب', sales: 6, revenue: 15000 },
          { id: '3', name: 'تطبيقات الهواتف الذكية', sales: 3, revenue: 10500 },
        ],
        topCustomers: [
          { id: '1', name: 'شركة الإمارات للتجارة', totalSpent: 21000, invoiceCount: 1 },
          { id: '2', name: 'مؤسسة الخليج للمقاولات', totalSpent: 12075, invoiceCount: 1 },
          { id: '3', name: 'شركة النور للتكنولوجيا', totalSpent: 12600, invoiceCount: 1 },
        ]
      };

      setReportData(mockData);
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('حدث خطأ أثناء تحميل بيانات التقارير');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground heading">
            {language === 'ar' ? 'التقارير والإحصائيات' : 'Reports & Analytics'}
          </h1>
          <p className="text-sm text-muted-foreground mt-1">
            {language === 'ar' ? 'Reports & Analytics' : 'التقارير والإحصائيات'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصفية' : 'Filter'}
          </Button>
          <Button variant="outline" size="sm" onClick={fetchReportData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث البيانات' : 'Refresh Data'}
          </Button>
          <Button variant="gradient" size="sm">
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={language === 'ar' ? 'إجمالي العملاء' : 'Total Customers'}
          value={reportData.totalCustomers.toLocaleString()}
          icon={<Users className="h-5 w-5" />}
          color="blue"
          trend={{
            value: 12,
            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
            isPositive: true
          }}
          className="animate-fade-in-up animate-delay-100"
        />

        <StatCard
          title={language === 'ar' ? 'إجمالي المنتجات' : 'Total Products'}
          value={reportData.totalProducts.toLocaleString()}
          icon={<Package className="h-5 w-5" />}
          color="green"
          trend={{
            value: 8,
            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
            isPositive: true
          }}
          className="animate-fade-in-up animate-delay-200"
        />

        <StatCard
          title={language === 'ar' ? 'إجمالي الفواتير' : 'Total Invoices'}
          value={reportData.totalInvoices.toLocaleString()}
          icon={<FileText className="h-5 w-5" />}
          color="yellow"
          trend={{
            value: 15,
            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
            isPositive: true
          }}
          className="animate-fade-in-up animate-delay-300"
        />

        <StatCard
          title={language === 'ar' ? 'إجمالي الإيرادات' : 'Total Revenue'}
          value={formatCurrency(reportData.totalRevenue, language)}
          icon={<DollarSign className="h-5 w-5" />}
          color="purple"
          trend={{
            value: 22,
            label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
            isPositive: true
          }}
          className="animate-fade-in-up animate-delay-400"
        />
      </div>

      {/* الإيرادات الشهرية */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4 text-blue-800">الإيرادات الشهرية</h3>
        <div className="space-y-4">
          {reportData.monthlyRevenue.map((month, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="font-medium">{month.month}</span>
              <span className="text-lg font-bold text-green-600">{formatCurrency(month.revenue)}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* أفضل المنتجات */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-blue-800">أفضل المنتجات مبيعاً</h3>
          <div className="space-y-4">
            {reportData.topProducts.map((product, index) => (
              <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">{product.name}</div>
                  <div className="text-sm text-gray-500">{product.sales} مبيعة</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-green-600">{formatCurrency(product.revenue)}</div>
                  <div className="text-sm text-gray-500">#{index + 1}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* أفضل العملاء */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4 text-blue-800">أفضل العملاء</h3>
          <div className="space-y-4">
            {reportData.topCustomers.map((customer, index) => (
              <div key={customer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">{customer.name}</div>
                  <div className="text-sm text-gray-500">{customer.invoiceCount} فاتورة</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-blue-600">{formatCurrency(customer.totalSpent)}</div>
                  <div className="text-sm text-gray-500">#{index + 1}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* أزرار التصدير */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4 text-blue-800">تصدير التقارير</h3>
        <div className="flex flex-wrap gap-4">
          <button
            type="button"
            onClick={() => toast({ title: 'قريباً', description: 'ميزة تصدير PDF قيد التطوير' })}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            تصدير PDF
          </button>
          <button
            type="button"
            onClick={() => toast({ title: 'قريباً', description: 'ميزة تصدير Excel قيد التطوير' })}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            تصدير Excel
          </button>
          <button
            type="button"
            onClick={() => toast({ title: 'قريباً', description: 'ميزة تصدير CSV قيد التطوير' })}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            تصدير CSV
          </button>
        </div>
      </div>
    </div>
  );
}
