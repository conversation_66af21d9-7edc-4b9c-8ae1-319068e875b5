'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { Download, Printer, FileText } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  price: number;
  cost?: number;
  recipeItems: RecipeItem[];
}

interface RecipeItem {
  id: number;
  productId: number;
  ingredientId: number;
  quantity: number;
  unit?: string;
  ingredient: {
    id: number;
    name: string;
    price: number;
    cost?: number;
  };
}

export default function RecipesReportPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/recipes');
      if (!response.ok) {
        throw new Error('فشل في جلب المنتجات المركبة');
      }
      const data = await response.json();
      setProducts(data);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب المنتجات المركبة:', error);
      toast.error('حدث خطأ أثناء جلب المنتجات المركبة');
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(price);
  };

  // حساب التكلفة الإجمالية للمنتج المركب
  const calculateTotalCost = (product: Product) => {
    return product.recipeItems.reduce((total, item) => {
      const cost = item.ingredient.cost || 0;
      return total + (cost * item.quantity);
    }, 0);
  };

  // حساب هامش الربح للمنتج المركب
  const calculateProfitMargin = (product: Product) => {
    const totalCost = calculateTotalCost(product);
    return product.price - totalCost;
  };

  // حساب نسبة هامش الربح
  const calculateProfitPercentage = (product: Product) => {
    const totalCost = calculateTotalCost(product);
    if (totalCost === 0) return 0;
    return ((product.price - totalCost) / product.price) * 100;
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    toast.info('جاري تصدير التقرير إلى PDF...');
    // هنا يمكن إضافة كود لتصدير التقرير إلى PDF
  };

  const handleExportExcel = () => {
    toast.info('جاري تصدير التقرير إلى Excel...');
    // هنا يمكن إضافة كود لتصدير التقرير إلى Excel
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Breadcrumb
          items={[
            { label: 'الرئيسية', href: '/dashboard' },
            { label: 'التقارير', href: '/dashboard/reports' },
            { label: 'تقرير التقادير', href: '/dashboard/reports/recipes' },
          ]}
        />
        <div className="flex space-x-2 rtl:space-x-reverse">
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            طباعة
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportPDF}>
            <FileText className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            PDF
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportExcel}>
            <Download className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            Excel
          </Button>
        </div>
      </div>

      <Card className="print:shadow-none">
        <CardHeader className="print:pb-0">
          <div className="text-center print:mb-4">
            <CardTitle className="text-2xl">تقرير المنتجات المركبة والتقادير</CardTitle>
            <p className="text-sm text-gray-500 mt-1">
              {new Date().toLocaleDateString('ar-AE', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </p>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center p-4">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              لا توجد منتجات مركبة حتى الآن
              <br />
              <span className="text-sm">No composite products found</span>
            </div>
          ) : (
            <div className="space-y-8">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border p-2 text-right">المنتج</th>
                      <th className="border p-2 text-center">عدد المقادير</th>
                      <th className="border p-2 text-center">سعر البيع</th>
                      <th className="border p-2 text-center">التكلفة الإجمالية</th>
                      <th className="border p-2 text-center">هامش الربح</th>
                      <th className="border p-2 text-center">نسبة الربح</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product) => {
                      const totalCost = calculateTotalCost(product);
                      const profitMargin = calculateProfitMargin(product);
                      const profitPercentage = calculateProfitPercentage(product);

                      return (
                        <tr key={product.id} className="hover:bg-gray-50">
                          <td className="border p-2">{product.name}</td>
                          <td className="border p-2 text-center">{product.recipeItems.length}</td>
                          <td className="border p-2 text-center">{formatPrice(product.price)}</td>
                          <td className="border p-2 text-center">{formatPrice(totalCost)}</td>
                          <td className="border p-2 text-center">{formatPrice(profitMargin)}</td>
                          <td className="border p-2 text-center">{profitPercentage.toFixed(2)}%</td>
                        </tr>
                      );
                    })}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-100 font-bold">
                      <td className="border p-2">الإجمالي</td>
                      <td className="border p-2 text-center">{products.reduce((total, product) => total + product.recipeItems.length, 0)}</td>
                      <td className="border p-2 text-center">{formatPrice(products.reduce((total, product) => total + product.price, 0))}</td>
                      <td className="border p-2 text-center">{formatPrice(products.reduce((total, product) => total + calculateTotalCost(product), 0))}</td>
                      <td className="border p-2 text-center">{formatPrice(products.reduce((total, product) => total + calculateProfitMargin(product), 0))}</td>
                      <td className="border p-2 text-center">
                        {(products.reduce((total, product) => total + calculateProfitMargin(product), 0) /
                          products.reduce((total, product) => total + product.price, 0) * 100).toFixed(2)}%
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>

              <h2 className="text-xl font-bold mt-8 mb-4">تفاصيل المقادير</h2>

              {products.map((product) => (
                <div key={product.id} className="mb-6 border rounded-md overflow-hidden">
                  <div className="bg-gray-100 p-3 font-bold flex justify-between">
                    <span>{product.name}</span>
                    <span>{formatPrice(product.price)}</span>
                  </div>
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border p-2 text-right">المكون</th>
                        <th className="border p-2 text-center">الكمية</th>
                        <th className="border p-2 text-center">الوحدة</th>
                        <th className="border p-2 text-center">تكلفة الوحدة</th>
                        <th className="border p-2 text-center">التكلفة الإجمالية</th>
                      </tr>
                    </thead>
                    <tbody>
                      {product.recipeItems.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="border p-2">{item.ingredient.name}</td>
                          <td className="border p-2 text-center">{item.quantity}</td>
                          <td className="border p-2 text-center">{item.unit || '-'}</td>
                          <td className="border p-2 text-center">{formatPrice(item.ingredient.cost || 0)}</td>
                          <td className="border p-2 text-center">{formatPrice((item.ingredient.cost || 0) * item.quantity)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-50 font-bold">
                        <td colSpan={4} className="border p-2 text-left">إجمالي التكلفة:</td>
                        <td className="border p-2 text-center">{formatPrice(calculateTotalCost(product))}</td>
                      </tr>
                      <tr className="bg-gray-50 font-bold text-primary">
                        <td colSpan={4} className="border p-2 text-left">هامش الربح:</td>
                        <td className="border p-2 text-center">{formatPrice(calculateProfitMargin(product))}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
