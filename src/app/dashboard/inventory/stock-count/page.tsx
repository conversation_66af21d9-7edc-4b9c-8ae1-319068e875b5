'use client';

import { useState, useEffect } from 'react';
interface Product {
  id: string;
  name: string;
  sku: string;
  price: number;
  cost: number;
  quantity: number;
  category: string;
}

interface StockCount {
  id: string;
  date: string;
  status: 'draft' | 'in_progress' | 'completed';
  notes: string;
  items: StockCountItem[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface StockCountItem {
  productId: string;
  expectedQuantity: number;
  actualQuantity: number;
  difference: number;
  notes?: string;
}

export default function StockCountPage() {
  const [stockCounts, setStockCounts] = useState<StockCount[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [currentStockCount, setCurrentStockCount] = useState<StockCount | null>(null);

  useEffect(() => {
    // استدعاء API للحصول على عمليات الجرد والمنتجات
    const fetchData = async () => {
      setLoading(true);
      try {
        // في التطبيق الحقيقي، هذا سيكون استدعاء API
        const stockCountsData = localStorage.getItem('stockCounts');
        if (stockCountsData) {
          setStockCounts(JSON.parse(stockCountsData));
        } else {
          setStockCounts([]);
        }

        const productsData = localStorage.getItem('products');
        if (productsData) {
          setProducts(JSON.parse(productsData));
        } else {
          setProducts([]);
        }
      } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // إضافة عملية جرد جديدة
  const handleAddStockCount = () => {
    // إنشاء عناصر الجرد من المنتجات الحالية
    const stockCountItems = products.map(product => ({
      productId: product.id,
      expectedQuantity: product.quantity,
      actualQuantity: 0,
      difference: -product.quantity,
      notes: ''
    }));

    const newStockCount: StockCount = {
      id: Math.random().toString(36).substring(2, 9),
      date: new Date().toISOString().split('T')[0],
      status: 'draft',
      notes: '',
      items: stockCountItems,
      createdBy: 'المستخدم الحالي',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setCurrentStockCount(newStockCount);
    setShowModal(true);
  };

  // تعديل عملية جرد
  const handleEditStockCount = (stockCount: StockCount) => {
    setCurrentStockCount(stockCount);
    setShowModal(true);
  };

  // حذف عملية جرد
  const handleDeleteStockCount = (id: string) => {
    if (confirm('هل أنت متأكد من حذف عملية الجرد هذه؟')) {
      const updatedStockCounts = stockCounts.filter(count => count.id !== id);
      setStockCounts(updatedStockCounts);
      localStorage.setItem('stockCounts', JSON.stringify(updatedStockCounts));
    }
  };

  // حفظ عملية الجرد
  const handleSaveStockCount = (formData: StockCount) => {
    if (currentStockCount) {
      // تحديث عملية جرد موجودة
      const updatedStockCounts = stockCounts.map(count =>
        count.id === currentStockCount.id
          ? { ...formData, updatedAt: new Date().toISOString() }
          : count
      );
      setStockCounts(updatedStockCounts);
      localStorage.setItem('stockCounts', JSON.stringify(updatedStockCounts));

      // إذا كانت الحالة "مكتملة"، قم بتحديث كميات المنتجات
      if (formData.status === 'completed') {
        updateProductQuantities(formData.items);
      }
    } else {
      // إضافة عملية جرد جديدة
      const newStockCount: StockCount = {
        ...formData,
        id: Math.random().toString(36).substring(2, 9),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const updatedStockCounts = [...stockCounts, newStockCount];
      setStockCounts(updatedStockCounts);
      localStorage.setItem('stockCounts', JSON.stringify(updatedStockCounts));

      // إذا كانت الحالة "مكتملة"، قم بتحديث كميات المنتجات
      if (newStockCount.status === 'completed') {
        updateProductQuantities(newStockCount.items);
      }
    }

    setShowModal(false);
  };

  // تحديث كميات المنتجات بناءً على نتائج الجرد
  const updateProductQuantities = (items: StockCountItem[]) => {
    const updatedProducts = products.map(product => {
      const stockItem = items.find(item => item.productId === product.id);
      if (stockItem) {
        return {
          ...product,
          quantity: stockItem.actualQuantity
        };
      }
      return product;
    });

    setProducts(updatedProducts);
    localStorage.setItem('products', JSON.stringify(updatedProducts));
  };

  // الحصول على نص حالة الجرد
  const getStatusText = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'مسودة | Draft';
      case 'in_progress':
        return 'قيد التنفيذ | In Progress';
      case 'completed':
        return 'مكتمل | Completed';
      default:
        return status;
    }
  };

  // الحصول على لون حالة الجرد
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1 dark:text-white">الجرد</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">Stock Count</p>
        </div>
        <button
          onClick={handleAddStockCount}
          className="px-4 py-2 bg-primary text-white rounded-md text-sm"
        >
          جرد جديد | New Stock Count
        </button>
      </div>

      {/* جدول عمليات الجرد */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="p-2 text-right border dark:border-gray-600">رقم الجرد</th>
              <th className="p-2 text-right border dark:border-gray-600">التاريخ</th>
              <th className="p-2 text-right border dark:border-gray-600">الحالة</th>
              <th className="p-2 text-right border dark:border-gray-600">عدد العناصر</th>
              <th className="p-2 text-right border dark:border-gray-600">الفروقات</th>
              <th className="p-2 text-right border dark:border-gray-600">المنشئ</th>
              <th className="p-2 text-center border dark:border-gray-600">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={7} className="p-4 text-center">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </td>
              </tr>
            ) : stockCounts.length === 0 ? (
              <tr>
                <td colSpan={7} className="p-4 text-center text-gray-500 dark:text-gray-400">
                  لا توجد عمليات جرد
                </td>
              </tr>
            ) : (
              stockCounts.map(stockCount => {
                // حساب إجمالي الفروقات
                const totalDifference = stockCount.items.reduce((total, item) => total + item.difference, 0);

                return (
                  <tr key={stockCount.id} className="border-b dark:border-gray-700">
                    <td className="p-2 border dark:border-gray-600">
                      {stockCount.id.substring(0, 6).toUpperCase()}
                    </td>
                    <td className="p-2 border dark:border-gray-600">
                      {new Date(stockCount.date).toLocaleDateString('ar-AE')}
                    </td>
                    <td className="p-2 border dark:border-gray-600">
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(stockCount.status)}`}>
                        {getStatusText(stockCount.status)}
                      </span>
                    </td>
                    <td className="p-2 border dark:border-gray-600">
                      {stockCount.items.length}
                    </td>
                    <td className="p-2 border dark:border-gray-600">
                      <span className={totalDifference < 0 ? 'text-red-500' : totalDifference > 0 ? 'text-green-500' : ''}>
                        {totalDifference}
                      </span>
                    </td>
                    <td className="p-2 border dark:border-gray-600">
                      {stockCount.createdBy}
                    </td>
                    <td className="p-2 border dark:border-gray-600 text-center">
                      <div className="flex justify-center space-x-1 space-x-reverse">
                        <button
                          onClick={() => handleEditStockCount(stockCount)}
                          className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-md text-xs"
                        >
                          تعديل
                        </button>
                        {stockCount.status !== 'completed' && (
                          <button
                            onClick={() => handleDeleteStockCount(stockCount.id)}
                            className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 rounded-md text-xs"
                          >
                            حذف
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* نموذج الجرد */}
      {showModal && currentStockCount && (
        <StockCountForm
          stockCount={currentStockCount}
          products={products}
          onSave={handleSaveStockCount}
          onCancel={() => setShowModal(false)}
        />
      )}
    </div>
  );
}

// مكون نموذج الجرد
function StockCountForm({ stockCount, products, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    ...stockCount,
    items: [...stockCount.items]
  });

  // تحديث بيانات النموذج
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // تحديث كمية منتج
  const handleQuantityChange = (productId, value) => {
    const actualQuantity = parseInt(value) || 0;

    setFormData(prev => {
      const updatedItems = prev.items.map(item => {
        if (item.productId === productId) {
          const difference = actualQuantity - item.expectedQuantity;
          return {
            ...item,
            actualQuantity,
            difference
          };
        }
        return item;
      });

      return {
        ...prev,
        items: updatedItems
      };
    });
  };

  // تحديث ملاحظات منتج
  const handleItemNotesChange = (productId, value) => {
    setFormData(prev => {
      const updatedItems = prev.items.map(item => {
        if (item.productId === productId) {
          return {
            ...item,
            notes: value
          };
        }
        return item;
      });

      return {
        ...prev,
        items: updatedItems
      };
    });
  };

  // حفظ نموذج الجرد
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  // الحصول على اسم المنتج من المعرف
  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId);
    return product ? product.name : 'غير معروف';
  };

  // الحصول على رمز المنتج من المعرف
  const getProductSku = (productId) => {
    const product = products.find(p => p.id === productId);
    return product ? product.sku : '';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-screen overflow-y-auto">
        <h2 className="text-xl font-bold mb-4 dark:text-white">
          {stockCount.status === 'draft' ? 'جرد جديد' : 'تعديل الجرد'}
        </h2>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                التاريخ <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              />
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                الحالة <span className="text-red-500">*</span>
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              >
                <option value="draft">مسودة | Draft</option>
                <option value="in_progress">قيد التنفيذ | In Progress</option>
                <option value="completed">مكتمل | Completed</option>
              </select>
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                المنشئ
              </label>
              <input
                type="text"
                name="createdBy"
                value={formData.createdBy}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                readOnly
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              ملاحظات
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              rows={2}
            ></textarea>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2 dark:text-white">عناصر الجرد</h3>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <th className="p-2 text-right border dark:border-gray-600">المنتج</th>
                    <th className="p-2 text-right border dark:border-gray-600">الرمز</th>
                    <th className="p-2 text-right border dark:border-gray-600">الكمية المتوقعة</th>
                    <th className="p-2 text-right border dark:border-gray-600">الكمية الفعلية</th>
                    <th className="p-2 text-right border dark:border-gray-600">الفرق</th>
                    <th className="p-2 text-right border dark:border-gray-600">ملاحظات</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.items.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="p-4 text-center text-gray-500 dark:text-gray-400">
                        لا توجد منتجات للجرد
                      </td>
                    </tr>
                  ) : (
                    formData.items.map((item, index) => (
                      <tr key={item.productId} className="border-b dark:border-gray-700">
                        <td className="p-2 border dark:border-gray-600">
                          {getProductName(item.productId)}
                        </td>
                        <td className="p-2 border dark:border-gray-600">
                          {getProductSku(item.productId)}
                        </td>
                        <td className="p-2 border dark:border-gray-600">
                          {item.expectedQuantity}
                        </td>
                        <td className="p-2 border dark:border-gray-600">
                          <input
                            type="number"
                            value={item.actualQuantity}
                            onChange={(e) => handleQuantityChange(item.productId, e.target.value)}
                            min="0"
                            className="w-20 p-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                          />
                        </td>
                        <td className="p-2 border dark:border-gray-600">
                          <span className={item.difference < 0 ? 'text-red-500' : item.difference > 0 ? 'text-green-500' : ''}>
                            {item.difference}
                          </span>
                        </td>
                        <td className="p-2 border dark:border-gray-600">
                          <input
                            type="text"
                            value={item.notes || ''}
                            onChange={(e) => handleItemNotesChange(item.productId, e.target.value)}
                            className="w-full p-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                            placeholder="ملاحظات"
                          />
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:text-white rounded-md"
            >
              إلغاء | Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md"
            >
              حفظ | Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
