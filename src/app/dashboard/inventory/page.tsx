'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Product, 
  InventoryItem, 
  InventoryLocation,
  InventoryAlert,
  isLowStock,
  isOutOfStock
} from '@/types/inventory';
import { 
  getProducts, 
  getInventoryItems, 
  getInventoryLocations,
  getInventoryAlerts,
  updateInventoryItem,
  adjustInventoryQuantity,
  updateAlertStatus
} from '@/lib/inventory-service';
import { formatCurrency } from '@/lib/utils';

export default function InventoryPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [locations, setLocations] = useState<InventoryLocation[]>([]);
  const [alerts, setAlerts] = useState<InventoryAlert[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showLowStock, setShowLowStock] = useState(false);
  const [showOutOfStock, setShowOutOfStock] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [adjustQuantity, setAdjustQuantity] = useState<number>(0);
  const [adjustmentNote, setAdjustmentNote] = useState('');

  // تحميل البيانات
  useEffect(() => {
    const loadData = () => {
      setLoading(true);
      try {
        const productsData = getProducts();
        const inventoryData = getInventoryItems();
        const locationsData = getInventoryLocations();
        const alertsData = getInventoryAlerts().filter(alert => alert.status !== 'resolved');
        
        setProducts(productsData);
        setInventoryItems(inventoryData);
        setLocations(locationsData);
        setAlerts(alertsData);
        
        // تعيين الموقع الافتراضي
        if (locationsData.length > 0 && !selectedLocation) {
          const defaultLocation = locationsData.find(loc => loc.isDefault);
          setSelectedLocation(defaultLocation?.id || locationsData[0].id);
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات المخزون:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
    
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(loadData, 30000);
    
    return () => clearInterval(interval);
  }, [selectedLocation]);

  // الحصول على المنتجات المصفاة
  const filteredProducts = products
    .filter(product => {
      // تصفية حسب نوع المنتج (فقط المنتجات المادية)
      if (product.type !== 'physical') return false;
      
      // تصفية حسب البحث
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          product.name.toLowerCase().includes(query) ||
          product.sku.toLowerCase().includes(query) ||
          (product.barcode && product.barcode.toLowerCase().includes(query))
        );
      }
      
      return true;
    })
    .map(product => {
      // إضافة معلومات المخزون لكل منتج
      const inventoryItem = inventoryItems.find(
        item => item.productId === product.id && item.locationId === selectedLocation
      );
      
      return {
        ...product,
        inventory: inventoryItem || {
          productId: product.id,
          locationId: selectedLocation,
          quantity: 0,
          minQuantity: 0,
          maxQuantity: 0,
          reorderPoint: 0,
          lastStockTake: '',
          updatedAt: ''
        }
      };
    })
    .filter(product => {
      // تصفية حسب حالة المخزون
      if (showLowStock && !isLowStock(product.inventory)) return false;
      if (showOutOfStock && !isOutOfStock(product.inventory)) return false;
      return true;
    });

  // معالجة تعديل كمية المخزون
  const handleAdjustQuantity = (productId: string) => {
    if (!adjustQuantity || !selectedLocation) return;
    
    try {
      adjustInventoryQuantity(
        productId,
        selectedLocation,
        adjustQuantity,
        'manual-adjustment',
        'adjustment',
        adjustmentNote
      );
      
      // تحديث البيانات
      const updatedInventoryItems = getInventoryItems();
      setInventoryItems(updatedInventoryItems);
      
      // إعادة تعيين القيم
      setAdjustQuantity(0);
      setAdjustmentNote('');
      setSelectedProduct(null);
    } catch (error) {
      console.error('خطأ في تعديل كمية المخزون:', error);
      alert('حدث خطأ أثناء تعديل كمية المخزون');
    }
  };

  // معالجة تحديث حالة التنبيه
  const handleAlertAction = (alertId: string, status: 'acknowledged' | 'resolved') => {
    try {
      updateAlertStatus(alertId, status, 'current-user');
      
      // تحديث التنبيهات
      const updatedAlerts = getInventoryAlerts().filter(alert => alert.status !== 'resolved');
      setAlerts(updatedAlerts);
    } catch (error) {
      console.error('خطأ في تحديث حالة التنبيه:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="mr-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">إدارة المخزون</h1>
          <p className="text-sm text-gray-500">Inventory Management</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => router.push('/dashboard/inventory/stocktake')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm"
          >
            جرد المخزون | Stock Take
          </button>
          <button
            onClick={() => router.push('/dashboard/inventory/movements')}
            className="px-4 py-2 bg-gray-600 text-white rounded-md text-sm"
          >
            حركات المخزون | Movements
          </button>
        </div>
      </div>

      {/* تنبيهات المخزون */}
      {alerts.length > 0 && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">
            <span>تنبيهات المخزون</span>
            <span className="text-xs text-gray-500 mr-1">Inventory Alerts</span>
          </h2>
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <ul className="divide-y divide-yellow-200">
              {alerts.map(alert => {
                const product = products.find(p => p.id === alert.productId);
                return (
                  <li key={alert.id} className="py-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="font-medium">{product?.name}</span>
                        <span className="text-sm text-gray-500 mr-2">({product?.sku})</span>
                        <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                      </div>
                      <div className="flex space-x-2 space-x-reverse">
                        {alert.status === 'new' && (
                          <button
                            onClick={() => handleAlertAction(alert.id, 'acknowledged')}
                            className="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-xs"
                          >
                            تأكيد | Acknowledge
                          </button>
                        )}
                        <button
                          onClick={() => handleAlertAction(alert.id, 'resolved')}
                          className="px-3 py-1 bg-green-100 text-green-800 rounded-md text-xs"
                        >
                          تم الحل | Resolve
                        </button>
                      </div>
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      )}

      {/* أدوات التصفية */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label htmlFor="location" className="block mb-2 text-sm font-medium">
            <span>الموقع</span>
            <span className="text-xs text-gray-500 mr-1">Location</span>
          </label>
          <select
            id="location"
            value={selectedLocation}
            onChange={(e) => setSelectedLocation(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            {locations.map(location => (
              <option key={location.id} value={location.id}>
                {location.name} {location.isDefault && '(الافتراضي)'}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="search" className="block mb-2 text-sm font-medium">
            <span>بحث</span>
            <span className="text-xs text-gray-500 mr-1">Search</span>
          </label>
          <input
            id="search"
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="اسم المنتج، الباركود، SKU..."
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
        <div className="flex items-end space-x-4 space-x-reverse">
          <div className="flex items-center">
            <input
              id="lowStock"
              type="checkbox"
              checked={showLowStock}
              onChange={(e) => setShowLowStock(e.target.checked)}
              className="h-4 w-4 text-primary border-gray-300 rounded"
            />
            <label htmlFor="lowStock" className="mr-2 text-sm">
              <span>المخزون المنخفض</span>
              <span className="text-xs text-gray-500 mr-1">Low Stock</span>
            </label>
          </div>
          <div className="flex items-center">
            <input
              id="outOfStock"
              type="checkbox"
              checked={showOutOfStock}
              onChange={(e) => setShowOutOfStock(e.target.checked)}
              className="h-4 w-4 text-primary border-gray-300 rounded"
            />
            <label htmlFor="outOfStock" className="mr-2 text-sm">
              <span>نفذ من المخزون</span>
              <span className="text-xs text-gray-500 mr-1">Out of Stock</span>
            </label>
          </div>
        </div>
      </div>

      {/* جدول المخزون */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-2 text-right border">
                <span>المنتج</span>
                <span className="text-xs text-gray-500 mr-1">Product</span>
              </th>
              <th className="p-2 text-right border">
                <span>SKU</span>
              </th>
              <th className="p-2 text-right border">
                <span>الكمية</span>
                <span className="text-xs text-gray-500 mr-1">Quantity</span>
              </th>
              <th className="p-2 text-right border">
                <span>نقطة إعادة الطلب</span>
                <span className="text-xs text-gray-500 mr-1">Reorder Point</span>
              </th>
              <th className="p-2 text-right border">
                <span>سعر التكلفة</span>
                <span className="text-xs text-gray-500 mr-1">Cost</span>
              </th>
              <th className="p-2 text-right border">
                <span>قيمة المخزون</span>
                <span className="text-xs text-gray-500 mr-1">Value</span>
              </th>
              <th className="p-2 text-center border">
                <span>الإجراءات</span>
                <span className="text-xs text-gray-500 mr-1">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredProducts.length === 0 ? (
              <tr>
                <td colSpan={7} className="p-4 text-center text-gray-500">
                  <span>لا توجد منتجات في المخزون</span>
                  <br />
                  <span className="text-sm">No products in inventory</span>
                </td>
              </tr>
            ) : (
              filteredProducts.map(product => (
                <tr key={product.id} className={`border-b ${isLowStock(product.inventory) ? 'bg-yellow-50' : isOutOfStock(product.inventory) ? 'bg-red-50' : ''}`}>
                  <td className="p-2 border">
                    <div className="font-medium">{product.name}</div>
                    {product.nameEn && <div className="text-xs text-gray-500">{product.nameEn}</div>}
                  </td>
                  <td className="p-2 border">
                    <div>{product.sku}</div>
                    {product.barcode && <div className="text-xs text-gray-500">{product.barcode}</div>}
                  </td>
                  <td className="p-2 border">
                    <div className={`font-medium ${isOutOfStock(product.inventory) ? 'text-red-600' : isLowStock(product.inventory) ? 'text-yellow-600' : ''}`}>
                      {product.inventory.quantity} {product.unit}
                    </div>
                  </td>
                  <td className="p-2 border">
                    {product.inventory.reorderPoint} {product.unit}
                  </td>
                  <td className="p-2 border">
                    {formatCurrency(product.costPrice)}
                  </td>
                  <td className="p-2 border">
                    {formatCurrency(product.costPrice * product.inventory.quantity)}
                  </td>
                  <td className="p-2 border text-center">
                    <div className="flex justify-center space-x-1 space-x-reverse">
                      <button
                        onClick={() => setSelectedProduct(product.id)}
                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs"
                      >
                        تعديل الكمية | Adjust
                      </button>
                      <button
                        onClick={() => router.push(`/dashboard/inventory/product/${product.id}`)}
                        className="px-2 py-1 bg-gray-100 text-gray-800 rounded-md text-xs"
                      >
                        التفاصيل | Details
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* نموذج تعديل الكمية */}
      {selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">
              <span>تعديل كمية المخزون</span>
              <span className="text-xs text-gray-500 mr-1">Adjust Inventory</span>
            </h2>
            
            <div className="mb-4">
              <label htmlFor="adjustQuantity" className="block mb-2 text-sm font-medium">
                <span>الكمية (+ للإضافة، - للخصم)</span>
                <span className="text-xs text-gray-500 mr-1">Quantity</span>
              </label>
              <input
                id="adjustQuantity"
                type="number"
                value={adjustQuantity}
                onChange={(e) => setAdjustQuantity(parseInt(e.target.value) || 0)}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
            
            <div className="mb-4">
              <label htmlFor="adjustmentNote" className="block mb-2 text-sm font-medium">
                <span>ملاحظات</span>
                <span className="text-xs text-gray-500 mr-1">Notes</span>
              </label>
              <textarea
                id="adjustmentNote"
                value={adjustmentNote}
                onChange={(e) => setAdjustmentNote(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
              ></textarea>
            </div>
            
            <div className="flex justify-end space-x-2 space-x-reverse">
              <button
                onClick={() => setSelectedProduct(null)}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                إلغاء | Cancel
              </button>
              <button
                onClick={() => handleAdjustQuantity(selectedProduct)}
                className="px-4 py-2 bg-primary text-white rounded-md"
              >
                حفظ | Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
