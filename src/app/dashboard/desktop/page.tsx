'use client';

import { ElectronFeatures } from '@/components/electron/electron-features';

/**
 * صفحة ميزات سطح المكتب
 * Desktop features page
 */
export default function DesktopFeaturesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-1">ميزات تطبيق سطح المكتب</h1>
        <p className="text-gray-500">Desktop Application Features</p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <ElectronFeatures />

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">مميزات إضافية في تطبيق سطح المكتب</h2>
          <p className="mb-4">يوفر تطبيق سطح المكتب العديد من المميزات الإضافية التي لا تتوفر في الإصدار المستند إلى الويب:</p>

          <ul className="list-disc list-inside space-y-2 mr-4">
            <li>العمل بدون اتصال بالإنترنت</li>
            <li>تصدير التقارير مباشرة إلى ملفات PDF أو Excel</li>
            <li>طباعة الفواتير والتقارير مباشرة</li>
            <li>الوصول إلى نظام الملفات المحلي</li>
            <li>إشعارات سطح المكتب</li>
            <li>تكامل مع أنظمة التشغيل</li>
            <li>أداء أفضل وسرعة استجابة أعلى</li>
            <li>حماية وأمان أفضل للبيانات</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
