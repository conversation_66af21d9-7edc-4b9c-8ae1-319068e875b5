'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusCircle, Search, Filter, Users, Star, Phone, Mail, MapPin, Edit, Trash2, Eye } from 'lucide-react';
import { Button, StatCard, Loading, Input, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { useI18n } from '@/lib/i18n';
import { toast } from 'sonner';

interface Supplier {
  id: number;
  name: string;
  nameEn?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  address?: string;
  city?: string;
  country?: string;
  contactPerson?: string;
  supplierType?: string;
  category?: string;
  rating?: number;
  creditLimit?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    purchases: number;
    expenses: number;
  };
}

export default function SuppliersPage() {
  const { t, language } = useI18n();
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    totalPurchases: 0,
    averageRating: 0
  });

  // محاكاة تحميل البيانات
  useEffect(() => {
    const loadSuppliers = async () => {
      setLoading(true);
      try {
        // محاكاة API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        const mockSuppliers: Supplier[] = [
          {
            id: 1,
            name: 'شركة الإمارات للتوريدات',
            nameEn: 'Emirates Supply Company',
            email: '<EMAIL>',
            phone: '+971-4-1234567',
            mobile: '+971-50-1234567',
            address: 'شارع الشيخ زايد، دبي',
            city: 'دبي',
            country: 'الإمارات العربية المتحدة',
            contactPerson: 'أحمد محمد',
            supplierType: 'vendor',
            category: 'مواد خام',
            rating: 4.5,
            creditLimit: 50000,
            isActive: true,
            createdAt: '2024-01-15',
            updatedAt: '2024-06-20',
            _count: { purchases: 25, expenses: 12 }
          },
          {
            id: 2,
            name: 'مؤسسة الخليج التجارية',
            nameEn: 'Gulf Trading Establishment',
            email: '<EMAIL>',
            phone: '+971-6-5678901',
            mobile: '+971-55-9876543',
            address: 'المنطقة الصناعية، الشارقة',
            city: 'الشارقة',
            country: 'الإمارات العربية المتحدة',
            contactPerson: 'فاطمة علي',
            supplierType: 'distributor',
            category: 'معدات',
            rating: 4.2,
            creditLimit: 75000,
            isActive: true,
            createdAt: '2024-02-10',
            updatedAt: '2024-06-19',
            _count: { purchases: 18, expenses: 8 }
          },
          {
            id: 3,
            name: 'شركة أبوظبي للخدمات',
            nameEn: 'Abu Dhabi Services Company',
            email: '<EMAIL>',
            phone: '+971-2-3456789',
            mobile: '+971-52-1357924',
            address: 'شارع الكورنيش، أبوظبي',
            city: 'أبوظبي',
            country: 'الإمارات العربية المتحدة',
            contactPerson: 'خالد السعدي',
            supplierType: 'service',
            category: 'خدمات',
            rating: 3.8,
            creditLimit: 30000,
            isActive: false,
            createdAt: '2024-03-05',
            updatedAt: '2024-06-18',
            _count: { purchases: 10, expenses: 5 }
          }
        ];

        setSuppliers(mockSuppliers);

        // حساب الإحصائيات
        const active = mockSuppliers.filter(s => s.isActive).length;
        const totalPurchases = mockSuppliers.reduce((sum, s) => sum + (s._count?.purchases || 0), 0);
        const averageRating = mockSuppliers.reduce((sum, s) => sum + (s.rating || 0), 0) / mockSuppliers.length;

        setStats({
          total: mockSuppliers.length,
          active,
          inactive: mockSuppliers.length - active,
          totalPurchases,
          averageRating: Math.round(averageRating * 10) / 10
        });

      } catch (error) {
        console.error('Error loading suppliers:', error);
        toast.error('حدث خطأ أثناء تحميل بيانات الموردين');
      } finally {
        setLoading(false);
      }
    };

    loadSuppliers();
  }, []);

  // تصفية الموردين حسب البحث
  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.nameEn?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.contactPerson?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground heading">
            {language === 'ar' ? 'إدارة الموردين' : 'Suppliers Management'}
          </h1>
          <p className="text-sm text-muted-foreground mt-1">
            {language === 'ar' ? 'Suppliers Management' : 'إدارة الموردين'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصفية' : 'Filter'}
          </Button>
          <Link href="/dashboard/suppliers/new">
            <Button variant="gradient">
              <PlusCircle className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة مورد' : 'Add Supplier'}
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <StatCard
          title={language === 'ar' ? 'إجمالي الموردين' : 'Total Suppliers'}
          value={stats.total.toLocaleString()}
          icon={<Users className="h-5 w-5" />}
          color="blue"
          className="animate-fade-in-up animate-delay-100"
        />
        <StatCard
          title={language === 'ar' ? 'موردين نشطين' : 'Active Suppliers'}
          value={stats.active.toLocaleString()}
          icon={<Users className="h-5 w-5" />}
          color="green"
          className="animate-fade-in-up animate-delay-200"
        />
        <StatCard
          title={language === 'ar' ? 'موردين غير نشطين' : 'Inactive Suppliers'}
          value={stats.inactive.toLocaleString()}
          icon={<Users className="h-5 w-5" />}
          color="red"
          className="animate-fade-in-up animate-delay-300"
        />
        <StatCard
          title={language === 'ar' ? 'إجمالي المشتريات' : 'Total Purchases'}
          value={stats.totalPurchases.toLocaleString()}
          icon={<Users className="h-5 w-5" />}
          color="purple"
          className="animate-fade-in-up animate-delay-400"
        />
        <StatCard
          title={language === 'ar' ? 'متوسط التقييم' : 'Average Rating'}
          value={`${stats.averageRating}/5`}
          icon={<Star className="h-5 w-5" />}
          color="yellow"
          className="animate-fade-in-up animate-delay-500"
        />
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={language === 'ar' ? 'البحث في الموردين...' : 'Search suppliers...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Suppliers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSuppliers.map((supplier) => (
          <Card key={supplier.id} className="hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg font-semibold text-foreground">
                    {supplier.name}
                  </CardTitle>
                  {supplier.nameEn && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {supplier.nameEn}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  {supplier.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">{supplier.rating}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* Contact Info */}
              <div className="space-y-2">
                {supplier.contactPerson && (
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{supplier.contactPerson}</span>
                  </div>
                )}
                {supplier.phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{supplier.phone}</span>
                  </div>
                )}
                {supplier.email && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{supplier.email}</span>
                  </div>
                )}
                {supplier.city && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{supplier.city}</span>
                  </div>
                )}
              </div>

              {/* Stats */}
              <div className="flex justify-between text-sm text-muted-foreground pt-2 border-t">
                <span>
                  {language === 'ar' ? 'مشتريات:' : 'Purchases:'} {supplier._count?.purchases || 0}
                </span>
                <span>
                  {language === 'ar' ? 'مصروفات:' : 'Expenses:'} {supplier._count?.expenses || 0}
                </span>
              </div>

              {/* Status */}
              <div className="flex items-center justify-between pt-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  supplier.isActive
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                }`}>
                  {supplier.isActive
                    ? (language === 'ar' ? 'نشط' : 'Active')
                    : (language === 'ar' ? 'غير نشط' : 'Inactive')
                  }
                </span>

                {/* Actions */}
                <div className="flex gap-1">
                  <Link href={`/dashboard/suppliers/${supplier.id}`}>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href={`/dashboard/suppliers/${supplier.id}/edit`}>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredSuppliers.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            {language === 'ar' ? 'لا توجد موردين' : 'No suppliers found'}
          </h3>
          <p className="text-muted-foreground mb-4">
            {language === 'ar'
              ? 'لم يتم العثور على موردين مطابقين لبحثك'
              : 'No suppliers match your search criteria'
            }
          </p>
          <Link href="/dashboard/suppliers/new">
            <Button variant="gradient">
              <PlusCircle className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة أول مورد' : 'Add First Supplier'}
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
