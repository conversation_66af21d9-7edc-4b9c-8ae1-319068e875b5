'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Sale,
  SaleItem,
  Payment,
  PaymentMethod
} from '@/types/pos';
import {
  createSale,
  addProductToSale,
  updateSaleItemQuantity,
  removeSaleItem,
  applySaleItemDiscount,
  addPaymentToSale,
  completeSale,
  cancelSale,
  getPOSSettings
} from '@/lib/pos-service';
import { Product } from '@/types/inventory';
import { getProducts, getInventoryItems } from '@/lib/inventory-service';
import { formatCurrency } from '@/lib/utils';
import { TaxSettings } from '@/components/ui/tax-settings';
import { getDefaultTaxSettings } from '@/lib/settings';
import { ProductCard } from '@/components/ui/product-card';
import { CartItem } from '@/components/ui/cart-item';

export default function POSPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentSale, setCurrentSale] = useState<Sale | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [paymentReference, setPaymentReference] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed'>('percentage');
  const [discountValue, setDiscountValue] = useState<number>(0);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [taxSettings, setTaxSettings] = useState<TaxSettings>(getDefaultTaxSettings());
  const [posSettings, setPosSettings] = useState(getPOSSettings());

  // تحميل البيانات
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // تحميل المنتجات
        const productsData = getProducts();
        setProducts(productsData);

        // استخراج الفئات الفريدة
        const uniqueCategories = Array.from(
          new Set(productsData.map(product => product.category))
        ).filter(Boolean);
        setCategories(uniqueCategories);

        // إنشاء مبيعة جديدة إذا لم تكن موجودة
        if (!currentSale) {
          const newSale = createSale();
          setCurrentSale(newSale);
        }

        // تحميل إعدادات نقاط البيع
        const settings = getPOSSettings();
        setPosSettings(settings);

        // تعيين طريقة الدفع الافتراضية
        setPaymentMethod(settings.defaultPaymentMethod);
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentSale]);

  // الحصول على المنتجات المصفاة
  const filteredProducts = products
    .filter(product => {
      // تصفية حسب الفئة
      if (selectedCategory !== 'all' && product.category !== selectedCategory) {
        return false;
      }

      // تصفية حسب البحث
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          product.name.toLowerCase().includes(query) ||
          product.sku.toLowerCase().includes(query) ||
          (product.barcode && product.barcode.toLowerCase().includes(query))
        );
      }

      return true;
    });

  // التحقق من توفر المنتج في المخزون
  const isProductInStock = (productId: string): boolean => {
    const inventoryItems = getInventoryItems();
    const item = inventoryItems.find(item => item.productId === productId && item.locationId === 'default');
    return item ? item.quantity > 0 : false;
  };

  // إضافة منتج إلى المبيعة
  const handleAddProduct = (productId: string) => {
    if (!currentSale) return;

    try {
      const updatedSale = addProductToSale(currentSale.id, productId);
      if (updatedSale) {
        setCurrentSale(updatedSale);
      }
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      alert('حدث خطأ أثناء إضافة المنتج');
    }
  };

  // تحديث كمية منتج في المبيعة
  const handleUpdateQuantity = (itemId: string, quantity: number) => {
    if (!currentSale) return;

    try {
      const updatedSale = updateSaleItemQuantity(currentSale.id, itemId, quantity);
      if (updatedSale) {
        setCurrentSale(updatedSale);
      }
    } catch (error) {
      console.error('خطأ في تحديث الكمية:', error);
      alert('حدث خطأ أثناء تحديث الكمية');
    }
  };

  // إزالة منتج من المبيعة
  const handleRemoveItem = (itemId: string) => {
    if (!currentSale) return;

    try {
      const updatedSale = removeSaleItem(currentSale.id, itemId);
      if (updatedSale) {
        setCurrentSale(updatedSale);
      }
    } catch (error) {
      console.error('خطأ في إزالة المنتج:', error);
      alert('حدث خطأ أثناء إزالة المنتج');
    }
  };

  // تطبيق خصم على منتج
  const handleApplyDiscount = () => {
    if (!currentSale || !selectedItemId) return;

    try {
      const updatedSale = applySaleItemDiscount(
        currentSale.id,
        selectedItemId,
        discountType,
        discountValue
      );

      if (updatedSale) {
        setCurrentSale(updatedSale);
        setShowDiscountModal(false);
        setSelectedItemId(null);
        setDiscountValue(0);
      }
    } catch (error) {
      console.error('خطأ في تطبيق الخصم:', error);
      alert('حدث خطأ أثناء تطبيق الخصم');
    }
  };

  // إضافة دفعة
  const handleAddPayment = () => {
    if (!currentSale) return;

    try {
      const updatedSale = addPaymentToSale(
        currentSale.id,
        paymentMethod,
        paymentAmount,
        paymentReference
      );

      if (updatedSale) {
        setCurrentSale(updatedSale);
        setShowPaymentModal(false);
        setPaymentAmount(0);
        setPaymentReference('');

        // إذا تم دفع المبلغ بالكامل، اسأل المستخدم عما إذا كان يريد إكمال المبيعة
        if (updatedSale.amountDue <= 0) {
          if (confirm('تم دفع المبلغ بالكامل. هل تريد إكمال المبيعة؟')) {
            handleCompleteSale();
          }
        }
      }
    } catch (error) {
      console.error('خطأ في إضافة الدفعة:', error);
      alert('حدث خطأ أثناء إضافة الدفعة');
    }
  };

  // إكمال المبيعة
  const handleCompleteSale = () => {
    if (!currentSale) return;

    try {
      const updatedSale = completeSale(currentSale.id);

      if (updatedSale) {
        alert('تم إكمال المبيعة بنجاح');

        // إنشاء مبيعة جديدة
        const newSale = createSale();
        setCurrentSale(newSale);

        // طباعة الإيصال إذا كان مطلوبًا
        if (posSettings.printReceipt) {
          router.push(`/dashboard/pos/receipt/${updatedSale.id}`);
        }
      }
    } catch (error) {
      console.error('خطأ في إكمال المبيعة:', error);
      alert('حدث خطأ أثناء إكمال المبيعة');
    }
  };

  // إلغاء المبيعة
  const handleCancelSale = () => {
    if (!currentSale) return;

    if (confirm('هل أنت متأكد من إلغاء المبيعة؟')) {
      try {
        cancelSale(currentSale.id);

        // إنشاء مبيعة جديدة
        const newSale = createSale();
        setCurrentSale(newSale);
      } catch (error) {
        console.error('خطأ في إلغاء المبيعة:', error);
        alert('حدث خطأ أثناء إلغاء المبيعة');
      }
    }
  };

  // فتح نافذة الخصم
  const handleOpenDiscountModal = (itemId: string) => {
    setSelectedItemId(itemId);
    setDiscountType('percentage');
    setDiscountValue(0);
    setShowDiscountModal(true);
  };

  // فتح نافذة الدفع
  const handleOpenPaymentModal = () => {
    if (!currentSale) return;

    setPaymentAmount(currentSale.amountDue);
    setShowPaymentModal(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="mr-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-bold mb-1">نقاط البيع</h1>
          <p className="text-sm text-gray-500">Point of Sale (POS)</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => router.push('/dashboard/pos/sales')}
            className="px-4 py-2 bg-gray-600 text-white rounded-md text-sm"
          >
            المبيعات | Sales
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* قائمة المنتجات */}
        <div className="lg:col-span-2">
          <div className="mb-4 flex flex-wrap gap-2">
            <div className="flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="بحث عن منتج..."
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
            <div className="w-40">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="all">جميع الفئات | All</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
            {filteredProducts.map(product => (
              <div
                key={product.id}
                className={`p-2 border rounded-md cursor-pointer transition-colors ${isProductInStock(product.id)
                    ? 'hover:bg-blue-50 border-gray-300'
                    : 'bg-gray-100 border-gray-300 opacity-60'
                  }`}
                onClick={() => isProductInStock(product.id) && handleAddProduct(product.id)}
              >
                {posSettings.showProductImages && product.images && product.images.length > 0 && (
                  <div className="h-20 flex items-center justify-center mb-2">
                    <img
                      src={product.images[0]}
                      alt={product.name}
                      className="max-h-full max-w-full object-contain"
                    />
                  </div>
                )}
                <div className="text-sm font-medium">{product.name}</div>
                <div className="text-xs text-gray-500">{product.sku}</div>
                <div className="mt-1 flex justify-between items-center">
                  <span className="font-bold">{formatCurrency(product.price)}</span>
                  {!isProductInStock(product.id) && (
                    <span className="text-xs text-red-600">نفذ من المخزون</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* تفاصيل المبيعة */}
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          {currentSale && (
            <>
              <div className="mb-4">
                <h2 className="text-lg font-semibold">
                  <span>المبيعة #{currentSale.saleNumber}</span>
                </h2>
                <p className="text-sm text-gray-500">
                  {new Date(currentSale.createdAt).toLocaleDateString('ar-AE')}
                </p>
              </div>

              <div className="mb-4 max-h-80 overflow-y-auto">
                {currentSale.items.length === 0 ? (
                  <div className="text-center text-gray-500 py-4">
                    <span>لا توجد منتجات في المبيعة</span>
                    <br />
                    <span className="text-sm">No products in sale</span>
                  </div>
                ) : (
                  <table className="w-full text-sm">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="p-2 text-right">المنتج</th>
                        <th className="p-2 text-center">الكمية</th>
                        <th className="p-2 text-left">السعر</th>
                        <th className="p-2 text-center">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {currentSale.items.map(item => (
                        <tr key={item.id} className="border-b">
                          <td className="p-2">
                            <div className="font-medium">{item.name}</div>
                            {item.discountAmount > 0 && (
                              <div className="text-xs text-green-600">
                                خصم: {item.discountType === 'percentage' ? `${item.discountValue}%` : formatCurrency(item.discountAmount)}
                              </div>
                            )}
                          </td>
                          <td className="p-2 text-center">
                            <div className="flex items-center justify-center">
                              <button
                                onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                                className="w-6 h-6 bg-gray-200 rounded-full"
                              >
                                -
                              </button>
                              <span className="mx-2">{item.quantity}</span>
                              <button
                                onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                                className="w-6 h-6 bg-gray-200 rounded-full"
                              >
                                +
                              </button>
                            </div>
                          </td>
                          <td className="p-2 text-left">
                            {formatCurrency(item.subtotal)}
                          </td>
                          <td className="p-2 text-center">
                            <div className="flex justify-center space-x-1 space-x-reverse">
                              {posSettings.allowDiscounts && (
                                <button
                                  onClick={() => handleOpenDiscountModal(item.id)}
                                  className="p-1 text-blue-600 hover:text-blue-800"
                                  title="خصم"
                                >
                                  %
                                </button>
                              )}
                              <button
                                onClick={() => handleRemoveItem(item.id)}
                                className="p-1 text-red-600 hover:text-red-800"
                                title="حذف"
                              >
                                ×
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between py-1">
                  <span>المجموع الفرعي:</span>
                  <span>{formatCurrency(currentSale.subtotal)}</span>
                </div>
                {currentSale.taxSettings.enabled && (
                  <div className="flex justify-between py-1">
                    <span>
                      {currentSale.taxSettings.inclusive
                        ? `ضريبة القيمة المضافة (${currentSale.taxSettings.rate}%) (مضمنة):`
                        : `ضريبة القيمة المضافة (${currentSale.taxSettings.rate}%):`}
                    </span>
                    <span>{formatCurrency(currentSale.taxAmount)}</span>
                  </div>
                )}
                {currentSale.discountAmount > 0 && (
                  <div className="flex justify-between py-1 text-green-600">
                    <span>الخصم:</span>
                    <span>-{formatCurrency(currentSale.discountAmount)}</span>
                  </div>
                )}
                <div className="flex justify-between py-1 font-bold text-lg">
                  <span>المجموع:</span>
                  <span>{formatCurrency(currentSale.total)}</span>
                </div>

                {currentSale.payments.length > 0 && (
                  <>
                    <div className="flex justify-between py-1">
                      <span>المدفوع:</span>
                      <span>{formatCurrency(currentSale.amountPaid)}</span>
                    </div>
                    <div className="flex justify-between py-1 font-bold">
                      <span>المتبقي:</span>
                      <span>{formatCurrency(currentSale.amountDue)}</span>
                    </div>
                  </>
                )}
              </div>

              <div className="mt-4 grid grid-cols-2 gap-2">
                <button
                  onClick={handleOpenPaymentModal}
                  disabled={currentSale.items.length === 0}
                  className={`py-2 px-4 rounded-md ${currentSale.items.length === 0
                      ? 'bg-gray-300 text-gray-500'
                      : 'bg-primary text-white'
                    }`}
                >
                  دفع | Payment
                </button>
                <button
                  onClick={handleCancelSale}
                  className="py-2 px-4 bg-red-600 text-white rounded-md"
                >
                  إلغاء | Cancel
                </button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* نافذة الدفع */}
      {showPaymentModal && currentSale && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">
              <span>إضافة دفعة</span>
              <span className="text-xs text-gray-500 mr-1">Add Payment</span>
            </h2>

            <div className="mb-4">
              <label htmlFor="paymentAmount" className="block mb-2 text-sm font-medium">
                <span>المبلغ</span>
                <span className="text-xs text-gray-500 mr-1">Amount</span>
              </label>
              <input
                id="paymentAmount"
                type="number"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                className="w-full p-2 border border-gray-300 rounded-md"
                min="0"
                step="0.01"
              />
              <div className="mt-1 text-sm text-gray-500">
                <span>المجموع: {formatCurrency(currentSale.total)}</span>
                <br />
                <span>المتبقي: {formatCurrency(currentSale.amountDue)}</span>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="paymentMethod" className="block mb-2 text-sm font-medium">
                <span>طريقة الدفع</span>
                <span className="text-xs text-gray-500 mr-1">Payment Method</span>
              </label>
              <select
                id="paymentMethod"
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="cash">نقدي | Cash</option>
                <option value="card">بطاقة | Card</option>
                <option value="bank_transfer">تحويل بنكي | Bank Transfer</option>
                <option value="check">شيك | Check</option>
                <option value="credit">آجل | Credit</option>
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="paymentReference" className="block mb-2 text-sm font-medium">
                <span>المرجع (اختياري)</span>
                <span className="text-xs text-gray-500 mr-1">Reference</span>
              </label>
              <input
                id="paymentReference"
                type="text"
                value={paymentReference}
                onChange={(e) => setPaymentReference(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="رقم البطاقة، رقم التحويل، إلخ"
              />
            </div>

            <div className="flex justify-end space-x-2 space-x-reverse">
              <button
                onClick={() => setShowPaymentModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                إلغاء | Cancel
              </button>
              <button
                onClick={handleAddPayment}
                disabled={paymentAmount <= 0}
                className={`px-4 py-2 rounded-md ${paymentAmount <= 0
                    ? 'bg-gray-300 text-gray-500'
                    : 'bg-primary text-white'
                  }`}
              >
                إضافة | Add
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة الخصم */}
      {showDiscountModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">
              <span>إضافة خصم</span>
              <span className="text-xs text-gray-500 mr-1">Add Discount</span>
            </h2>

            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                <span>نوع الخصم</span>
                <span className="text-xs text-gray-500 mr-1">Discount Type</span>
              </label>
              <div className="flex space-x-4 space-x-reverse">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="percentage"
                    checked={discountType === 'percentage'}
                    onChange={() => setDiscountType('percentage')}
                    className="mr-2"
                  />
                  <span>نسبة مئوية | Percentage</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="fixed"
                    checked={discountType === 'fixed'}
                    onChange={() => setDiscountType('fixed')}
                    className="mr-2"
                  />
                  <span>مبلغ ثابت | Fixed</span>
                </label>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="discountValue" className="block mb-2 text-sm font-medium">
                <span>قيمة الخصم</span>
                <span className="text-xs text-gray-500 mr-1">
                  {discountType === 'percentage' ? 'Percentage (%)' : 'Amount'}
                </span>
              </label>
              <input
                id="discountValue"
                type="number"
                value={discountValue}
                onChange={(e) => setDiscountValue(parseFloat(e.target.value) || 0)}
                className="w-full p-2 border border-gray-300 rounded-md"
                min="0"
                step={discountType === 'percentage' ? '1' : '0.01'}
                max={discountType === 'percentage' ? '100' : undefined}
              />
            </div>

            <div className="flex justify-end space-x-2 space-x-reverse">
              <button
                onClick={() => setShowDiscountModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                إلغاء | Cancel
              </button>
              <button
                onClick={handleApplyDiscount}
                disabled={discountValue <= 0}
                className={`px-4 py-2 rounded-md ${discountValue <= 0
                    ? 'bg-gray-300 text-gray-500'
                    : 'bg-primary text-white'
                  }`}
              >
                تطبيق | Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
