'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Sale, SaleStatus } from '@/types/pos';
import { getSales, refundSale } from '@/lib/pos-service';
import { formatCurrency } from '@/lib/utils';

export default function SalesPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [sales, setSales] = useState<Sale[]>([]);
  const [filteredSales, setFilteredSales] = useState<Sale[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');

  // تحميل البيانات
  useEffect(() => {
    const loadData = () => {
      setLoading(true);
      try {
        const salesData = getSales();
        setSales(salesData);

        // تعيين التاريخ الافتراضي (آخر 30 يوم)
        if (!dateFrom) {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          setDateFrom(thirtyDaysAgo.toISOString().split('T')[0]);
        }

        if (!dateTo) {
          const today = new Date();
          setDateTo(today.toISOString().split('T')[0]);
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات المبيعات:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dateFrom, dateTo]);

  // تصفية المبيعات
  useEffect(() => {
    const filterSales = () => {
      let filtered = [...sales];

      // تصفية حسب الحالة
      if (selectedStatus !== 'all') {
        filtered = filtered.filter(sale => sale.status === selectedStatus);
      }

      // تصفية حسب التاريخ
      if (dateFrom) {
        const fromDate = new Date(dateFrom);
        fromDate.setHours(0, 0, 0, 0);
        filtered = filtered.filter(sale => new Date(sale.createdAt) >= fromDate);
      }

      if (dateTo) {
        const toDate = new Date(dateTo);
        toDate.setHours(23, 59, 59, 999);
        filtered = filtered.filter(sale => new Date(sale.createdAt) <= toDate);
      }

      // تصفية حسب البحث
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(sale =>
          sale.saleNumber.toLowerCase().includes(query) ||
          (sale.customerName && sale.customerName.toLowerCase().includes(query))
        );
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      setFilteredSales(filtered);
    };

    filterSales();
  }, [sales, selectedStatus, dateFrom, dateTo, searchQuery]);

  // الحصول على نص حالة المبيعة
  const getSaleStatusText = (status: SaleStatus): string => {
    switch (status) {
      case 'draft':
        return 'مسودة | Draft';
      case 'completed':
        return 'مكتملة | Completed';
      case 'refunded':
        return 'مسترجعة | Refunded';
      case 'partially_refunded':
        return 'مسترجعة جزئياً | Partially Refunded';
      case 'cancelled':
        return 'ملغاة | Cancelled';
      default:
        return status;
    }
  };

  // الحصول على لون حالة المبيعة
  const getSaleStatusColor = (status: SaleStatus): string => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'refunded':
        return 'bg-red-100 text-red-800';
      case 'partially_refunded':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // استرجاع المبيعة
  const handleRefundSale = (saleId: string) => {
    if (confirm('هل أنت متأكد من استرجاع هذه المبيعة؟')) {
      try {
        refundSale(saleId, true);

        // تحديث البيانات
        const updatedSales = getSales();
        setSales(updatedSales);

        alert('تم استرجاع المبيعة بنجاح');
      } catch (error) {
        console.error('خطأ في استرجاع المبيعة:', error);
        alert('حدث خطأ أثناء استرجاع المبيعة');
      }
    }
  };

  // حساب إجمالي المبيعات المصفاة
  const calculateTotalSales = (): number => {
    return filteredSales
      .filter(sale => sale.status === 'completed')
      .reduce((total, sale) => total + sale.total, 0);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="mr-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">المبيعات</h1>
          <p className="text-sm text-gray-500">Sales</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => router.push('/dashboard/pos')}
            className="px-4 py-2 bg-primary text-white rounded-md text-sm"
          >
            نقاط البيع | POS
          </button>
        </div>
      </div>

      {/* ملخص المبيعات */}
      <div className="mb-6 bg-gray-50 border border-gray-200 rounded-md p-4">
        <h2 className="text-lg font-semibold mb-2">
          <span>ملخص المبيعات</span>
          <span className="text-xs text-gray-500 mr-1">Sales Summary</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-md border border-gray-200">
            <div className="text-sm text-gray-500">إجمالي المبيعات</div>
            <div className="text-2xl font-bold">{formatCurrency(calculateTotalSales())}</div>
          </div>
          <div className="bg-white p-4 rounded-md border border-gray-200">
            <div className="text-sm text-gray-500">عدد المبيعات</div>
            <div className="text-2xl font-bold">
              {filteredSales.filter(sale => sale.status === 'completed').length}
            </div>
          </div>
          <div className="bg-white p-4 rounded-md border border-gray-200">
            <div className="text-sm text-gray-500">متوسط قيمة المبيعة</div>
            <div className="text-2xl font-bold">
              {formatCurrency(
                filteredSales.filter(sale => sale.status === 'completed').length > 0
                  ? calculateTotalSales() / filteredSales.filter(sale => sale.status === 'completed').length
                  : 0
              )}
            </div>
          </div>
        </div>
      </div>

      {/* أدوات التصفية */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label htmlFor="status" className="block mb-2 text-sm font-medium">
            <span>الحالة</span>
            <span className="text-xs text-gray-500 mr-1">Status</span>
          </label>
          <select
            id="status"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="all">الكل | All</option>
            <option value="completed">مكتملة | Completed</option>
            <option value="refunded">مسترجعة | Refunded</option>
            <option value="partially_refunded">مسترجعة جزئياً | Partially Refunded</option>
            <option value="cancelled">ملغاة | Cancelled</option>
            <option value="draft">مسودة | Draft</option>
          </select>
        </div>
        <div>
          <label htmlFor="dateFrom" className="block mb-2 text-sm font-medium">
            <span>من تاريخ</span>
            <span className="text-xs text-gray-500 mr-1">From Date</span>
          </label>
          <input
            id="dateFrom"
            type="date"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
        <div>
          <label htmlFor="dateTo" className="block mb-2 text-sm font-medium">
            <span>إلى تاريخ</span>
            <span className="text-xs text-gray-500 mr-1">To Date</span>
          </label>
          <input
            id="dateTo"
            type="date"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
        <div>
          <label htmlFor="search" className="block mb-2 text-sm font-medium">
            <span>بحث</span>
            <span className="text-xs text-gray-500 mr-1">Search</span>
          </label>
          <input
            id="search"
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="رقم المبيعة، اسم العميل..."
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
      </div>

      {/* جدول المبيعات */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-2 text-right border">
                <span>رقم المبيعة</span>
                <span className="text-xs text-gray-500 mr-1">Sale #</span>
              </th>
              <th className="p-2 text-right border">
                <span>التاريخ</span>
                <span className="text-xs text-gray-500 mr-1">Date</span>
              </th>
              <th className="p-2 text-right border">
                <span>العميل</span>
                <span className="text-xs text-gray-500 mr-1">Customer</span>
              </th>
              <th className="p-2 text-right border">
                <span>المنتجات</span>
                <span className="text-xs text-gray-500 mr-1">Items</span>
              </th>
              <th className="p-2 text-right border">
                <span>المجموع</span>
                <span className="text-xs text-gray-500 mr-1">Total</span>
              </th>
              <th className="p-2 text-right border">
                <span>الحالة</span>
                <span className="text-xs text-gray-500 mr-1">Status</span>
              </th>
              <th className="p-2 text-center border">
                <span>الإجراءات</span>
                <span className="text-xs text-gray-500 mr-1">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredSales.length === 0 ? (
              <tr>
                <td colSpan={7} className="p-4 text-center text-gray-500">
                  <span>لا توجد مبيعات</span>
                  <br />
                  <span className="text-sm">No sales</span>
                </td>
              </tr>
            ) : (
              filteredSales.map(sale => (
                <tr key={sale.id} className="border-b">
                  <td className="p-2 border">
                    <div className="font-medium">{sale.saleNumber}</div>
                  </td>
                  <td className="p-2 border">
                    <div>{new Date(sale.createdAt).toLocaleDateString('ar-AE')}</div>
                    <div className="text-xs text-gray-500">
                      {new Date(sale.createdAt).toLocaleTimeString('ar-AE')}
                    </div>
                  </td>
                  <td className="p-2 border">
                    {sale.customerName || 'عميل عام | Walk-in Customer'}
                  </td>
                  <td className="p-2 border">
                    <div>{sale.items.length} منتج</div>
                    <div className="text-xs text-gray-500">
                      {sale.items.map(item => item.name).slice(0, 2).join(', ')}
                      {sale.items.length > 2 && '...'}
                    </div>
                  </td>
                  <td className="p-2 border font-medium">
                    {formatCurrency(sale.total, sale.currency)}
                  </td>
                  <td className="p-2 border">
                    <span className={`px-2 py-1 rounded-full text-xs ${getSaleStatusColor(sale.status)}`}>
                      {getSaleStatusText(sale.status)}
                    </span>
                  </td>
                  <td className="p-2 border text-center">
                    <div className="flex justify-center space-x-1 space-x-reverse">
                      <button
                        onClick={() => router.push(`/dashboard/pos/receipt/${sale.id}`)}
                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs"
                      >
                        عرض | View
                      </button>
                      {sale.status === 'completed' && (
                        <button
                          onClick={() => handleRefundSale(sale.id)}
                          className="px-2 py-1 bg-red-100 text-red-800 rounded-md text-xs"
                        >
                          استرجاع | Refund
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
