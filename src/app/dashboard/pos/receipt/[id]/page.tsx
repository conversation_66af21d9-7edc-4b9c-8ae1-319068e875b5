'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Sale } from '@/types/pos';
import { getSaleById, getPOSSettings } from '@/lib/pos-service';
import { formatCurrency } from '@/lib/utils';
import { getCompanyInfo } from '@/lib/settings';

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [sale, setSale] = useState<Sale | null>(null);
  const [posSettings, setPosSettings] = useState(getPOSSettings());
  const [companyInfo, setCompanyInfo] = useState(getCompanyInfo());

  // تحميل البيانات
  useEffect(() => {
    const loadData = () => {
      setLoading(true);
      try {
        const saleData = getSaleById(resolvedParams.id);
        if (!saleData) {
          alert('المبيعة غير موجودة');
          router.push('/dashboard/pos/sales');
          return;
        }

        setSale(saleData);
        setPosSettings(getPOSSettings());
        setCompanyInfo(getCompanyInfo());
      } catch (error) {
        console.error('خطأ في تحميل بيانات المبيعة:', error);
        alert('حدث خطأ أثناء تحميل بيانات المبيعة');
        router.push('/dashboard/pos/sales');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [resolvedParams.id, router]);

  // طباعة الإيصال
  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <span className="mr-3">جاري التحميل... | Loading...</span>
      </div>
    );
  }

  if (!sale) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600">
          <p>المبيعة غير موجودة</p>
          <p className="text-sm">Sale not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6 print:hidden">
        <div>
          <h1 className="text-2xl font-bold mb-1">إيصال المبيعة</h1>
          <p className="text-sm text-gray-500">Sale Receipt</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={handlePrint}
            className="px-4 py-2 bg-primary text-white rounded-md text-sm"
          >
            طباعة | Print
          </button>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-gray-600 text-white rounded-md text-sm"
          >
            رجوع | Back
          </button>
        </div>
      </div>

      <div className="max-w-2xl mx-auto border border-gray-200 rounded-md p-6 print:border-0 print:p-0">
        {/* رأس الإيصال */}
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold">{companyInfo.name}</h2>
          <h3 className="text-lg">{companyInfo.nameEn}</h3>
          <p className="text-sm">{companyInfo.address}</p>
          <p className="text-sm">{companyInfo.phone}</p>
          <p className="text-sm">{companyInfo.email}</p>
          <p className="text-sm">الرقم الضريبي: {companyInfo.taxNumber}</p>
          <div className="mt-4 text-lg font-bold border-t border-b py-2">
            {posSettings.receiptHeader}
          </div>
        </div>

        {/* معلومات المبيعة */}
        <div className="mb-6">
          <div className="flex justify-between mb-2">
            <div>
              <span className="font-medium">رقم المبيعة:</span>
              <span className="mr-1">{sale.saleNumber}</span>
            </div>
            <div>
              <span className="font-medium">التاريخ:</span>
              <span className="mr-1">{new Date(sale.createdAt).toLocaleDateString('ar-AE')}</span>
            </div>
          </div>
          <div className="flex justify-between mb-2">
            <div>
              <span className="font-medium">العميل:</span>
              <span className="mr-1">{sale.customerName || 'عميل عام | Walk-in Customer'}</span>
            </div>
            <div>
              <span className="font-medium">الوقت:</span>
              <span className="mr-1">{new Date(sale.createdAt).toLocaleTimeString('ar-AE')}</span>
            </div>
          </div>
        </div>

        {/* جدول المنتجات */}
        <div className="mb-6">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-50">
                <th className="p-2 text-right border">
                  <span>المنتج</span>
                  <span className="text-xs text-gray-500 mr-1">Item</span>
                </th>
                <th className="p-2 text-center border">
                  <span>الكمية</span>
                  <span className="text-xs text-gray-500 mr-1">Qty</span>
                </th>
                <th className="p-2 text-left border">
                  <span>السعر</span>
                  <span className="text-xs text-gray-500 mr-1">Price</span>
                </th>
                <th className="p-2 text-left border">
                  <span>المجموع</span>
                  <span className="text-xs text-gray-500 mr-1">Total</span>
                </th>
              </tr>
            </thead>
            <tbody>
              {sale.items.map(item => (
                <tr key={item.id} className="border-b">
                  <td className="p-2 border">
                    <div className="font-medium">{item.name}</div>
                    {item.discountAmount > 0 && (
                      <div className="text-xs text-gray-600">
                        خصم: {item.discountType === 'percentage' ? `${item.discountValue}%` : formatCurrency(item.discountAmount, sale.currency)}
                      </div>
                    )}
                  </td>
                  <td className="p-2 border text-center">
                    {item.quantity}
                  </td>
                  <td className="p-2 border text-left">
                    {formatCurrency(item.price, sale.currency)}
                  </td>
                  <td className="p-2 border text-left">
                    {formatCurrency(item.subtotal, sale.currency)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* ملخص المبيعة */}
        <div className="mb-6">
          <div className="flex justify-between py-1">
            <span>المجموع الفرعي:</span>
            <span>{formatCurrency(sale.subtotal, sale.currency)}</span>
          </div>
          {sale.taxSettings.enabled && (
            <div className="flex justify-between py-1">
              <span>
                {sale.taxSettings.inclusive
                  ? `ضريبة القيمة المضافة (${sale.taxSettings.rate}%) (مضمنة):`
                  : `ضريبة القيمة المضافة (${sale.taxSettings.rate}%):`}
              </span>
              <span>{formatCurrency(sale.taxAmount, sale.currency)}</span>
            </div>
          )}
          {sale.discountAmount > 0 && (
            <div className="flex justify-between py-1">
              <span>الخصم:</span>
              <span>-{formatCurrency(sale.discountAmount, sale.currency)}</span>
            </div>
          )}
          <div className="flex justify-between py-1 font-bold text-lg border-t mt-2 pt-2">
            <span>المجموع:</span>
            <span>{formatCurrency(sale.total, sale.currency)}</span>
          </div>
        </div>

        {/* معلومات الدفع */}
        {sale.payments.length > 0 && (
          <div className="mb-6">
            <h3 className="font-bold mb-2">طرق الدفع:</h3>
            <table className="w-full">
              <tbody>
                {sale.payments.map(payment => (
                  <tr key={payment.id}>
                    <td className="py-1">
                      {payment.method === 'cash' && 'نقدي | Cash'}
                      {payment.method === 'card' && 'بطاقة | Card'}
                      {payment.method === 'bank_transfer' && 'تحويل بنكي | Bank Transfer'}
                      {payment.method === 'check' && 'شيك | Check'}
                      {payment.method === 'credit' && 'آجل | Credit'}
                      {payment.method === 'multiple' && 'متعدد | Multiple'}
                    </td>
                    <td className="py-1 text-left">
                      {formatCurrency(payment.amount, sale.currency)}
                    </td>
                  </tr>
                ))}
                <tr className="font-bold">
                  <td className="py-1">المدفوع:</td>
                  <td className="py-1 text-left">
                    {formatCurrency(sale.amountPaid, sale.currency)}
                  </td>
                </tr>
                {sale.amountDue > 0 && (
                  <tr className="font-bold">
                    <td className="py-1">المتبقي:</td>
                    <td className="py-1 text-left">
                      {formatCurrency(sale.amountDue, sale.currency)}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* تذييل الإيصال */}
        <div className="text-center mt-8 pt-4 border-t">
          <p className="mb-2">{posSettings.receiptFooter}</p>
          <p className="text-sm">تم إنشاء هذا الإيصال بواسطة نظام أمين بلس | This receipt was generated by Amin Plus | أمين بلس System</p>
        </div>
      </div>
    </div>
  );
}
