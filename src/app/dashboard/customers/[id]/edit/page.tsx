'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  taxNumber?: string;
  createdAt: Date;
}

export default function EditCustomerPage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    taxNumber: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // جلب بيانات العميل من localStorage
    const fetchCustomer = () => {
      try {
        const customersData = localStorage.getItem('customers');
        if (customersData) {
          const customers: Customer[] = JSON.parse(customersData);
          const foundCustomer = customers.find(c => c.id === params.id);

          if (foundCustomer) {
            // تعبئة النموذج ببيانات العميل
            setFormData({
              name: foundCustomer.name,
              email: foundCustomer.email,
              phone: foundCustomer.phone,
              address: foundCustomer.address || '',
              taxNumber: foundCustomer.taxNumber || '',
            });
          } else {
            // إذا لم يتم العثور على العميل
            if (window.showToast) {
              window.showToast('لم يتم العثور على العميل', 'error');
            }
            // الانتقال إلى صفحة العملاء بعد ثانية واحدة
            setTimeout(() => {
              router.push('/dashboard/customers');
            }, 1000);
          }
        }
      } catch (error) {
        console.error('خطأ في جلب بيانات العميل:', error);
        if (window.showToast) {
          window.showToast('حدث خطأ أثناء جلب بيانات العميل', 'error');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [params.id, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // إزالة رسالة الخطأ عند تغيير القيمة
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // التحقق من اسم العميل
    if (!formData.name.trim()) {
      newErrors.name = 'اسم العميل مطلوب';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'يجب أن يكون اسم العميل أكثر من حرفين';
    }

    // التحقق من البريد الإلكتروني (إذا تم إدخاله)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'يرجى إدخال بريد إلكتروني صحيح';
    }

    // التحقق من رقم الهاتف (إذا تم إدخاله)
    if (formData.phone && formData.phone.length < 8) {
      newErrors.phone = 'يجب أن يكون رقم الهاتف صحيحًا';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من صحة البيانات
    if (!validateForm()) {
      // عرض إشعار خطأ
      if (window.showToast) {
        window.showToast('يرجى تصحيح الأخطاء في النموذج', 'warning');
      }
      return;
    }

    setIsSubmitting(true);

    try {
      // جلب بيانات العملاء من localStorage
      const customersData = localStorage.getItem('customers');
      if (customersData) {
        const customers: Customer[] = JSON.parse(customersData);

        // البحث عن العميل وتحديث بياناته
        const updatedCustomers = customers.map(customer => {
          if (customer.id === params.id) {
            return {
              ...customer,
              name: formData.name,
              email: formData.email,
              phone: formData.phone,
              address: formData.address,
              taxNumber: formData.taxNumber,
            };
          }
          return customer;
        });

        // حفظ البيانات المحدثة في localStorage
        localStorage.setItem('customers', JSON.stringify(updatedCustomers));

        // عرض إشعار نجاح
        if (window.showToast) {
          window.showToast('تم تحديث بيانات العميل بنجاح', 'success');
        }

        // الانتقال إلى صفحة تفاصيل العميل بعد ثانية واحدة
        setTimeout(() => {
          router.push(`/dashboard/customers/${params.id}`);
        }, 1000);
      }
    } catch (error) {
      console.error('خطأ في تحديث بيانات العميل:', error);

      // عرض إشعار خطأ
      if (window.showToast) {
        window.showToast('حدث خطأ أثناء تحديث بيانات العميل', 'error');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // العودة إلى صفحة تفاصيل العميل
  const handleCancel = () => {
    router.push(`/dashboard/customers/${params.id}`);
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <style dangerouslySetInnerHTML={{
          __html: `
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}} />
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: '1.5rem' }}>
        <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '0.25rem' }}>
          تعديل بيانات العميل
        </h1>
        <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
          Edit Customer
        </p>
      </div>

      <div style={{
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        padding: '1.5rem',
        marginBottom: '2rem'
      }}>
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '1.5rem' }}>
            <div style={{ marginBottom: '0.5rem' }}>
              <label htmlFor="name" style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'medium' }}>
                <span>اسم العميل</span> <span style={{ color: '#ef4444' }}>*</span>
                <span style={{ fontSize: '0.75rem', marginRight: '0.5rem', color: '#6b7280' }}>Customer Name</span>
              </label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: errors.name ? '1px solid #ef4444' : '1px solid #e5e7eb',
                  borderRadius: '0.375rem'
                }}
              />
              {errors.name && (
                <p style={{ color: '#ef4444', fontSize: '0.875rem', marginTop: '0.25rem' }}>{errors.name}</p>
              )}
            </div>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '1rem',
            marginBottom: '1.5rem'
          }}>
            <div>
              <label htmlFor="email" style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'medium' }}>
                <span>البريد الإلكتروني</span>
                <span style={{ fontSize: '0.75rem', marginRight: '0.5rem', color: '#6b7280' }}>Email</span>
              </label>
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: errors.email ? '1px solid #ef4444' : '1px solid #e5e7eb',
                  borderRadius: '0.375rem'
                }}
              />
              {errors.email && (
                <p style={{ color: '#ef4444', fontSize: '0.875rem', marginTop: '0.25rem' }}>{errors.email}</p>
              )}
            </div>

            <div>
              <label htmlFor="phone" style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'medium' }}>
                <span>رقم الهاتف</span>
                <span style={{ fontSize: '0.75rem', marginRight: '0.5rem', color: '#6b7280' }}>Phone</span>
              </label>
              <input
                id="phone"
                name="phone"
                type="text"
                value={formData.phone}
                onChange={handleChange}
                placeholder="مثال: 05xxxxxxxx"
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: errors.phone ? '1px solid #ef4444' : '1px solid #e5e7eb',
                  borderRadius: '0.375rem'
                }}
              />
              {errors.phone && (
                <p style={{ color: '#ef4444', fontSize: '0.875rem', marginTop: '0.25rem' }}>{errors.phone}</p>
              )}
            </div>

            <div>
              <label htmlFor="taxNumber" style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'medium' }}>
                <span>الرقم الضريبي</span>
                <span style={{ fontSize: '0.75rem', marginRight: '0.5rem', color: '#6b7280' }}>Tax ID</span>
              </label>
              <input
                id="taxNumber"
                name="taxNumber"
                type="text"
                value={formData.taxNumber}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #e5e7eb',
                  borderRadius: '0.375rem'
                }}
              />
            </div>
          </div>

          <div style={{ marginBottom: '1.5rem' }}>
            <label htmlFor="address" style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'medium' }}>
              <span>العنوان</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem', color: '#6b7280' }}>Address</span>
            </label>
            <textarea
              id="address"
              name="address"
              rows={3}
              value={formData.address}
              onChange={handleChange}
              placeholder="أدخل العنوان التفصيلي للعميل"
              style={{
                width: '100%',
                padding: '0.5rem',
                border: '1px solid #e5e7eb',
                borderRadius: '0.375rem'
              }}
            />
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '2rem' }}>
            <button
              type="button"
              onClick={handleCancel}
              style={{
                display: 'inline-block',
                padding: '0.5rem 1rem',
                backgroundColor: 'white',
                color: '#4b5563',
                borderRadius: '0.375rem',
                border: '1px solid #e5e7eb',
                cursor: 'pointer'
              }}
            >
              إلغاء | Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                display: 'inline-block',
                padding: '0.5rem 1rem',
                backgroundColor: '#2563eb',
                color: 'white',
                borderRadius: '0.375rem',
                border: 'none',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                opacity: isSubmitting ? 0.7 : 1
              }}
            >
              {isSubmitting ? 'جاري الحفظ... | Saving...' : 'حفظ التغييرات | Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
