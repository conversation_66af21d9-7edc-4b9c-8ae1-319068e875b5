'use client';

import { useState, useEffect, use } from 'react';
import { useParams, useRouter } from 'next/navigation';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  taxNumber?: string;
  createdAt: Date;
}

export default function CustomerDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // جلب بيانات العميل من localStorage
    const fetchCustomer = () => {
      try {
        const customersData = localStorage.getItem('customers');
        if (customersData) {
          const customers: Customer[] = JSON.parse(customersData);
          const foundCustomer = customers.find(c => c.id === resolvedParams.id);

          if (foundCustomer) {
            // تحويل التاريخ إلى كائن Date
            setCustomer({
              ...foundCustomer,
              createdAt: new Date(foundCustomer.createdAt)
            });
          } else {
            // إذا لم يتم العثور على العميل
            if (window.showToast) {
              window.showToast('لم يتم العثور على العميل', 'error');
            }
            // الانتقال إلى صفحة العملاء بعد ثانية واحدة
            setTimeout(() => {
              router.push('/dashboard/customers');
            }, 1000);
          }
        }
      } catch (error) {
        console.error('خطأ في جلب بيانات العميل:', error);
        if (window.showToast) {
          window.showToast('حدث خطأ أثناء جلب بيانات العميل', 'error');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [resolvedParams.id, router]);

  // تنسيق التاريخ
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-SA');
  };

  // العودة إلى صفحة العملاء
  const handleBack = () => {
    router.push('/dashboard/customers');
  };

  // الانتقال إلى صفحة تعديل العميل
  const handleEdit = () => {
    router.push(`/dashboard/customers/${resolvedParams.id}/edit`);
  };

  // حذف العميل
  const handleDelete = () => {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        const customersData = localStorage.getItem('customers');
        if (customersData) {
          const customers: Customer[] = JSON.parse(customersData);
          const updatedCustomers = customers.filter(c => c.id !== resolvedParams.id);

          // تحديث localStorage
          localStorage.setItem('customers', JSON.stringify(updatedCustomers));

          // عرض إشعار نجاح
          if (window.showToast) {
            window.showToast('تم حذف العميل بنجاح', 'success');
          }

          // الانتقال إلى صفحة العملاء بعد ثانية واحدة
          setTimeout(() => {
            router.push('/dashboard/customers');
          }, 1000);
        }
      } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        if (window.showToast) {
          window.showToast('حدث خطأ أثناء حذف العميل', 'error');
        }
      }
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <style dangerouslySetInnerHTML={{
          __html: `
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}} />
      </div>
    );
  }

  if (!customer) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem' }}>
        <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>
          لم يتم العثور على العميل
        </h2>
        <button
          onClick={handleBack}
          style={{
            display: 'inline-block',
            padding: '0.5rem 1rem',
            backgroundColor: '#3b82f6',
            color: 'white',
            borderRadius: '0.375rem',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          العودة إلى قائمة العملاء
        </button>
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '0.25rem' }}>
            تفاصيل العميل
          </h1>
          <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            Customer Details
          </p>
        </div>
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <button
            onClick={handleBack}
            style={{
              display: 'inline-block',
              padding: '0.5rem 1rem',
              backgroundColor: 'white',
              color: '#4b5563',
              borderRadius: '0.375rem',
              border: '1px solid #e5e7eb',
              cursor: 'pointer'
            }}
          >
            العودة | Back
          </button>
          <button
            onClick={handleEdit}
            style={{
              display: 'inline-block',
              padding: '0.5rem 1rem',
              backgroundColor: '#10b981',
              color: 'white',
              borderRadius: '0.375rem',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            تعديل | Edit
          </button>
          <button
            onClick={handleDelete}
            style={{
              display: 'inline-block',
              padding: '0.5rem 1rem',
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: '0.375rem',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            حذف | Delete
          </button>
        </div>
      </div>

      <div style={{
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        padding: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>
          <div>
            <h3 style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              <span>اسم العميل</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem' }}>Name</span>
            </h3>
            <p style={{ fontSize: '1rem', fontWeight: 'medium' }}>{customer.name}</p>
          </div>
          <div>
            <h3 style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              <span>البريد الإلكتروني</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem' }}>Email</span>
            </h3>
            <p style={{ fontSize: '1rem' }}>{customer.email || '-'}</p>
          </div>
          <div>
            <h3 style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              <span>رقم الهاتف</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem' }}>Phone</span>
            </h3>
            <p style={{ fontSize: '1rem' }}>{customer.phone || '-'}</p>
          </div>
          <div>
            <h3 style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              <span>الرقم الضريبي</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem' }}>Tax ID</span>
            </h3>
            <p style={{ fontSize: '1rem' }}>{customer.taxNumber || '-'}</p>
          </div>
          <div>
            <h3 style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              <span>تاريخ الإضافة</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem' }}>Date</span>
            </h3>
            <p style={{ fontSize: '1rem' }}>{formatDate(customer.createdAt)}</p>
          </div>
          <div>
            <h3 style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              <span>رقم العميل</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem' }}>ID</span>
            </h3>
            <p style={{ fontSize: '1rem' }}>{customer.id}</p>
          </div>
        </div>

        {customer.address && (
          <div style={{ marginTop: '1.5rem' }}>
            <h3 style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              <span>العنوان</span>
              <span style={{ fontSize: '0.75rem', marginRight: '0.5rem' }}>Address</span>
            </h3>
            <p style={{ fontSize: '1rem' }}>{customer.address}</p>
          </div>
        )}
      </div>

      <div style={{
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        padding: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{ marginBottom: '1rem' }}>
          <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '0.25rem' }}>
            الفواتير
          </h2>
          <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            Invoices
          </p>
        </div>
        <div style={{ textAlign: 'center', padding: '2rem', color: '#6b7280' }}>
          لا توجد فواتير لهذا العميل
          <br />
          <span style={{ fontSize: '0.875rem' }}>No invoices found for this customer</span>
        </div>
      </div>
    </div>
  );
}
