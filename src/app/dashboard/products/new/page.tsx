'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/hooks/use-translation';
import { useToast } from '@/hooks/use-toast';

interface ProductFormData {
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: string;
  cost: string;
  category: string;
  categoryEn: string;
  sku: string;
  unit: string;
  unitEn: string;
  taxRate: string;
  status: 'active' | 'inactive';
  trackInventory: boolean;
  currentStock: string;
  minStock: string;
  maxStock: string;
}

export default function NewProductPage() {
  const router = useRouter();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    nameEn: '',
    description: '',
    descriptionEn: '',
    price: '',
    cost: '',
    category: '',
    categoryEn: '',
    sku: '',
    unit: 'قطعة',
    unitEn: 'Unit',
    taxRate: '5',
    status: 'active',
    trackInventory: false,
    currentStock: '0',
    minStock: '0',
    maxStock: '0'
  });

  const [errors, setErrors] = useState<Partial<ProductFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<ProductFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'السعر مطلوب';
    } else if (isNaN(parseFloat(formData.price)) || parseFloat(formData.price) < 0) {
      newErrors.price = 'السعر يجب أن يكون رقم موجب';
    }

    if (formData.cost && (isNaN(parseFloat(formData.cost)) || parseFloat(formData.cost) < 0)) {
      newErrors.cost = 'التكلفة يجب أن تكون رقم موجب';
    }

    if (formData.taxRate && (isNaN(parseFloat(formData.taxRate)) || parseFloat(formData.taxRate) < 0)) {
      newErrors.taxRate = 'معدل الضريبة يجب أن يكون رقم موجب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price),
          cost: formData.cost ? parseFloat(formData.cost) : 0,
          taxRate: formData.taxRate ? parseFloat(formData.taxRate) : 0,
          currentStock: formData.trackInventory ? parseInt(formData.currentStock) : 0,
          minStock: formData.trackInventory ? parseInt(formData.minStock) : 0,
          maxStock: formData.trackInventory ? parseInt(formData.maxStock) : 0,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء إنشاء المنتج');
      }

      const newProduct = await response.json();
      
      toast({
        title: 'تم إنشاء المنتج بنجاح',
        description: `تم إنشاء المنتج ${newProduct.name} بنجاح`,
        variant: 'success'
      });

      router.push('/dashboard/products');
    } catch (error) {
      console.error('Error creating product:', error);
      toast({
        title: 'خطأ في إنشاء المنتج',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // إزالة الخطأ عند تعديل الحقل
    if (errors[field as keyof Partial<ProductFormData>]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6);
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const sku = `PROD-${timestamp}-${randomNum}`;
    handleInputChange('sku', sku);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">إضافة منتج جديد</h1>
          <p className="text-sm text-gray-500">Add New Product</p>
        </div>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          العودة / Back
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* معلومات المنتج الأساسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-2">
              اسم المنتج (عربي) <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="أدخل اسم المنتج"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              اسم المنتج (إنجليزي)
            </label>
            <input
              type="text"
              value={formData.nameEn}
              onChange={(e) => handleInputChange('nameEn', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter product name in English"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">
              وصف المنتج (عربي)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="أدخل وصف المنتج"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">
              وصف المنتج (إنجليزي)
            </label>
            <textarea
              value={formData.descriptionEn}
              onChange={(e) => handleInputChange('descriptionEn', e.target.value)}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter product description in English"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              السعر (درهم) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.price ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0.00"
            />
            {errors.price && <p className="text-red-500 text-sm mt-1">{errors.price}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              التكلفة (درهم)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.cost}
              onChange={(e) => handleInputChange('cost', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.cost ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0.00"
            />
            {errors.cost && <p className="text-red-500 text-sm mt-1">{errors.cost}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              الفئة (عربي)
            </label>
            <input
              type="text"
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="أدخل فئة المنتج"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              الفئة (إنجليزي)
            </label>
            <input
              type="text"
              value={formData.categoryEn}
              onChange={(e) => handleInputChange('categoryEn', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter product category in English"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              رمز المنتج (SKU)
            </label>
            <div className="flex space-x-2 rtl:space-x-reverse">
              <input
                type="text"
                value={formData.sku}
                onChange={(e) => handleInputChange('sku', e.target.value)}
                className="flex-1 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="PROD-001"
              />
              <button
                type="button"
                onClick={generateSKU}
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
              >
                توليد
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              الوحدة (عربي)
            </label>
            <select
              value={formData.unit}
              onChange={(e) => handleInputChange('unit', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="قطعة">قطعة</option>
              <option value="كيلو">كيلو</option>
              <option value="متر">متر</option>
              <option value="لتر">لتر</option>
              <option value="ساعة">ساعة</option>
              <option value="يوم">يوم</option>
              <option value="شهر">شهر</option>
              <option value="سنة">سنة</option>
              <option value="خدمة">خدمة</option>
              <option value="مشروع">مشروع</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              الوحدة (إنجليزي)
            </label>
            <select
              value={formData.unitEn}
              onChange={(e) => handleInputChange('unitEn', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="Unit">Unit</option>
              <option value="Kg">Kg</option>
              <option value="Meter">Meter</option>
              <option value="Liter">Liter</option>
              <option value="Hour">Hour</option>
              <option value="Day">Day</option>
              <option value="Month">Month</option>
              <option value="Year">Year</option>
              <option value="Service">Service</option>
              <option value="Project">Project</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              معدل الضريبة (%)
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              max="100"
              value={formData.taxRate}
              onChange={(e) => handleInputChange('taxRate', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.taxRate ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="5"
            />
            {errors.taxRate && <p className="text-red-500 text-sm mt-1">{errors.taxRate}</p>}
          </div>
        </div>

        {/* إدارة المخزون */}
        <div className="border-t pt-6">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="trackInventory"
              checked={formData.trackInventory}
              onChange={(e) => handleInputChange('trackInventory', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="trackInventory" className="mr-2 text-sm font-medium">
              تتبع المخزون
            </label>
          </div>

          {formData.trackInventory && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  المخزون الحالي
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.currentStock}
                  onChange={(e) => handleInputChange('currentStock', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  الحد الأدنى للمخزون
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.minStock}
                  onChange={(e) => handleInputChange('minStock', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  الحد الأقصى للمخزون
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.maxStock}
                  onChange={(e) => handleInputChange('maxStock', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>
            </div>
          )}
        </div>

        {/* حالة المنتج */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold mb-4">حالة المنتج</h3>
          <div>
            <label className="block text-sm font-medium mb-2">
              الحالة
            </label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value as 'active' | 'inactive')}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
            </select>
          </div>
        </div>

        {/* أزرار الحفظ */}
        <div className="flex justify-end space-x-4 rtl:space-x-reverse pt-6 border-t">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            إلغاء
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'جاري الحفظ...' : 'حفظ المنتج'}
          </button>
        </div>
      </form>
    </div>
  );
}
