'use client';

import { useState, useEffect, use } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';

interface Product {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  cost: number;
  category: string;
  categoryEn: string;
  sku: string;
  unit: string;
  unitEn: string;
  taxRate: number;
  status: 'active' | 'inactive';
  inventory: {
    trackInventory: boolean;
    currentStock: number;
    minStock: number;
    maxStock: number;
  };
  createdAt: string;
  updatedAt: string;
}

export default function ProductDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const response = await fetch(`/api/products/${resolvedParams.id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch product');
        }

        const productData = await response.json();
        setProduct(productData);
      } catch (error) {
        console.error('Error fetching product:', error);
        toast({
          title: 'خطأ في تحميل بيانات المنتج',
          description: 'حدث خطأ أثناء تحميل بيانات المنتج',
          variant: 'destructive'
        });
        router.push('/dashboard/products');
      } finally {
        setLoading(false);
      }
    };

    if (resolvedParams.id) {
      fetchProduct();
    }
  }, [resolvedParams.id, router, toast]);

  const handleDelete = async () => {
    if (!product) return;

    if (window.confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`)) {
      try {
        const response = await fetch(`/api/products/${product.id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete product');
        }

        toast({
          title: 'تم حذف المنتج بنجاح',
          description: `تم حذف المنتج "${product.name}" بنجاح`,
          variant: 'success'
        });

        router.push('/dashboard/products');
      } catch (error) {
        console.error('Error deleting product:', error);
        toast({
          title: 'خطأ في حذف المنتج',
          description: 'حدث خطأ أثناء حذف المنتج',
          variant: 'destructive'
        });
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-AE');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="mr-3">جاري تحميل بيانات المنتج...</span>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-bold text-gray-600">المنتج غير موجود</h2>
        <p className="text-gray-500">Product not found</p>
        <Link
          href="/dashboard/products"
          className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          العودة إلى قائمة المنتجات
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">{product.name}</h1>
          <p className="text-sm text-gray-500">{product.nameEn}</p>
        </div>
        <div className="flex space-x-4 rtl:space-x-reverse">
          <Link
            href={`/dashboard/products/${product.id}/edit`}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            تعديل / Edit
          </Link>
          <button
            onClick={handleDelete}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            حذف / Delete
          </button>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            العودة / Back
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* معلومات أساسية */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-800">معلومات أساسية</h3>
            <div className="space-y-3">
              <div>
                <span className="font-medium">رمز المنتج:</span>
                <span className="mr-2">{product.sku}</span>
              </div>
              <div>
                <span className="font-medium">الفئة:</span>
                <span className="mr-2">{product.category || '-'}</span>
                {product.categoryEn && (
                  <span className="text-gray-500">({product.categoryEn})</span>
                )}
              </div>
              <div>
                <span className="font-medium">الوحدة:</span>
                <span className="mr-2">{product.unit}</span>
                {product.unitEn && (
                  <span className="text-gray-500">({product.unitEn})</span>
                )}
              </div>
              <div>
                <span className="font-medium">الحالة:</span>
                <span className={`mr-2 px-2 py-1 rounded text-sm ${
                  product.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {product.status === 'active' ? 'نشط' : 'غير نشط'}
                </span>
              </div>
            </div>
          </div>

          {/* الوصف */}
          {(product.description || product.descriptionEn) && (
            <div>
              <h3 className="text-lg font-semibold mb-4 text-blue-800">الوصف</h3>
              <div className="space-y-2">
                {product.description && (
                  <p className="text-gray-700">{product.description}</p>
                )}
                {product.descriptionEn && (
                  <p className="text-gray-600 text-sm">{product.descriptionEn}</p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* معلومات مالية */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-800">معلومات مالية</h3>
            <div className="space-y-3">
              <div>
                <span className="font-medium">سعر البيع:</span>
                <span className="mr-2 text-lg font-bold text-green-600">
                  {formatCurrency(product.price)}
                </span>
              </div>
              <div>
                <span className="font-medium">التكلفة:</span>
                <span className="mr-2">{formatCurrency(product.cost)}</span>
              </div>
              <div>
                <span className="font-medium">الربح:</span>
                <span className="mr-2 text-blue-600 font-medium">
                  {formatCurrency(product.price - product.cost)}
                </span>
              </div>
              <div>
                <span className="font-medium">معدل الضريبة:</span>
                <span className="mr-2">{product.taxRate}%</span>
              </div>
            </div>
          </div>

          {/* معلومات المخزون */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-800">معلومات المخزون</h3>
            <div className="space-y-3">
              <div>
                <span className="font-medium">تتبع المخزون:</span>
                <span className={`mr-2 px-2 py-1 rounded text-sm ${
                  product.inventory.trackInventory
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {product.inventory.trackInventory ? 'نعم' : 'لا'}
                </span>
              </div>
              {product.inventory.trackInventory && (
                <>
                  <div>
                    <span className="font-medium">المخزون الحالي:</span>
                    <span className={`mr-2 font-bold ${
                      product.inventory.currentStock <= product.inventory.minStock
                        ? 'text-red-600'
                        : product.inventory.currentStock <= product.inventory.minStock * 2
                        ? 'text-yellow-600'
                        : 'text-green-600'
                    }`}>
                      {product.inventory.currentStock}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">الحد الأدنى:</span>
                    <span className="mr-2">{product.inventory.minStock}</span>
                  </div>
                  <div>
                    <span className="font-medium">الحد الأقصى:</span>
                    <span className="mr-2">{product.inventory.maxStock}</span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* معلومات النظام */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-800">معلومات النظام</h3>
            <div className="space-y-3">
              <div>
                <span className="font-medium">تاريخ الإنشاء:</span>
                <span className="mr-2">{formatDate(product.createdAt)}</span>
              </div>
              <div>
                <span className="font-medium">آخر تحديث:</span>
                <span className="mr-2">{formatDate(product.updatedAt)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
