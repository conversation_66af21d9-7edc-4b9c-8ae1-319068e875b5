'use client';

import { useState, useEffect, use } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';

interface ProductFormData {
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: string;
  cost: string;
  category: string;
  categoryEn: string;
  sku: string;
  unit: string;
  unitEn: string;
  taxRate: string;
  status: 'active' | 'inactive';
  trackInventory: boolean;
  currentStock: string;
  minStock: string;
  maxStock: string;
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    nameEn: '',
    description: '',
    descriptionEn: '',
    price: '',
    cost: '',
    category: '',
    categoryEn: '',
    sku: '',
    unit: 'قطعة',
    unitEn: 'Unit',
    taxRate: '5',
    status: 'active',
    trackInventory: false,
    currentStock: '0',
    minStock: '0',
    maxStock: '0'
  });

  const [errors, setErrors] = useState<Partial<ProductFormData>>({});

  // تحميل بيانات المنتج
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const response = await fetch(`/api/products/${resolvedParams.id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch product');
        }

        const product = await response.json();
        setFormData({
          name: product.name || '',
          nameEn: product.nameEn || '',
          description: product.description || '',
          descriptionEn: product.descriptionEn || '',
          price: product.price?.toString() || '',
          cost: product.cost?.toString() || '',
          category: product.category || '',
          categoryEn: product.categoryEn || '',
          sku: product.sku || '',
          unit: product.unit || 'قطعة',
          unitEn: product.unitEn || 'Unit',
          taxRate: product.taxRate?.toString() || '5',
          status: product.status || 'active',
          trackInventory: product.inventory?.trackInventory || false,
          currentStock: product.inventory?.currentStock?.toString() || '0',
          minStock: product.inventory?.minStock?.toString() || '0',
          maxStock: product.inventory?.maxStock?.toString() || '0'
        });
      } catch (error) {
        console.error('Error fetching product:', error);
        toast({
          title: 'خطأ في تحميل بيانات المنتج',
          description: 'حدث خطأ أثناء تحميل بيانات المنتج',
          variant: 'destructive'
        });
        router.push('/dashboard/products');
      } finally {
        setFetchLoading(false);
      }
    };

    if (resolvedParams.id) {
      fetchProduct();
    }
  }, [resolvedParams.id, router, toast]);

  const validateForm = (): boolean => {
    const newErrors: Partial<ProductFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'السعر مطلوب';
    } else if (isNaN(parseFloat(formData.price)) || parseFloat(formData.price) < 0) {
      newErrors.price = 'السعر يجب أن يكون رقم موجب';
    }

    if (formData.cost && (isNaN(parseFloat(formData.cost)) || parseFloat(formData.cost) < 0)) {
      newErrors.cost = 'التكلفة يجب أن تكون رقم موجب';
    }

    if (formData.taxRate && (isNaN(parseFloat(formData.taxRate)) || parseFloat(formData.taxRate) < 0)) {
      newErrors.taxRate = 'معدل الضريبة يجب أن يكون رقم موجب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/products/${resolvedParams.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price),
          cost: formData.cost ? parseFloat(formData.cost) : 0,
          taxRate: formData.taxRate ? parseFloat(formData.taxRate) : 0,
          currentStock: formData.trackInventory ? parseInt(formData.currentStock) : 0,
          minStock: formData.trackInventory ? parseInt(formData.minStock) : 0,
          maxStock: formData.trackInventory ? parseInt(formData.maxStock) : 0,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء تحديث المنتج');
      }

      const updatedProduct = await response.json();

      toast({
        title: 'تم تحديث المنتج بنجاح',
        description: `تم تحديث المنتج ${updatedProduct.name} بنجاح`,
        variant: 'success'
      });

      router.push('/dashboard/products');
    } catch (error) {
      console.error('Error updating product:', error);
      toast({
        title: 'خطأ في تحديث المنتج',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // إزالة الخطأ عند تعديل الحقل
    if (errors[field as keyof Partial<ProductFormData>]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (fetchLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="mr-3">جاري تحميل بيانات المنتج...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">تعديل المنتج</h1>
          <p className="text-sm text-gray-500">Edit Product</p>
        </div>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          العودة / Back
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* معلومات المنتج الأساسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-2">
              اسم المنتج (عربي) <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="أدخل اسم المنتج"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              اسم المنتج (إنجليزي)
            </label>
            <input
              type="text"
              value={formData.nameEn}
              onChange={(e) => handleInputChange('nameEn', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter product name in English"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              السعر (درهم) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.price ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0.00"
            />
            {errors.price && <p className="text-red-500 text-sm mt-1">{errors.price}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              التكلفة (درهم)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.cost}
              onChange={(e) => handleInputChange('cost', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.cost ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0.00"
            />
            {errors.cost && <p className="text-red-500 text-sm mt-1">{errors.cost}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              رمز المنتج (SKU)
            </label>
            <input
              type="text"
              value={formData.sku}
              onChange={(e) => handleInputChange('sku', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="PROD-001"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              معدل الضريبة (%)
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              max="100"
              value={formData.taxRate}
              onChange={(e) => handleInputChange('taxRate', e.target.value)}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.taxRate ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="5"
            />
            {errors.taxRate && <p className="text-red-500 text-sm mt-1">{errors.taxRate}</p>}
          </div>
        </div>

        {/* حالة المنتج */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold mb-4">حالة المنتج</h3>
          <div>
            <label className="block text-sm font-medium mb-2">
              الحالة
            </label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value as 'active' | 'inactive')}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
            </select>
          </div>
        </div>

        {/* أزرار الحفظ */}
        <div className="flex justify-end space-x-4 rtl:space-x-reverse pt-6 border-t">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            إلغاء
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'جاري التحديث...' : 'تحديث المنتج'}
          </button>
        </div>
      </form>
    </div>
  );
}
