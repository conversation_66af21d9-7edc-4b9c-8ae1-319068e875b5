'use client';

import { useState, useEffect } from 'react';
import { formatCurrency } from '@/lib/utils';

interface BudgetItem {
  id: string;
  category: string;
  amount: number;
  type: 'income' | 'expense';
  description: string;
  month: number;
  year: number;
  createdAt: string;
  updatedAt: string;
}

interface BudgetSummary {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  categories: {
    [key: string]: {
      income: number;
      expense: number;
    };
  };
}

export default function BudgetPage() {
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [currentItem, setCurrentItem] = useState<BudgetItem | null>(null);
  const [filter, setFilter] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    type: '',
    category: ''
  });

  // فئات الموازنة
  const budgetCategories = [
    'مبيعات | Sales',
    'رواتب | Salaries',
    'إيجار | Rent',
    'مرافق | Utilities',
    'معدات | Equipment',
    'تسويق | Marketing',
    'صيانة | Maintenance',
    'مشتريات | Purchases',
    'ضرائب | Taxes',
    'تأمين | Insurance',
    'أخرى | Other'
  ];

  useEffect(() => {
    // استدعاء API للحصول على بنود الموازنة
    const fetchBudgetItems = async () => {
      setLoading(true);
      try {
        // في التطبيق الحقيقي، هذا سيكون استدعاء API
        const data = localStorage.getItem('budgetItems');
        if (data) {
          setBudgetItems(JSON.parse(data));
        } else {
          setBudgetItems([]);
        }
      } catch (error) {
        console.error('خطأ في جلب بنود الموازنة:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBudgetItems();
  }, []);

  // تصفية بنود الموازنة
  const filteredItems = budgetItems.filter(item => {
    let match = true;

    if (filter.month && item.month !== filter.month) {
      match = false;
    }

    if (filter.year && item.year !== filter.year) {
      match = false;
    }

    if (filter.type && item.type !== filter.type) {
      match = false;
    }

    if (filter.category && item.category !== filter.category) {
      match = false;
    }

    return match;
  });

  // حساب ملخص الموازنة
  const calculateBudgetSummary = (): BudgetSummary => {
    const summary: BudgetSummary = {
      totalIncome: 0,
      totalExpense: 0,
      balance: 0,
      categories: {}
    };

    filteredItems.forEach(item => {
      // إضافة إلى المجموع الكلي
      if (item.type === 'income') {
        summary.totalIncome += item.amount;
      } else {
        summary.totalExpense += item.amount;
      }

      // إضافة إلى فئة
      if (!summary.categories[item.category]) {
        summary.categories[item.category] = { income: 0, expense: 0 };
      }

      if (item.type === 'income') {
        summary.categories[item.category].income += item.amount;
      } else {
        summary.categories[item.category].expense += item.amount;
      }
    });

    // حساب الرصيد
    summary.balance = summary.totalIncome - summary.totalExpense;

    return summary;
  };

  const budgetSummary = calculateBudgetSummary();

  // إضافة بند موازنة جديد
  const handleAddItem = () => {
    setCurrentItem(null);
    setShowModal(true);
  };

  // تعديل بند موازنة
  const handleEditItem = (item: BudgetItem) => {
    setCurrentItem(item);
    setShowModal(true);
  };

  // حذف بند موازنة
  const handleDeleteItem = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا البند؟')) {
      const updatedItems = budgetItems.filter(item => item.id !== id);
      setBudgetItems(updatedItems);
      localStorage.setItem('budgetItems', JSON.stringify(updatedItems));
    }
  };

  // حفظ بند الموازنة (إضافة أو تعديل)
  const handleSaveItem = (formData: any) => {
    if (currentItem) {
      // تعديل بند موجود
      const updatedItems = budgetItems.map(item =>
        item.id === currentItem.id
          ? { ...item, ...formData, updatedAt: new Date().toISOString() }
          : item
      );
      setBudgetItems(updatedItems);
      localStorage.setItem('budgetItems', JSON.stringify(updatedItems));
    } else {
      // إضافة بند جديد
      const newItem: BudgetItem = {
        id: Math.random().toString(36).substring(2, 9),
        ...formData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const updatedItems = [...budgetItems, newItem];
      setBudgetItems(updatedItems);
      localStorage.setItem('budgetItems', JSON.stringify(updatedItems));
    }

    setShowModal(false);
  };

  // الحصول على اسم الشهر
  const getMonthName = (month: number): string => {
    const months = [
      'يناير | January',
      'فبراير | February',
      'مارس | March',
      'أبريل | April',
      'مايو | May',
      'يونيو | June',
      'يوليو | July',
      'أغسطس | August',
      'سبتمبر | September',
      'أكتوبر | October',
      'نوفمبر | November',
      'ديسمبر | December'
    ];

    return months[month - 1] || '';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1 dark:text-white">الموازنة</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">Budget</p>
        </div>
        <button
          onClick={handleAddItem}
          className="px-4 py-2 bg-primary text-white rounded-md text-sm"
        >
          إضافة بند | Add Item
        </button>
      </div>

      {/* ملخص الموازنة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
          <h3 className="text-sm text-green-500 dark:text-green-300">إجمالي الإيرادات</h3>
          <p className="text-2xl font-bold text-green-700 dark:text-green-100">
            {formatCurrency(budgetSummary.totalIncome)}
          </p>
        </div>

        <div className="bg-red-50 dark:bg-red-900 p-4 rounded-lg">
          <h3 className="text-sm text-red-500 dark:text-red-300">إجمالي المصروفات</h3>
          <p className="text-2xl font-bold text-red-700 dark:text-red-100">
            {formatCurrency(budgetSummary.totalExpense)}
          </p>
        </div>

        <div className={`${
          budgetSummary.balance >= 0
            ? 'bg-blue-50 dark:bg-blue-900'
            : 'bg-yellow-50 dark:bg-yellow-900'
        } p-4 rounded-lg`}>
          <h3 className={`text-sm ${
            budgetSummary.balance >= 0
              ? 'text-blue-500 dark:text-blue-300'
              : 'text-yellow-500 dark:text-yellow-300'
          }`}>الرصيد</h3>
          <p className={`text-2xl font-bold ${
            budgetSummary.balance >= 0
              ? 'text-blue-700 dark:text-blue-100'
              : 'text-yellow-700 dark:text-yellow-100'
          }`}>
            {formatCurrency(budgetSummary.balance)}
          </p>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold mb-3 dark:text-white">تصفية الموازنة</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">الشهر</label>
            <select
              value={filter.month}
              onChange={(e) => setFilter({...filter, month: parseInt(e.target.value)})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                <option key={month} value={month}>{getMonthName(month)}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">السنة</label>
            <select
              value={filter.year}
              onChange={(e) => setFilter({...filter, year: parseInt(e.target.value)})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">النوع</label>
            <select
              value={filter.type}
              onChange={(e) => setFilter({...filter, type: e.target.value as any})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="">الكل</option>
              <option value="income">إيرادات | Income</option>
              <option value="expense">مصروفات | Expense</option>
            </select>
          </div>

          <div>
            <label className="block text-sm mb-1 dark:text-gray-300">الفئة</label>
            <select
              value={filter.category}
              onChange={(e) => setFilter({...filter, category: e.target.value})}
              className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="">الكل</option>
              {budgetCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* جدول بنود الموازنة */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="p-2 text-right border dark:border-gray-600">الفئة</th>
              <th className="p-2 text-right border dark:border-gray-600">النوع</th>
              <th className="p-2 text-right border dark:border-gray-600">المبلغ</th>
              <th className="p-2 text-right border dark:border-gray-600">الوصف</th>
              <th className="p-2 text-right border dark:border-gray-600">الشهر</th>
              <th className="p-2 text-right border dark:border-gray-600">السنة</th>
              <th className="p-2 text-center border dark:border-gray-600">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={7} className="p-4 text-center">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                </td>
              </tr>
            ) : filteredItems.length === 0 ? (
              <tr>
                <td colSpan={7} className="p-4 text-center text-gray-500 dark:text-gray-400">
                  لا توجد بنود موازنة مطابقة للفلاتر المحددة
                </td>
              </tr>
            ) : (
              filteredItems.map(item => (
                <tr key={item.id} className="border-b dark:border-gray-700">
                  <td className="p-2 border dark:border-gray-600">{item.category}</td>
                  <td className="p-2 border dark:border-gray-600">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      item.type === 'income'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                    }`}>
                      {item.type === 'income' ? 'إيرادات | Income' : 'مصروفات | Expense'}
                    </span>
                  </td>
                  <td className="p-2 border dark:border-gray-600">
                    {formatCurrency(item.amount)}
                  </td>
                  <td className="p-2 border dark:border-gray-600">{item.description}</td>
                  <td className="p-2 border dark:border-gray-600">{getMonthName(item.month)}</td>
                  <td className="p-2 border dark:border-gray-600">{item.year}</td>
                  <td className="p-2 border dark:border-gray-600 text-center">
                    <div className="flex justify-center space-x-1 space-x-reverse">
                      <button
                        onClick={() => handleEditItem(item)}
                        className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-md text-xs"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleDeleteItem(item.id)}
                        className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 rounded-md text-xs"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* نموذج إضافة/تعديل بند موازنة */}
      {showModal && (
        <BudgetItemForm
          item={currentItem}
          categories={budgetCategories}
          onSave={handleSaveItem}
          onCancel={() => setShowModal(false)}
        />
      )}
    </div>
  );
}

// مكون نموذج بند الموازنة
function BudgetItemForm({ item, categories, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    category: item?.category || categories[0],
    amount: item?.amount || '',
    type: item?.type || 'expense',
    description: item?.description || '',
    month: item?.month || new Date().getMonth() + 1,
    year: item?.year || new Date().getFullYear()
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' || name === 'month' || name === 'year'
        ? parseFloat(value) || ''
        : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  // الحصول على اسم الشهر
  const getMonthName = (month: number): string => {
    const months = [
      'يناير | January',
      'فبراير | February',
      'مارس | March',
      'أبريل | April',
      'مايو | May',
      'يونيو | June',
      'يوليو | July',
      'أغسطس | August',
      'سبتمبر | September',
      'أكتوبر | October',
      'نوفمبر | November',
      'ديسمبر | December'
    ];

    return months[month - 1] || '';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4 dark:text-white">
          {item ? 'تعديل بند موازنة' : 'إضافة بند موازنة جديد'}
        </h2>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              النوع <span className="text-red-500">*</span>
            </label>
            <div className="flex space-x-4 space-x-reverse">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="type"
                  value="income"
                  checked={formData.type === 'income'}
                  onChange={handleChange}
                  className="h-4 w-4 text-primary border-gray-300"
                  required
                />
                <span className="mr-2 dark:text-white">إيرادات | Income</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="type"
                  value="expense"
                  checked={formData.type === 'expense'}
                  onChange={handleChange}
                  className="h-4 w-4 text-primary border-gray-300"
                  required
                />
                <span className="mr-2 dark:text-white">مصروفات | Expense</span>
              </label>
            </div>
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              الفئة <span className="text-red-500">*</span>
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              المبلغ <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium dark:text-white">
              الوصف
            </label>
            <input
              type="text"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
            />
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                الشهر <span className="text-red-500">*</span>
              </label>
              <select
                name="month"
                value={formData.month}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              >
                {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                  <option key={month} value={month}>{getMonthName(month)}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium dark:text-white">
                السنة <span className="text-red-500">*</span>
              </label>
              <select
                name="year"
                value={formData.year}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                required
              >
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:text-white rounded-md"
            >
              إلغاء | Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md"
            >
              {item ? 'تحديث | Update' : 'إضافة | Add'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
