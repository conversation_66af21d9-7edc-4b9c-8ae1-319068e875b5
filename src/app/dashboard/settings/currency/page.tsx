'use client';

/**
 * صفحة إعدادات العملة - تتيح للمستخدم تعديل إعدادات العملة
 * Currency Settings Page - Allows user to modify currency settings
 */

import { useState, useEffect } from 'react';
import { useI18n } from '@/lib/i18n';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {RotateCw, Save, RefreshCw} from 'lucide-react';
import CurrencySelector from '@/components/ui/currency-selector';
import CurrencyConverter from '@/components/ui/currency-converter';
import { CurrencyCode, DEFAULT_CURRENCY, CURRENCIES } from '@/lib/utils';
import { getSystemSettings, saveSystemSettings } from '@/lib/settings';
import { updateExchangeRates, getStoredExchangeRates } from '@/lib/services/currency-service';

/**
 * صفحة إعدادات العملة
 * Currency Settings Page
 */
export default function CurrencySettingsPage() {
  const { t, language } = useI18n();
  const [activeTab, setActiveTab] = useState('general');
  const [defaultCurrency, setDefaultCurrency] = useState<CurrencyCode>(DEFAULT_CURRENCY);
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [exchangeRates, setExchangeRates] = useState<Record<CurrencyCode, number>>({} as Record<CurrencyCode, number>);
  const [editableRates, setEditableRates] = useState<Record<CurrencyCode, string>>({} as Record<CurrencyCode, string>);
  const [isSaving, setIsSaving] = useState(false);

  // تحميل الإعدادات عند تحميل الصفحة
  // Load settings when page loads
  useEffect(() => {
    const settings = getSystemSettings();
    setDefaultCurrency(settings.defaultCurrency || DEFAULT_CURRENCY);

    // تحميل أسعار الصرف
    // Load exchange rates
    const storedRates = getStoredExchangeRates();
    setExchangeRates(storedRates.rates);
    setLastUpdated(new Date(storedRates.date));

    // تهيئة أسعار الصرف القابلة للتعديل
    // Initialize editable exchange rates
    const initialEditableRates: Record<CurrencyCode, string> = {} as Record<CurrencyCode, string>;
    Object.entries(storedRates.rates).forEach(([code, rate]) => {
      initialEditableRates[code as CurrencyCode] = rate.toString();
    });
    setEditableRates(initialEditableRates);
  }, []);

  // حفظ الإعدادات
  // Save settings
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);

      // حفظ العملة الافتراضية
      // Save default currency
      const settings = getSystemSettings();
      saveSystemSettings({
        ...settings,
        defaultCurrency
      });

      // حفظ أسعار الصرف المعدلة
      // Save modified exchange rates
      const updatedRates = { ...exchangeRates };
      Object.entries(editableRates).forEach(([code, rateStr]) => {
        const rate = parseFloat(rateStr);
        if (!isNaN(rate) && rate > 0) {
          updatedRates[code as CurrencyCode] = rate;
        }
      });

      // تحديث أسعار الصرف المخزنة
      // Update stored exchange rates
      const storedRates = getStoredExchangeRates();
      const updatedStoredRates = {
        ...storedRates,
        rates: updatedRates
      };

      // استخدام خدمة العملة لتخزين أسعار الصرف
      // Use currency service to store exchange rates
      if (typeof window !== 'undefined' && window.CurrencyService) {
        window.CurrencyService.storeExchangeRates(updatedStoredRates);
      }

      toast.success(t('settings.saved') || 'Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error(t('settings.saveError') || 'Error saving settings');
    } finally {
      setIsSaving(false);
    }
  };

  // تحديث أسعار الصرف
  // Update exchange rates
  const handleUpdateRates = async () => {
    try {
      setIsUpdating(true);

      // استخدام خدمة العملة لتحديث أسعار الصرف
      // Use currency service to update exchange rates
      const updatedRates = await updateExchangeRates();

      setExchangeRates(updatedRates.rates);
      setLastUpdated(new Date(updatedRates.date));

      // تحديث أسعار الصرف القابلة للتعديل
      // Update editable exchange rates
      const updatedEditableRates: Record<CurrencyCode, string> = {} as Record<CurrencyCode, string>;
      Object.entries(updatedRates.rates).forEach(([code, rate]) => {
        updatedEditableRates[code as CurrencyCode] = rate.toString();
      });
      setEditableRates(updatedEditableRates);

      toast.success(t('currency.ratesUpdated') || 'Exchange rates updated successfully');
    } catch (error) {
      console.error('Error updating exchange rates:', error);
      toast.error(t('currency.updateError') || 'Error updating exchange rates');
    } finally {
      setIsUpdating(false);
    }
  };

  // تغيير قيمة سعر الصرف
  // Change exchange rate value
  const handleRateChange = (currency: CurrencyCode, value: string) => {
    setEditableRates(prev => ({
      ...prev,
      [currency]: value
    }));
  };

  // إعادة تعيين سعر الصرف إلى القيمة الأصلية
  // Reset exchange rate to original value
  const handleResetRate = (currency: CurrencyCode) => {
    setEditableRates(prev => ({
      ...prev,
      [currency]: exchangeRates[currency].toString()
    }));
  };

  return (
    <div className="container py-6">
      <h1 className="text-2xl font-bold mb-6">
        {t('settings.currencySettings') || 'Currency Settings'}
      </h1>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">
            {t('settings.general') || 'General'}
          </TabsTrigger>
          <TabsTrigger value="rates">
            {t('currency.exchangeRates') || 'Exchange Rates'}
          </TabsTrigger>
          <TabsTrigger value="converter">
            {t('currency.converter') || 'Currency Converter'}
          </TabsTrigger>
        </TabsList>

        {/* إعدادات عامة */}
        {/* General Settings */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.general') || 'General'}</CardTitle>
              <CardDescription>
                {t('settings.currencyGeneralDescription') || 'Manage general currency settings'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="default-currency">
                  {t('settings.defaultCurrency') || 'Default Currency'}
                </Label>
                <CurrencySelector
                  value={defaultCurrency}
                  onChange={setDefaultCurrency}
                  variant="combobox"
                  showSymbol={true}
                  showFlag={true}
                  showSearch={true}
                  showLabel={false}
                  className="w-full"
                />
                <p className="text-sm text-muted-foreground">
                  {t('settings.defaultCurrencyDescription') || 'This currency will be used as the default throughout the application'}
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                    {t('common.saving') || 'Saving...'}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t('common.save') || 'Save'}
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* أسعار الصرف */}
        {/* Exchange Rates */}
        <TabsContent value="rates">
          <Card>
            <CardHeader>
              <CardTitle>{t('currency.exchangeRates') || 'Exchange Rates'}</CardTitle>
              <CardDescription>
                {t('currency.exchangeRatesDescription') || 'Manage currency exchange rates'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="text-sm text-muted-foreground">
                  {t('currency.lastUpdated') || 'Last updated'}: {lastUpdated.toLocaleString(language === 'ar' ? 'ar-AE' : 'en-US')}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUpdateRates}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  {t('currency.updateRates') || 'Update Rates'}
                </Button>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4 font-medium text-sm">
                  <div>{t('currency.currency') || 'Currency'}</div>
                  <div>{t('currency.exchangeRate') || 'Exchange Rate'}</div>
                  <div>{t('common.actions') || 'Actions'}</div>
                </div>

                {CURRENCIES.map(currency => (
                  <div key={currency.code} className="grid grid-cols-3 gap-4 items-center">
                    <div className="flex items-center">
                      <span className="mr-2 text-lg">
                        {currency.code === 'AED' ? '🇦🇪' :
                         currency.code === 'SAR' ? '🇸🇦' :
                         currency.code === 'QAR' ? '🇶🇦' :
                         currency.code === 'BHD' ? '🇧🇭' :
                         currency.code === 'KWD' ? '🇰🇼' :
                         currency.code === 'OMR' ? '🇴🇲' :
                         currency.code === 'EGP' ? '🇪🇬' :
                         currency.code === 'JOD' ? '🇯🇴' :
                         currency.code === 'LBP' ? '🇱🇧' :
                         currency.code === 'USD' ? '🇺🇸' :
                         currency.code === 'EUR' ? '🇪🇺' :
                         currency.code === 'GBP' ? '🇬🇧' : ''}
                      </span>
                      <span>
                        {language === 'ar' ? currency.nameAr : currency.nameEn}
                        <span className="text-muted-foreground ml-1">({currency.code})</span>
                      </span>
                    </div>
                    <div>
                      {currency.code === 'AED' ? (
                        <div className="flex items-center">
                          <span className="font-medium">1.0000</span>
                          <span className="text-muted-foreground ml-2">{t('currency.baseCurrency') || 'Base Currency'}</span>
                        </div>
                      ) : (
                        <Input
                          type="number"
                          value={editableRates[currency.code] || ''}
                          onChange={(e) => handleRateChange(currency.code, e.target.value)}
                          step="0.0001"
                          min="0"
                          disabled={currency.code === 'AED'}
                        />
                      )}
                    </div>
                    <div>
                      {currency.code !== 'AED' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleResetRate(currency.code)}
                          disabled={editableRates[currency.code] === exchangeRates[currency.code]?.toString()}
                        >
                          <RefreshCw className="h-4 w-4 mr-1" />
                          {t('common.reset') || 'Reset'}
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                    {t('common.saving') || 'Saving...'}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t('common.save') || 'Save'}
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* محول العملات */}
        {/* Currency Converter */}
        <TabsContent value="converter">
          <CurrencyConverter showHeader={true} showHistory={true} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
