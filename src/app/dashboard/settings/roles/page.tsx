// src/app/dashboard/settings/roles/page.tsx
import { PermissionGuard } from "@/components/auth/permission-guard";
import { RoleManagement } from "@/components/settings/role-management";

export default function RolesPage() {
  return (
    <PermissionGuard permission="MANAGE_SETTINGS">
      <div className="container py-6">
        <h1 className="mb-6 text-2xl font-bold">إدارة الأدوار والصلاحيات</h1>
        <RoleManagement />
      </div>
    </PermissionGuard>
  );
}