'use client'

import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'

export default function ForbiddenPage() {
  const router = useRouter()
  const { status } = useSession()

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50">
      <div className="w-full max-w-md text-center">
        <div className="mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-red-100 text-red-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="h-12 w-12"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <h1 className="mb-4 text-3xl font-bold text-gray-900">غير مصرح</h1>
        <p className="mb-8 text-lg text-gray-600">
          ليس لديك صلاحية للوصول إلى هذه الصفحة.
        </p>
        <div className="space-y-4">
          <Button onClick={() => router.back()} className="mx-2 border border-gray-300">
            العودة للصفحة السابقة
          </Button>
          <Button onClick={() => router.push('/dashboard')} className="mx-2">
            الذهاب إلى لوحة التحكم
          </Button>
          {status === 'unauthenticated' && (
            <Button onClick={() => router.push('/auth/login')} className="mx-2">
              تسجيل الدخول
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
