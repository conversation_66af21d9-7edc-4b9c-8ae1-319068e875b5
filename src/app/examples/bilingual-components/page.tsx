'use client';

import { useState } from 'react';
import Link from 'next/link';
import { HomeIcon } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BilingualDate, BilingualDateRange, BilingualRelativeDate } from '@/components/common/bilingual-date';
import { BilingualCurrency, SimpleCurrency } from '@/components/common/bilingual-currency';
import { LanguageSwitcher, LanguageToggle } from '@/components/common/language-switcher';

export default function BilingualComponentsPage() {
  const [activeTab, setActiveTab] = useState('date');
  const [amount, setAmount] = useState(1250.75);
  const [currency, setCurrency] = useState('AED');
  const [dateFormat, setDateFormat] = useState<'short' | 'medium' | 'long' | 'full'>('medium');
  const [showHijri, setShowHijri] = useState(true);
  
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);
  
  const nextMonth = new Date(today);
  nextMonth.setMonth(nextMonth.getMonth() + 1);

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
            <li className="inline-flex items-center">
              <Link href="/dashboard" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <HomeIcon className="h-4 w-4 ml-2" />
                الرئيسية
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/examples" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                  أمثلة
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-sm font-medium text-gray-500">المكونات ثنائية اللغة</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">المكونات ثنائية اللغة</h1>
        <h2 className="text-xl font-normal text-gray-700 mb-2">Bilingual Components</h2>
        <p className="text-muted-foreground">
          أمثلة على استخدام المكونات ثنائية اللغة في تطبيق أمين بلس | Examples of using bilingual components in Amin Plus application
        </p>
      </div>

      <div className="flex justify-end mb-4">
        <LanguageSwitcher />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="date">التاريخ | Date</TabsTrigger>
          <TabsTrigger value="currency">العملة | Currency</TabsTrigger>
          <TabsTrigger value="language">اللغة | Language</TabsTrigger>
        </TabsList>

        {/* مكونات التاريخ - Date Components */}
        <TabsContent value="date" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>التاريخ ثنائي اللغة | Bilingual Date</CardTitle>
              <CardDescription>
                عرض التاريخ باللغتين العربية والإنجليزية | Display date in both Arabic and English
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="flex-1">
                  <Label className="mb-2 block">تنسيق التاريخ | Date Format</Label>
                  <Select value={dateFormat} onValueChange={(value) => setDateFormat(value as any)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="short">مختصر | Short</SelectItem>
                      <SelectItem value="medium">متوسط | Medium</SelectItem>
                      <SelectItem value="long">طويل | Long</SelectItem>
                      <SelectItem value="full">كامل | Full</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="show-hijri"
                    checked={showHijri}
                    onChange={(e) => setShowHijri(e.target.checked)}
                  />
                  <Label htmlFor="show-hijri">
                    إظهار التاريخ الهجري | Show Hijri Date
                  </Label>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">اليوم | Today</h3>
                  <BilingualDate
                    date={today}
                    format={dateFormat}
                    showHijri={showHijri}
                    className="text-lg"
                  />
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">الأمس | Yesterday</h3>
                  <BilingualDate
                    date={yesterday}
                    format={dateFormat}
                    showHijri={showHijri}
                    className="text-lg"
                  />
                </div>
              </div>

              <div className="p-4 border rounded-md">
                <h3 className="text-sm font-medium mb-2">نطاق التاريخ | Date Range</h3>
                <BilingualDateRange
                  startDate={lastWeek}
                  endDate={nextMonth}
                  format={dateFormat === 'full' ? 'long' : dateFormat}
                  language="ar"
                  className="text-lg block mb-2"
                />
                <BilingualDateRange
                  startDate={lastWeek}
                  endDate={nextMonth}
                  format={dateFormat === 'full' ? 'long' : dateFormat}
                  language="en"
                  className="text-lg block"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">التاريخ النسبي (عربي) | Relative Date (Arabic)</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>الآن:</span>
                      <BilingualRelativeDate date={new Date()} language="ar" />
                    </div>
                    <div className="flex justify-between">
                      <span>منذ ساعة:</span>
                      <BilingualRelativeDate date={new Date(Date.now() - 60 * 60 * 1000)} language="ar" />
                    </div>
                    <div className="flex justify-between">
                      <span>منذ يوم:</span>
                      <BilingualRelativeDate date={yesterday} language="ar" />
                    </div>
                    <div className="flex justify-between">
                      <span>منذ أسبوع:</span>
                      <BilingualRelativeDate date={lastWeek} language="ar" />
                    </div>
                  </div>
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">التاريخ النسبي (إنجليزي) | Relative Date (English)</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Now:</span>
                      <BilingualRelativeDate date={new Date()} language="en" />
                    </div>
                    <div className="flex justify-between">
                      <span>1 hour ago:</span>
                      <BilingualRelativeDate date={new Date(Date.now() - 60 * 60 * 1000)} language="en" />
                    </div>
                    <div className="flex justify-between">
                      <span>1 day ago:</span>
                      <BilingualRelativeDate date={yesterday} language="en" />
                    </div>
                    <div className="flex justify-between">
                      <span>1 week ago:</span>
                      <BilingualRelativeDate date={lastWeek} language="en" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* مكونات العملة - Currency Components */}
        <TabsContent value="currency" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>العملة ثنائية اللغة | Bilingual Currency</CardTitle>
              <CardDescription>
                عرض المبالغ المالية باللغتين العربية والإنجليزية | Display monetary amounts in both Arabic and English
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="flex-1">
                  <Label className="mb-2 block">المبلغ | Amount</Label>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[amount]}
                      min={0}
                      max={10000}
                      step={0.25}
                      onValueChange={(value) => setAmount(value[0])}
                      className="flex-1"
                    />
                    <span className="min-w-[80px] text-right">{amount.toFixed(2)}</span>
                  </div>
                </div>
                <div className="w-32">
                  <Label className="mb-2 block">العملة | Currency</Label>
                  <Select value={currency} onValueChange={setCurrency}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AED">AED</SelectItem>
                      <SelectItem value="SAR">SAR</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">العربية أولاً | Arabic First</h3>
                  <BilingualCurrency
                    amount={amount}
                    currency={currency}
                    primaryLanguage="ar"
                    className="text-lg font-bold"
                  />
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">الإنجليزية أولاً | English First</h3>
                  <BilingualCurrency
                    amount={amount}
                    currency={currency}
                    primaryLanguage="en"
                    className="text-lg font-bold"
                  />
                </div>
              </div>

              <div className="p-4 border rounded-md">
                <h3 className="text-sm font-medium mb-2">المبلغ بالأرقام والحروف | Amount in Numbers and Words</h3>
                <BilingualCurrency
                  amount={amount}
                  currency={currency}
                  showInWords={true}
                  className="text-lg"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">تنسيق بسيط (عربي) | Simple Format (Arabic)</h3>
                  <SimpleCurrency
                    amount={amount}
                    currency={currency}
                    language="ar"
                    className="text-lg font-bold"
                  />
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">تنسيق بسيط (إنجليزي) | Simple Format (English)</h3>
                  <SimpleCurrency
                    amount={amount}
                    currency={currency}
                    language="en"
                    className="text-lg font-bold"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* مكونات اللغة - Language Components */}
        <TabsContent value="language" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>مكونات تبديل اللغة | Language Switcher Components</CardTitle>
              <CardDescription>
                مكونات لتبديل اللغة بين العربية والإنجليزية | Components for switching between Arabic and English
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-4">قائمة منسدلة | Dropdown Menu</h3>
                  <div className="flex flex-wrap gap-4">
                    <LanguageSwitcher variant="default" />
                    <LanguageSwitcher variant="outline" />
                    <LanguageSwitcher variant="ghost" />
                  </div>
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-4">أحجام مختلفة | Different Sizes</h3>
                  <div className="flex flex-wrap gap-4">
                    <LanguageSwitcher size="sm" />
                    <LanguageSwitcher size="default" />
                    <LanguageSwitcher size="lg" />
                  </div>
                </div>
              </div>

              <div className="p-4 border rounded-md">
                <h3 className="text-sm font-medium mb-4">زر تبديل اللغة | Language Toggle Button</h3>
                <div className="flex flex-wrap gap-4">
                  <LanguageToggle variant="default" />
                  <LanguageToggle variant="outline" />
                  <LanguageToggle variant="ghost" />
                </div>
              </div>

              <div className="p-4 border rounded-md">
                <h3 className="text-sm font-medium mb-4">أيقونة فقط | Icon Only</h3>
                <div className="flex flex-wrap gap-4">
                  <LanguageSwitcher size="icon" showText={false} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
