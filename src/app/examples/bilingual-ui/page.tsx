'use client';

import { useState } from 'react';
import Link from 'next/link';
import { HomeIcon, Save, Send, Mail, Bell, Info, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/language-context';
import { BilingualText, BilingualHeading, BilingualDescription, BilingualLabel } from '@/components/common/bilingual-text';
import { BilingualButton, BilingualLinkButton } from '@/components/common/bilingual-button';
import { BilingualInput, BilingualTextarea, BilingualForm } from '@/components/common/bilingual-form';
import { bilingualToast } from '@/components/ui/bilingual-toast';

export default function BilingualUIPage() {
  const { language } = useLanguage();
  const [activeTab, setActiveTab] = useState('text');
  
  // حالة النموذج
  const [formData, setFormData] = useState({
    nameAr: '',
    nameEn: '',
    descriptionAr: '',
    descriptionEn: '',
    emailAr: '',
    emailEn: '',
  });
  
  const [formLoading, setFormLoading] = useState(false);
  
  // تحديث بيانات النموذج
  const updateFormData = (field: string, valueAr: string, valueEn: string) => {
    setFormData(prev => ({
      ...prev,
      [`${field}Ar`]: valueAr,
      [`${field}En`]: valueEn,
    }));
  };
  
  // معالجة إرسال النموذج
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFormLoading(true);
    
    // محاكاة طلب API
    setTimeout(() => {
      bilingualToast.success(
        'تم حفظ البيانات بنجاح',
        'Data saved successfully'
      );
      setFormLoading(false);
    }, 1500);
  };
  
  // عرض إشعارات مختلفة
  const showToast = (type: 'success' | 'error' | 'warning' | 'info') => {
    switch (type) {
      case 'success':
        bilingualToast.success(
          'تمت العملية بنجاح',
          'Operation completed successfully'
        );
        break;
      case 'error':
        bilingualToast.error(
          'حدث خطأ أثناء تنفيذ العملية',
          'An error occurred during the operation'
        );
        break;
      case 'warning':
        bilingualToast.warning(
          'تحذير: يرجى التحقق من البيانات المدخلة',
          'Warning: Please check the entered data'
        );
        break;
      case 'info':
        bilingualToast.info(
          'معلومات: تم تحديث النظام',
          'Information: System has been updated'
        );
        break;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
            <li className="inline-flex items-center">
              <Link href="/dashboard" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <HomeIcon className="h-4 w-4 ml-2" />
                {language === 'ar' ? 'الرئيسية' : 'Home'}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/examples" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                  {language === 'ar' ? 'أمثلة' : 'Examples'}
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-sm font-medium text-gray-500">
                  {language === 'ar' ? 'واجهة المستخدم ثنائية اللغة' : 'Bilingual UI'}
                </span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="mb-6">
        <BilingualHeading
          ar="واجهة المستخدم ثنائية اللغة"
          en="Bilingual User Interface"
          as="h1"
          className="text-3xl font-bold mb-2"
        />
        <BilingualDescription
          ar="أمثلة على استخدام مكونات واجهة المستخدم ثنائية اللغة في تطبيق أمين بلس"
          en="Examples of using bilingual UI components in Amin Plus application"
        />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="text">
            <BilingualText
              ar="النصوص"
              en="Text"
              showBothLanguages={false}
            />
          </TabsTrigger>
          <TabsTrigger value="buttons">
            <BilingualText
              ar="الأزرار"
              en="Buttons"
              showBothLanguages={false}
            />
          </TabsTrigger>
          <TabsTrigger value="forms">
            <BilingualText
              ar="النماذج"
              en="Forms"
              showBothLanguages={false}
            />
          </TabsTrigger>
        </TabsList>

        {/* مكونات النص - Text Components */}
        <TabsContent value="text" className="space-y-6">
          <Card>
            <CardHeader>
              <BilingualHeading
                ar="مكونات النص ثنائية اللغة"
                en="Bilingual Text Components"
                as={CardTitle}
              />
              <BilingualDescription
                ar="عرض النصوص باللغتين العربية والإنجليزية"
                en="Display text in both Arabic and English"
              />
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="نص بسيط"
                      en="Simple Text"
                      showBothLanguages={false}
                    />
                  </h3>
                  <BilingualText
                    ar="مرحبًا بك في تطبيق أمين بلس"
                    en="Welcome to Amin Plus application"
                    className="text-lg"
                  />
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="نص بفاصل مخصص"
                      en="Text with Custom Separator"
                      showBothLanguages={false}
                    />
                  </h3>
                  <BilingualText
                    ar="مرحبًا بك في تطبيق أمين بلس"
                    en="Welcome to Amin Plus application"
                    separator=" - "
                    className="text-lg"
                  />
                </div>
              </div>

              <div className="p-4 border rounded-md">
                <h3 className="text-sm font-medium mb-2">
                  <BilingualText
                    ar="العناوين"
                    en="Headings"
                    showBothLanguages={false}
                  />
                </h3>
                <div className="space-y-4">
                  <BilingualHeading
                    ar="عنوان المستوى الأول"
                    en="Heading Level One"
                    as="h1"
                    className="text-2xl font-bold"
                  />
                  <BilingualHeading
                    ar="عنوان المستوى الثاني"
                    en="Heading Level Two"
                    as="h2"
                    className="text-xl font-semibold"
                  />
                  <BilingualHeading
                    ar="عنوان المستوى الثالث"
                    en="Heading Level Three"
                    as="h3"
                    className="text-lg font-medium"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="الوصف"
                      en="Description"
                      showBothLanguages={false}
                    />
                  </h3>
                  <BilingualDescription
                    ar="هذا وصف توضيحي لميزات التطبيق. يمكن استخدامه في أي مكان تحتاج فيه إلى عرض نص وصفي."
                    en="This is a descriptive text for application features. It can be used anywhere you need to display descriptive text."
                  />
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="التسميات"
                      en="Labels"
                      showBothLanguages={false}
                    />
                  </h3>
                  <div className="space-y-2">
                    <BilingualLabel
                      ar="اسم المستخدم"
                      en="Username"
                      htmlFor="username"
                    />
                    <BilingualLabel
                      ar="كلمة المرور"
                      en="Password"
                      htmlFor="password"
                      required
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* مكونات الأزرار - Button Components */}
        <TabsContent value="buttons" className="space-y-6">
          <Card>
            <CardHeader>
              <BilingualHeading
                ar="مكونات الأزرار ثنائية اللغة"
                en="Bilingual Button Components"
                as={CardTitle}
              />
              <BilingualDescription
                ar="أزرار تعرض النصوص باللغتين العربية والإنجليزية"
                en="Buttons that display text in both Arabic and English"
              />
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="أنواع الأزرار"
                      en="Button Variants"
                      showBothLanguages={false}
                    />
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <BilingualButton
                      textAr="زر أساسي"
                      textEn="Primary Button"
                      variant="default"
                    />
                    <BilingualButton
                      textAr="زر ثانوي"
                      textEn="Secondary Button"
                      variant="secondary"
                    />
                    <BilingualButton
                      textAr="زر محيطي"
                      textEn="Outline Button"
                      variant="outline"
                    />
                    <BilingualButton
                      textAr="زر شبح"
                      textEn="Ghost Button"
                      variant="ghost"
                    />
                    <BilingualButton
                      textAr="زر تدميري"
                      textEn="Destructive Button"
                      variant="destructive"
                    />
                    <BilingualButton
                      textAr="زر رابط"
                      textEn="Link Button"
                      variant="link"
                    />
                  </div>
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="أحجام الأزرار"
                      en="Button Sizes"
                      showBothLanguages={false}
                    />
                  </h3>
                  <div className="flex flex-wrap items-center gap-2">
                    <BilingualButton
                      textAr="زر صغير"
                      textEn="Small Button"
                      size="sm"
                    />
                    <BilingualButton
                      textAr="زر متوسط"
                      textEn="Default Button"
                      size="default"
                    />
                    <BilingualButton
                      textAr="زر كبير"
                      textEn="Large Button"
                      size="lg"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="أزرار مع أيقونات"
                      en="Buttons with Icons"
                      showBothLanguages={false}
                    />
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <BilingualButton
                      textAr="حفظ"
                      textEn="Save"
                      icon={<Save className="h-4 w-4" />}
                      iconPosition="start"
                    />
                    <BilingualButton
                      textAr="إرسال"
                      textEn="Send"
                      icon={<Send className="h-4 w-4" />}
                      iconPosition="end"
                      variant="secondary"
                    />
                    <BilingualButton
                      textAr="بريد إلكتروني"
                      textEn="Email"
                      icon={<Mail className="h-4 w-4" />}
                      variant="outline"
                    />
                    <BilingualButton
                      textAr="إشعارات"
                      textEn="Notifications"
                      icon={<Bell className="h-4 w-4" />}
                      variant="ghost"
                    />
                  </div>
                </div>
                
                <div className="p-4 border rounded-md">
                  <h3 className="text-sm font-medium mb-2">
                    <BilingualText
                      ar="أزرار التحميل"
                      en="Loading Buttons"
                      showBothLanguages={false}
                    />
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <BilingualButton
                      textAr="حفظ"
                      textEn="Save"
                      loading={true}
                      loadingTextAr="جاري الحفظ..."
                      loadingTextEn="Saving..."
                    />
                    <BilingualButton
                      textAr="إرسال"
                      textEn="Send"
                      loading={true}
                      variant="secondary"
                    />
                  </div>
                </div>
              </div>

              <div className="p-4 border rounded-md">
                <h3 className="text-sm font-medium mb-2">
                  <BilingualText
                    ar="أزرار الروابط"
                    en="Link Buttons"
                    showBothLanguages={false}
                  />
                </h3>
                <div className="flex flex-wrap gap-2">
                  <BilingualLinkButton
                    textAr="الصفحة الرئيسية"
                    textEn="Home Page"
                    href="/dashboard"
                  />
                  <BilingualLinkButton
                    textAr="الإعدادات"
                    textEn="Settings"
                    href="/settings"
                    variant="outline"
                  />
                  <BilingualLinkButton
                    textAr="المساعدة"
                    textEn="Help"
                    href="/help"
                    variant="secondary"
                    icon={<Info className="h-4 w-4" />}
                  />
                </div>
              </div>

              <div className="p-4 border rounded-md">
                <h3 className="text-sm font-medium mb-2">
                  <BilingualText
                    ar="أزرار الإشعارات"
                    en="Notification Buttons"
                    showBothLanguages={false}
                  />
                </h3>
                <div className="flex flex-wrap gap-2">
                  <BilingualButton
                    textAr="نجاح"
                    textEn="Success"
                    icon={<CheckCircle className="h-4 w-4" />}
                    onClick={() => showToast('success')}
                    className="bg-green-600 hover:bg-green-700"
                  />
                  <BilingualButton
                    textAr="خطأ"
                    textEn="Error"
                    icon={<XCircle className="h-4 w-4" />}
                    onClick={() => showToast('error')}
                    className="bg-red-600 hover:bg-red-700"
                  />
                  <BilingualButton
                    textAr="تحذير"
                    textEn="Warning"
                    icon={<AlertTriangle className="h-4 w-4" />}
                    onClick={() => showToast('warning')}
                    className="bg-amber-600 hover:bg-amber-700"
                  />
                  <BilingualButton
                    textAr="معلومات"
                    textEn="Info"
                    icon={<Info className="h-4 w-4" />}
                    onClick={() => showToast('info')}
                    className="bg-blue-600 hover:bg-blue-700"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* مكونات النماذج - Form Components */}
        <TabsContent value="forms" className="space-y-6">
          <Card>
            <CardHeader>
              <BilingualHeading
                ar="مكونات النماذج ثنائية اللغة"
                en="Bilingual Form Components"
                as={CardTitle}
              />
              <BilingualDescription
                ar="نماذج تدعم إدخال البيانات باللغتين العربية والإنجليزية"
                en="Forms that support data entry in both Arabic and English"
              />
            </CardHeader>
            <CardContent>
              <BilingualForm
                onSubmit={handleSubmit}
                loading={formLoading}
                className="space-y-6"
              >
                <BilingualInput
                  labelAr="الاسم"
                  labelEn="Name"
                  valueAr={formData.nameAr}
                  valueEn={formData.nameEn}
                  onChange={(valueAr, valueEn) => updateFormData('name', valueAr, valueEn)}
                  required
                />
                
                <BilingualTextarea
                  labelAr="الوصف"
                  labelEn="Description"
                  valueAr={formData.descriptionAr}
                  valueEn={formData.descriptionEn}
                  onChange={(valueAr, valueEn) => updateFormData('description', valueAr, valueEn)}
                  rows={4}
                />
                
                <BilingualInput
                  labelAr="البريد الإلكتروني"
                  labelEn="Email"
                  valueAr={formData.emailAr}
                  valueEn={formData.emailEn}
                  onChange={(valueAr, valueEn) => updateFormData('email', valueAr, valueEn)}
                  type="email"
                  errorAr={formData.emailAr && !formData.emailAr.includes('@') ? 'يرجى إدخال بريد إلكتروني صحيح' : ''}
                  errorEn={formData.emailEn && !formData.emailEn.includes('@') ? 'Please enter a valid email' : ''}
                />
                
                <div className="p-4 border rounded-md">
                  <BilingualHeading
                    ar="عرض حقل واحد فقط"
                    en="Show Single Input Only"
                    as="h3"
                    className="text-lg font-medium mb-4"
                  />
                  
                  <BilingualInput
                    labelAr="الاسم"
                    labelEn="Name"
                    valueAr={formData.nameAr}
                    valueEn={formData.nameEn}
                    onChange={(valueAr, valueEn) => updateFormData('name', valueAr, valueEn)}
                    showBothInputs={false}
                    className="mb-4"
                  />
                  
                  <BilingualTextarea
                    labelAr="الوصف"
                    labelEn="Description"
                    valueAr={formData.descriptionAr}
                    valueEn={formData.descriptionEn}
                    onChange={(valueAr, valueEn) => updateFormData('description', valueAr, valueEn)}
                    showBothInputs={false}
                    rows={4}
                  />
                </div>
              </BilingualForm>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
