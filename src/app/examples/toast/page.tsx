'use client';

import { useState } from 'react';
import Link from 'next/link';
import { HomeIcon } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { bilingualToast } from '@/components/ui/bilingual-toast';

export default function ToastExamplePage() {
  const [activeTab, setActiveTab] = useState('bilingual');
  const [messageAr, setMessageAr] = useState('تم تنفيذ العملية بنجاح');
  const [messageEn, setMessageEn] = useState('Operation completed successfully');
  const [duration, setDuration] = useState(5000);

  // عرض إشعار ثنائي اللغة
  const showBilingualToast = (type: 'success' | 'error' | 'warning' | 'info') => {
    switch (type) {
      case 'success':
        bilingualToast.success(messageAr, messageEn, duration);
        break;
      case 'error':
        bilingualToast.error(messageAr, messageEn, duration);
        break;
      case 'warning':
        bilingualToast.warning(messageAr, messageEn, duration);
        break;
      case 'info':
        bilingualToast.info(messageAr, messageEn, duration);
        break;
    }
  };

  // عرض إشعار عادي
  const showRegularToast = (type: 'success' | 'error' | 'info' | 'warning') => {
    if (typeof window !== 'undefined') {
      window.showToast(
        `${messageAr} | ${messageEn}`,
        type,
        duration
      );
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
            <li className="inline-flex items-center">
              <Link href="/dashboard" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <HomeIcon className="h-4 w-4 ml-2" />
                الرئيسية
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/examples" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                  أمثلة
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-sm font-medium text-gray-500">الإشعارات</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">الإشعارات</h1>
        <h2 className="text-xl font-normal text-gray-700 mb-2">Toast Notifications</h2>
        <p className="text-muted-foreground">
          أمثلة على استخدام مكونات الإشعارات في تطبيق أمين بلس | Examples of using toast notification components in Amin Plus application
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="bilingual">إشعارات ثنائية اللغة | Bilingual Toasts</TabsTrigger>
          <TabsTrigger value="regular">إشعارات عادية | Regular Toasts</TabsTrigger>
        </TabsList>

        {/* إشعارات ثنائية اللغة - Bilingual Toasts */}
        <TabsContent value="bilingual" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>إشعارات ثنائية اللغة | Bilingual Toasts</CardTitle>
              <CardDescription>
                عرض إشعارات باللغتين العربية والإنجليزية | Display notifications in both Arabic and English
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="message-ar">الرسالة بالعربية | Message in Arabic</Label>
                  <Input
                    id="message-ar"
                    dir="rtl"
                    value={messageAr}
                    onChange={(e) => setMessageAr(e.target.value)}
                    placeholder="أدخل الرسالة بالعربية"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message-en">الرسالة بالإنجليزية | Message in English</Label>
                  <Input
                    id="message-en"
                    dir="ltr"
                    value={messageEn}
                    onChange={(e) => setMessageEn(e.target.value)}
                    placeholder="Enter message in English"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration">مدة العرض (بالمللي ثانية) | Duration (in milliseconds)</Label>
                <Input
                  id="duration"
                  type="number"
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value))}
                  min={1000}
                  max={10000}
                  step={1000}
                />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button onClick={() => showBilingualToast('success')} className="bg-green-600 hover:bg-green-700">
                  نجاح | Success
                </Button>
                <Button onClick={() => showBilingualToast('error')} className="bg-red-600 hover:bg-red-700">
                  خطأ | Error
                </Button>
                <Button onClick={() => showBilingualToast('warning')} className="bg-amber-600 hover:bg-amber-700">
                  تحذير | Warning
                </Button>
                <Button onClick={() => showBilingualToast('info')} className="bg-blue-600 hover:bg-blue-700">
                  معلومات | Info
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* إشعارات عادية - Regular Toasts */}
        <TabsContent value="regular" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>إشعارات عادية | Regular Toasts</CardTitle>
              <CardDescription>
                عرض إشعارات بتنسيق عادي | Display notifications in regular format
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="combined-message">الرسالة | Message</Label>
                <Input
                  id="combined-message"
                  value={`${messageAr} | ${messageEn}`}
                  readOnly
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration-regular">مدة العرض (بالمللي ثانية) | Duration (in milliseconds)</Label>
                <Input
                  id="duration-regular"
                  type="number"
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value))}
                  min={1000}
                  max={10000}
                  step={1000}
                />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button onClick={() => showRegularToast('success')} className="bg-green-600 hover:bg-green-700">
                  نجاح | Success
                </Button>
                <Button onClick={() => showRegularToast('error')} className="bg-red-600 hover:bg-red-700">
                  خطأ | Error
                </Button>
                <Button onClick={() => showRegularToast('warning')} className="bg-amber-600 hover:bg-amber-700">
                  تحذير | Warning
                </Button>
                <Button onClick={() => showRegularToast('info')} className="bg-blue-600 hover:bg-blue-700">
                  معلومات | Info
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
