'use client';

import { useState } from 'react';
import {HomeIcon, BarChart, Users, Package, Settings, FileText, ShoppingCart, Calendar} from 'lucide-react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/language-context';
import { BilingualText, BilingualHeading, BilingualDescription } from '@/components/common/bilingual-text';
import { BilingualTable } from '@/components/common/bilingual-table';
import { BilingualCard, BilingualStatCard, BilingualActionCard } from '@/components/common/bilingual-card';
import { BilingualList, BilingualFeatureList } from '@/components/common/bilingual-list';
import { BilingualBreadcrumb, BilingualNav } from '@/components/common/bilingual-navigation';
import { BilingualTabs, BilingualTabsCard } from '@/components/common/bilingual-tabs';
export default function BilingualAdvancedPage() {
  const { language } = useLanguage();
  const [activeTab, setActiveTab] = useState('tables');

  // بيانات للجدول
  const tableData = [
    { id: 1, nameAr: 'منتج 1', nameEn: 'Product 1', price: 100, quantity: 10 },
    { id: 2, nameAr: 'منتج 2', nameEn: 'Product 2', price: 200, quantity: 5 },
    { id: 3, nameAr: 'منتج 3', nameEn: 'Product 3', price: 150, quantity: 8 },
    { id: 4, nameAr: 'منتج 4', nameEn: 'Product 4', price: 300, quantity: 3 },
    { id: 5, nameAr: 'منتج 5', nameEn: 'Product 5', price: 250, quantity: 6 },
  ];

  // أعمدة الجدول
  const tableColumns = [
    {
      accessorKey: 'id',
      headerAr: '#',
      headerEn: '#',
      className: 'w-16',
    },
    {
      accessorKey: 'nameAr',
      headerAr: 'اسم المنتج',
      headerEn: 'Product Name',
      cell: (item) => (
        <BilingualText
          ar={item.nameAr}
          en={item.nameEn}
        />
      ),
    },
    {
      accessorKey: 'price',
      headerAr: 'السعر',
      headerEn: 'Price',
      cell: (item) => (
        <span>{item.price} AED</span>
      ),
      footerAr: 'المجموع: 1000 AED',
      footerEn: 'Total: 1000 AED',
    },
    {
      accessorKey: 'quantity',
      headerAr: 'الكمية',
      headerEn: 'Quantity',
      cell: (item) => (
        <span>{item.quantity}</span>
      ),
      footerAr: 'المجموع: 32',
      footerEn: 'Total: 32',
    },
  ];

  // بيانات للقائمة
  const listItems = [
    {
      textAr: 'دعم اللغة العربية والإنجليزية',
      textEn: 'Support for Arabic and English languages',
    },
    {
      textAr: 'واجهة مستخدم ثنائية اللغة',
      textEn: 'Bilingual user interface',
    },
    {
      textAr: 'تبديل سهل بين اللغات',
      textEn: 'Easy language switching',
    },
    {
      textAr: 'دعم الاتجاه من اليمين إلى اليسار',
      textEn: 'Right-to-left (RTL) support',
    },
    {
      textAr: 'تنسيق التاريخ والوقت حسب اللغة',
      textEn: 'Date and time formatting based on language',
    },
  ];

  // بيانات للميزات
  const features = [
    {
      titleAr: 'إدارة المخزون',
      titleEn: 'Inventory Management',
      descriptionAr: 'إدارة المخزون بكفاءة وسهولة',
      descriptionEn: 'Efficiently manage your inventory',
      included: true,
    },
    {
      titleAr: 'إدارة المبيعات',
      titleEn: 'Sales Management',
      descriptionAr: 'تتبع المبيعات وإدارة العملاء',
      descriptionEn: 'Track sales and manage customers',
      included: true,
    },
    {
      titleAr: 'التقارير المتقدمة',
      titleEn: 'Advanced Reporting',
      descriptionAr: 'تقارير تفصيلية ورسوم بيانية',
      descriptionEn: 'Detailed reports and charts',
      included: true,
    },
    {
      titleAr: 'التكامل مع المحاسبة',
      titleEn: 'Accounting Integration',
      descriptionAr: 'التكامل مع برامج المحاسبة الشائعة',
      descriptionEn: 'Integration with popular accounting software',
      included: false,
    },
    {
      titleAr: 'تطبيق الجوال',
      titleEn: 'Mobile App',
      descriptionAr: 'الوصول إلى النظام من أي مكان',
      descriptionEn: 'Access the system from anywhere',
      included: false,
    },
  ];

  // بيانات للتبويبات
  const tabsData = [
    {
      id: 'tab1',
      titleAr: 'المبيعات',
      titleEn: 'Sales',
      icon: <ShoppingCart className="h-4 w-4" />,
      contentAr: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">إحصائيات المبيعات</h3>
          <p>هذا القسم يعرض إحصائيات المبيعات والتقارير.</p>
        </div>
      ),
      contentEn: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Sales Statistics</h3>
          <p>This section displays sales statistics and reports.</p>
        </div>
      ),
    },
    {
      id: 'tab2',
      titleAr: 'العملاء',
      titleEn: 'Customers',
      icon: <Users className="h-4 w-4" />,
      contentAr: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">إدارة العملاء</h3>
          <p>هذا القسم يعرض معلومات العملاء وسجل المعاملات.</p>
        </div>
      ),
      contentEn: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Customer Management</h3>
          <p>This section displays customer information and transaction history.</p>
        </div>
      ),
    },
    {
      id: 'tab3',
      titleAr: 'المنتجات',
      titleEn: 'Products',
      icon: <Package className="h-4 w-4" />,
      contentAr: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">إدارة المنتجات</h3>
          <p>هذا القسم يعرض قائمة المنتجات والمخزون.</p>
        </div>
      ),
      contentEn: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Product Management</h3>
          <p>This section displays product list and inventory.</p>
        </div>
      ),
    },
  ];

  // بيانات للتنقل
  const navItems = [
    {
      type: 'item' as const,
      labelAr: 'لوحة التحكم',
      labelEn: 'Dashboard',
      href: '/dashboard',
      icon: <BarChart className="h-4 w-4" />,
    },
    {
      type: 'item' as const,
      labelAr: 'المبيعات',
      labelEn: 'Sales',
      href: '/sales',
      icon: <ShoppingCart className="h-4 w-4" />,
    },
    {
      type: 'group' as const,
      labelAr: 'إدارة المنتجات',
      labelEn: 'Product Management',
      icon: <Package className="h-4 w-4" />,
      items: [
        {
          labelAr: 'المنتجات',
          labelEn: 'Products',
          href: '/products',
          icon: <Package className="h-4 w-4" />,
        },
        {
          labelAr: 'الفئات',
          labelEn: 'Categories',
          href: '/categories',
        },
        {
          labelAr: 'المخزون',
          labelEn: 'Inventory',
          href: '/inventory',
        },
      ],
    },
    {
      type: 'item' as const,
      labelAr: 'العملاء',
      labelEn: 'Customers',
      href: '/customers',
      icon: <Users className="h-4 w-4" />,
    },
    {
      type: 'item' as const,
      labelAr: 'التقارير',
      labelEn: 'Reports',
      href: '/reports',
      icon: <FileText className="h-4 w-4" />,
    },
    {
      type: 'item' as const,
      labelAr: 'الإعدادات',
      labelEn: 'Settings',
      href: '/settings',
      icon: <Settings className="h-4 w-4" />,
    },
  ];

  // بيانات لمسار التنقل
  const breadcrumbItems = [
    {
      labelAr: 'الرئيسية',
      labelEn: 'Home',
      href: '/dashboard',
      icon: <HomeIcon className="h-4 w-4" />,
    },
    {
      labelAr: 'أمثلة',
      labelEn: 'Examples',
      href: '/examples',
    },
    {
      labelAr: 'المكونات المتقدمة',
      labelEn: 'Advanced Components',
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <BilingualBreadcrumb
          items={breadcrumbItems}
          className="mb-4"
        />
      </div>

      <div className="mb-6">
        <BilingualHeading
          ar="المكونات ثنائية اللغة المتقدمة"
          en="Advanced Bilingual Components"
          as="h1"
          className="text-3xl font-bold mb-2"
        />
        <BilingualDescription
          ar="أمثلة على استخدام المكونات ثنائية اللغة المتقدمة في تطبيق أمين بلس"
          en="Examples of using advanced bilingual components in Amin Plus application"
        />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 md:grid-cols-5 mb-4">
          <TabsTrigger value="tables">
            <BilingualText
              ar="الجداول"
              en="Tables"
              showBothLanguages={false}
            />
          </TabsTrigger>
          <TabsTrigger value="cards">
            <BilingualText
              ar="البطاقات"
              en="Cards"
              showBothLanguages={false}
            />
          </TabsTrigger>
          <TabsTrigger value="lists">
            <BilingualText
              ar="القوائم"
              en="Lists"
              showBothLanguages={false}
            />
          </TabsTrigger>
          <TabsTrigger value="navigation">
            <BilingualText
              ar="التنقل"
              en="Navigation"
              showBothLanguages={false}
            />
          </TabsTrigger>
          <TabsTrigger value="tabs">
            <BilingualText
              ar="التبويبات"
              en="Tabs"
              showBothLanguages={false}
            />
          </TabsTrigger>
        </TabsList>

        {/* مكونات الجداول - Table Components */}
        <TabsContent value="tables" className="space-y-6">
          <Card>
            <CardHeader>
              <BilingualHeading
                ar="جدول ثنائي اللغة"
                en="Bilingual Table"
                as={CardTitle}
              />
              <BilingualDescription
                ar="عرض البيانات في جدول ثنائي اللغة"
                en="Display data in a bilingual table"
              />
            </CardHeader>
            <CardContent>
              <BilingualTable
                data={tableData}
                columns={tableColumns}
                captionAr="قائمة المنتجات"
                captionEn="Product List"
                pagination={{
                  pageIndex: 0,
                  pageSize: 5,
                  pageCount: 2,
                  onPageChange: () => {},
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* مكونات البطاقات - Card Components */}
        <TabsContent value="cards" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <BilingualCard
              titleAr="بطاقة ثنائية اللغة"
              titleEn="Bilingual Card"
              descriptionAr="وصف البطاقة باللغة العربية"
              descriptionEn="Card description in English"
              contentAr={
                <p>هذا محتوى البطاقة باللغة العربية. يمكن أن يكون أي محتوى React.</p>
              }
              contentEn={
                <p>This is the card content in English. It can be any React content.</p>
              }
              footerAr="تذييل البطاقة"
              footerEn="Card footer"
            />

            <BilingualStatCard
              titleAr="إجمالي المبيعات"
              titleEn="Total Sales"
              valueAr="10,500 درهم"
              valueEn="AED 10,500"
              changeAr="زيادة 15% عن الشهر الماضي"
              changeEn="15% increase from last month"
              changeType="positive"
              icon={<BarChart className="h-8 w-8 text-blue-500" />}
            />

            <BilingualActionCard
              titleAr="إنشاء فاتورة جديدة"
              titleEn="Create New Invoice"
              descriptionAr="إنشاء فاتورة جديدة للعملاء"
              descriptionEn="Create a new invoice for customers"
              buttonTextAr="إنشاء فاتورة"
              buttonTextEn="Create Invoice"
              href="/invoices/new"
              icon={<FileText className="h-8 w-8 text-green-500" />}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <BilingualStatCard
              titleAr="العملاء"
              titleEn="Customers"
              valueAr="1,234"
              valueEn="1,234"
              changeAr="زيادة 5%"
              changeEn="5% increase"
              changeType="positive"
              icon={<Users className="h-6 w-6 text-indigo-500" />}
              showBothLanguages={false}
            />

            <BilingualStatCard
              titleAr="المنتجات"
              titleEn="Products"
              valueAr="567"
              valueEn="567"
              changeAr="زيادة 12%"
              changeEn="12% increase"
              changeType="positive"
              icon={<Package className="h-6 w-6 text-amber-500" />}
              showBothLanguages={false}
            />

            <BilingualStatCard
              titleAr="الطلبات"
              titleEn="Orders"
              valueAr="89"
              valueEn="89"
              changeAr="نقص 3%"
              changeEn="3% decrease"
              changeType="negative"
              icon={<ShoppingCart className="h-6 w-6 text-red-500" />}
              showBothLanguages={false}
            />

            <BilingualStatCard
              titleAr="المواعيد"
              titleEn="Appointments"
              valueAr="45"
              valueEn="45"
              changeAr="بدون تغيير"
              changeEn="No change"
              changeType="neutral"
              icon={<Calendar className="h-6 w-6 text-purple-500" />}
              showBothLanguages={false}
            />
          </div>
        </TabsContent>

        {/* مكونات القوائم - List Components */}
        <TabsContent value="lists" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <BilingualHeading
                  ar="قائمة ثنائية اللغة"
                  en="Bilingual List"
                  as={CardTitle}
                />
                <BilingualDescription
                  ar="عرض قائمة بنود ثنائية اللغة"
                  en="Display a list of bilingual items"
                />
              </CardHeader>
              <CardContent>
                <BilingualList
                  items={listItems}
                  iconType="bullet"
                  showBothLanguages={true}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <BilingualHeading
                  ar="قائمة ميزات ثنائية اللغة"
                  en="Bilingual Feature List"
                  as={CardTitle}
                />
                <BilingualDescription
                  ar="عرض قائمة ميزات ثنائية اللغة"
                  en="Display a list of bilingual features"
                />
              </CardHeader>
              <CardContent>
                <BilingualFeatureList
                  features={features}
                  showBothLanguages={true}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* مكونات التنقل - Navigation Components */}
        <TabsContent value="navigation" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <BilingualHeading
                  ar="مسار التنقل ثنائي اللغة"
                  en="Bilingual Breadcrumb"
                  as={CardTitle}
                />
                <BilingualDescription
                  ar="عرض مسار التنقل بشكل ثنائي اللغة"
                  en="Display breadcrumb navigation bilingually"
                />
              </CardHeader>
              <CardContent>
                <BilingualBreadcrumb
                  items={breadcrumbItems}
                  showBothLanguages={true}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <BilingualHeading
                  ar="قائمة التنقل ثنائية اللغة"
                  en="Bilingual Navigation"
                  as={CardTitle}
                />
                <BilingualDescription
                  ar="عرض قائمة تنقل ثنائية اللغة"
                  en="Display bilingual navigation menu"
                />
              </CardHeader>
              <CardContent>
                <BilingualNav
                  items={navItems}
                  showBothLanguages={true}
                  className="max-w-xs"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* مكونات التبويبات - Tabs Components */}
        <TabsContent value="tabs" className="space-y-6">
          <BilingualTabsCard
            tabs={tabsData}
            titleAr="تبويبات ثنائية اللغة"
            titleEn="Bilingual Tabs"
            descriptionAr="عرض محتوى في تبويبات ثنائية اللغة"
            descriptionEn="Display content in bilingual tabs"
            showBothLanguages={true}
            showBothContents={true}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <BilingualHeading
                  ar="تبويبات أفقية"
                  en="Horizontal Tabs"
                  as={CardTitle}
                />
              </CardHeader>
              <CardContent>
                <BilingualTabs
                  tabs={tabsData}
                  orientation="horizontal"
                  showBothLanguages={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <BilingualHeading
                  ar="تبويبات عمودية"
                  en="Vertical Tabs"
                  as={CardTitle}
                />
              </CardHeader>
              <CardContent>
                <BilingualTabs
                  tabs={tabsData}
                  orientation="vertical"
                  showBothLanguages={false}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
