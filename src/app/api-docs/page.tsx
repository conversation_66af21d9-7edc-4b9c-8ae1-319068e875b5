"use client";

import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

export default function ApiDocsPage() {
  const [activeTab, setActiveTab] = useState('invoices');

  return (
    <div className="p-6 max-w-5xl mx-auto">
      <h1 className="text-3xl font-semibold mb-2">توثيق واجهة برمجة التطبيقات</h1>
      <h2 className="text-xl font-normal text-gray-700 mb-4">API Documentation</h2>
      <p className="mb-8 text-gray-600">
        هذه الصفحة توثق واجهات API المتاحة في نظام أمين بلس | Amin Plus.
        <br />
        This page documents the available APIs in the Amin Plus | أمين بلس system.
      </p>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-8">
          <TabsTrigger value="invoices">الفواتير</TabsTrigger>
          <TabsTrigger value="customers">العملاء</TabsTrigger>
          <TabsTrigger value="products">المنتجات</TabsTrigger>
          <TabsTrigger value="users">المستخدمين</TabsTrigger>
        </TabsList>

        <TabsContent value="invoices">
          <h2 className="text-2xl font-medium mb-4">إدارة الفواتير</h2>

          <div className="mb-8">
            <div className="bg-blue-50 p-2 inline-block rounded mb-2">GET /api/invoices</div>
            <h3 className="text-lg font-medium mb-2">الحصول على قائمة الفواتير</h3>
            <p className="mb-2">يمكن تصفية النتائج باستخدام معلمات الاستعلام التالية:</p>
            <ul className="list-disc ps-6 mb-4">
              <li>customerId: تصفية حسب معرف العميل</li>
              <li>status: تصفية حسب حالة الفاتورة (DRAFT, PAID, SENT, OVERDUE, CANCELLED)</li>
            </ul>
            <div className="mb-4">
              <h4 className="font-medium mb-2">استجابة نموذجية:</h4>
              <pre className="bg-gray-100 p-4 rounded overflow-auto">
                {JSON.stringify([
                  {
                    id: "cln4n32k0000cx0dlnuki20h2",
                    invoiceNumber: "INV-2023-00001",
                    customerId: "cln4n32j0000ax0dl0qnf82k2",
                    userId: "cln4n32a0000bx0dl7y8xc2g1",
                    status: "DRAFT",
                    issueDate: "2023-10-01T10:45:00.000Z",
                    subtotal: 1000,
                    taxRate: 15,
                    taxAmount: 150,
                    discount: 0,
                    total: 1150,
                    customer: {
                      id: "cln4n32j0000ax0dl0qnf82k2",
                      name: "عميل نموذجي"
                    },
                    items: [
                      {
                        id: "cln4n32l0000dx0dl5xdv80h7",
                        productId: "cln4n32i0000cx0dl5zd1g2j7",
                        quantity: 2,
                        unitPrice: 500,
                        total: 1000,
                        product: {
                          name: "منتج نموذجي"
                        }
                      }
                    ]
                  }
                ], null, 2)}
              </pre>
            </div>
          </div>

          <div className="mb-8">
            <div className="bg-green-50 p-2 inline-block rounded mb-2">POST /api/invoices</div>
            <h3 className="text-lg font-medium mb-2">إنشاء فاتورة جديدة</h3>
            <p className="mb-4">يتطلب توفير البيانات التالية:</p>
            <pre className="bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify({
                customerId: "cln4n32j0000ax0dl0qnf82k2",
                status: "DRAFT", // اختياري، الافتراضي "DRAFT"
                dueDate: "2023-11-01", // اختياري
                taxRate: 15, // اختياري، الافتراضي 15
                discount: 0, // اختياري، الافتراضي 0
                notes: "ملاحظات على الفاتورة", // اختياري
                items: [
                  {
                    productId: "cln4n32i0000cx0dl5zd1g2j7",
                    quantity: 2,
                    unitPrice: 500,
                    description: "وصف اختياري",
                    taxRate: 15, // اختياري، الافتراضي هو قيمة taxRate في الفاتورة
                    discount: 0 // اختياري، الافتراضي 0
                  }
                ]
              }, null, 2)}
            </pre>
          </div>

          {/* وهكذا لباقي عمليات API */}
        </TabsContent>

        <TabsContent value="customers">
          {/* توثيق API العملاء */}
          <h2 className="text-2xl font-medium mb-4">إدارة العملاء</h2>
          {/* محتوى التوثيق */}
        </TabsContent>

        <TabsContent value="products">
          {/* توثيق API المنتجات */}
          <h2 className="text-2xl font-medium mb-4">إدارة المنتجات</h2>
          {/* محتوى التوثيق */}
        </TabsContent>

        <TabsContent value="users">
          {/* توثيق API المستخدمين */}
          <h2 className="text-2xl font-medium mb-4">إدارة المستخدمين</h2>
          {/* محتوى التوثيق */}
        </TabsContent>
      </Tabs>
    </div>
  );
}
