"use client";

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

export default function AuthErrorPage() {
    const searchParams = useSearchParams();
    const error = searchParams.get('error');

    // Map error codes to Arabic messages
    const errorMessages: Record<string, string> = {
        'CredentialsSignin': 'فشل تسجيل الدخول. يرجى التحقق من بيانات الاعتماد الخاصة بك.',
        'OAuthSignin': 'حدث خطأ أثناء محاولة تسجيل الدخول باستخدام مزود خارجي.',
        'OAuthCallback': 'حدث خطأ أثناء استلام بيانات الإشارة من مزود خارجي.',
        'OAuthCreateAccount': 'فشل إنشاء حساب المستخدم.',
        'EmailCreateAccount': 'فشل إنشاء حساب المستخدم باستخدام البريد الإلكتروني.',
        'Callback': 'حدث خطأ أثناء معالجة طلبك.',
        'OAuthAccountNotLinked': 'البريد الإلكتروني مستخدم بالفعل مع حساب آخر.',
        'EmailSignin': 'فشل إرسال رابط تسجيل الدخول عبر البريد الإلكتروني.',
        'CredentialsSignup': 'فشل إنشاء حساب جديد.',
        'default': 'حدث خطأ غير معروف أثناء المصادقة.'
    };

    const errorMessage = error ? (errorMessages[error] || errorMessages.default) : errorMessages.default;

    return (
        <div className="flex min-h-screen items-center justify-center bg-gray-100">
            <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-md text-center">
                <h1 className="mb-6 text-2xl font-bold text-gray-900">خطأ في المصادقة</h1>
                <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md">
                    {errorMessage}
                </div>
                <Link
                    href="/auth/login"
                    className="inline-block rounded-md bg-indigo-600 py-2 px-4 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                    العودة إلى صفحة تسجيل الدخول
                </Link>
            </div>
        </div>
    );
}
