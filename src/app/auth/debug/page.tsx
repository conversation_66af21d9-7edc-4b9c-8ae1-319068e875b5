'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/enhanced-card';
import { Button } from '@/components/ui/enhanced-button';
import { Badge } from '@/components/ui/badge';

export default function AuthDebugPage() {
  const { data: session, status } = useSession();
  const [authConfig, setAuthConfig] = useState<any>(null);
  const [apiTest, setApiTest] = useState<any>(null);

  useEffect(() => {
    // فحص تكوين NextAuth
    const checkAuthConfig = async () => {
      try {
        const response = await fetch('/api/auth/providers');
        const providers = await response.json();
        setAuthConfig({ providers, status: 'success' });
      } catch (error) {
        setAuthConfig({ error: error.message, status: 'error' });
      }
    };

    // فحص API
    const testApi = async () => {
      try {
        const response = await fetch('/api/health');
        const data = await response.json();
        setApiTest({ data, status: response.status });
      } catch (error) {
        setApiTest({ error: error.message, status: 'error' });
      }
    };

    checkAuthConfig();
    testApi();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'authenticated':
        return <Badge variant="default" className="bg-green-500">مصادق عليه</Badge>;
      case 'unauthenticated':
        return <Badge variant="secondary">غير مصادق عليه</Badge>;
      case 'loading':
        return <Badge variant="outline">جاري التحميل...</Badge>;
      default:
        return <Badge variant="destructive">غير معروف</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">تشخيص NextAuth</h1>
        <p className="text-muted-foreground">فحص حالة المصادقة والتكوين</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* حالة الجلسة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              حالة الجلسة
              {getStatusBadge(status)}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>الحالة:</strong> {status}
            </div>
            {session && (
              <div className="space-y-2">
                <div><strong>المستخدم:</strong> {session.user?.name || 'غير محدد'}</div>
                <div><strong>البريد الإلكتروني:</strong> {session.user?.email || 'غير محدد'}</div>
                <div><strong>انتهاء الصلاحية:</strong> {session.expires || 'غير محدد'}</div>
              </div>
            )}
            {!session && status === 'unauthenticated' && (
              <div className="text-muted-foreground">
                لا توجد جلسة نشطة
              </div>
            )}
          </CardContent>
        </Card>

        {/* تكوين NextAuth */}
        <Card>
          <CardHeader>
            <CardTitle>تكوين NextAuth</CardTitle>
          </CardHeader>
          <CardContent>
            {authConfig ? (
              <div className="space-y-2">
                {authConfig.status === 'success' ? (
                  <div>
                    <div className="text-green-600 mb-2">✅ التكوين صحيح</div>
                    <div><strong>المزودون المتاحون:</strong></div>
                    <ul className="list-disc list-inside ml-4">
                      {Object.keys(authConfig.providers || {}).map(provider => (
                        <li key={provider}>{provider}</li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <div>
                    <div className="text-red-600 mb-2">❌ خطأ في التكوين</div>
                    <div className="text-sm text-muted-foreground">
                      {authConfig.error}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div>جاري فحص التكوين...</div>
            )}
          </CardContent>
        </Card>

        {/* فحص API */}
        <Card>
          <CardHeader>
            <CardTitle>فحص API</CardTitle>
          </CardHeader>
          <CardContent>
            {apiTest ? (
              <div className="space-y-2">
                {apiTest.status === 200 ? (
                  <div>
                    <div className="text-green-600 mb-2">✅ API يعمل بشكل صحيح</div>
                    <div><strong>الحالة:</strong> {apiTest.status}</div>
                  </div>
                ) : (
                  <div>
                    <div className="text-red-600 mb-2">❌ خطأ في API</div>
                    <div className="text-sm text-muted-foreground">
                      {apiTest.error || `HTTP ${apiTest.status}`}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div>جاري فحص API...</div>
            )}
          </CardContent>
        </Card>

        {/* متغيرات البيئة */}
        <Card>
          <CardHeader>
            <CardTitle>متغيرات البيئة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>NEXTAUTH_URL:</strong> 
              <span className="ml-2 text-sm font-mono">
                {process.env.NEXT_PUBLIC_NEXTAUTH_URL || 'غير محدد'}
              </span>
            </div>
            <div>
              <strong>NODE_ENV:</strong> 
              <span className="ml-2 text-sm font-mono">
                {process.env.NODE_ENV}
              </span>
            </div>
            <div>
              <strong>NEXTAUTH_SECRET:</strong> 
              <span className="ml-2 text-sm font-mono">
                {process.env.NEXTAUTH_SECRET ? '✅ محدد' : '❌ غير محدد'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-center space-x-4">
        <Button 
          onClick={() => window.location.reload()}
          variant="outline"
        >
          إعادة تحميل الصفحة
        </Button>
        <Button 
          onClick={() => window.location.href = '/auth/login'}
          variant="default"
        >
          صفحة تسجيل الدخول
        </Button>
      </div>
    </div>
  );
}
