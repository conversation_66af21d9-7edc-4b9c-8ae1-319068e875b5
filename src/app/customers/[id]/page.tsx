'use client';

import { useEffect, useState, use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { getStatusClass, getStatusText } from '@/lib/invoice-helpers';
import { formatCurrency } from '@/lib/utils';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, Pencil, Trash2 } from 'lucide-react';

interface Invoice {
  id: string;
  number: string;
  date: string;
  status: string;
  total: number;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string | null;
  address: string | null;
  taxNumber: string | null;
  contactPerson: string | null;
  notes: string | null;
  invoices: Invoice[];
}

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  const router = useRouter();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        const response = await fetch(`/api/customers/${resolvedParams.id}`);
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('العميل غير موجود');
          }
          throw new Error('فشل في جلب بيانات العميل');
        }

        const data = await response.json();
        setCustomer(data);
      } catch (err: any) {
        console.error('خطأ في جلب بيانات العميل:', err);
        setError(err.message || 'حدث خطأ غير متوقع');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [resolvedParams.id]);

  const handleDeleteClick = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      const response = await fetch(`/api/customers/${resolvedParams.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف العميل');
      }

      router.push('/dashboard/customers');
      router.refresh();
    } catch (err: any) {
      console.error('خطأ أثناء حذف العميل:', err);
      setError(err.message || 'حدث خطأ أثناء حذف العميل');
      setIsDeleting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spinner className="h-10 w-10" />
        <span className="mr-2">جاري تحميل بيانات العميل...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="max-w-md mx-auto my-10">
        <AlertTitle>خطأ</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!customer) {
    return (
      <div className="text-center py-10 bg-gray-50 rounded-lg max-w-md mx-auto">
        <p className="text-gray-600 mb-4">لا توجد بيانات للعميل</p>
        <Button onClick={() => router.push('/dashboard/customers')}>
          <ArrowRight className="ml-2 h-4 w-4" />
          العودة لقائمة العملاء
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* رأس الصفحة */}
      <div className="mb-8 flex justify-between items-center">
        <h1 className="text-2xl font-bold">معلومات العميل</h1>
        <div className="flex space-x-2 space-x-reverse rtl:space-x-reverse">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/customers')}
            className="ml-2"
          >
            <ArrowRight className="ml-2 h-4 w-4" />
            العودة
          </Button>

          <Link href={`/dashboard/customers/${resolvedParams.id}/edit`}>
            <Button variant="secondary" className="ml-2">
              <Pencil className="ml-2 h-4 w-4" />
              تعديل
            </Button>
          </Link >

          <Button
            variant="destructive"
            onClick={handleDeleteClick}
            disabled={isDeleting}
          >
            <Trash2 className="ml-2 h-4 w-4" />
            {isDeleting ? 'جاري الحذف...' : 'حذف'}
          </Button>
        </div >
      </div >

      {/* بطاقة معلومات العميل */}
      < Card className="mb-8" >
        <CardHeader>
          <CardTitle>بيانات العميل الأساسية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-gray-500 text-sm">الاسم</p>
              <p className="font-medium">{customer.name}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">البريد الإلكتروني</p>
              <p className="font-medium">{customer.email || '-'}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">رقم الهاتف</p>
              <p className="font-medium">{customer.phone || '-'}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">الرقم الضريبي</p>
              <p className="font-medium">{customer.taxNumber || '-'}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">شخص التواصل</p>
              <p className="font-medium">{customer.contactPerson || '-'}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">العنوان</p>
              <p className="font-medium">{customer.address || '-'}</p>
            </div>
            {customer.notes && (
              <div className="md:col-span-2">
                <p className="text-gray-500 text-sm">ملاحظات</p>
                <p className="font-medium">{customer.notes}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card >

      {/* قائمة الفواتير */}
      < div className="mb-4 flex justify-between items-center" >
        <h2 className="text-xl font-bold">فواتير العميل</h2>
        <Link href={`/dashboard/invoices/new?customerId=${customer.id}`}>
          <Button size="sm" variant="outline">إنشاء فاتورة جديدة</Button>
        </Link>
      </div >

      {
        customer.invoices.length === 0 ? (
          <div className="text-center py-10 bg-gray-50 rounded-lg">
            <p className="text-gray-600 mb-4">لا توجد فواتير لهذا العميل بعد.</p>
            <Link href={`/dashboard/invoices/new?customerId=${customer.id}`}>
              <Button>إنشاء فاتورة جديدة</Button>
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto rounded-lg border">
            <table className="min-w-full bg-white">
              <thead className="bg-gray-100 text-gray-700">
                <tr>
                  <th className="py-3 px-4 text-right">رقم الفاتورة</th>
                  <th className="py-3 px-4 text-right">التاريخ</th>
                  <th className="py-3 px-4 text-right">المبلغ</th>
                  <th className="py-3 px-4 text-right">الحالة</th>
                  <th className="py-3 px-4 text-right">إجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {customer.invoices.map((invoice) => (
                  <tr key={invoice.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">{invoice.number}</td>
                    <td className="py-3 px-4">
                      {new Date(invoice.date).toLocaleDateString('ar-EG')}
                    </td>
                    <td className="py-3 px-4">{formatCurrency(invoice.total)}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(invoice.status as any)}`}>
                        {getStatusText(invoice.status as any)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <Link
                        href={`/dashboard/invoices/${invoice.id}`} // صحيح
                        className="text-blue-500 hover:text-blue-700"
                      >
                        عرض
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )
      }
    </div >
  );
}

