'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * صفحة إعادة توجيه للتقارير
 * Redirect page for reports
 */
export default function ReportsRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // إعادة توجيه المستخدم إلى صفحة التقارير في لوحة التحكم
    router.replace('/dashboard/reports');
  }, [router]);

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-t-4 border-gray-200 border-t-primary mx-auto mb-4"></div>
        <p className="text-lg">جاري التوجيه إلى صفحة التقارير...</p>
        <p className="text-sm text-gray-500">Redirecting to reports page...</p>
      </div>
    </div>
  );
}