import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Button, ButtonGroup, FloatingActionButton, TooltipButton } from '@/components/ui/enhanced-button';

describe('Enhanced Button Components', () => {
  describe('Button', () => {
    it('renders with default props', () => {
      render(<Button>Test Button</Button>);
      const button = screen.getByRole('button', { name: /test button/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('bg-primary');
    });

    it('renders with different variants', () => {
      const { rerender } = render(<Button variant="destructive">Delete</Button>);
      expect(screen.getByRole('button')).toHaveClass('bg-destructive');

      rerender(<Button variant="outline">Outline</Button>);
      expect(screen.getByRole('button')).toHaveClass('border-input');

      rerender(<Button variant="ghost">Ghost</Button>);
      expect(screen.getByRole('button')).toHaveClass('hover:bg-accent');
    });

    it('renders with different sizes', () => {
      const { rerender } = render(<Button size="sm">Small</Button>);
      expect(screen.getByRole('button')).toHaveClass('h-9');

      rerender(<Button size="lg">Large</Button>);
      expect(screen.getByRole('button')).toHaveClass('h-13');
    });

    it('shows loading state', () => {
      render(<Button loading>Loading</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('aria-busy', 'true');
    });

    it('handles click events', () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Click me</Button>);

      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('is disabled when disabled prop is true', () => {
      render(<Button disabled>Disabled</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('renders with left and right icons', () => {
      const LeftIcon = () => <span data-testid="left-icon">←</span>;
      const RightIcon = () => <span data-testid="right-icon">→</span>;

      render(
        <Button leftIcon={<LeftIcon />} rightIcon={<RightIcon />}>
          With Icons
        </Button>
      );

      expect(screen.getByTestId('left-icon')).toBeInTheDocument();
      expect(screen.getByTestId('right-icon')).toBeInTheDocument();
    });
  });

  describe('ButtonGroup', () => {
    it('renders multiple buttons', () => {
      render(
        <ButtonGroup>
          <Button>First</Button>
          <Button>Second</Button>
          <Button>Third</Button>
        </ButtonGroup>
      );

      expect(screen.getAllByRole('button')).toHaveLength(3);
    });

    it('applies correct styling to button group', () => {
      render(
        <ButtonGroup>
          <Button>First</Button>
          <Button>Second</Button>
        </ButtonGroup>
      );

      const group = screen.getByRole('group');
      expect(group).toHaveClass('flex'); // تم تحديث للتطابق مع التصميم الفعلي
    });
  });

  describe('FloatingActionButton', () => {
    it('renders FAB with correct styling', () => {
      render(<FloatingActionButton>+</FloatingActionButton>);
      const fab = screen.getByRole('button');
      expect(fab).toHaveClass('fixed', 'bottom-6', 'right-6', 'rounded-full');
    });

    it('handles click events', () => {
      const handleClick = jest.fn();
      render(<FloatingActionButton onClick={handleClick}>+</FloatingActionButton>);

      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('TooltipButton', () => {
    it('renders button with tooltip', () => {
      render(<TooltipButton tooltip="This is a tooltip">Hover me</TooltipButton>);
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('shows tooltip on hover', async () => {
      render(<TooltipButton tooltip="Tooltip text">Hover me</TooltipButton>);
      const button = screen.getByRole('button');

      fireEvent.mouseEnter(button);
      // Note: Testing tooltip visibility might require additional setup
      // depending on your tooltip implementation
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<Button aria-label="Custom label">Button</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Custom label');
    });

    it('supports keyboard navigation', () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Keyboard accessible</Button>);
      const button = screen.getByRole('button');

      button.focus();
      fireEvent.keyDown(button, { key: 'Enter' });
      expect(handleClick).toHaveBeenCalledTimes(1);

      fireEvent.keyDown(button, { key: ' ' });
      expect(handleClick).toHaveBeenCalledTimes(2);
    });
  });
});
