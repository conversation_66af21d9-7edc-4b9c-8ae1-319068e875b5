import { hash, compare, genSalt } from 'bcryptjs';
import crypto from 'crypto';

/**
 * تشفير كلمة المرور باستخدام bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12; // مستوى تشفير عالي
  const salt = await genSalt(saltRounds);
  return hash(password, salt);
}

/**
 * التحقق من كلمة المرور
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return compare(password, hashedPassword);
}

/**
 * إنشاء مفتاح عشوائي آمن
 */
export function generateSecureKey(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * إنشاء رمز تحقق عشوائي
 */
export function generateVerificationCode(length: number = 6): string {
  const chars = '0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * تشفير البيانات الحساسة باستخدام AES
 */
export function encryptSensitiveData(data: string, key?: string): { encrypted: string; iv: string } {
  const algorithm = 'aes-256-gcm';
  const secretKey = key || process.env.ENCRYPTION_KEY || generateSecureKey();
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipher(algorithm, secretKey);
  cipher.setAAD(Buffer.from('additional-data'));

  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  const authTag = cipher.getAuthTag();

  return {
    encrypted: encrypted + ':' + authTag.toString('hex'),
    iv: iv.toString('hex')
  };
}

/**
 * فك تشفير البيانات الحساسة
 */
export function decryptSensitiveData(encryptedData: string, iv: string, key?: string): string {
  const algorithm = 'aes-256-gcm';
  const secretKey = key || process.env.ENCRYPTION_KEY || '';

  const [encrypted, authTag] = encryptedData.split(':');

  const decipher = crypto.createDecipher(algorithm, secretKey);
  decipher.setAAD(Buffer.from('additional-data'));
  decipher.setAuthTag(Buffer.from(authTag, 'hex'));

  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

/**
 * إنشاء hash آمن للملفات
 */
export function generateFileHash(buffer: Buffer): string {
  return crypto.createHash('sha256').update(buffer).digest('hex');
}

/**
 * إنشاء توقيع رقمي للبيانات
 */
export function signData(data: string, secret?: string): string {
  const key = secret || process.env.SIGNING_SECRET || generateSecureKey();
  return crypto.createHmac('sha256', key).update(data).digest('hex');
}

/**
 * التحقق من التوقيع الرقمي
 */
export function verifySignature(data: string, signature: string, secret?: string): boolean {
  const key = secret || process.env.SIGNING_SECRET || '';
  const expectedSignature = crypto.createHmac('sha256', key).update(data).digest('hex');
  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
}

/**
 * إنشاء رمز JWT آمن
 */
export function generateSecureToken(payload: any, expiresIn: string = '24h'): string {
  const secret = process.env.JWT_SECRET || generateSecureKey();
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const now = Math.floor(Date.now() / 1000);
  const exp = now + parseExpiresIn(expiresIn);

  const jwtPayload = {
    ...payload,
    iat: now,
    exp: exp
  };

  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
  const encodedPayload = Buffer.from(JSON.stringify(jwtPayload)).toString('base64url');

  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64url');

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

/**
 * تحويل مدة انتهاء الصلاحية إلى ثوانٍ
 */
function parseExpiresIn(expiresIn: string): number {
  const match = expiresIn.match(/^(\d+)([smhd])$/);
  if (!match) return 24 * 60 * 60; // افتراضي: 24 ساعة

  const value = parseInt(match[1]);
  const unit = match[2];

  switch (unit) {
    case 's': return value;
    case 'm': return value * 60;
    case 'h': return value * 60 * 60;
    case 'd': return value * 24 * 60 * 60;
    default: return 24 * 60 * 60;
  }
}

/**
 * إنشاء كلمة مرور قوية عشوائية
 */
export function generateStrongPassword(length: number = 16): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  const allChars = lowercase + uppercase + numbers + symbols;
  let password = '';

  // ضمان وجود حرف واحد على الأقل من كل نوع
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];

  // إكمال باقي الطول
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // خلط الأحرف
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * تقييم قوة كلمة المرور
 */
export function assessPasswordStrength(password: string): {
  score: number;
  feedback: string[];
  strength: 'weak' | 'fair' | 'good' | 'strong';
} {
  const feedback: string[] = [];
  let score = 0;

  // الطول
  if (password.length >= 8) score += 1;
  else feedback.push('كلمة المرور قصيرة جداً (أقل من 8 أحرف)');

  if (password.length >= 12) score += 1;

  // الأحرف الصغيرة
  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('أضف أحرف صغيرة');

  // الأحرف الكبيرة
  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('أضف أحرف كبيرة');

  // الأرقام
  if (/[0-9]/.test(password)) score += 1;
  else feedback.push('أضف أرقام');

  // الرموز الخاصة
  if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) score += 1;
  else feedback.push('أضف رموز خاصة');

  // تجنب التكرار
  if (!/(.)\1{2,}/.test(password)) score += 1;
  else feedback.push('تجنب تكرار الأحرف');

  // تحديد القوة
  let strength: 'weak' | 'fair' | 'good' | 'strong';
  if (score <= 2) strength = 'weak';
  else if (score <= 4) strength = 'fair';
  else if (score <= 6) strength = 'good';
  else strength = 'strong';

  return { score, feedback, strength };
}
