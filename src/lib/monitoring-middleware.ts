import { NextRequest, NextResponse } from 'next/server';
import { performance } from 'perf_hooks';
import { applicationMonitor } from './monitoring';
import { logger } from './logger';
import { v4 as uuidv4 } from 'uuid';

interface RequestContext {
  requestId: string;
  startTime: number;
  method: string;
  url: string;
  userAgent?: string;
  ip?: string;
  userId?: string;
}

/**
 * Middleware للمراقبة والتسجيل
 */
export function createMonitoringMiddleware() {
  return async function monitoringMiddleware(
    request: NextRequest,
    next: () => Promise<NextResponse>
  ): Promise<NextResponse> {
    const requestId = uuidv4();
    const startTime = performance.now();

    const context: RequestContext = {
      requestId,
      startTime,
      method: request.method,
      url: request.url,
      userAgent: request.headers.get('user-agent') || undefined,
      ip: getClientIP(request)
    };

    // إضافة Request ID إلى headers
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-request-id', requestId);

    try {
      // تسجيل بداية الطلب
      logger.debug(`Request started: ${context.method} ${context.url}`, {
        requestId,
        ip: context.ip,
        userAgent: context.userAgent
      });

      // تنفيذ الطلب
      const response = await next();

      // حساب وقت الاستجابة
      const responseTime = performance.now() - startTime;
      const statusCode = response.status;

      // تسجيل المقاييس
      recordRequestMetrics(context, statusCode, responseTime);

      // تسجيل الطلب
      logger.logRequest(
        context.method,
        context.url,
        statusCode,
        responseTime,
        context.userId,
        context.ip,
        context.userAgent
      );

      // إضافة headers المراقبة إلى الاستجابة
      const responseHeaders = new Headers(response.headers);
      responseHeaders.set('x-request-id', requestId);
      responseHeaders.set('x-response-time', `${responseTime.toFixed(2)}ms`);

      return new NextResponse(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders
      });

    } catch (error) {
      const responseTime = performance.now() - startTime;

      // تسجيل الخطأ
      recordErrorMetrics(context, error);

      logger.error(`Request failed: ${context.method} ${context.url}`, error, {
        requestId,
        responseTime,
        ip: context.ip,
        userAgent: context.userAgent
      });

      // إعادة رمي الخطأ
      throw error;
    }
  };
}

/**
 * تسجيل مقاييس الطلب
 */
function recordRequestMetrics(
  context: RequestContext,
  statusCode: number,
  responseTime: number
): void {
  const tags = {
    method: context.method,
    status_code: statusCode.toString(),
    endpoint: getEndpointFromUrl(context.url)
  };

  // تسجيل وقت الاستجابة
  applicationMonitor.recordMetric('api_response_time', responseTime, tags, 'ms');

  // تسجيل عدد الطلبات
  applicationMonitor.recordMetric('api_requests_total', 1, tags, 'count');

  // تسجيل حالة الاستجابة
  if (statusCode >= 200 && statusCode < 300) {
    applicationMonitor.recordMetric('api_requests_success', 1, tags, 'count');
  } else if (statusCode >= 400 && statusCode < 500) {
    applicationMonitor.recordMetric('api_requests_client_error', 1, tags, 'count');
  } else if (statusCode >= 500) {
    applicationMonitor.recordMetric('api_requests_server_error', 1, tags, 'count');
  }

  // حساب معدل الخطأ
  const totalRequests = applicationMonitor.getMetricStats('api_requests_total')?.sum || 0;
  const errorRequests = (
    (applicationMonitor.getMetricStats('api_requests_client_error')?.sum || 0) +
    (applicationMonitor.getMetricStats('api_requests_server_error')?.sum || 0)
  );

  if (totalRequests > 0) {
    const errorRate = errorRequests / totalRequests;
    applicationMonitor.recordMetric('api_error_rate', errorRate, {}, 'percentage');
  }
}

/**
 * تسجيل مقاييس الأخطاء
 */
function recordErrorMetrics(context: RequestContext, error: any): void {
  const tags = {
    method: context.method,
    endpoint: getEndpointFromUrl(context.url),
    error_type: error.name || 'UnknownError'
  };

  applicationMonitor.recordMetric('api_errors_total', 1, tags, 'count');

  // تسجيل أنواع الأخطاء المختلفة
  if (error.name === 'ValidationError') {
    applicationMonitor.recordMetric('validation_errors', 1, tags, 'count');
  } else if (error.name === 'DatabaseError') {
    applicationMonitor.recordMetric('database_errors', 1, tags, 'count');
  } else if (error.name === 'AuthenticationError') {
    applicationMonitor.recordMetric('auth_errors', 1, tags, 'count');
  }
}

/**
 * استخراج endpoint من URL
 */
function getEndpointFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;

    // تبسيط المسار (إزالة IDs والمعاملات الديناميكية)
    return pathname
      .replace(/\/\d+/g, '/:id') // استبدال الأرقام بـ :id
      .replace(/\/[a-f0-9-]{36}/g, '/:uuid') // استبدال UUIDs بـ :uuid
      .replace(/\/[a-f0-9]{24}/g, '/:objectid'); // استبدال ObjectIDs بـ :objectid
  } catch {
    return '/unknown';
  }
}

/**
 * الحصول على عنوان IP الخاص بالعميل
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const real = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');

  if (cfConnectingIp) {
    return cfConnectingIp;
  }

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (real) {
    return real.trim();
  }

  return 'unknown';
}

/**
 * Middleware لمراقبة استخدام الذاكرة
 */
export function createMemoryMonitoringMiddleware() {
  return function memoryMonitoringMiddleware() {
    const memoryUsage = process.memoryUsage();

    // تسجيل استخدام الذاكرة
    applicationMonitor.recordMetric('memory_heap_used', memoryUsage.heapUsed, {}, 'bytes');
    applicationMonitor.recordMetric('memory_heap_total', memoryUsage.heapTotal, {}, 'bytes');
    applicationMonitor.recordMetric('memory_external', memoryUsage.external, {}, 'bytes');
    applicationMonitor.recordMetric('memory_rss', memoryUsage.rss, {}, 'bytes');

    // حساب نسبة استخدام الذاكرة
    const heapUsagePercentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    applicationMonitor.recordMetric('memory_usage_percentage', heapUsagePercentage, {}, 'percentage');
  };
}

/**
 * Middleware لمراقبة أداء قاعدة البيانات
 */
export function createDatabaseMonitoringWrapper<T extends (...args: any[]) => Promise<any>>(
  operation: string,
  table: string,
  fn: T
): T {
  return (async (...args: any[]) => {
    const startTime = performance.now();

    try {
      const result = await fn(...args);
      const duration = performance.now() - startTime;

      // تسجيل العملية الناجحة
      applicationMonitor.recordMetric('database_operation_duration', duration, {
        operation,
        table,
        status: 'success'
      }, 'ms');

      applicationMonitor.recordMetric('database_operations_total', 1, {
        operation,
        table,
        status: 'success'
      }, 'count');

      logger.logDatabaseOperation(operation, table, duration, true);

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;

      // تسجيل العملية الفاشلة
      applicationMonitor.recordMetric('database_operation_duration', duration, {
        operation,
        table,
        status: 'error'
      }, 'ms');

      applicationMonitor.recordMetric('database_operations_total', 1, {
        operation,
        table,
        status: 'error'
      }, 'count');

      applicationMonitor.recordMetric('database_connection_errors', 1, {
        operation,
        table
      }, 'count');

      logger.logDatabaseOperation(operation, table, duration, false, error.message);

      throw error;
    }
  }) as T;
}

/**
 * بدء المراقبة الدورية للنظام
 */
export function startSystemMonitoring(): void {
  const memoryMonitor = createMemoryMonitoringMiddleware();

  // مراقبة الذاكرة كل 30 ثانية
  setInterval(() => {
    memoryMonitor();
  }, 30000);

  // مراقبة معلومات النظام كل دقيقة
  setInterval(() => {
    const uptime = process.uptime();
    applicationMonitor.recordMetric('system_uptime', uptime, {}, 'seconds');

    const cpuUsage = process.cpuUsage();
    applicationMonitor.recordMetric('cpu_user_time', cpuUsage.user, {}, 'microseconds');
    applicationMonitor.recordMetric('cpu_system_time', cpuUsage.system, {}, 'microseconds');
  }, 60000);
}
