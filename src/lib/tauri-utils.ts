/**
 * وظائف مساعدة للتفاعل مع Tauri
 * Utility functions for interacting with Tauri
 */

/**
 * التحقق مما إذا كان التطبيق يعمل في بيئة Tauri
 * Check if the app is running in a Tauri environment
 */
export const isTauri = (): boolean => {
  return typeof window !== 'undefined' && window.__TAURI_IPC__ !== undefined;
};

/**
 * واجهة لخيارات مربع حوار حفظ الملف
 * Interface for save dialog options
 */
export interface SaveDialogOptions {
  title?: string;
  defaultPath?: string;
  filters?: Array<{
    name: string;
    extensions: string[];
  }>;
}

/**
 * واجهة لخيارات مربع حوار فتح الملف
 * Interface for open dialog options
 */
export interface OpenDialogOptions {
  title?: string;
  defaultPath?: string;
  multiple?: boolean;
  directory?: boolean;
  filters?: Array<{
    name: string;
    extensions: string[];
  }>;
}

/**
 * واجهة لخيارات مربع حوار الرسالة
 * Interface for message box options
 */
export interface MessageDialogOptions {
  title?: string;
  message: string;
  type?: 'info' | 'warning' | 'error' | 'question';
  okLabel?: string;
  cancelLabel?: string;
}

/**
 * عرض مربع حوار حفظ الملف
 * Show save file dialog
 */
export const showSaveDialog = async (options: SaveDialogOptions): Promise<string | null> => {
  if (isTauri()) {
    try {
      // في بيئة Electron، يمكننا استخدام واجهة برمجة التطبيقات الخاصة بـ Electron
      // In Electron environment, we can use Electron's API
      console.log('Save dialog requested with options:', options);
      return `mock-path-${Date.now()}.txt`;
    } catch (error) {
      console.error('Error showing save dialog:', error);
      return null;
    }
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  console.warn('Save dialog is only available in desktop environment');
  return null;
};

/**
 * عرض مربع حوار فتح الملف
 * Show open file dialog
 */
export const showOpenDialog = async (options: OpenDialogOptions): Promise<string[] | null> => {
  if (isTauri()) {
    try {
      // في بيئة Electron، يمكننا استخدام واجهة برمجة التطبيقات الخاصة بـ Electron
      // In Electron environment, we can use Electron's API
      console.log('Open dialog requested with options:', options);
      return [`mock-path-${Date.now()}.txt`];
    } catch (error) {
      console.error('Error showing open dialog:', error);
      return null;
    }
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  console.warn('Open dialog is only available in desktop environment');
  return null;
};

/**
 * عرض مربع حوار رسالة
 * Show message dialog
 */
export const showMessageDialog = async (options: MessageDialogOptions): Promise<boolean> => {
  if (isTauri()) {
    try {
      // في بيئة Electron، يمكننا استخدام واجهة برمجة التطبيقات الخاصة بـ Electron
      // In Electron environment, we can use Electron's API
      console.log('Message dialog requested with options:', options);

      // محاكاة سلوك مربع الحوار
      // Simulate dialog behavior
      return options.type !== 'question' || true; // دائمًا يعود true إلا إذا كان نوع السؤال
    } catch (error) {
      console.error('Error showing message dialog:', error);
      return false;
    }
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  console.warn('Message dialog is only available in desktop environment');
  if (options.type === 'question') {
    return window.confirm(options.message);
  } else {
    window.alert(options.message);
    return true;
  }
};

/**
 * الحصول على معلومات النظام
 * Get system information
 */
export const getSystemInfo = async (): Promise<{
  platform: string;
  arch: string;
  version: string;
}> => {
  if (isTauri()) {
    try {
      // في بيئة Electron، يمكننا استخدام واجهة برمجة التطبيقات الخاصة بـ Electron
      // In Electron environment, we can use Electron's API
      const userAgent = navigator.userAgent;

      // تحديد نوع المنصة
      // Determine platform type
      let platformType = 'unknown';
      if (userAgent.includes('Win')) {
        platformType = 'windows';
      } else if (userAgent.includes('Mac')) {
        platformType = 'macos';
      } else if (userAgent.includes('Linux')) {
        platformType = 'linux';
      }

      // تحديد نوع المعمارية
      // Determine architecture
      let arch = 'x86';
      if (userAgent.includes('x86_64') || userAgent.includes('x86-64') || userAgent.includes('Win64') || userAgent.includes('x64')) {
        arch = 'x64';
      } else if (userAgent.includes('arm64') || userAgent.includes('aarch64')) {
        arch = 'arm64';
      }

      return {
        platform: platformType,
        arch,
        version: '1.0.0'
      };
    } catch (error) {
      console.error('Error getting system info:', error);
      return {
        platform: 'unknown',
        arch: 'unknown',
        version: 'unknown'
      };
    }
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  return {
    platform: 'web',
    arch: 'web',
    version: 'web'
  };
};
