import {
  Product,
  InventoryItem,
  InventoryMovement,
  InventoryLocation,
  InventoryAlert,
  StockTake,
  StockTakeItem
} from '@/types/inventory';

// الحصول على جميع المنتجات
export function getProducts(): Product[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedProducts = localStorage.getItem('products');
  if (!savedProducts) {
    return [];
  }

  try {
    return JSON.parse(savedProducts);
  } catch (error) {
    console.error('خطأ في قراءة المنتجات:', error);
    return [];
  }
}

// حفظ المنتجات
export function saveProducts(products: Product[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('products', JSON.stringify(products));
}

// الحصول على منتج بواسطة المعرف
export function getProductById(id: string): Product | undefined {
  const products = getProducts();
  return products.find(product => product.id === id);
}

// الحصول على منتج بواسطة الباركود
export function getProductByBarcode(barcode: string): Product | undefined {
  const products = getProducts();
  return products.find(product => product.barcode === barcode);
}

// الحصول على منتج بواسطة SKU
export function getProductBySku(sku: string): Product | undefined {
  const products = getProducts();
  return products.find(product => product.sku === sku);
}

// إضافة منتج جديد
export function addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product {
  const products = getProducts();

  const newProduct: Product = {
    ...product,
    id: Math.random().toString(36).substring(2, 15),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  products.push(newProduct);
  saveProducts(products);

  // إنشاء عنصر مخزون افتراضي للمنتج الجديد
  if (product.type === 'physical') {
    const locations = getInventoryLocations();
    const defaultLocation = locations.find(location => location.isDefault);

    if (defaultLocation) {
      addInventoryItem({
        productId: newProduct.id,
        locationId: defaultLocation.id,
        quantity: 0,
        minQuantity: 0,
        maxQuantity: 100,
        reorderPoint: 10,
        lastStockTake: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
  }

  return newProduct;
}

// تحديث منتج
export function updateProduct(id: string, updates: Partial<Product>): Product | undefined {
  const products = getProducts();
  const index = products.findIndex(product => product.id === id);

  if (index === -1) {
    return undefined;
  }

  const updatedProduct: Product = {
    ...products[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  products[index] = updatedProduct;
  saveProducts(products);

  return updatedProduct;
}

// حذف منتج
export function deleteProduct(id: string): boolean {
  const products = getProducts();
  const filteredProducts = products.filter(product => product.id !== id);

  if (filteredProducts.length === products.length) {
    return false;
  }

  saveProducts(filteredProducts);

  // حذف عناصر المخزون المرتبطة بالمنتج
  const inventoryItems = getInventoryItems();
  const filteredItems = inventoryItems.filter(item => item.productId !== id);
  saveInventoryItems(filteredItems);

  return true;
}

// الحصول على عناصر المخزون
export function getInventoryItems(): InventoryItem[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedItems = localStorage.getItem('inventoryItems');
  if (!savedItems) {
    return [];
  }

  try {
    return JSON.parse(savedItems);
  } catch (error) {
    console.error('خطأ في قراءة عناصر المخزون:', error);
    return [];
  }
}

// حفظ عناصر المخزون
export function saveInventoryItems(items: InventoryItem[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('inventoryItems', JSON.stringify(items));
}

// الحصول على عنصر مخزون بواسطة المنتج والموقع
export function getInventoryItem(productId: string, locationId: string): InventoryItem | undefined {
  const items = getInventoryItems();
  return items.find(item => item.productId === productId && item.locationId === locationId);
}

// إضافة عنصر مخزون
export function addInventoryItem(item: InventoryItem): InventoryItem {
  const items = getInventoryItems();

  // التحقق من عدم وجود عنصر مخزون للمنتج والموقع
  const existingItem = items.find(i => i.productId === item.productId && i.locationId === item.locationId);
  if (existingItem) {
    return updateInventoryItem(item.productId, item.locationId, item);
  }

  items.push(item);
  saveInventoryItems(items);

  // إنشاء حركة مخزون للإضافة الأولية
  if (item.quantity > 0) {
    addInventoryMovement({
      productId: item.productId,
      locationId: item.locationId,
      type: 'in',
      quantity: item.quantity,
      previousQuantity: 0,
      newQuantity: item.quantity,
      reference: 'initial',
      referenceType: 'adjustment',
      createdBy: 'system',
      createdAt: new Date().toISOString()
    });
  }

  // التحقق من تنبيهات المخزون
  checkInventoryAlerts(item);

  return item;
}

// تحديث عنصر مخزون
export function updateInventoryItem(productId: string, locationId: string, updates: Partial<InventoryItem>): InventoryItem {
  const items = getInventoryItems();
  const index = items.findIndex(item => item.productId === productId && item.locationId === locationId);

  if (index === -1) {
    // إذا لم يكن العنصر موجودًا، قم بإنشائه
    const newItem: InventoryItem = {
      productId,
      locationId,
      quantity: 0,
      minQuantity: 0,
      maxQuantity: 100,
      reorderPoint: 10,
      lastStockTake: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...updates
    };

    return addInventoryItem(newItem);
  }

  const oldQuantity = items[index].quantity;
  const newQuantity = updates.quantity !== undefined ? updates.quantity : oldQuantity;

  const updatedItem: InventoryItem = {
    ...items[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  items[index] = updatedItem;
  saveInventoryItems(items);

  // إذا تغيرت الكمية، قم بإنشاء حركة مخزون
  if (updates.quantity !== undefined && updates.quantity !== oldQuantity) {
    addInventoryMovement({
      productId,
      locationId,
      type: updates.quantity > oldQuantity ? 'in' : 'out',
      quantity: Math.abs(updates.quantity - oldQuantity),
      previousQuantity: oldQuantity,
      newQuantity: updates.quantity,
      reference: 'manual',
      referenceType: 'adjustment',
      createdBy: 'system',
      createdAt: new Date().toISOString()
    });
  }

  // التحقق من تنبيهات المخزون
  checkInventoryAlerts(updatedItem);

  return updatedItem;
}

// تعديل كمية المخزون
export function adjustInventoryQuantity(
  productId: string,
  locationId: string,
  quantity: number,
  reference: string,
  referenceType: InventoryMovement['referenceType'],
  notes?: string
): InventoryItem | undefined {
  const item = getInventoryItem(productId, locationId);

  if (!item) {
    return undefined;
  }

  const oldQuantity = item.quantity;
  const newQuantity = oldQuantity + quantity;

  // تحديث كمية المخزون
  const updatedItem = updateInventoryItem(productId, locationId, { quantity: newQuantity });

  // إنشاء حركة مخزون
  addInventoryMovement({
    productId,
    locationId,
    type: quantity > 0 ? 'in' : 'out',
    quantity: Math.abs(quantity),
    previousQuantity: oldQuantity,
    newQuantity,
    reference,
    referenceType,
    notes,
    createdBy: 'system',
    createdAt: new Date().toISOString()
  });

  return updatedItem;
}

// الحصول على حركات المخزون
export function getInventoryMovements(): InventoryMovement[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedMovements = localStorage.getItem('inventoryMovements');
  if (!savedMovements) {
    return [];
  }

  try {
    return JSON.parse(savedMovements);
  } catch (error) {
    console.error('خطأ في قراءة حركات المخزون:', error);
    return [];
  }
}

// حفظ حركات المخزون
export function saveInventoryMovements(movements: InventoryMovement[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('inventoryMovements', JSON.stringify(movements));
}

// إضافة حركة مخزون
export function addInventoryMovement(movement: Omit<InventoryMovement, 'id'>): InventoryMovement {
  const movements = getInventoryMovements();

  const newMovement: InventoryMovement = {
    ...movement,
    id: Math.random().toString(36).substring(2, 15)
  };

  movements.push(newMovement);
  saveInventoryMovements(movements);

  return newMovement;
}

// الحصول على حركات المخزون لمنتج معين
export function getProductMovements(productId: string): InventoryMovement[] {
  const movements = getInventoryMovements();
  return movements.filter(movement => movement.productId === productId);
}

// الحصول على مواقع المخزون
export function getInventoryLocations(): InventoryLocation[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedLocations = localStorage.getItem('inventoryLocations');
  if (!savedLocations) {
    // إنشاء موقع افتراضي إذا لم تكن هناك مواقع
    const defaultLocation: InventoryLocation = {
      id: 'default',
      name: 'المخزن الرئيسي',
      nameEn: 'Main Warehouse',
      type: 'warehouse',
      isDefault: true,
      active: true
    };

    saveInventoryLocations([defaultLocation]);
    return [defaultLocation];
  }

  try {
    return JSON.parse(savedLocations);
  } catch (error) {
    console.error('خطأ في قراءة مواقع المخزون:', error);
    return [];
  }
}

// حفظ مواقع المخزون
export function saveInventoryLocations(locations: InventoryLocation[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('inventoryLocations', JSON.stringify(locations));
}

// إضافة موقع مخزون
export function addInventoryLocation(location: Omit<InventoryLocation, 'id'>): InventoryLocation {
  const locations = getInventoryLocations();

  // إذا كان الموقع الجديد هو الافتراضي، قم بإلغاء تعيين الموقع الافتراضي الحالي
  if (location.isDefault) {
    locations.forEach(loc => {
      if (loc.isDefault) {
        loc.isDefault = false;
      }
    });
  }

  const newLocation: InventoryLocation = {
    ...location,
    id: Math.random().toString(36).substring(2, 15)
  };

  locations.push(newLocation);
  saveInventoryLocations(locations);

  return newLocation;
}

// تحديث موقع مخزون
export function updateInventoryLocation(id: string, updates: Partial<InventoryLocation>): InventoryLocation | undefined {
  const locations = getInventoryLocations();
  const index = locations.findIndex(location => location.id === id);

  if (index === -1) {
    return undefined;
  }

  // إذا كان التحديث يجعل الموقع هو الافتراضي، قم بإلغاء تعيين الموقع الافتراضي الحالي
  if (updates.isDefault) {
    locations.forEach(loc => {
      if (loc.id !== id && loc.isDefault) {
        loc.isDefault = false;
      }
    });
  }

  const updatedLocation: InventoryLocation = {
    ...locations[index],
    ...updates
  };

  locations[index] = updatedLocation;
  saveInventoryLocations(locations);

  return updatedLocation;
}

// حذف موقع مخزون
export function deleteInventoryLocation(id: string): boolean {
  const locations = getInventoryLocations();

  // لا يمكن حذف الموقع الافتراضي
  const locationToDelete = locations.find(location => location.id === id);
  if (!locationToDelete || locationToDelete.isDefault) {
    return false;
  }

  const filteredLocations = locations.filter(location => location.id !== id);

  if (filteredLocations.length === locations.length) {
    return false;
  }

  saveInventoryLocations(filteredLocations);

  // حذف عناصر المخزون المرتبطة بالموقع
  const inventoryItems = getInventoryItems();
  const filteredItems = inventoryItems.filter(item => item.locationId !== id);
  saveInventoryItems(filteredItems);

  return true;
}

// الحصول على تنبيهات المخزون
export function getInventoryAlerts(): InventoryAlert[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedAlerts = localStorage.getItem('inventoryAlerts');
  if (!savedAlerts) {
    return [];
  }

  try {
    return JSON.parse(savedAlerts);
  } catch (error) {
    console.error('خطأ في قراءة تنبيهات المخزون:', error);
    return [];
  }
}

// حفظ تنبيهات المخزون
export function saveInventoryAlerts(alerts: InventoryAlert[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('inventoryAlerts', JSON.stringify(alerts));
}

// إضافة تنبيه مخزون
export function addInventoryAlert(alert: Omit<InventoryAlert, 'id' | 'createdAt'>): InventoryAlert {
  const alerts = getInventoryAlerts();

  // التحقق من عدم وجود تنبيه مماثل
  const existingAlert = alerts.find(
    a => a.productId === alert.productId &&
         a.locationId === alert.locationId &&
         a.type === alert.type &&
         a.status !== 'resolved'
  );

  if (existingAlert) {
    return existingAlert;
  }

  const newAlert: InventoryAlert = {
    ...alert,
    id: Math.random().toString(36).substring(2, 15),
    createdAt: new Date().toISOString()
  };

  alerts.push(newAlert);
  saveInventoryAlerts(alerts);

  return newAlert;
}

// تحديث حالة تنبيه مخزون
export function updateAlertStatus(
  id: string,
  status: 'acknowledged' | 'resolved',
  userId: string
): InventoryAlert | undefined {
  const alerts = getInventoryAlerts();
  const index = alerts.findIndex(alert => alert.id === id);

  if (index === -1) {
    return undefined;
  }

  const updatedAlert: InventoryAlert = {
    ...alerts[index],
    status
  };

  if (status === 'acknowledged') {
    updatedAlert.acknowledgedBy = userId;
    updatedAlert.acknowledgedAt = new Date().toISOString();
  } else if (status === 'resolved') {
    updatedAlert.resolvedBy = userId;
    updatedAlert.resolvedAt = new Date().toISOString();
  }

  alerts[index] = updatedAlert;
  saveInventoryAlerts(alerts);

  return updatedAlert;
}

// التحقق من تنبيهات المخزون لعنصر معين
export function checkInventoryAlerts(item: InventoryItem): void {
  // التحقق من المخزون المنخفض
  if (item.quantity <= item.reorderPoint && item.quantity > 0) {
    addInventoryAlert({
      productId: item.productId,
      locationId: item.locationId,
      type: 'low_stock',
      status: 'new',
      message: `المخزون منخفض: ${item.quantity} وحدة متبقية (نقطة إعادة الطلب: ${item.reorderPoint})`
    });
  }

  // التحقق من نفاد المخزون
  if (item.quantity <= 0) {
    addInventoryAlert({
      productId: item.productId,
      locationId: item.locationId,
      type: 'out_of_stock',
      status: 'new',
      message: 'المنتج غير متوفر في المخزون'
    });
  }

  // التحقق من المخزون الزائد
  if (item.quantity >= item.maxQuantity) {
    addInventoryAlert({
      productId: item.productId,
      locationId: item.locationId,
      type: 'overstock',
      status: 'new',
      message: `المخزون زائد: ${item.quantity} وحدة (الحد الأقصى: ${item.maxQuantity})`
    });
  }
}

// الحصول على عمليات الجرد
export function getStockTakes(): StockTake[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedStockTakes = localStorage.getItem('stockTakes');
  if (!savedStockTakes) {
    return [];
  }

  try {
    return JSON.parse(savedStockTakes);
  } catch (error) {
    console.error('خطأ في قراءة عمليات الجرد:', error);
    return [];
  }
}

// حفظ عمليات الجرد
export function saveStockTakes(stockTakes: StockTake[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('stockTakes', JSON.stringify(stockTakes));
}

// إنشاء عملية جرد جديدة
export function createStockTake(locationId: string, createdBy: string): StockTake {
  const stockTakes = getStockTakes();

  const newStockTake: StockTake = {
    id: Math.random().toString(36).substring(2, 15),
    locationId,
    status: 'draft',
    startDate: new Date().toISOString(),
    createdBy,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  stockTakes.push(newStockTake);
  saveStockTakes(stockTakes);

  // إنشاء عناصر الجرد لجميع المنتجات في الموقع
  const inventoryItems = getInventoryItems().filter(item => item.locationId === locationId);

  const stockTakeItems: StockTakeItem[] = inventoryItems.map(item => ({
    stockTakeId: newStockTake.id,
    productId: item.productId,
    expectedQuantity: item.quantity,
    actualQuantity: 0,
    discrepancy: -item.quantity,
    updatedBy: createdBy,
    updatedAt: new Date().toISOString()
  }));

  saveStockTakeItems(stockTakeItems);

  return newStockTake;
}

// تحديث حالة عملية الجرد
export function updateStockTakeStatus(id: string, status: StockTake['status']): StockTake | undefined {
  const stockTakes = getStockTakes();
  const index = stockTakes.findIndex(stockTake => stockTake.id === id);

  if (index === -1) {
    return undefined;
  }

  const updatedStockTake: StockTake = {
    ...stockTakes[index],
    status,
    updatedAt: new Date().toISOString()
  };

  if (status === 'completed') {
    updatedStockTake.endDate = new Date().toISOString();

    // تحديث المخزون بناءً على نتائج الجرد
    const stockTakeItems = getStockTakeItems().filter(item => item.stockTakeId === id);

    stockTakeItems.forEach(item => {
      updateInventoryItem(item.productId, updatedStockTake.locationId, {
        quantity: item.actualQuantity,
        lastStockTake: new Date().toISOString()
      });

      // إنشاء حركة مخزون للتعديل
      if (item.discrepancy !== 0) {
        addInventoryMovement({
          productId: item.productId,
          locationId: updatedStockTake.locationId,
          type: item.discrepancy > 0 ? 'in' : 'out',
          quantity: Math.abs(item.discrepancy),
          previousQuantity: item.expectedQuantity,
          newQuantity: item.actualQuantity,
          reference: `stocktake-${id}`,
          referenceType: 'stocktake',
          notes: item.notes,
          createdBy: updatedStockTake.createdBy,
          createdAt: new Date().toISOString()
        });
      }
    });
  }

  stockTakes[index] = updatedStockTake;
  saveStockTakes(stockTakes);

  return updatedStockTake;
}

// الحصول على عناصر الجرد
export function getStockTakeItems(): StockTakeItem[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedItems = localStorage.getItem('stockTakeItems');
  if (!savedItems) {
    return [];
  }

  try {
    return JSON.parse(savedItems);
  } catch (error) {
    console.error('خطأ في قراءة عناصر الجرد:', error);
    return [];
  }
}

// حفظ عناصر الجرد
export function saveStockTakeItems(items: StockTakeItem[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('stockTakeItems', JSON.stringify(items));
}

// تحديث كمية عنصر الجرد الفعلية
export function updateStockTakeItemQuantity(
  stockTakeId: string,
  productId: string,
  actualQuantity: number,
  updatedBy: string,
  notes?: string
): StockTakeItem | undefined {
  const items = getStockTakeItems();
  const index = items.findIndex(item => item.stockTakeId === stockTakeId && item.productId === productId);

  if (index === -1) {
    return undefined;
  }

  const updatedItem: StockTakeItem = {
    ...items[index],
    actualQuantity,
    discrepancy: actualQuantity - items[index].expectedQuantity,
    notes,
    updatedBy,
    updatedAt: new Date().toISOString()
  };

  items[index] = updatedItem;
  saveStockTakeItems(items);

  return updatedItem;
}

// الحصول على عناصر الجرد لعملية جرد معينة
export function getStockTakeItemsByStockTakeId(stockTakeId: string): StockTakeItem[] {
  const items = getStockTakeItems();
  return items.filter(item => item.stockTakeId === stockTakeId);
}
