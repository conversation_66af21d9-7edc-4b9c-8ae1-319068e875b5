import { z } from 'zod';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
}

/**
 * تنظيف وتعقيم النصوص من المحتوى الضار
 */
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') return '';

  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // إزالة script tags
    .replace(/javascript:/gi, '') // إزالة javascript: protocols
    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '') // إزالة event handlers
    .replace(/<[^>]*>/g, '') // إزالة HTML tags
    .replace(/&[#\w]+;/g, ''); // إزالة HTML entities
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== 'string') return false;

  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  // Additional checks
  if (email.length > 254) return false; // RFC 5321 limit
  if (email.includes('..')) return false; // No consecutive dots
  if (email.startsWith('.') || email.endsWith('.')) return false; // No leading/trailing dots
  if (email.startsWith('@') || email.endsWith('@')) return false; // No leading/trailing @

  return emailRegex.test(email);
}

/**
 * التحقق من قوة كلمة المرور
 */
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * مخططات التحقق لنقاط النهاية المختلفة
 */
const validationSchemas = {
  '/api/auth/login': z.object({
    email: z.string().email('بريد إلكتروني غير صالح'),
    password: z.string().min(1, 'كلمة المرور مطلوبة')
  }),
  
  '/api/auth/register': z.object({
    name: z.string().min(2, 'الاسم يجب أن يكون حرفين على الأقل'),
    email: z.string().email('بريد إلكتروني غير صالح'),
    password: z.string().min(8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
  }),
  
  '/api/customers': z.object({
    name: z.string().min(2, 'اسم العميل مطلوب'),
    email: z.string().email('بريد إلكتروني غير صالح').optional(),
    phone: z.string().optional(),
    address: z.string().optional(),
    taxNumber: z.string().optional()
  }),
  
  '/api/products': z.object({
    name: z.string().min(1, 'اسم المنتج مطلوب'),
    description: z.string().optional(),
    barcode: z.string().min(1, 'الباركود مطلوب'),
    price: z.number().positive('السعر يجب أن يكون أكبر من صفر'),
    cost: z.number().min(0, 'التكلفة لا يمكن أن تكون سالبة'),
    stockQty: z.number().int().min(0, 'الكمية لا يمكن أن تكون سالبة'),
    categoryId: z.string().min(1, 'الفئة مطلوبة')
  }),
  
  '/api/invoices': z.object({
    customerId: z.string().min(1, 'العميل مطلوب'),
    items: z.array(z.object({
      productId: z.string().min(1, 'المنتج مطلوب'),
      quantity: z.number().positive('الكمية يجب أن تكون أكبر من صفر'),
      unitPrice: z.number().positive('سعر الوحدة يجب أن يكون أكبر من صفر')
    })).min(1, 'عنصر واحد على الأقل مطلوب'),
    notes: z.string().optional()
  })
};

/**
 * التحقق من صحة المدخلات بناءً على المسار
 */
export function validateInput(data: any, path: string): ValidationResult {
  try {
    // تنظيف البيانات النصية
    const sanitizedData = sanitizeData(data);
    
    // العثور على المخطط المناسب
    const schema = validationSchemas[path as keyof typeof validationSchemas];
    
    if (!schema) {
      // إذا لم يوجد مخطط محدد، قم بالتحقق الأساسي
      return basicValidation(sanitizedData);
    }
    
    // تطبيق التحقق باستخدام Zod
    const result = schema.safeParse(sanitizedData);
    
    if (result.success) {
      return {
        isValid: true,
        errors: [],
        sanitizedData: result.data
      };
    } else {
      return {
        isValid: false,
        errors: result.error.errors.map(err => err.message)
      };
    }
  } catch (error) {
    return {
      isValid: false,
      errors: ['خطأ في التحقق من البيانات']
    };
  }
}

/**
 * تنظيف البيانات بشكل تكراري
 */
function sanitizeData(data: any): any {
  if (typeof data === 'string') {
    return sanitizeString(data);
  }
  
  if (Array.isArray(data)) {
    return data.map(item => sanitizeData(item));
  }
  
  if (data && typeof data === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[sanitizeString(key)] = sanitizeData(value);
    }
    return sanitized;
  }
  
  return data;
}

/**
 * التحقق الأساسي للبيانات
 */
function basicValidation(data: any): ValidationResult {
  const errors: string[] = [];
  
  // التحقق من الحجم
  const jsonString = JSON.stringify(data);
  if (jsonString.length > 1024 * 1024) { // 1MB
    errors.push('حجم البيانات كبير جداً');
  }
  
  // التحقق من العمق
  if (getObjectDepth(data) > 10) {
    errors.push('البيانات معقدة جداً');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData: data
  };
}

/**
 * حساب عمق الكائن
 */
function getObjectDepth(obj: any): number {
  if (obj === null || typeof obj !== 'object') {
    return 0;
  }
  
  let maxDepth = 0;
  for (const value of Object.values(obj)) {
    const depth = getObjectDepth(value);
    maxDepth = Math.max(maxDepth, depth);
  }
  
  return maxDepth + 1;
}
