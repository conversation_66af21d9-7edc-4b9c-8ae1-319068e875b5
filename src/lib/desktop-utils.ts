/**
 * مكتبة مساعدة للتعامل مع تطبيقات سطح المكتب (Electron و Tauri)
 * Desktop utilities library for handling Electron and Tauri desktop apps
 */

// تعريف الأنواع لإضافتها إلى كائن النافذة
declare global {
  interface Window {
    electron: {
      invoke: <T = any>(channel: string, data?: any) => Promise<T>;
      openFile: (filePath: string) => Promise<boolean>;
      saveFile: (options: { defaultPath?: string; filters?: FileFilter[] }) => Promise<string | null>;
      platform: string;
      // أضف المزيد من الوظائف حسب الحاجة
    } | undefined;
    __TAURI__?: {
      invoke: <T = any>(command: string, args?: Record<string, unknown>) => Promise<T>;
      shell: {
        open: (path: string) => Promise<void>;
      };
      dialog: {
        save: (options?: { defaultPath?: string; filters?: FileFilter[] }) => Promise<string | null>;
        open: (options?: { defaultPath?: string; filters?: FileFilter[]; multiple?: boolean }) => Promise<string | string[]>;
      };
      // أضف المزيد من الوظائف حسب الحاجة
    };
  }
}

// تعريف نوع فلتر الملفات
interface FileFilter {
  name: string;
  extensions: string[];
}

/**
 * تحقق مما إذا كان التطبيق يعمل كتطبيق سطح مكتب (Electron أو Tauri)
 * Check if the app is running as a desktop app (Electron or Tauri)
 *
 * @returns {boolean} ما إذا كان التطبيق يعمل كتطبيق سطح مكتب
 */
export const isDesktopApp = (): boolean => isElectron() || isTauri();

/**
 * تحقق مما إذا كان التطبيق يعمل على Electron
 * Check if the app is running on Electron
 *
 * @returns {boolean} ما إذا كان التطبيق يعمل على Electron
 */
export const isElectron = (): boolean => {
  try {
    return typeof window !== 'undefined' &&
      (window.electron !== undefined ||
        window.process?.versions?.electron !== undefined);
  } catch (error) {
    return false;
  }
};

/**
 * تحقق مما إذا كان التطبيق يعمل على Tauri
 * Check if the app is running on Tauri
 *
 * @returns {boolean} ما إذا كان التطبيق يعمل على Tauri
 */
export const isTauri = (): boolean => {
  try {
    return typeof window !== 'undefined' && window.__TAURI__ !== undefined;
  } catch (error) {
    return false;
  }
};

/**
 * استدعاء دالة أصلية للنظام من Electron أو Tauri
 * Invoke a native function from Electron or Tauri
 *
 * @template T نوع القيمة المرجعة
 * @param {string} command اسم الأمر أو القناة
 * @param {Record<string, unknown>} [args] معاملات الأمر
 * @returns {Promise<T>} وعد بنتيجة الاستدعاء
 * @throws {Error} خطأ إذا لم يكن التطبيق يعمل على Electron أو Tauri
 */
export const invokeNative = async <T = any>(
  command: string,
  args?: Record<string, unknown>
): Promise<T> => {
  if (isElectron() && window.electron?.invoke) {
    return window.electron.invoke<T>(command, args);
  }

  if (isTauri() && window.__TAURI__?.invoke) {
    return window.__TAURI__.invoke<T>(command, args);
  }

  throw new Error('Desktop framework not available');
};

/**
 * فتح ملف باستخدام Electron أو Tauri
 * Open a file using Electron or Tauri
 *
 * @param {string} filePath مسار الملف
 * @returns {Promise<boolean>} وعد بنجاح العملية
 */
export const openFile = async (filePath: string): Promise<boolean> => {
  try {
    if (isElectron() && window.electron?.openFile) {
      return await window.electron.openFile(filePath);
    }

    if (isTauri() && window.__TAURI__?.shell) {
      await window.__TAURI__.shell.open(filePath);
      return true;
    }

    // في حالة التطبيق الويب، يمكن محاولة الفتح عبر نافذة جديدة
    if (typeof window !== 'undefined' && filePath.startsWith('http')) {
      window.open(filePath, '_blank');
      return true;
    }

    return false;
  } catch (error) {
    console.error('Failed to open file:', error);
    return false;
  }
};

/**
 * حفظ ملف باستخدام Electron أو Tauri
 * Save a file using Electron or Tauri
 *
 * @param {Object} options خيارات الحفظ
 * @param {string} [options.defaultPath] المسار الافتراضي للحفظ
 * @param {FileFilter[]} [options.filters] فلاتر أنواع الملفات
 * @returns {Promise<string | null>} وعد بمسار الملف المحفوظ أو null
 */
export const saveFile = async (
  options: { defaultPath?: string; filters?: FileFilter[] } = {}
): Promise<string | null> => {
  try {
    if (isElectron() && window.electron?.saveFile) {
      return await window.electron.saveFile(options);
    }

    if (isTauri() && window.__TAURI__?.dialog) {
      return await window.__TAURI__.dialog.save(options);
    }

    // في حالة التطبيق الويب، يمكن استخدام FileSaver.js أو بدائل أخرى
    console.warn('File saving not supported in web mode');
    return null;
  } catch (error) {
    console.error('Failed to save file:', error);
    return null;
  }
};

/**
 * الحصول على نظام التشغيل الحالي
 * Get the current operating system
 *
 * @returns {string} اسم نظام التشغيل (windows, macos, linux, web)
 */
export const getPlatform = (): string => {
  if (isElectron() && window.electron?.platform) {
    return window.electron.platform;
  }

  if (isTauri()) {
    // Tauri currently doesn't provide an easy way to get the platform through JavaScript
    // This could be implemented by calling a Rust command
    // For now we make a guess based on user agent
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('windows')) return 'windows';
    if (userAgent.includes('macintosh')) return 'macos';
    if (userAgent.includes('linux')) return 'linux';
  }

  return 'web';
};

/**
 * معرفة ما إذا كان التطبيق يعمل في وضع التطوير
 * Check if the app is running in development mode
 *
 * @returns {Promise<boolean>} وعد بما إذا كان التطبيق في وضع التطوير
 */
export const isDevelopment = async (): Promise<boolean> => {
  try {
    if (isElectron()) {
      return await invokeNative<boolean>('is-dev');
    }

    if (isTauri()) {
      // يمكن تنفيذ هذا عبر استدعاء أمر Rust
      // هذا مجرد مثال، قد تحتاج إلى تنفيذ الأمر في جانب Rust
      return await invokeNative<boolean>('is_dev');
    }

    // لتطبيقات الويب
    return process.env.NODE_ENV === 'development';
  } catch (error) {
    // في حالة الخطأ، افترض أنه ليس وضع التطوير
    return false;
  }
};

/**
 * تصغير نافذة التطبيق
 * Minimize the application window
 */
export const minimizeWindow = async (): Promise<boolean> => {
  try {
    if (isElectron()) {
      return await invokeNative<boolean>('minimize-window');
    }
    if (isTauri()) {
      return await invokeNative<boolean>('window_minimize');
    }
    return false;
  } catch (error) {
    console.error('Failed to minimize window:', error);
    return false;
  }
};

/**
 * تكبير/استعادة نافذة التطبيق
 * Maximize/restore the application window
 */
export const toggleMaximizeWindow = async (): Promise<boolean> => {
  try {
    if (isElectron()) {
      return await invokeNative<boolean>('toggle-maximize-window');
    }
    if (isTauri()) {
      return await invokeNative<boolean>('window_toggle_maximize');
    }
    return false;
  } catch (error) {
    console.error('Failed to toggle maximize window:', error);
    // تسجيل معلومات إضافية تساعد في تشخيص المشكلة
    ,
      isTauri: isTauri(),
      platform: typeof window !== 'undefined' ? navigator.platform : 'unknown'
    });
    return false;
  }
};

/**
 * إغلاق نافذة التطبيق
 * Close the application window
 */
export const closeWindow = async (): Promise<boolean> => {
  try {
    if (isElectron()) {
      return await invokeNative<boolean>('close-window');
    }
    if (isTauri()) {
      return await invokeNative<boolean>('window_close');
    }
    return false;
  } catch (error) {
    console.error('Failed to close window:', error);
    return false;
  }
};

/**
 * عرض إشعار سطح المكتب
 * Show a desktop notification
 *
 * @param {Object} options خيارات الإشعار
 * @param {string} options.title عنوان الإشعار
 * @param {string} options.body محتوى الإشعار
 * @param {string} [options.icon] مسار أيقونة الإشعار
 * @returns {Promise<boolean>} وعد بنجاح العملية
 */
export const showNotification = async (
  options: { title: string; body: string; icon?: string }
): Promise<boolean> => {
  try {
    if (isElectron()) {
      return await invokeNative<boolean>('show-notification', options);
    }
    if (isTauri()) {
      return await invokeNative<boolean>('notification_show', options);
    }

    // بديل للويب إذا كانت الإشعارات مدعومة
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(options.title, {
          body: options.body,
          icon: options.icon
        });
        return true;
      } else if (Notification.permission !== 'denied') {
        const permission = await Notification.requestPermission();
        if (permission === 'granted') {
          new Notification(options.title, {
            body: options.body,
            icon: options.icon
          });
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    console.error('Failed to show notification:', error);
    return false;
  }
};

/**
 * قراءة ملف من نظام الملفات
 * Read a file from the filesystem
 *
 * @param {string} filePath مسار الملف
 * @param {string} [encoding='utf8'] ترميز الملف
 * @returns {Promise<string | ArrayBuffer | null>} محتوى الملف
 */
export const readFile = async (
  filePath: string,
  encoding: BufferEncoding = 'utf8'
): Promise<string | ArrayBuffer | null> => {
  try {
    if (isElectron()) {
      return await invokeNative<string>('read-file', { filePath, encoding });
    }
    if (isTauri()) {
      // استخدام fs من Tauri
      return await invokeNative<string>('read_file', { path: filePath, encoding });
    }

    return null;
  } catch (error) {
    console.error('Failed to read file:', error);
    return null;
  }
};

/**
 * كتابة ملف إلى نظام الملفات
 * Write a file to the filesystem
 *
 * @param {string} filePath مسار الملف
 * @param {string | ArrayBuffer} contents محتوى الملف
 * @returns {Promise<boolean>} وعد بنجاح العملية
 */
export const writeFile = async (
  filePath: string,
  contents: string | ArrayBuffer
): Promise<boolean> => {
  try {
    if (isElectron()) {
      return await invokeNative<boolean>('write-file', { filePath, contents });
    }
    if (isTauri()) {
      // استخدام fs من Tauri
      return await invokeNative<boolean>('write_file', { path: filePath, contents });
    }

    return false;
  } catch (error) {
    console.error('Failed to write file:', error);
    return false;
  }
};

/**
 * الحصول على معلومات التطبيق
 * Get application information
 *
 * @returns {Promise<{ name: string; version: string; description?: string }>} معلومات التطبيق
 */
export const getAppInfo = async (): Promise<{
  name: string;
  version: string;
  description?: string
} | null> => {
  try {
    if (isElectron()) {
      return await invokeNative('get-app-info');
    }
    if (isTauri()) {
      return await invokeNative('app_info');
    }

    return null;
  } catch (error) {
    console.error('Failed to get app info:', error);
    return null;
  }
};

/**
 * تخزين قيمة في إعدادات التطبيق
 * Store a value in application settings
 *
 * @param {string} key مفتاح القيمة
 * @param {any} value القيمة المراد تخزينها
 * @returns {Promise<boolean>} وعد بنجاح العملية
 */
export const setAppSetting = async (key: string, value: any): Promise<boolean> => {
  try {
    if (isElectron()) {
      return await invokeNative<boolean>('set-setting', { key, value });
    }
    if (isTauri()) {
      return await invokeNative<boolean>('store_set', { key, value });
    }

    // استخدام localStorage للويب
    if (typeof window !== 'undefined') {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    }

    return false;
  } catch (error) {
    console.error('Failed to set app setting:', error);
    return false;
  }
};

/**
 * استرجاع قيمة من إعدادات التطبيق
 * Retrieve a value from application settings
 *
 * @param {string} key مفتاح القيمة
 * @param {any} defaultValue القيمة الافتراضية
 * @returns {Promise<any>} القيمة المخزنة
 */
export const getAppSetting = async <T>(key: string, defaultValue: T): Promise<T> => {
  try {
    if (isElectron()) {
      return await invokeNative<T>('get-setting', { key, defaultValue });
    }
    if (isTauri()) {
      return await invokeNative<T>('store_get', { key, defaultValue });
    }

    // استخدام localStorage للويب
    if (typeof window !== 'undefined') {
      const item = localStorage.getItem(key);
      if (item === null) return defaultValue;
      try {
        return JSON.parse(item) as T;
      } catch {
        return defaultValue;
      }
    }

    return defaultValue;
  } catch (error) {
    console.error('Failed to get app setting:', error);
    return defaultValue;
  }
};

export default {
  isDesktopApp,
  isElectron,
  isTauri,
  invokeNative,
  openFile,
  saveFile,
  getPlatform,
  isDevelopment,
  // وظائف إدارة النوافذ
  minimizeWindow,
  toggleMaximizeWindow,
  closeWindow,
  // وظائف الإشعارات
  showNotification,
  // وظائف نظام الملفات
  readFile,
  writeFile,
  // وظائف المعلومات والإعدادات
  getAppInfo,
  setAppSetting,
  getAppSetting
};
