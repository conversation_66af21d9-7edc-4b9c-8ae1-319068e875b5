import { User, UserRole, Permission, DEFAULT_PERMISSIONS } from '@/types/user';

// الحصول على جميع المستخدمين
export function getUsers(): User[] {
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const savedUsers = localStorage.getItem('users');
    if (!savedUsers) {
      // إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
      const adminUser = createDefaultAdminUser();
      saveUsers([adminUser]);
      return [adminUser];
    }

    const parsedUsers = JSON.parse(savedUsers);

    // التحقق من أن البيانات المستردة هي مصفوفة
    if (!Array.isArray(parsedUsers)) {
      throw new Error('بيانات المستخدمين ليست بالتنسيق الصحيح');
    }

    return parsedUsers;
  } catch (error) {
    console.error('خطأ في قراءة المستخدمين:', error);
    // في حالة حدوث خطأ، نعيد مستخدم افتراضي
    const adminUser = createDefaultAdminUser();
    saveUsers([adminUser]);
    return [adminUser];
  }
}

// حفظ المستخدمين
export function saveUsers(users: User[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // التحقق من أن البيانات المرسلة هي مصفوفة
    if (!Array.isArray(users)) {
      throw new Error('بيانات المستخدمين ليست بالتنسيق الصحيح');
    }

    localStorage.setItem('users', JSON.stringify(users));
  } catch (error) {
    console.error('خطأ في حفظ المستخدمين:', error);
  }
}

// إنشاء مستخدم افتراضي
function createDefaultAdminUser(): User {
  return {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'مدير النظام',
    role: 'admin',
    permissions: getPermissionsFromRole('admin'),
    active: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}

// الحصول على الصلاحيات من الدور
export function getPermissionsFromRole(role: UserRole): Permission[] {
  const permissionKeys = DEFAULT_PERMISSIONS[role] || [];

  return permissionKeys.map(key => {
    const [module, action] = key.split('.') as [Permission['module'], Permission['action']];

    return {
      id: `${module}-${action}`,
      name: `${module}.${action}`,
      description: `${getModuleDescription(module)} - ${getActionDescription(action)}`,
      module,
      action
    };
  });
}

// الحصول على وصف الوحدة
function getModuleDescription(module: Permission['module']): string {
  const descriptions: Record<Permission['module'], string> = {
    invoices: 'الفواتير',
    customers: 'العملاء',
    products: 'المنتجات',
    inventory: 'المخزون',
    pos: 'نقاط البيع',
    reports: 'التقارير',
    settings: 'الإعدادات',
    users: 'المستخدمين'
  };

  return descriptions[module] || module;
}

// الحصول على وصف الإجراء
function getActionDescription(action: Permission['action']): string {
  const descriptions: Record<Permission['action'], string> = {
    view: 'عرض',
    create: 'إنشاء',
    edit: 'تعديل',
    delete: 'حذف',
    approve: 'موافقة',
    export: 'تصدير'
  };

  return descriptions[action] || action;
}

// الحصول على مستخدم بواسطة المعرف
export function getUserById(id: string): User | undefined {
  const users = getUsers();
  return users.find(user => user.id === id);
}

// الحصول على مستخدم بواسطة اسم المستخدم
export function getUserByUsername(username: string): User | undefined {
  const users = getUsers();
  return users.find(user => user.username === username);
}

// إضافة مستخدم جديد
export function addUser(userData: Omit<User, 'id' | 'permissions' | 'createdAt' | 'updatedAt'>): User {
  const users = getUsers();

  // التحقق من عدم وجود مستخدم بنفس اسم المستخدم أو البريد الإلكتروني
  const existingUsername = users.find(user => user.username === userData.username);
  if (existingUsername) {
    throw new Error('اسم المستخدم موجود بالفعل');
  }

  const existingEmail = users.find(user => user.email === userData.email);
  if (existingEmail) {
    throw new Error('البريد الإلكتروني موجود بالفعل');
  }

  const newUser: User = {
    ...userData,
    id: Math.random().toString(36).substring(2, 15),
    permissions: getPermissionsFromRole(userData.role),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  users.push(newUser);
  saveUsers(users);

  return newUser;
}

// تحديث مستخدم
export function updateUser(id: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): User | undefined {
  const users = getUsers();
  const index = users.findIndex(user => user.id === id);

  if (index === -1) {
    return undefined;
  }

  // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم أو البريد الإلكتروني
  if (updates.username) {
    const existingUsername = users.find(user => user.username === updates.username && user.id !== id);
    if (existingUsername) {
      throw new Error('اسم المستخدم موجود بالفعل');
    }
  }

  if (updates.email) {
    const existingEmail = users.find(user => user.email === updates.email && user.id !== id);
    if (existingEmail) {
      throw new Error('البريد الإلكتروني موجود بالفعل');
    }
  }

  // تحديث الصلاحيات إذا تم تغيير الدور
  let permissions = users[index].permissions;
  if (updates.role && updates.role !== users[index].role) {
    permissions = getPermissionsFromRole(updates.role);
  }

  const updatedUser: User = {
    ...users[index],
    ...updates,
    permissions,
    updatedAt: new Date().toISOString()
  };

  users[index] = updatedUser;
  saveUsers(users);

  return updatedUser;
}

// حذف مستخدم
export function deleteUser(id: string): boolean {
  // لا يمكن حذف المستخدم الافتراضي
  if (id === '1') {
    return false;
  }

  const users = getUsers();
  const filteredUsers = users.filter(user => user.id !== id);

  if (filteredUsers.length === users.length) {
    return false;
  }

  saveUsers(filteredUsers);
  return true;
}

// تسجيل دخول المستخدم
export function loginUser(username: string, password: string): User | null {
  // في التطبيق الحقيقي، يجب التحقق من كلمة المرور
  // هنا نفترض أن كلمة المرور هي "password" لجميع المستخدمين للتبسيط

  if (password !== 'password') {
    return null;
  }

  const user = getUserByUsername(username);
  if (!user) {
    return null;
  }

  if (!user.active) {
    return null;
  }

  // تحديث وقت آخر تسجيل دخول
  updateUser(user.id, { lastLogin: new Date().toISOString() });

  return user;
}

// تسجيل خروج المستخدم
export function logoutUser(): void {
  // في التطبيق الحقيقي، يمكن إزالة رمز الجلسة أو ما شابه
  }
