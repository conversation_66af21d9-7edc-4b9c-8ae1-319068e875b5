/**
 * مراقب الأمان في الوقت الفعلي
 * Real-time Security Monitor
 */

import { prisma } from './prisma';
import { logSecurityEvent } from './security-logger';

/**
 * مراقب الأمان في الوقت الفعلي
 * Real-time Security Monitor
 */
export class SecurityMonitor {
  private static instance: SecurityMonitor;
  private alerts: Map<string, { count: number; timestamp: number }> = new Map();
  private readonly ALERT_THRESHOLD = 5;
  private readonly ALERT_WINDOW = 5 * 60 * 1000; // 5 minutes
  private readonly CLEANUP_INTERVAL = 60 * 1000; // 1 minute

  constructor() {
    // تنظيف دوري للتنبيهات القديمة
    setInterval(() => {
      this.cleanupOldAlerts();
    }, this.CLEANUP_INTERVAL);
  }

  static getInstance(): SecurityMonitor {
    if (!this.instance) {
      this.instance = new SecurityMonitor();
    }
    return this.instance;
  }

  /**
   * مراقبة الأنشطة المشبوهة
   */
  async monitorActivity(
    userId: string,
    action: string,
    ip: string,
    details?: any
  ): Promise<void> {
    const key = `${userId}-${action}-${ip}`;
    const now = Date.now();

    // الحصول على العداد الحالي أو إنشاء جديد
    const current = this.alerts.get(key) || { count: 0, timestamp: now };

    // إعادة تعيين العداد إذا انتهت النافزة الزمنية
    if (now - current.timestamp > this.ALERT_WINDOW) {
      current.count = 0;
      current.timestamp = now;
    }

    current.count++;
    this.alerts.set(key, current);

    // فحص إذا تم تجاوز الحد المسموح
    if (current.count >= this.ALERT_THRESHOLD) {
      await this.triggerSecurityAlert({
        type: 'SUSPICIOUS_ACTIVITY_PATTERN',
        userId,
        ip,
        action,
        count: current.count,
        details
      });

      // إعادة تعيين العداد
      this.alerts.delete(key);
    }
  }

  /**
   * مراقبة محاولات الوصول غير المصرح بها
   */
  async monitorUnauthorizedAccess(
    ip: string,
    path: string,
    userAgent: string,
    userId?: string
  ): Promise<void> {
    await logSecurityEvent({
      type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
      userId,
      ip,
      userAgent,
      path,
      details: `Unauthorized access attempt to: ${path}`,
      severity: 'HIGH'
    });

    // مراقبة تكرار المحاولات من نفس IP
    await this.monitorActivity(ip, 'UNAUTHORIZED_ACCESS', ip, { path });
  }

  /**
   * مراقبة تغييرات البيانات الحساسة
   */
  async monitorSensitiveDataChange(
    userId: string,
    dataType: string,
    oldValue: any,
    newValue: any,
    ip: string
  ): Promise<void> {
    await logSecurityEvent({
      type: 'SENSITIVE_DATA_MODIFIED',
      userId,
      ip,
      details: `${dataType} modified. Old: ${JSON.stringify(oldValue)}, New: ${JSON.stringify(newValue)}`,
      severity: 'MEDIUM'
    });

    // فحص التغييرات المتكررة
    await this.monitorActivity(userId, 'DATA_MODIFICATION', ip, { dataType });
  }

  /**
   * مراقبة عمليات التصدير والتحميل
   */
  async monitorDataExport(
    userId: string,
    exportType: string,
    recordCount: number,
    ip: string
  ): Promise<void> {
    const severity = recordCount > 1000 ? 'HIGH' : recordCount > 100 ? 'MEDIUM' : 'LOW';

    await logSecurityEvent({
      type: 'DATA_EXPORT',
      userId,
      ip,
      details: `Data export: ${exportType}, Records: ${recordCount}`,
      severity
    });

    // مراقبة عمليات التصدير المتكررة
    if (recordCount > 100) {
      await this.monitorActivity(userId, 'BULK_EXPORT', ip, { exportType, recordCount });
    }
  }

  /**
   * تشغيل تنبيه أمني
   */
  private async triggerSecurityAlert(alert: any): Promise<void> {
    await logSecurityEvent({
      type: alert.type,
      userId: alert.userId,
      ip: alert.ip,
      details: `Suspicious pattern detected: ${alert.action} performed ${alert.count} times in ${this.ALERT_WINDOW / 1000} seconds`,
      severity: 'CRITICAL'
    });

    // إرسال تنبيه فوري للمشرفين
    await this.notifyAdministrators(alert);

    // تطبيق إجراءات أمنية تلقائية
    await this.applySecurityMeasures(alert);
  }

  /**
   * إشعار المشرفين
   */
  private async notifyAdministrators(alert: any): Promise<void> {
    try {
      // الحصول على قائمة المشرفين
      const admins = await prisma.user.findMany({
        where: { role: 'admin' },
        select: { id: true, email: true, name: true }
      });

      // إنشاء إشعار في النظام
      for (const admin of admins) {
        await prisma.notification.create({
          data: {
            userId: admin.id,
            title: 'تنبيه أمني عاجل',
            message: `تم اكتشاف نشاط مشبوه: ${alert.action} من المستخدم ${alert.userId} من IP: ${alert.ip}`,
            type: 'security',
            priority: 'high',
            isRead: false
          }
        });
      }

      // إرسال بريد إلكتروني (إذا كان متاحاً)
      await this.sendEmailAlert(admins, alert);
    } catch (error) {
      console.error('Failed to notify administrators:', error);
    }
  }

  /**
   * إرسال تنبيه بالبريد الإلكتروني
   */
  private async sendEmailAlert(admins: any[], alert: any): Promise<void> {
    // تنفيذ إرسال البريد الإلكتروني
    // يمكن استخدام مكتبة مثل nodemailer
    );
  }

  /**
   * تطبيق إجراءات أمنية تلقائية
   */
  private async applySecurityMeasures(alert: any): Promise<void> {
    try {
      // حظر IP مؤقت للأنشطة المشبوهة الشديدة
      if (alert.count > 10) {
        await this.temporaryIPBlock(alert.ip, 30 * 60 * 1000); // 30 minutes
      }

      // تعليق حساب المستخدم للأنشطة الحرجة
      if (alert.type === 'SUSPICIOUS_ACTIVITY_PATTERN' && alert.count > 15) {
        await this.suspendUser(alert.userId, 'Suspicious activity detected');
      }

      // تسجيل الإجراء المتخذ
      await logSecurityEvent({
        type: 'SECURITY_MEASURE_APPLIED',
        userId: alert.userId,
        ip: alert.ip,
        details: `Security measures applied for alert: ${alert.type}`,
        severity: 'HIGH'
      });
    } catch (error) {
      console.error('Failed to apply security measures:', error);
    }
  }

  /**
   * حظر IP مؤقت
   */
  private async temporaryIPBlock(ip: string, duration: number): Promise<void> {
    await prisma.blockedIP.create({
      data: {
        ip,
        reason: 'Suspicious activity detected',
        blockedUntil: new Date(Date.now() + duration),
        isActive: true
      }
    });
  }

  /**
   * تعليق حساب المستخدم
   */
  private async suspendUser(userId: string, reason: string): Promise<void> {
    await prisma.user.update({
      where: { id: parseInt(userId) },
      data: {
        status: 'suspended',
        suspensionReason: reason,
        suspendedAt: new Date()
      }
    });
  }

  /**
   * تنظيف التنبيهات القديمة
   */
  private cleanupOldAlerts(): void {
    const cutoff = Date.now() - this.ALERT_WINDOW;

    for (const [key, alert] of this.alerts.entries()) {
      if (alert.timestamp < cutoff) {
        this.alerts.delete(key);
      }
    }
  }

  /**
   * الحصول على إحصائيات الأمان
   */
  async getSecurityStats(): Promise<any> {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [
      totalEvents,
      criticalEvents,
      recentEvents,
      weeklyEvents,
      blockedIPs,
      suspendedUsers
    ] = await Promise.all([
      prisma.securityLog.count(),
      prisma.securityLog.count({ where: { severity: 'CRITICAL' } }),
      prisma.securityLog.count({ where: { timestamp: { gte: last24Hours } } }),
      prisma.securityLog.count({ where: { timestamp: { gte: lastWeek } } }),
      prisma.blockedIP.count({ where: { isActive: true } }),
      prisma.user.count({ where: { status: 'suspended' } })
    ]);

    return {
      totalEvents,
      criticalEvents,
      recentEvents,
      weeklyEvents,
      blockedIPs,
      suspendedUsers,
      activeAlerts: this.alerts.size
    };
  }

  /**
   * الحصول على أحدث الأحداث الأمنية
   */
  async getRecentSecurityEvents(limit: number = 50): Promise<any[]> {
    return await prisma.securityLog.findMany({
      orderBy: { timestamp: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
  }
}
