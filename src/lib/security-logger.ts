import { prisma } from '@/lib/prisma';

export interface SecurityEvent {
  type: SecurityEventType;
  userId?: string;
  ip?: string;
  userAgent?: string;
  path?: string;
  method?: string;
  details?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export type SecurityEventType = 
  | 'LOGIN_SUCCESS'
  | 'LOGIN_FAILURE'
  | 'LOGOUT'
  | 'UNAUTHORIZED_ACCESS_ATTEMPT'
  | 'RATE_LIMIT_EXCEEDED'
  | 'INSUFFICIENT_PERMISSIONS'
  | 'INSUFFICIENT_ROLE'
  | 'INVALID_INPUT'
  | 'API_ACCESS'
  | 'API_ERROR'
  | 'PASSWORD_CHANGE'
  | 'ACCOUNT_LOCKED'
  | 'SUSPICIOUS_ACTIVITY'
  | 'DATA_BREACH_ATTEMPT'
  | 'SQL_INJECTION_ATTEMPT'
  | 'XSS_ATTEMPT';

/**
 * تسجيل حدث أمني
 */
export async function logSecurityEvent(event: SecurityEvent): Promise<void> {
  try {
    // تحديد مستوى الخطورة تلقائياً إذا لم يتم تحديده
    const severity = event.severity || getSeverityByType(event.type);
    
    // تسجيل في قاعدة البيانات
    await prisma.securityLog.create({
      data: {
        type: event.type,
        userId: event.userId ? parseInt(event.userId) : null,
        ip: event.ip,
        userAgent: event.userAgent,
        path: event.path,
        method: event.method,
        details: event.details,
        severity,
        timestamp: new Date()
      }
    });

    // تسجيل في ملف النظام للأحداث الحرجة
    if (severity === 'CRITICAL' || severity === 'HIGH') {
      console.error(`[SECURITY ${severity}] ${event.type}:`, {
        userId: event.userId,
        ip: event.ip,
        path: event.path,
        details: event.details,
        timestamp: new Date().toISOString()
      });
    }

    // إرسال تنبيه للمشرفين للأحداث الحرجة
    if (severity === 'CRITICAL') {
      await sendSecurityAlert(event);
    }

  } catch (error) {
    // في حالة فشل التسجيل، سجل في console على الأقل
    console.error('Failed to log security event:', error);
    console.error('Original security event:', event);
  }
}

/**
 * تحديد مستوى الخطورة بناءً على نوع الحدث
 */
function getSeverityByType(type: SecurityEventType): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const severityMap: Record<SecurityEventType, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
    'LOGIN_SUCCESS': 'LOW',
    'LOGIN_FAILURE': 'MEDIUM',
    'LOGOUT': 'LOW',
    'UNAUTHORIZED_ACCESS_ATTEMPT': 'HIGH',
    'RATE_LIMIT_EXCEEDED': 'MEDIUM',
    'INSUFFICIENT_PERMISSIONS': 'MEDIUM',
    'INSUFFICIENT_ROLE': 'MEDIUM',
    'INVALID_INPUT': 'MEDIUM',
    'API_ACCESS': 'LOW',
    'API_ERROR': 'MEDIUM',
    'PASSWORD_CHANGE': 'MEDIUM',
    'ACCOUNT_LOCKED': 'HIGH',
    'SUSPICIOUS_ACTIVITY': 'HIGH',
    'DATA_BREACH_ATTEMPT': 'CRITICAL',
    'SQL_INJECTION_ATTEMPT': 'CRITICAL',
    'XSS_ATTEMPT': 'HIGH'
  };

  return severityMap[type] || 'MEDIUM';
}

/**
 * إرسال تنبيه أمني للمشرفين
 */
async function sendSecurityAlert(event: SecurityEvent): Promise<void> {
  try {
    // هنا يمكن إضافة إرسال بريد إلكتروني أو إشعار
    // For now, just log to console
    console.warn(`🚨 CRITICAL SECURITY ALERT: ${event.type}`, {
      userId: event.userId,
      ip: event.ip,
      details: event.details,
      timestamp: new Date().toISOString()
    });

    // يمكن إضافة تكامل مع خدمات التنبيه مثل:
    // - Slack
    // - Discord
    // - Email
    // - SMS
    
  } catch (error) {
    console.error('Failed to send security alert:', error);
  }
}

/**
 * الحصول على سجلات الأمان للمستخدم
 */
export async function getUserSecurityLogs(
  userId: string,
  limit: number = 50
): Promise<any[]> {
  try {
    return await prisma.securityLog.findMany({
      where: { userId: parseInt(userId) },
      orderBy: { timestamp: 'desc' },
      take: limit,
      select: {
        id: true,
        type: true,
        ip: true,
        userAgent: true,
        path: true,
        method: true,
        details: true,
        severity: true,
        timestamp: true
      }
    });
  } catch (error) {
    console.error('Failed to get user security logs:', error);
    return [];
  }
}

/**
 * الحصول على سجلات الأمان حسب النوع
 */
export async function getSecurityLogsByType(
  type: SecurityEventType,
  limit: number = 100
): Promise<any[]> {
  try {
    return await prisma.securityLog.findMany({
      where: { type },
      orderBy: { timestamp: 'desc' },
      take: limit
    });
  } catch (error) {
    console.error('Failed to get security logs by type:', error);
    return [];
  }
}

/**
 * تنظيف السجلات القديمة
 */
export async function cleanupOldSecurityLogs(daysToKeep: number = 90): Promise<void> {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const result = await prisma.securityLog.deleteMany({
      where: {
        timestamp: {
          lt: cutoffDate
        },
        severity: {
          in: ['LOW', 'MEDIUM'] // احتفظ بالسجلات عالية الخطورة لفترة أطول
        }
      }
    });

    console.log(`Cleaned up ${result.count} old security logs`);
  } catch (error) {
    console.error('Failed to cleanup old security logs:', error);
  }
}

/**
 * كشف الأنشطة المشبوهة
 */
export async function detectSuspiciousActivity(
  ip: string,
  timeWindowMinutes: number = 15
): Promise<boolean> {
  try {
    const cutoffTime = new Date();
    cutoffTime.setMinutes(cutoffTime.getMinutes() - timeWindowMinutes);

    const suspiciousEvents = await prisma.securityLog.count({
      where: {
        ip,
        timestamp: { gte: cutoffTime },
        type: {
          in: [
            'LOGIN_FAILURE',
            'UNAUTHORIZED_ACCESS_ATTEMPT',
            'RATE_LIMIT_EXCEEDED',
            'INVALID_INPUT'
          ]
        }
      }
    });

    // إذا كان هناك أكثر من 10 أحداث مشبوهة في 15 دقيقة
    return suspiciousEvents > 10;
  } catch (error) {
    console.error('Failed to detect suspicious activity:', error);
    return false;
  }
}
