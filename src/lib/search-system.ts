import { prisma } from './prisma';
import { logger } from './logger';

export type SearchEntity = 'CUSTOMERS' | 'PRODUCTS' | 'INVOICES' | 'ALL';

interface SearchOptions {
  query: string;
  entity: SearchEntity;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  fuzzy?: boolean;
}

interface SearchResult {
  entity: string;
  id: number;
  title: string;
  subtitle?: string;
  description?: string;
  metadata?: Record<string, any>;
  relevanceScore?: number;
}

interface SearchResponse {
  results: SearchResult[];
  totalCount: number;
  searchTime: number;
  suggestions?: string[];
}

/**
 * نظام البحث المتقدم والذكي
 */
export class SearchSystem {

  /**
   * البحث الشامل
   */
  async search(options: SearchOptions, userId: string): Promise<SearchResponse> {
    const startTime = Date.now();

    try {
      logger.debug('Search started', {
        userId,
        query: options.query,
        entity: options.entity,
        filters: options.filters
      });

      let results: SearchResult[] = [];
      let totalCount = 0;

      if (options.entity === 'ALL') {
        // البحث في جميع الكيانات
        const [customers, products, invoices] = await Promise.all([
          this.searchCustomers(options),
          this.searchProducts(options),
          this.searchInvoices(options)
        ]);

        results = [...customers.results, ...products.results, ...invoices.results];
        totalCount = customers.totalCount + products.totalCount + invoices.totalCount;
      } else {
        // البحث في كيان محدد
        const entityResults = await this.searchEntity(options);
        results = entityResults.results;
        totalCount = entityResults.totalCount;
      }

      // ترتيب النتائج حسب الصلة
      if (options.fuzzy) {
        results = this.rankResults(results, options.query);
      }

      // تطبيق الحد والإزاحة
      const limit = options.limit || 20;
      const offset = options.offset || 0;
      const paginatedResults = results.slice(offset, offset + limit);

      const searchTime = Date.now() - startTime;

      // إنشاء اقتراحات البحث
      const suggestions = await this.generateSuggestions(options.query, options.entity);

      logger.debug('Search completed', {
        userId,
        query: options.query,
        resultCount: paginatedResults.length,
        totalCount,
        searchTime
      });

      return {
        results: paginatedResults,
        totalCount,
        searchTime,
        suggestions
      };

    } catch (error) {
      const searchTime = Date.now() - startTime;

      logger.error('Search failed', error, {
        userId,
        query: options.query,
        entity: options.entity,
        searchTime
      });

      return {
        results: [],
        totalCount: 0,
        searchTime,
        suggestions: []
      };
    }
  }

  /**
   * البحث في كيان محدد
   */
  private async searchEntity(options: SearchOptions): Promise<{ results: SearchResult[]; totalCount: number }> {
    switch (options.entity) {
      case 'CUSTOMERS':
        return this.searchCustomers(options);
      case 'PRODUCTS':
        return this.searchProducts(options);
      case 'INVOICES':
        return this.searchInvoices(options);
      default:
        return { results: [], totalCount: 0 };
    }
  }

  /**
   * البحث في العملاء
   */
  private async searchCustomers(options: SearchOptions): Promise<{ results: SearchResult[]; totalCount: number }> {
    const searchTerms = this.prepareSearchTerms(options.query);

    const where: any = {
      OR: [
        { name: { contains: options.query, mode: 'insensitive' } },
        { email: { contains: options.query, mode: 'insensitive' } },
        { phone: { contains: options.query, mode: 'insensitive' } },
        { address: { contains: options.query, mode: 'insensitive' } }
      ]
    };

    // تطبيق الفلاتر الإضافية
    if (options.filters?.createdAfter) {
      where.createdAt = { gte: new Date(options.filters.createdAfter) };
    }

    const [customers, totalCount] = await Promise.all([
      prisma.customer.findMany({
        where,
        orderBy: this.buildOrderBy(options.sortBy, options.sortOrder),
        take: options.limit || 20,
        skip: options.offset || 0
      }),
      prisma.customer.count({ where })
    ]);

    const results: SearchResult[] = customers.map(customer => ({
      entity: 'CUSTOMER',
      id: customer.id,
      title: customer.name,
      subtitle: customer.email || customer.phone || '',
      description: customer.address || '',
      metadata: {
        taxNumber: customer.taxNumber,
        createdAt: customer.createdAt
      },
      relevanceScore: this.calculateRelevance(options.query, [
        customer.name,
        customer.email || '',
        customer.phone || ''
      ])
    }));

    return { results, totalCount };
  }

  /**
   * البحث في المنتجات
   */
  private async searchProducts(options: SearchOptions): Promise<{ results: SearchResult[]; totalCount: number }> {
    const where: any = {
      OR: [
        { name: { contains: options.query, mode: 'insensitive' } },
        { description: { contains: options.query, mode: 'insensitive' } },
        { barcode: { contains: options.query, mode: 'insensitive' } }
      ]
    };

    // تطبيق الفلاتر الإضافية
    if (options.filters?.categoryId) {
      where.categoryId = parseInt(options.filters.categoryId);
    }

    if (options.filters?.lowStock) {
      where.stockQty = { lte: 10 };
    }

    if (options.filters?.priceRange) {
      where.price = {
        gte: options.filters.priceRange.min || 0,
        lte: options.filters.priceRange.max || 999999
      };
    }

    const [products, totalCount] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: true
        },
        orderBy: this.buildOrderBy(options.sortBy, options.sortOrder),
        take: options.limit || 20,
        skip: options.offset || 0
      }),
      prisma.product.count({ where })
    ]);

    const results: SearchResult[] = products.map(product => ({
      entity: 'PRODUCT',
      id: product.id,
      title: product.name,
      subtitle: `${product.price} درهم - ${product.stockQty} متوفر`,
      description: product.description || '',
      metadata: {
        barcode: product.barcode,
        category: product.category.name,
        price: product.price,
        stockQty: product.stockQty,
        cost: product.cost
      },
      relevanceScore: this.calculateRelevance(options.query, [
        product.name,
        product.description || '',
        product.barcode,
        product.category.name
      ])
    }));

    return { results, totalCount };
  }

  /**
   * البحث في الفواتير
   */
  private async searchInvoices(options: SearchOptions): Promise<{ results: SearchResult[]; totalCount: number }> {
    const where: any = {
      OR: [
        { invoiceNumber: { contains: options.query, mode: 'insensitive' } },
        { notes: { contains: options.query, mode: 'insensitive' } },
        { customer: { name: { contains: options.query, mode: 'insensitive' } } }
      ]
    };

    // تطبيق الفلاتر الإضافية
    if (options.filters?.paymentStatus) {
      where.paymentStatus = options.filters.paymentStatus;
    }

    if (options.filters?.dateRange) {
      where.createdAt = {
        gte: new Date(options.filters.dateRange.startDate),
        lte: new Date(options.filters.dateRange.endDate)
      };
    }

    if (options.filters?.amountRange) {
      where.total = {
        gte: options.filters.amountRange.min || 0,
        lte: options.filters.amountRange.max || 999999
      };
    }

    const [invoices, totalCount] = await Promise.all([
      prisma.invoice.findMany({
        where,
        include: {
          customer: true,
          items: {
            include: {
              product: true
            }
          }
        },
        orderBy: this.buildOrderBy(options.sortBy, options.sortOrder, 'createdAt'),
        take: options.limit || 20,
        skip: options.offset || 0
      }),
      prisma.invoice.count({ where })
    ]);

    const results: SearchResult[] = invoices.map(invoice => ({
      entity: 'INVOICE',
      id: invoice.id,
      title: `فاتورة ${invoice.invoiceNumber}`,
      subtitle: `${invoice.customer.name} - ${invoice.total} درهم`,
      description: invoice.notes || `${invoice.items.length} صنف`,
      metadata: {
        customerName: invoice.customer.name,
        total: invoice.total,
        paymentStatus: invoice.paymentStatus,
        itemCount: invoice.items.length,
        createdAt: invoice.createdAt
      },
      relevanceScore: this.calculateRelevance(options.query, [
        invoice.invoiceNumber,
        invoice.customer.name,
        invoice.notes || ''
      ])
    }));

    return { results, totalCount };
  }

  /**
   * تحضير مصطلحات البحث
   */
  private prepareSearchTerms(query: string): string[] {
    return query
      .toLowerCase()
      .split(/\s+/)
      .filter(term => term.length > 1);
  }

  /**
   * حساب درجة الصلة
   */
  private calculateRelevance(query: string, fields: string[]): number {
    const queryLower = query.toLowerCase();
    let score = 0;

    fields.forEach(field => {
      const fieldLower = field.toLowerCase();

      // تطابق كامل
      if (fieldLower === queryLower) {
        score += 100;
      }
      // يبدأ بالاستعلام
      else if (fieldLower.startsWith(queryLower)) {
        score += 80;
      }
      // يحتوي على الاستعلام
      else if (fieldLower.includes(queryLower)) {
        score += 60;
      }
      // تطابق جزئي للكلمات
      else {
        const queryWords = queryLower.split(/\s+/);
        const fieldWords = fieldLower.split(/\s+/);

        queryWords.forEach(queryWord => {
          fieldWords.forEach(fieldWord => {
            if (fieldWord.includes(queryWord) || queryWord.includes(fieldWord)) {
              score += 20;
            }
          });
        });
      }
    });

    return score;
  }

  /**
   * ترتيب النتائج حسب الصلة
   */
  private rankResults(results: SearchResult[], query: string): SearchResult[] {
    return results.sort((a, b) => {
      const scoreA = a.relevanceScore || 0;
      const scoreB = b.relevanceScore || 0;
      return scoreB - scoreA;
    });
  }

  /**
   * بناء ترتيب الاستعلام
   */
  private buildOrderBy(sortBy?: string, sortOrder: 'asc' | 'desc' = 'desc', defaultField = 'id'): any {
    const field = sortBy || defaultField;
    return { [field]: sortOrder };
  }

  /**
   * إنشاء اقتراحات البحث
   */
  private async generateSuggestions(query: string, entity: SearchEntity): Promise<string[]> {
    if (query.length < 2) return [];

    try {
      const suggestions: string[] = [];

      // اقتراحات من أسماء العملاء
      if (entity === 'CUSTOMERS' || entity === 'ALL') {
        const customers = await prisma.customer.findMany({
          where: {
            name: {
              contains: query,
              mode: 'insensitive'
            }
          },
          select: { name: true },
          take: 5
        });

        suggestions.push(...customers.map(c => c.name));
      }

      // اقتراحات من أسماء المنتجات
      if (entity === 'PRODUCTS' || entity === 'ALL') {
        const products = await prisma.product.findMany({
          where: {
            name: {
              contains: query,
              mode: 'insensitive'
            }
          },
          select: { name: true },
          take: 5
        });

        suggestions.push(...products.map(p => p.name));
      }

      // إزالة التكرارات وترتيب
      return [...new Set(suggestions)]
        .filter(s => s.toLowerCase() !== query.toLowerCase())
        .slice(0, 5);

    } catch (error) {
      logger.error('Failed to generate search suggestions', error);
      return [];
    }
  }

  /**
   * البحث المتقدم مع فلاتر معقدة
   */
  async advancedSearch(
    query: string,
    filters: {
      entities?: SearchEntity[];
      dateRange?: { startDate: Date; endDate: Date };
      priceRange?: { min: number; max: number };
      categories?: number[];
      paymentStatuses?: string[];
      customFilters?: Record<string, any>;
    },
    userId: string
  ): Promise<SearchResponse> {
    const startTime = Date.now();

    try {
      const results: SearchResult[] = [];
      let totalCount = 0;

      const entities = filters.entities || ['ALL'];

      for (const entity of entities) {
        const searchOptions: SearchOptions = {
          query,
          entity,
          filters: {
            ...filters.customFilters,
            dateRange: filters.dateRange,
            priceRange: filters.priceRange,
            categoryId: filters.categories?.[0], // تبسيط للمثال
            paymentStatus: filters.paymentStatuses?.[0] // تبسيط للمثال
          },
          fuzzy: true,
          limit: 100 // حد أعلى للبحث المتقدم
        };

        const entityResults = await this.searchEntity(searchOptions);
        results.push(...entityResults.results);
        totalCount += entityResults.totalCount;
      }

      // ترتيب النتائج النهائية
      const rankedResults = this.rankResults(results, query);

      const searchTime = Date.now() - startTime;

      logger.info('Advanced search completed', {
        userId,
        query,
        filters,
        resultCount: rankedResults.length,
        searchTime
      });

      return {
        results: rankedResults,
        totalCount,
        searchTime,
        suggestions: await this.generateSuggestions(query, 'ALL')
      };

    } catch (error) {
      logger.error('Advanced search failed', error, { userId, query, filters });

      return {
        results: [],
        totalCount: 0,
        searchTime: Date.now() - startTime,
        suggestions: []
      };
    }
  }
}

// مثيل نظام البحث العام
export const searchSystem = new SearchSystem();
