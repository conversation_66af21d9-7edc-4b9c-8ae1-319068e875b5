/**
 * وظائف مساعدة للتفاعل مع Electron
 * Utility functions for interacting with Electron
 */

/**
 * التحقق بدقة مما إذا كان التطبيق يعمل في بيئة Electron
 * يستخدم مجموعة من الفحوصات المتعددة للتأكد من وجود Electron
 *
 * Accurately check if the app is running in an Electron environment
 * Uses multiple checks to ensure Electron is present
 */
export const isElectron = (): boolean => {
  // فحص أساسي: التأكد من وجود كائن window
  // Basic check: Make sure window object exists
  if (typeof window === 'undefined') return false;

  // الفحص الرئيسي: وجود عملية renderer في Electron
  // Main check: Presence of renderer process in Electron
  const hasRendererProcess =
    typeof window.process === 'object' &&
    (window.process as any)?.type === 'renderer';

  // فحوصات إضافية للتأكيد
  // Additional checks for confirmation
  const hasElectronProperty =
    typeof (window as any).electron !== 'undefined';

  const hasNodeIntegration =
    typeof window.require === 'function';

  const hasVersions =
    typeof (window.process as any)?.versions?.electron !== 'undefined';

  // اعتبر التطبيق يعمل في Electron إذا كان الفحص الرئيسي مع واحد على الأقل من الفحوصات الإضافية صحيحاً
  // Consider the app running in Electron if the main check with at least one of the additional checks is true
  return hasRendererProcess && (hasElectronProperty || hasNodeIntegration || hasVersions);
};

/**
 * التحقق من قدرات Electron المتاحة في التطبيق الحالي
 * يمكن استخدامه للتحقق من ميزات معينة في Electron قبل استخدامها
 *
 * Check Electron capabilities available in the current app
 * Can be used to check for specific Electron features before using them
 */
export const getElectronCapabilities = () => {
  if (!isElectron()) {
    return {
      available: false,
      filesystem: false,
      printing: false,
      notifications: false,
      ipc: false
    };
  }

  // التحقق من وجود كائن electron الذي يحتوي على واجهات API المختلفة
  // Check for the electron object that contains various API interfaces
  const api = (window as any).electron;

  return {
    available: true,
    filesystem: typeof api?.filesystem === 'object',
    printing: typeof api?.printing === 'function' || typeof api?.printPDF === 'function',
    notifications: typeof api?.showNotification === 'function',
    ipc: typeof api?.send === 'function' || typeof api?.invoke === 'function'
  };
};

/**
 * استدعاء آمن لواجهة API في Electron
 * يتحقق أولاً من وجود الواجهة قبل استدعائها لتجنب الأخطاء
 *
 * Safely call an API interface in Electron
 * First checks for the existence of the interface before calling it to avoid errors
 */
export const callElectronApi = async <T>(
  apiPath: string,
  args?: any
): Promise<T | null> => {
  if (!isElectron()) return null;

  try {
    const api = (window as any).electron;
    const pathParts = apiPath.split('.');

    // الوصول إلى الدالة المطلوبة حسب المسار (مثل "filesystem.saveFile")
    // Access the required function according to the path (e.g. "filesystem.saveFile")
    let method = api;
    for (const part of pathParts) {
      if (!method[part]) return null;
      method = method[part];
    }

    if (typeof method !== 'function') return null;

    // استدعاء الدالة مع المعاملات
    // Call the function with parameters
    return await method(args);
  } catch (error) {
    console.error(`Error calling Electron API (${apiPath}):`, error);
    return null;
  }
};

/**
 * واجهة لخيارات مربع حوار حفظ الملف
 * Interface for save dialog options
 */
export interface SaveDialogOptions {
  title?: string;
  defaultPath?: string;
  buttonLabel?: string;
  filters?: Array<{
    name: string;
    extensions: string[];
  }>;
  message?: string;
  nameFieldLabel?: string;
  showsTagField?: boolean;
  properties?: string[];
  securityScopedBookmarks?: boolean;
}

/**
 * واجهة لخيارات مربع حوار فتح الملف
 * Interface for open dialog options
 */
export interface OpenDialogOptions {
  title?: string;
  defaultPath?: string;
  buttonLabel?: string;
  filters?: Array<{
    name: string;
    extensions: string[];
  }>;
  properties?: string[];
  message?: string;
  securityScopedBookmarks?: boolean;
}

/**
 * واجهة لخيارات مربع حوار الرسالة
 * Interface for message box options
 */
export interface MessageBoxOptions {
  type?: 'none' | 'info' | 'error' | 'question' | 'warning';
  title?: string;
  message: string;
  detail?: string;
  buttons?: string[];
  defaultId?: number;
  cancelId?: number;
  noLink?: boolean;
  normalizeAccessKeys?: boolean;
  icon?: string;
}

/**
 * عرض مربع حوار حفظ الملف
 * Show save file dialog
 */
export const showSaveDialog = async (options: SaveDialogOptions): Promise<{ canceled: boolean; filePath?: string }> => {
  if (isElectron()) {
    return await callElectronApi<{ canceled: boolean; filePath?: string }>('showSaveDialog', options) ||
      { canceled: true };
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  console.warn('Save dialog is only available in Electron environment');
  return { canceled: true };
};

/**
 * عرض مربع حوار فتح الملف
 * Show open file dialog
 */
export const showOpenDialog = async (options: OpenDialogOptions): Promise<{ canceled: boolean; filePaths: string[] }> => {
  if (isElectron()) {
    return await callElectronApi<{ canceled: boolean; filePaths: string[] }>('showOpenDialog', options) ||
      { canceled: true, filePaths: [] };
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  console.warn('Open dialog is only available in Electron environment');
  return { canceled: true, filePaths: [] };
};

/**
 * عرض مربع حوار رسالة
 * Show message box
 */
export const showMessageBox = async (options: MessageBoxOptions): Promise<{ response: number; checkboxChecked: boolean }> => {
  if (isElectron()) {
    return await callElectronApi<{ response: number; checkboxChecked: boolean }>('showMessageBox', options) ||
      { response: 0, checkboxChecked: false };
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  console.warn('Message box is only available in Electron environment');
  alert(options.message);
  return { response: 0, checkboxChecked: false };
};

/**
 * الحصول على معلومات النظام
 * Get system information
 */
export const getSystemInfo = async (): Promise<{
  platform: string;
  arch: string;
  version: string;
  nodeVersion?: string;
  chromeVersion?: string;
}> => {
  if (isElectron()) {
    const info = await callElectronApi<{
      platform: string;
      arch: string;
      version: string;
      nodeVersion: string;
      chromeVersion: string;
    }>('getSystemInfo');

    if (info) return info;

    // محاولة الحصول على معلومات النظام من خلال navigator
    // Try to get system info through navigator
    const userAgent = navigator.userAgent;

    // تحديد نوع المنصة
    // Determine platform type
    let platform = 'unknown';
    if (userAgent.includes('Win')) {
      platform = 'windows';
    } else if (userAgent.includes('Mac')) {
      platform = 'macos';
    } else if (userAgent.includes('Linux')) {
      platform = 'linux';
    }

    // تحديد نوع المعمارية
    // Determine architecture
    let arch = 'x86';
    if (userAgent.includes('x86_64') || userAgent.includes('x86-64') || userAgent.includes('Win64') || userAgent.includes('x64')) {
      arch = 'x64';
    } else if (userAgent.includes('arm64') || userAgent.includes('aarch64')) {
      arch = 'arm64';
    }

    return {
      platform,
      arch,
      version: '1.0.0'
    };
  }

  // محاكاة في بيئة الويب
  // Fallback for web environment
  return {
    platform: 'web',
    arch: 'web',
    version: 'web'
  };
};
