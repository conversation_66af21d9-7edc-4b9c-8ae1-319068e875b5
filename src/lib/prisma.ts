import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query', 'error', 'warn'],
  errorFormat: 'pretty',
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// دالة مساعدة لإغلاق الاتصال
export async function disconnectPrisma() {
  await prisma.$disconnect();
}

// دالة مساعدة للتحقق من الاتصال
export async function checkDatabaseConnection() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { success: true, message: 'Database connection successful' };
  } catch (error) {
    console.error('Database connection failed:', error);
    return { success: false, message: 'Database connection failed', error };
  }
}

// دالة مساعدة لتنفيذ المعاملات
export async function executeTransaction<T>(
  operations: (prisma: PrismaClient) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(async (tx) => {
    return await operations(tx as PrismaClient);
  });
}

// دالة مساعدة للبحث مع الترقيم
export interface PaginationOptions {
  page?: number;
  limit?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export function calculatePagination(options: PaginationOptions) {
  const page = Math.max(1, options.page || 1);
  const limit = Math.min(100, Math.max(1, options.limit || 10));
  const skip = (page - 1) * limit;

  return { page, limit, skip };
}

export function createPaginatedResult<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): PaginatedResult<T> {
  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

// دالة مساعدة للبحث النصي
export function createSearchFilter(searchTerm: string, fields: string[]) {
  if (!searchTerm || !fields.length) return {};

  return {
    OR: fields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive' as const,
      },
    })),
  };
}

// دالة مساعدة لإنشاء رقم فاتورة فريد
export async function generateInvoiceNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');

  // البحث عن آخر فاتورة في الشهر الحالي
  const lastInvoice = await prisma.invoice.findFirst({
    where: {
      number: {
        startsWith: `INV-${year}${month}`,
      },
    },
    orderBy: {
      number: 'desc',
    },
  });

  let nextNumber = 1;
  if (lastInvoice) {
    const lastNumber = parseInt(lastInvoice.number.split('-')[2]);
    nextNumber = lastNumber + 1;
  }

  return `INV-${year}${month}-${String(nextNumber).padStart(4, '0')}`;
}

// دالة مساعدة لإنشاء رمز منتج فريد
export async function generateProductSKU(prefix: string = 'PROD'): Promise<string> {
  const timestamp = Date.now().toString().slice(-6);
  const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

  let sku = `${prefix}-${timestamp}-${randomNum}`;

  // التحقق من عدم وجود SKU مشابه
  const existingProduct = await prisma.product.findUnique({
    where: { sku },
  });

  if (existingProduct) {
    // إعادة المحاولة مع رقم عشوائي جديد
    return generateProductSKU(prefix);
  }

  return sku;
}

export default prisma;