/**
 * نظام الأمان المتقدم لأمين بلس
 * Advanced Security System for Amin Plus
 */

import crypto from 'crypto';
import { prisma } from './prisma';
import { logSecurityEvent } from './security-logger';

/**
 * مدير التشفير المتقدم
 * Advanced Encryption Manager
 */
export class AdvancedEncryption {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;

  /**
   * إنشاء مفتاح تشفير قوي
   */
  static generateEncryptionKey(): string {
    return crypto.randomBytes(this.KEY_LENGTH).toString('hex');
  }

  /**
   * تشفير البيانات مع مصادقة
   */
  static encrypt(data: string, key?: string): {
    encrypted: string;
    iv: string;
    tag: string;
    salt: string;
  } {
    const salt = crypto.randomBytes(16);
    const derivedKey = crypto.pbkdf2Sync(
      key || process.env.MASTER_KEY || 'default-key',
      salt,
      100000,
      this.KEY_LENGTH,
      'sha256'
    );

    const iv = crypto.randomBytes(this.IV_LENGTH);
    const cipher = crypto.createCipherGCM(this.ALGORITHM, derivedKey, iv);

    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
      salt: salt.toString('hex')
    };
  }

  /**
   * فك التشفير مع التحقق من المصادقة
   */
  static decrypt(
    encrypted: string,
    iv: string,
    tag: string,
    salt: string,
    key?: string
  ): string {
    const derivedKey = crypto.pbkdf2Sync(
      key || process.env.MASTER_KEY || 'default-key',
      Buffer.from(salt, 'hex'),
      100000,
      this.KEY_LENGTH,
      'sha256'
    );

    const decipher = crypto.createDecipherGCM(
      this.ALGORITHM,
      derivedKey,
      Buffer.from(iv, 'hex')
    );

    decipher.setAuthTag(Buffer.from(tag, 'hex'));

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * تشفير البيانات المالية الحساسة
   */
  static encryptFinancialData(data: any): string {
    const jsonData = JSON.stringify(data);
    const result = this.encrypt(jsonData);
    return JSON.stringify(result);
  }

  /**
   * فك تشفير البيانات المالية
   */
  static decryptFinancialData(encryptedData: string): any {
    const parsed = JSON.parse(encryptedData);
    const decrypted = this.decrypt(
      parsed.encrypted,
      parsed.iv,
      parsed.tag,
      parsed.salt
    );
    return JSON.parse(decrypted);
  }
}

/**
 * مدير كشف التهديدات
 * Threat Detection Manager
 */
export class ThreatDetection {
  private static readonly MAX_LOGIN_ATTEMPTS = 5;
  private static readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
  private static readonly SUSPICIOUS_PATTERNS = [
    /union\s+select/i,
    /drop\s+table/i,
    /<script/i,
    /javascript:/i,
    /eval\(/i,
    /document\.cookie/i
  ];

  /**
   * كشف محاولات تسجيل الدخول المشبوهة
   */
  static async detectSuspiciousLogin(
    email: string,
    ip: string,
    userAgent: string
  ): Promise<{ blocked: boolean; reason?: string }> {
    // فحص عدد المحاولات الفاشلة
    const recentAttempts = await prisma.securityLog.count({
      where: {
        type: 'LOGIN_FAILED',
        ip,
        timestamp: {
          gte: new Date(Date.now() - this.LOCKOUT_DURATION)
        }
      }
    });

    if (recentAttempts >= this.MAX_LOGIN_ATTEMPTS) {
      await logSecurityEvent({
        type: 'ACCOUNT_LOCKED',
        ip,
        userAgent,
        details: `Account locked due to ${recentAttempts} failed attempts`,
        severity: 'HIGH'
      });

      return {
        blocked: true,
        reason: 'Too many failed login attempts. Account temporarily locked.'
      };
    }

    // فحص الأنماط المشبوهة في البريد الإلكتروني
    if (this.containsSuspiciousPattern(email)) {
      await logSecurityEvent({
        type: 'SUSPICIOUS_INPUT',
        ip,
        userAgent,
        details: `Suspicious pattern detected in email: ${email}`,
        severity: 'HIGH'
      });

      return {
        blocked: true,
        reason: 'Suspicious input detected'
      };
    }

    return { blocked: false };
  }

  /**
   * كشف الأنماط المشبوهة في المدخلات
   */
  static containsSuspiciousPattern(input: string): boolean {
    return this.SUSPICIOUS_PATTERNS.some(pattern => pattern.test(input));
  }

  /**
   * كشف محاولات SQL Injection
   */
  static detectSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/i,
      /(--|\/\*|\*\/|;)/,
      /(\b(or|and)\b\s*\d+\s*=\s*\d+)/i,
      /(\b(or|and)\b\s*['"].*['"])/i
    ];

    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * كشف محاولات XSS
   */
  static detectXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  /**
   * تحليل سلوك المستخدم
   */
  static async analyzeBehavior(
    userId: string,
    action: string,
    ip: string
  ): Promise<{ suspicious: boolean; score: number }> {
    // الحصول على سجل الأنشطة الأخيرة
    const recentActivities = await prisma.securityLog.findMany({
      where: {
        userId: parseInt(userId),
        timestamp: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // آخر 24 ساعة
        }
      },
      orderBy: { timestamp: 'desc' },
      take: 100
    });

    let suspiciousScore = 0;

    // فحص تغيير IP المتكرر
    const uniqueIPs = new Set(recentActivities.map(a => a.ip));
    if (uniqueIPs.size > 5) {
      suspiciousScore += 30;
    }

    // فحص الأنشطة في أوقات غير عادية
    const currentHour = new Date().getHours();
    if (currentHour < 6 || currentHour > 22) {
      suspiciousScore += 10;
    }

    // فحص تكرار الأنشطة
    const actionCount = recentActivities.filter(a => 
      a.details?.includes(action)
    ).length;
    if (actionCount > 50) {
      suspiciousScore += 25;
    }

    // فحص أنماط الوصول السريع
    const rapidActions = recentActivities.filter(a => 
      Date.now() - a.timestamp.getTime() < 60000 // آخر دقيقة
    );
    if (rapidActions.length > 10) {
      suspiciousScore += 40;
    }

    const isSuspicious = suspiciousScore > 50;

    if (isSuspicious) {
      await logSecurityEvent({
        type: 'SUSPICIOUS_BEHAVIOR',
        userId,
        ip,
        details: `Suspicious behavior detected. Score: ${suspiciousScore}`,
        severity: suspiciousScore > 75 ? 'CRITICAL' : 'HIGH'
      });
    }

    return {
      suspicious: isSuspicious,
      score: suspiciousScore
    };
  }
}

/**
 * مدير الجلسات الآمنة
 * Secure Session Manager
 */
export class SecureSessionManager {
  private static readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private static readonly MAX_CONCURRENT_SESSIONS = 3;

  /**
   * إنشاء جلسة آمنة
   */
  static async createSecureSession(
    userId: string,
    ip: string,
    userAgent: string
  ): Promise<string> {
    // إنشاء معرف جلسة آمن
    const sessionId = crypto.randomBytes(32).toString('hex');
    const sessionToken = crypto.randomBytes(64).toString('hex');

    // تشفير معلومات الجلسة
    const sessionData = AdvancedEncryption.encrypt(JSON.stringify({
      userId,
      ip,
      userAgent,
      createdAt: Date.now(),
      lastActivity: Date.now()
    }));

    // حفظ الجلسة في قاعدة البيانات
    await prisma.userSession.create({
      data: {
        id: sessionId,
        userId: parseInt(userId),
        token: sessionToken,
        ip,
        userAgent,
        encryptedData: JSON.stringify(sessionData),
        expiresAt: new Date(Date.now() + this.SESSION_TIMEOUT),
        isActive: true
      }
    });

    // تنظيف الجلسات القديمة
    await this.cleanupExpiredSessions(userId);

    return sessionToken;
  }

  /**
   * التحقق من صحة الجلسة
   */
  static async validateSession(
    sessionToken: string,
    ip: string
  ): Promise<{ valid: boolean; userId?: string; shouldRefresh?: boolean }> {
    try {
      const session = await prisma.userSession.findFirst({
        where: {
          token: sessionToken,
          isActive: true,
          expiresAt: { gt: new Date() }
        }
      });

      if (!session) {
        return { valid: false };
      }

      // فك تشفير بيانات الجلسة
      const encryptedData = JSON.parse(session.encryptedData);
      const sessionData = JSON.parse(AdvancedEncryption.decrypt(
        encryptedData.encrypted,
        encryptedData.iv,
        encryptedData.tag,
        encryptedData.salt
      ));

      // التحقق من IP (اختياري - يمكن تعطيله للمستخدمين المتنقلين)
      if (process.env.STRICT_IP_VALIDATION === 'true' && sessionData.ip !== ip) {
        await this.invalidateSession(sessionToken);
        await logSecurityEvent({
          type: 'SESSION_IP_MISMATCH',
          userId: sessionData.userId,
          ip,
          details: `IP mismatch. Expected: ${sessionData.ip}, Got: ${ip}`,
          severity: 'HIGH'
        });
        return { valid: false };
      }

      // تحديث آخر نشاط
      await prisma.userSession.update({
        where: { id: session.id },
        data: { lastActivity: new Date() }
      });

      // فحص إذا كانت الجلسة تحتاج تجديد
      const shouldRefresh = Date.now() - session.lastActivity.getTime() > 
        this.SESSION_TIMEOUT / 2;

      return {
        valid: true,
        userId: sessionData.userId,
        shouldRefresh
      };

    } catch (error) {
      console.error('Session validation error:', error);
      return { valid: false };
    }
  }

  /**
   * إلغاء الجلسة
   */
  static async invalidateSession(sessionToken: string): Promise<void> {
    await prisma.userSession.updateMany({
      where: { token: sessionToken },
      data: { isActive: false }
    });
  }

  /**
   * تنظيف الجلسات المنتهية الصلاحية
   */
  static async cleanupExpiredSessions(userId?: string): Promise<void> {
    const where: any = {
      OR: [
        { expiresAt: { lt: new Date() } },
        { isActive: false }
      ]
    };

    if (userId) {
      where.userId = parseInt(userId);
    }

    await prisma.userSession.deleteMany({ where });

    // تحديد عدد الجلسات المتزامنة
    if (userId) {
      const activeSessions = await prisma.userSession.findMany({
        where: {
          userId: parseInt(userId),
          isActive: true,
          expiresAt: { gt: new Date() }
        },
        orderBy: { lastActivity: 'desc' }
      });

      if (activeSessions.length > this.MAX_CONCURRENT_SESSIONS) {
        const sessionsToRemove = activeSessions.slice(this.MAX_CONCURRENT_SESSIONS);
        await prisma.userSession.deleteMany({
          where: {
            id: { in: sessionsToRemove.map(s => s.id) }
          }
        });
      }
    }
  }
}
