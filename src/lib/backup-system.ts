import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { createReadStream, createWriteStream } from 'fs';
import { createGzip } from 'zlib';
import { pipeline } from 'stream/promises';
import { logSecurityEvent } from './security-logger';

const execAsync = promisify(exec);

interface BackupConfig {
  enabled: boolean;
  intervalHours: number;
  retentionDays: number;
  backupPath: string;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}

interface BackupResult {
  success: boolean;
  filePath?: string;
  size?: number;
  duration?: number;
  error?: string;
}

/**
 * إعدادات النسخ الاحتياطي الافتراضية
 */
const DEFAULT_CONFIG: BackupConfig = {
  enabled: process.env.BACKUP_ENABLED === 'true',
  intervalHours: parseInt(process.env.BACKUP_INTERVAL_HOURS || '24'),
  retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS || '30'),
  backupPath: path.join(process.cwd(), 'backups'),
  compressionEnabled: true,
  encryptionEnabled: true
};

/**
 * إنشاء نسخة احتياطية من قاعدة البيانات
 */
export async function createDatabaseBackup(config: Partial<BackupConfig> = {}): Promise<BackupResult> {
  const startTime = Date.now();
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  if (!finalConfig.enabled) {
    return { success: false, error: 'النسخ الاحتياطي معطل' };
  }

  try {
    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    await ensureBackupDirectory(finalConfig.backupPath);

    // إنشاء اسم الملف
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `backup-${timestamp}.db`;
    const filePath = path.join(finalConfig.backupPath, fileName);

    // نسخ قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'prisma', 'dev.db');
    await fs.copyFile(dbPath, filePath);

    let finalPath = filePath;
    let fileSize = (await fs.stat(filePath)).size;

    // ضغط الملف إذا كان مفعلاً
    if (finalConfig.compressionEnabled) {
      const compressedPath = `${filePath}.gz`;
      await compressFile(filePath, compressedPath);
      await fs.unlink(filePath); // حذف الملف غير المضغوط
      finalPath = compressedPath;
      fileSize = (await fs.stat(compressedPath)).size;
    }

    // تشفير الملف إذا كان مفعلاً
    if (finalConfig.encryptionEnabled) {
      const encryptedPath = `${finalPath}.enc`;
      await encryptFile(finalPath, encryptedPath);
      await fs.unlink(finalPath); // حذف الملف غير المشفر
      finalPath = encryptedPath;
      fileSize = (await fs.stat(encryptedPath)).size;
    }

    const duration = Date.now() - startTime;

    // تسجيل النسخة الاحتياطية
    await logSecurityEvent({
      type: 'BACKUP_CREATED',
      details: `Backup created: ${path.basename(finalPath)}, Size: ${formatFileSize(fileSize)}, Duration: ${duration}ms`
    });

    // تنظيف النسخ القديمة
    await cleanupOldBackups(finalConfig);

    return {
      success: true,
      filePath: finalPath,
      size: fileSize,
      duration
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    await logSecurityEvent({
      type: 'BACKUP_FAILED',
      details: `Backup failed: ${errorMessage}`,
      severity: 'HIGH'
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * استعادة قاعدة البيانات من نسخة احتياطية
 */
export async function restoreFromBackup(backupFilePath: string): Promise<BackupResult> {
  const startTime = Date.now();

  try {
    if (!(await fs.access(backupFilePath).then(() => true).catch(() => false))) {
      throw new Error('ملف النسخة الاحتياطية غير موجود');
    }

    let workingPath = backupFilePath;

    // فك التشفير إذا كان الملف مشفراً
    if (backupFilePath.endsWith('.enc')) {
      const decryptedPath = backupFilePath.replace('.enc', '');
      await decryptFile(backupFilePath, decryptedPath);
      workingPath = decryptedPath;
    }

    // فك الضغط إذا كان الملف مضغوطاً
    if (workingPath.endsWith('.gz')) {
      const decompressedPath = workingPath.replace('.gz', '');
      await decompressFile(workingPath, decompressedPath);
      if (workingPath !== backupFilePath) {
        await fs.unlink(workingPath); // حذف الملف المؤقت
      }
      workingPath = decompressedPath;
    }

    // استعادة قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'prisma', 'dev.db');
    
    // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
    const currentBackupPath = `${dbPath}.backup-${Date.now()}`;
    await fs.copyFile(dbPath, currentBackupPath);

    // استعادة قاعدة البيانات
    await fs.copyFile(workingPath, dbPath);

    // تنظيف الملفات المؤقتة
    if (workingPath !== backupFilePath) {
      await fs.unlink(workingPath);
    }

    const duration = Date.now() - startTime;

    await logSecurityEvent({
      type: 'BACKUP_RESTORED',
      details: `Database restored from: ${path.basename(backupFilePath)}, Duration: ${duration}ms`
    });

    return {
      success: true,
      filePath: dbPath,
      duration
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    await logSecurityEvent({
      type: 'RESTORE_FAILED',
      details: `Restore failed: ${errorMessage}`,
      severity: 'HIGH'
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * الحصول على قائمة النسخ الاحتياطية المتاحة
 */
export async function listBackups(backupPath?: string): Promise<Array<{
  fileName: string;
  filePath: string;
  size: number;
  createdAt: Date;
  isEncrypted: boolean;
  isCompressed: boolean;
}>> {
  const finalBackupPath = backupPath || DEFAULT_CONFIG.backupPath;
  
  try {
    const files = await fs.readdir(finalBackupPath);
    const backupFiles = files.filter(file => 
      file.startsWith('backup-') && 
      (file.endsWith('.db') || file.endsWith('.gz') || file.endsWith('.enc'))
    );

    const backups = await Promise.all(
      backupFiles.map(async (fileName) => {
        const filePath = path.join(finalBackupPath, fileName);
        const stats = await fs.stat(filePath);
        
        return {
          fileName,
          filePath,
          size: stats.size,
          createdAt: stats.birthtime,
          isEncrypted: fileName.endsWith('.enc'),
          isCompressed: fileName.includes('.gz')
        };
      })
    );

    return backups.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

  } catch (error) {
    console.error('Failed to list backups:', error);
    return [];
  }
}

/**
 * تنظيف النسخ الاحتياطية القديمة
 */
async function cleanupOldBackups(config: BackupConfig): Promise<void> {
  try {
    const backups = await listBackups(config.backupPath);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - config.retentionDays);

    const oldBackups = backups.filter(backup => backup.createdAt < cutoffDate);

    for (const backup of oldBackups) {
      await fs.unlink(backup.filePath);
      console.log(`Deleted old backup: ${backup.fileName}`);
    }

    if (oldBackups.length > 0) {
      await logSecurityEvent({
        type: 'BACKUP_CLEANUP',
        details: `Cleaned up ${oldBackups.length} old backup files`
      });
    }

  } catch (error) {
    console.error('Failed to cleanup old backups:', error);
  }
}

/**
 * ضغط ملف
 */
async function compressFile(inputPath: string, outputPath: string): Promise<void> {
  const input = createReadStream(inputPath);
  const output = createWriteStream(outputPath);
  const gzip = createGzip({ level: 9 });

  await pipeline(input, gzip, output);
}

/**
 * فك ضغط ملف
 */
async function decompressFile(inputPath: string, outputPath: string): Promise<void> {
  const { createGunzip } = await import('zlib');
  const input = createReadStream(inputPath);
  const output = createWriteStream(outputPath);
  const gunzip = createGunzip();

  await pipeline(input, gunzip, output);
}

/**
 * تشفير ملف (مبسط - يحتاج تحسين للإنتاج)
 */
async function encryptFile(inputPath: string, outputPath: string): Promise<void> {
  // هذا مثال مبسط - في الإنتاج يجب استخدام تشفير أقوى
  const data = await fs.readFile(inputPath);
  const encrypted = Buffer.from(data).toString('base64');
  await fs.writeFile(outputPath, encrypted);
}

/**
 * فك تشفير ملف (مبسط - يحتاج تحسين للإنتاج)
 */
async function decryptFile(inputPath: string, outputPath: string): Promise<void> {
  // هذا مثال مبسط - في الإنتاج يجب استخدام فك تشفير أقوى
  const encrypted = await fs.readFile(inputPath, 'utf8');
  const decrypted = Buffer.from(encrypted, 'base64');
  await fs.writeFile(outputPath, decrypted);
}

/**
 * التأكد من وجود مجلد النسخ الاحتياطية
 */
async function ensureBackupDirectory(backupPath: string): Promise<void> {
  try {
    await fs.access(backupPath);
  } catch {
    await fs.mkdir(backupPath, { recursive: true });
  }
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}
