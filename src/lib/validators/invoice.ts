import { z } from 'zod'

export const invoiceItemSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, { message: 'اسم المنتج مطلوب' }),
  quantity: z.number().int().min(1, { message: 'الكمية يجب أن تكون على الأقل 1' }),
  price: z.number().min(0, { message: 'السعر يجب أن يكون صفر أو أكثر' }),
})

export const invoiceSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, { message: 'رقم الفاتورة مطلوب' }),
  date: z.string(),
  dueDate: z.string().optional(),
  customerId: z.string().min(1, { message: 'يجب اختيار العميل' }),
  status: z.enum(['draft', 'sent', 'paid', 'overdue']),
  items: z.array(invoiceItemSchema).min(1, { message: 'يجب إضافة عنصر واحد على الأقل' }),
})

export type InvoiceItemFormData = z.infer<typeof invoiceItemSchema>
export type InvoiceFormData = z.infer<typeof invoiceSchema>