import { performance } from 'perf_hooks';
import { logSecurityEvent } from './security-logger';

interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
  unit?: string;
}

interface AlertRule {
  name: string;
  metric: string;
  condition: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  duration: number; // in milliseconds
  enabled: boolean;
  lastTriggered?: Date;
  cooldown: number; // in milliseconds
}

/**
 * نظام مراقبة شامل للتطبيق
 */
export class ApplicationMonitor {
  private metrics: Map<string, MetricData[]> = new Map();
  private alerts: AlertRule[] = [];
  private readonly MAX_METRICS_PER_TYPE = 1000;

  constructor() {
    this.initializeDefaultAlerts();
    this.startPeriodicCleanup();
  }

  /**
   * تسجيل مقياس جديد
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>, unit?: string): void {
    const metric: MetricData = {
      name,
      value,
      timestamp: new Date(),
      tags,
      unit
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metricArray = this.metrics.get(name)!;
    metricArray.push(metric);

    // الاحتفاظ بآخر N مقياس فقط
    if (metricArray.length > this.MAX_METRICS_PER_TYPE) {
      metricArray.shift();
    }

    // فحص التنبيهات
    this.checkAlerts(name, value);
  }

  /**
   * قياس وقت تنفيذ دالة
   */
  async measureExecutionTime<T>(
    name: string,
    fn: () => Promise<T>,
    tags?: Record<string, string>
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      
      this.recordMetric(`${name}_duration`, duration, tags, 'ms');
      this.recordMetric(`${name}_success`, 1, tags, 'count');
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric(`${name}_duration`, duration, tags, 'ms');
      this.recordMetric(`${name}_error`, 1, tags, 'count');
      
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات المقياس
   */
  getMetricStats(name: string, timeWindow?: number): {
    count: number;
    average: number;
    min: number;
    max: number;
    latest: number;
    sum: number;
  } | null {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) return null;

    let filteredMetrics = metrics;
    
    if (timeWindow) {
      const cutoffTime = new Date(Date.now() - timeWindow);
      filteredMetrics = metrics.filter(m => m.timestamp >= cutoffTime);
    }

    if (filteredMetrics.length === 0) return null;

    const values = filteredMetrics.map(m => m.value);
    const sum = values.reduce((a, b) => a + b, 0);
    const count = values.length;
    const average = sum / count;
    const min = Math.min(...values);
    const max = Math.max(...values);
    const latest = values[values.length - 1];

    return { count, average, min, max, latest, sum };
  }

  /**
   * الحصول على جميع المقاييس
   */
  getAllMetrics(timeWindow?: number): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [name] of this.metrics) {
      const stats = this.getMetricStats(name, timeWindow);
      if (stats) {
        result[name] = stats;
      }
    }
    
    return result;
  }

  /**
   * إضافة قاعدة تنبيه
   */
  addAlert(alert: AlertRule): void {
    this.alerts.push(alert);
  }

  /**
   * فحص التنبيهات
   */
  private checkAlerts(metricName: string, value: number): void {
    const relevantAlerts = this.alerts.filter(
      alert => alert.enabled && alert.metric === metricName
    );

    for (const alert of relevantAlerts) {
      if (this.shouldTriggerAlert(alert, value)) {
        this.triggerAlert(alert, value);
      }
    }
  }

  /**
   * تحديد ما إذا كان يجب تشغيل التنبيه
   */
  private shouldTriggerAlert(alert: AlertRule, value: number): boolean {
    // فحص فترة التهدئة
    if (alert.lastTriggered) {
      const timeSinceLastTrigger = Date.now() - alert.lastTriggered.getTime();
      if (timeSinceLastTrigger < alert.cooldown) {
        return false;
      }
    }

    // فحص الشرط
    switch (alert.condition) {
      case 'gt': return value > alert.threshold;
      case 'gte': return value >= alert.threshold;
      case 'lt': return value < alert.threshold;
      case 'lte': return value <= alert.threshold;
      case 'eq': return value === alert.threshold;
      default: return false;
    }
  }

  /**
   * تشغيل التنبيه
   */
  private async triggerAlert(alert: AlertRule, value: number): Promise<void> {
    alert.lastTriggered = new Date();

    // تسجيل التنبيه
    await logSecurityEvent({
      type: 'SYSTEM',
      details: `Alert triggered: ${alert.name} - ${alert.metric} ${alert.condition} ${alert.threshold} (current: ${value})`,
      severity: 'HIGH'
    });

    // إرسال إشعار (يمكن تخصيصه)
    await this.sendAlertNotification(alert, value);
  }

  /**
   * إرسال إشعار التنبيه
   */
  private async sendAlertNotification(alert: AlertRule, value: number): Promise<void> {
    // هنا يمكن إضافة تكامل مع خدمات الإشعارات
    console.warn(`🚨 ALERT: ${alert.name}`, {
      metric: alert.metric,
      condition: `${alert.condition} ${alert.threshold}`,
      currentValue: value,
      timestamp: new Date().toISOString()
    });

    // يمكن إضافة تكامل مع:
    // - Slack
    // - Discord
    // - Email
    // - SMS
    // - PagerDuty
    // - Webhook
  }

  /**
   * تهيئة التنبيهات الافتراضية
   */
  private initializeDefaultAlerts(): void {
    this.alerts = [
      {
        name: 'High API Response Time',
        metric: 'api_response_time',
        condition: 'gt',
        threshold: 5000, // 5 seconds
        duration: 60000, // 1 minute
        enabled: true,
        cooldown: 300000 // 5 minutes
      },
      {
        name: 'High Error Rate',
        metric: 'api_error_rate',
        condition: 'gt',
        threshold: 0.1, // 10%
        duration: 300000, // 5 minutes
        enabled: true,
        cooldown: 600000 // 10 minutes
      },
      {
        name: 'High Memory Usage',
        metric: 'memory_usage_percentage',
        condition: 'gt',
        threshold: 90, // 90%
        duration: 120000, // 2 minutes
        enabled: true,
        cooldown: 300000 // 5 minutes
      },
      {
        name: 'Database Connection Issues',
        metric: 'database_connection_errors',
        condition: 'gt',
        threshold: 5,
        duration: 60000, // 1 minute
        enabled: true,
        cooldown: 180000 // 3 minutes
      },
      {
        name: 'Failed Login Attempts',
        metric: 'failed_login_attempts',
        condition: 'gt',
        threshold: 10,
        duration: 300000, // 5 minutes
        enabled: true,
        cooldown: 600000 // 10 minutes
      }
    ];
  }

  /**
   * تنظيف دوري للمقاييس القديمة
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      
      for (const [name, metrics] of this.metrics) {
        const filteredMetrics = metrics.filter(m => m.timestamp >= cutoffTime);
        this.metrics.set(name, filteredMetrics);
      }
    }, 60 * 60 * 1000); // كل ساعة
  }

  /**
   * تصدير المقاييس بتنسيق Prometheus
   */
  exportPrometheusMetrics(): string {
    let output = '';
    
    for (const [name, metrics] of this.metrics) {
      if (metrics.length === 0) continue;
      
      const latest = metrics[metrics.length - 1];
      const metricName = name.replace(/[^a-zA-Z0-9_]/g, '_');
      
      output += `# HELP ${metricName} Application metric\n`;
      output += `# TYPE ${metricName} gauge\n`;
      
      if (latest.tags) {
        const tags = Object.entries(latest.tags)
          .map(([key, value]) => `${key}="${value}"`)
          .join(',');
        output += `${metricName}{${tags}} ${latest.value}\n`;
      } else {
        output += `${metricName} ${latest.value}\n`;
      }
    }
    
    return output;
  }

  /**
   * الحصول على تقرير صحة النظام
   */
  getHealthReport(): {
    status: 'healthy' | 'warning' | 'critical';
    metrics: Record<string, any>;
    activeAlerts: string[];
    uptime: number;
  } {
    const metrics = this.getAllMetrics(300000); // آخر 5 دقائق
    const activeAlerts: string[] = [];
    
    // فحص التنبيهات النشطة
    for (const alert of this.alerts) {
      if (alert.enabled && alert.lastTriggered) {
        const timeSinceAlert = Date.now() - alert.lastTriggered.getTime();
        if (timeSinceAlert < alert.cooldown) {
          activeAlerts.push(alert.name);
        }
      }
    }
    
    // تحديد حالة النظام
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (activeAlerts.length > 0) {
      status = 'warning';
    }
    
    // فحص المقاييس الحرجة
    const errorRate = metrics.api_error_rate?.latest || 0;
    const responseTime = metrics.api_response_time?.latest || 0;
    
    if (errorRate > 0.2 || responseTime > 10000) {
      status = 'critical';
    }
    
    return {
      status,
      metrics,
      activeAlerts,
      uptime: process.uptime()
    };
  }
}

// مثيل مراقب التطبيق العام
export const applicationMonitor = new ApplicationMonitor();
