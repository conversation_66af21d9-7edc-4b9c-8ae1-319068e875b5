import {clsx} from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * تنسيق التاريخ بتنسيق محدد
 * Format date with specific format
 */
export function formatDate(date: Date | string, language: string = "ar"): string {
  const locale = language === 'ar' ? 'ar-AE' : 'en-US';
  return new Date(date).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * تنسيق التاريخ بتنسيق ثنائي اللغة (عربي/إنجليزي)
 * Format date in bilingual format (Arabic/English)
 */
export function formatDateBilingual(dateString: string | Date): { ar: string, en: string } {
  try {
    const date = new Date(dateString);
    const arDate = date.toLocaleDateString('ar-AE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const enDate = date.toLocaleDateString('en-AE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    return { ar: arDate, en: enDate };
  } catch (error) {
    console.error('Error formatting date:', error);
    const fallback = typeof dateString === 'string' ? dateString : dateString.toString();
    return { ar: fallback, en: fallback };
  }
}

// أنواع العملات المدعومة
// Supported currency types
export type CurrencyCode =
  | "AED" // درهم إماراتي - UAE Dirham
  | "SAR" // ريال سعودي - Saudi Riyal
  | "QAR" // ريال قطري - Qatari Riyal
  | "BHD" // دينار بحريني - Bahraini Dinar
  | "KWD" // دينار كويتي - Kuwaiti Dinar
  | "OMR" // ريال عماني - Omani Riyal
  | "EGP" // جنيه مصري - Egyptian Pound
  | "JOD" // دينار أردني - Jordanian Dinar
  | "LBP" // ليرة لبنانية - Lebanese Pound
  | "USD" // دولار أمريكي - US Dollar
  | "EUR" // يورو - Euro
  | "GBP"; // جنيه إسترليني - British Pound

// معلومات العملات
// Currency information
export interface CurrencyInfo {
  code: CurrencyCode;
  nameAr: string;
  nameEn: string;
  symbol: string;
  locale: string;
  decimalPlaces: number;
  isPopular?: boolean;
  group?: 'middle-east' | 'international' | 'other';
  symbolPosition?: 'before' | 'after';
  exchangeRate?: number; // سعر الصرف مقابل الدرهم الإماراتي - Exchange rate against AED
}

// قائمة العملات المدعومة مع معلوماتها
// List of supported currencies with their information
export const CURRENCIES: CurrencyInfo[] = [
  {
    code: "AED",
    nameAr: "درهم إماراتي",
    nameEn: "UAE Dirham",
    symbol: "د.إ",
    locale: "ar-AE",
    decimalPlaces: 2,
    isPopular: true,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 1.0
  },
  {
    code: "SAR",
    nameAr: "ريال سعودي",
    nameEn: "Saudi Riyal",
    symbol: "ر.س",
    locale: "ar-SA",
    decimalPlaces: 2,
    isPopular: true,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 1.02
  },
  {
    code: "QAR",
    nameAr: "ريال قطري",
    nameEn: "Qatari Riyal",
    symbol: "ر.ق",
    locale: "ar-QA",
    decimalPlaces: 2,
    isPopular: false,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 1.01
  },
  {
    code: "BHD",
    nameAr: "دينار بحريني",
    nameEn: "Bahraini Dinar",
    symbol: "د.ب",
    locale: "ar-BH",
    decimalPlaces: 3,
    isPopular: false,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 0.102
  },
  {
    code: "KWD",
    nameAr: "دينار كويتي",
    nameEn: "Kuwaiti Dinar",
    symbol: "د.ك",
    locale: "ar-KW",
    decimalPlaces: 3,
    isPopular: false,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 0.082
  },
  {
    code: "OMR",
    nameAr: "ريال عماني",
    nameEn: "Omani Riyal",
    symbol: "ر.ع",
    locale: "ar-OM",
    decimalPlaces: 3,
    isPopular: false,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 0.105
  },
  {
    code: "EGP",
    nameAr: "جنيه مصري",
    nameEn: "Egyptian Pound",
    symbol: "ج.م",
    locale: "ar-EG",
    decimalPlaces: 2,
    isPopular: false,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 8.52
  },
  {
    code: "JOD",
    nameAr: "دينار أردني",
    nameEn: "Jordanian Dinar",
    symbol: "د.أ",
    locale: "ar-JO",
    decimalPlaces: 3,
    isPopular: false,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 0.194
  },
  {
    code: "LBP",
    nameAr: "ليرة لبنانية",
    nameEn: "Lebanese Pound",
    symbol: "ل.ل",
    locale: "ar-LB",
    decimalPlaces: 0,
    isPopular: false,
    group: 'middle-east',
    symbolPosition: 'after',
    exchangeRate: 413.22
  },
  {
    code: "USD",
    nameAr: "دولار أمريكي",
    nameEn: "US Dollar",
    symbol: "$",
    locale: "en-US",
    decimalPlaces: 2,
    isPopular: true,
    group: 'international',
    symbolPosition: 'before',
    exchangeRate: 0.272
  },
  {
    code: "EUR",
    nameAr: "يورو",
    nameEn: "Euro",
    symbol: "€",
    locale: "en-EU",
    decimalPlaces: 2,
    isPopular: true,
    group: 'international',
    symbolPosition: 'before',
    exchangeRate: 0.25
  },
  {
    code: "GBP",
    nameAr: "جنيه إسترليني",
    nameEn: "British Pound",
    symbol: "£",
    locale: "en-GB",
    decimalPlaces: 2,
    isPopular: true,
    group: 'international',
    symbolPosition: 'before',
    exchangeRate: 0.214
  }
];

// الإعدادات الافتراضية للعملة
// Default currency settings
export const DEFAULT_CURRENCY: CurrencyCode = "AED";
export const DEFAULT_LOCALE = "ar-AE";

// الحصول على معلومات العملة من الرمز
// Get currency information from code
export function getCurrencyInfo(currencyCode: CurrencyCode = DEFAULT_CURRENCY): CurrencyInfo {
  return CURRENCIES.find(c => c.code === currencyCode) || CURRENCIES[0];
}

// تنسيق العملة مع دعم عملات متعددة واللغات المختلفة
// Format currency with support for multiple currencies and languages
export function formatCurrency(
  amount: number,
  language: string = 'ar',
  currencyCode: CurrencyCode = DEFAULT_CURRENCY,
  options: Intl.NumberFormatOptions = {}
): string {
  try {
    const currencyInfo = getCurrencyInfo(currencyCode);
    // تحديد اللغة المناسبة للتنسيق
    // Determine appropriate locale for formatting
    const locale = language === 'ar' ? currencyInfo.locale : currencyInfo.locale.replace('ar-', 'en-');

    // استخدام خدمة تنسيق العملة إذا كانت متوفرة
    // Use currency formatting service if available
    if (typeof window !== 'undefined' && window.CurrencyService?.formatCurrencyAmount) {
      return window.CurrencyService.formatCurrencyAmount(amount, currencyCode, language, options);
    }

    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currencyCode,
      maximumFractionDigits: currencyInfo.decimalPlaces,
      ...options
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    // في حالة حدوث خطأ، نعود إلى التنسيق البسيط
    // In case of error, return simple format
    const currencyInfo = getCurrencyInfo(currencyCode);
    return `${amount.toFixed(currencyInfo.decimalPlaces)} ${currencyInfo.symbol}`;
  }
}

// الحصول على رمز العملة
// Get currency symbol
export function getCurrencySymbol(
  language: string = 'ar',
  currencyCode: CurrencyCode = DEFAULT_CURRENCY
): string {
  try {
    // استخدام خدمة العملة إذا كانت متوفرة
    // Use currency service if available
    if (typeof window !== 'undefined' && window.CurrencyService?.getCurrencySymbol) {
      return window.CurrencyService.getCurrencySymbol(currencyCode, language);
    }

    const currencyInfo = getCurrencyInfo(currencyCode);
    // تحديد اللغة المناسبة للتنسيق
    // Determine appropriate locale for formatting
    const locale = language === 'ar' ? currencyInfo.locale : currencyInfo.locale.replace('ar-', 'en-');

    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      currencyDisplay: 'symbol',
    });

    return formatter.formatToParts(0).find(part => part.type === 'currency')?.value ?? currencyInfo.symbol;
  } catch (error) {
    console.error('Error getting currency symbol:', error);
    return getCurrencyInfo(currencyCode).symbol;
  }
}

// الحصول على اسم العملة
// Get currency name
export function getCurrencyName(
  currencyCode: CurrencyCode = DEFAULT_CURRENCY,
  language: string = 'ar'
): string {
  try {
    // استخدام خدمة العملة إذا كانت متوفرة
    // Use currency service if available
    if (typeof window !== 'undefined' && window.CurrencyService?.getCurrencyName) {
      return window.CurrencyService.getCurrencyName(currencyCode, language);
    }

    const currencyInfo = getCurrencyInfo(currencyCode);
    return language === 'ar' ? currencyInfo.nameAr : currencyInfo.nameEn;
  } catch (error) {
    console.error('Error getting currency name:', error);
    const currencyInfo = getCurrencyInfo(currencyCode);
    return language === 'ar' ? currencyInfo.nameAr : currencyInfo.nameEn;
  }
}

// تحويل مبلغ من عملة إلى أخرى
// Convert amount from one currency to another
export function convertCurrency(
  amount: number,
  fromCurrency: CurrencyCode,
  toCurrency: CurrencyCode
): number {
  try {
    // استخدام خدمة العملة إذا كانت متوفرة
    // Use currency service if available
    if (typeof window !== 'undefined' && window.CurrencyService?.convertCurrency) {
      return window.CurrencyService.convertCurrency(amount, fromCurrency, toCurrency);
    }

    // إذا كانت العملتان متطابقتين، أعد المبلغ كما هو
    // If currencies are the same, return amount as is
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // الحصول على معلومات العملات
    // Get currency information
    const fromCurrencyInfo = getCurrencyInfo(fromCurrency);
    const toCurrencyInfo = getCurrencyInfo(toCurrency);

    // التحويل باستخدام أسعار الصرف المخزنة
    // Convert using stored exchange rates
    if (fromCurrencyInfo.exchangeRate && toCurrencyInfo.exchangeRate) {
      // حساب المبلغ بالدرهم الإماراتي أولاً (العملة الأساسية)
      // Calculate amount in AED first (base currency)
      const amountInAED = fromCurrency === 'AED'
        ? amount
        : amount / fromCurrencyInfo.exchangeRate;

      // تحويل المبلغ من الدرهم الإماراتي إلى العملة المطلوبة
      // Convert amount from AED to target currency
      const convertedAmount = toCurrency === 'AED'
        ? amountInAED
        : amountInAED * toCurrencyInfo.exchangeRate;

      // تقريب المبلغ إلى عدد المنازل العشرية المناسب للعملة المطلوبة
      // Round amount to appropriate decimal places for target currency
      return Number(convertedAmount.toFixed(toCurrencyInfo.decimalPlaces));
    }

    // إذا لم تتوفر أسعار الصرف، أعد المبلغ كما هو
    // If exchange rates are not available, return amount as is
    return amount;
  } catch (error) {
    console.error('Error converting currency:', error);
    return amount;
  }
}

// أنواع الضرائب المدعومة
// Supported tax types
export type TaxType =
  | "VAT"    // ضريبة القيمة المضافة - Value Added Tax
  | "GST"    // ضريبة السلع والخدمات - Goods and Services Tax
  | "INCOME" // ضريبة الدخل - Income Tax
  | "SALES"  // ضريبة المبيعات - Sales Tax
  | "CUSTOM" // ضريبة مخصصة - Custom Tax
  | "NONE";  // بدون ضريبة - No Tax

// معلومات الضريبة
// Tax information
export interface TaxInfo {
  type: TaxType;
  nameAr: string;
  nameEn: string;
  defaultRate: number;
  isDefault: boolean;
}

// قائمة الضرائب المدعومة
// List of supported taxes
export const TAXES: TaxInfo[] = [
  { type: "VAT", nameAr: "ضريبة القيمة المضافة", nameEn: "Value Added Tax", defaultRate: 5, isDefault: true },
  { type: "GST", nameAr: "ضريبة السلع والخدمات", nameEn: "Goods and Services Tax", defaultRate: 5, isDefault: false },
  { type: "SALES", nameAr: "ضريبة المبيعات", nameEn: "Sales Tax", defaultRate: 10, isDefault: false },
  { type: "INCOME", nameAr: "ضريبة الدخل", nameEn: "Income Tax", defaultRate: 15, isDefault: false },
  { type: "CUSTOM", nameAr: "ضريبة مخصصة", nameEn: "Custom Tax", defaultRate: 0, isDefault: false },
  { type: "NONE", nameAr: "بدون ضريبة", nameEn: "No Tax", defaultRate: 0, isDefault: false }
];

// الضريبة الافتراضية
// Default tax
export const DEFAULT_TAX_TYPE: TaxType = "VAT";
export const DEFAULT_TAX_RATE = 5; // 5%

// الحصول على معلومات الضريبة من النوع
// Get tax information from type
export function getTaxInfo(taxType: TaxType = DEFAULT_TAX_TYPE): TaxInfo {
  return TAXES.find(t => t.type === taxType) || TAXES[0];
}

// حساب مبلغ الضريبة
// Calculate tax amount
export function calculateTax(amount: number, taxRate: number): number {
  return amount * (taxRate / 100);
}

// حساب المبلغ مع الضريبة
// Calculate amount with tax
export function calculateAmountWithTax(amount: number, taxRate: number): number {
  return amount + calculateTax(amount, taxRate);
}

// حساب المبلغ بدون الضريبة
// Calculate amount without tax
export function calculateAmountWithoutTax(amountWithTax: number, taxRate: number): number {
  return amountWithTax / (1 + (taxRate / 100));
}

// تنسيق الأرقام
// Format numbers
export function formatNumber(
  number: number,
  language: string = 'ar',
  options: Intl.NumberFormatOptions = {}
): string {
  try {
    const locale = language === 'ar' ? 'ar-AE' : 'en-US';
    return new Intl.NumberFormat(locale, options).format(number);
  } catch (error) {
    console.error('Error formatting number:', error);
    return number.toString();
  }
}

// اختصار النص
// Truncate text
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// توليد معرف عشوائي
// Generate random ID
export function generateRandomId(): string {
  return Math.random().toString(36).substring(2, 9);
}

// تأخير تنفيذ الدالة
// Debounce function execution
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return function (...args: Parameters<T>): void {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// وظيفة استخراج الأحرف الأولى من الاسم
// Function to extract initials from name
export function getInitials(name: string): string {
  if (!name) return '';

  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}
