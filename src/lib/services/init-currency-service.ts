/**
 * تهيئة خدمة العملات - تقوم بتهيئة خدمة العملات وتسجيلها في النافذة
 * Initialize Currency Service - Initializes the currency service and registers it in the window
 */

import CurrencyService from './currency-service';

/**
 * تهيئة خدمة العملات
 * Initialize currency service
 */
export function initCurrencyService(): void {
  if (typeof window !== 'undefined') {
    // تسجيل خدمة العملات في النافذة
    // Register currency service in window
    window.CurrencyService = CurrencyService;

    // تحديث أسعار الصرف عند بدء التطبيق
    // Update exchange rates when application starts
    CurrencyService.updateExchangeRates()
      .then(() => {
        })
      .catch((error) => {
        console.error('Error updating exchange rates:', error);
      });
  }
}

/**
 * تصدير الدالة الافتراضية
 * Export default function
 */
export default initCurrencyService;
