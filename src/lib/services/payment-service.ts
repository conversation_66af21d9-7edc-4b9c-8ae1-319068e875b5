/**
 * خدمة المدفوعات - توفر وظائف موحدة لمعالجة المدفوعات
 * Payment Service - Provides unified functions for payment processing
 */

import { toast } from 'sonner';

// أنواع طرق الدفع
// Payment method types
export type PaymentMethodType = 'credit_card' | 'bank_transfer' | 'cash' | 'wallet' | 'paypal' | 'stripe';

// واجهة بيانات بطاقة الائتمان
// Credit card data interface
export interface CreditCardData {
  cardNumber: string;
  cardName: string;
  cardExpiry: string;
  cardCvv: string;
}

// واجهة بيانات التحويل البنكي
// Bank transfer data interface
export interface BankTransferData {
  reference: string;
  date: string;
  proof?: File;
}

// واجهة بيانات PayPal
// PayPal data interface
export interface PayPalData {
  email: string;
  password: string;
}

// واجهة بيانات المدفوعات
// Payment data interface
export interface PaymentData {
  invoiceId: string;
  invoiceNumber: string;
  amount: number;
  paymentMethod: PaymentMethodType;
  creditCard?: CreditCardData;
  bankTransfer?: BankTransferData;
  paypal?: PayPalData;
  walletSelected?: boolean;
}

// واجهة نتيجة المدفوعات
// Payment result interface
export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  paymentDate?: Date;
}

/**
 * التحقق من صحة بيانات بطاقة الائتمان
 * Validate credit card data
 */
export function validateCreditCard(data: CreditCardData): { valid: boolean; error?: string } {
  if (!data.cardNumber || !data.cardName || !data.cardExpiry || !data.cardCvv) {
    return { valid: false, error: 'يرجى إدخال جميع بيانات البطاقة | Please enter all card details' };
  }

  // التحقق من صحة رقم البطاقة (تبسيط)
  // Validate card number (simplified)
  if (data.cardNumber.replace(/\s/g, '').length !== 16) {
    return { valid: false, error: 'رقم البطاقة غير صحيح | Invalid card number' };
  }

  // التحقق من صحة تاريخ الانتهاء (تبسيط)
  // Validate expiry date (simplified)
  const expiryRegex = /^(0[1-9]|1[0-2])\/([0-9]{2})$/;
  if (!expiryRegex.test(data.cardExpiry)) {
    return { valid: false, error: 'تاريخ انتهاء البطاقة غير صحيح (MM/YY) | Invalid expiry date (MM/YY)' };
  }

  // التحقق من صحة رمز CVV (تبسيط)
  // Validate CVV code (simplified)
  if (data.cardCvv.length !== 3) {
    return { valid: false, error: 'رمز CVV غير صحيح | Invalid CVV code' };
  }

  return { valid: true };
}

/**
 * التحقق من صحة بيانات التحويل البنكي
 * Validate bank transfer data
 */
export function validateBankTransfer(data: BankTransferData): { valid: boolean; error?: string } {
  if (!data.reference || !data.date) {
    return { valid: false, error: 'يرجى إدخال رقم مرجعي وتاريخ التحويل | Please enter reference number and transfer date' };
  }

  return { valid: true };
}

/**
 * التحقق من صحة بيانات PayPal
 * Validate PayPal data
 */
export function validatePayPal(data: PayPalData): { valid: boolean; error?: string } {
  if (!data.email || !data.password) {
    return { valid: false, error: 'يرجى إدخال بريدك الإلكتروني وكلمة المرور لـ PayPal | Please enter your PayPal email and password' };
  }

  return { valid: true };
}

/**
 * التحقق من صحة بيانات المحفظة الإلكترونية
 * Validate wallet data
 */
export function validateWallet(walletSelected?: boolean): { valid: boolean; error?: string } {
  if (!walletSelected) {
    return { valid: false, error: 'يرجى تأكيد الدفع باستخدام المحفظة الإلكترونية | Please confirm payment using e-wallet' };
  }

  return { valid: true };
}

/**
 * معالجة الدفع
 * Process payment
 */
export async function processPayment(paymentData: PaymentData): Promise<PaymentResult> {
  try {
    // التحقق من صحة بيانات الدفع حسب طريقة الدفع
    // Validate payment data based on payment method
    let validationResult = { valid: true, error: undefined };

    switch (paymentData.paymentMethod) {
      case 'credit_card':
        if (paymentData.creditCard) {
          validationResult = validateCreditCard(paymentData.creditCard);
        } else {
          validationResult = { valid: false, error: 'بيانات البطاقة غير متوفرة | Card data not available' };
        }
        break;
      case 'bank_transfer':
        if (paymentData.bankTransfer) {
          validationResult = validateBankTransfer(paymentData.bankTransfer);
        } else {
          validationResult = { valid: false, error: 'بيانات التحويل البنكي غير متوفرة | Bank transfer data not available' };
        }
        break;
      case 'paypal':
        if (paymentData.paypal) {
          validationResult = validatePayPal(paymentData.paypal);
        } else {
          validationResult = { valid: false, error: 'بيانات PayPal غير متوفرة | PayPal data not available' };
        }
        break;
      case 'wallet':
        validationResult = validateWallet(paymentData.walletSelected);
        break;
      case 'cash':
        // لا يوجد تحقق إضافي للدفع النقدي
        // No additional validation for cash payment
        break;
      default:
        validationResult = { valid: false, error: 'طريقة دفع غير معتمدة | Unsupported payment method' };
    }

    if (!validationResult.valid) {
      return {
        success: false,
        error: validationResult.error,
      };
    }

    // محاكاة عملية الدفع
    // Simulate payment processing
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // إنشاء رقم معاملة عشوائي
    // Generate random transaction ID
    const gatewayPrefix = 
      paymentData.paymentMethod === 'credit_card' ? 'CC' :
      paymentData.paymentMethod === 'bank_transfer' ? 'BT' :
      paymentData.paymentMethod === 'paypal' ? 'PP' :
      paymentData.paymentMethod === 'wallet' ? 'WL' :
      paymentData.paymentMethod === 'stripe' ? 'ST' : 'CS';
    
    const randomTransactionId = `${gatewayPrefix}${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`;

    // تحديث حالة الفاتورة إلى "مدفوعة"
    // Update invoice status to "PAID"
    try {
      const response = await fetch(`/api/invoices/${paymentData.invoiceId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'PAID' }),
      });

      if (!response.ok) {
        console.error('فشل في تحديث حالة الفاتورة | Failed to update invoice status');
        // نستمر في العملية حتى لو فشل تحديث حالة الفاتورة
        // Continue with the process even if updating the invoice status fails
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة الفاتورة | Error updating invoice status:', error);
      // نستمر في العملية حتى لو فشل تحديث حالة الفاتورة
      // Continue with the process even if updating the invoice status fails
    }

    return {
      success: true,
      transactionId: randomTransactionId,
      paymentDate: new Date(),
    };
  } catch (error) {
    console.error('خطأ في عملية الدفع | Payment processing error:', error);
    return {
      success: false,
      error: 'حدث خطأ أثناء معالجة الدفع | An error occurred during payment processing',
    };
  }
}

/**
 * تنسيق رقم البطاقة أثناء الكتابة
 * Format card number during typing
 */
export function formatCardNumber(value: string): string {
  const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
  const matches = v.match(/\d{4,16}/g);
  const match = (matches && matches[0]) || '';
  const parts = [];

  for (let i = 0, len = match.length; i < len; i += 4) {
    parts.push(match.substring(i, i + 4));
  }

  if (parts.length) {
    return parts.join(' ');
  } else {
    return value;
  }
}

/**
 * تنسيق تاريخ انتهاء البطاقة أثناء الكتابة
 * Format expiry date during typing
 */
export function formatExpiryDate(value: string): string {
  const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');

  if (v.length >= 2) {
    return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
  }

  return v;
}

/**
 * الحصول على معلومات الحساب البنكي
 * Get bank account information
 */
export function getBankAccountInfo() {
  return {
    bankName: 'بنك أمين بلس | Amin Plus Bank',
    accountNumber: '**********',
    iban: '***********************',
    beneficiaryName: 'شركة أمين بلس | Amin Plus Company',
    email: '<EMAIL>',
  };
}

/**
 * الحصول على معلومات الدفع النقدي
 * Get cash payment information
 */
export function getCashPaymentInfo() {
  return {
    address: 'دبي، الإمارات العربية المتحدة | Dubai, United Arab Emirates',
    workingHours: 'من الأحد إلى الخميس | Sunday to Thursday, 9:00 AM - 5:00 PM',
  };
}
