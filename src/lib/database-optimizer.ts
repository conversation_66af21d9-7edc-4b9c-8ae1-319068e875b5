import { prisma } from './prisma';
import { performance } from 'perf_hooks';
import { logSecurityEvent } from './security-logger';

interface QueryPerformance {
  query: string;
  duration: number;
  timestamp: Date;
  params?: any;
}

interface OptimizationSuggestion {
  type: 'index' | 'query' | 'schema';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  suggestion: string;
  estimatedImprovement: string;
}

/**
 * مراقب أداء الاستعلامات
 */
export class QueryPerformanceMonitor {
  private slowQueries: QueryPerformance[] = [];
  private readonly SLOW_QUERY_THRESHOLD = 1000; // 1 ثانية

  /**
   * تسجيل أداء استعلام
   */
  recordQuery(query: string, duration: number, params?: any): void {
    const queryPerf: QueryPerformance = {
      query,
      duration,
      timestamp: new Date(),
      params
    };

    // إذا كان الاستعلام بطيئاً، سجله
    if (duration > this.SLOW_QUERY_THRESHOLD) {
      this.slowQueries.push(queryPerf);

      // الاحتفاظ بآخر 100 استعلام بطيء فقط
      if (this.slowQueries.length > 100) {
        this.slowQueries.shift();
      }

      // تسجيل الاستعلام البطيء
      console.warn(`Slow query detected: ${query} took ${duration}ms`);
    }
  }

  /**
   * الحصول على الاستعلامات البطيئة
   */
  getSlowQueries(limit: number = 20): QueryPerformance[] {
    return this.slowQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }

  /**
   * مسح سجلات الاستعلامات البطيئة
   */
  clearSlowQueries(): void {
    this.slowQueries = [];
  }

  /**
   * الحصول على إحصائيات الأداء
   */
  getPerformanceStats(): {
    totalSlowQueries: number;
    averageDuration: number;
    maxDuration: number;
    minDuration: number;
  } {
    if (this.slowQueries.length === 0) {
      return {
        totalSlowQueries: 0,
        averageDuration: 0,
        maxDuration: 0,
        minDuration: 0
      };
    }

    const durations = this.slowQueries.map(q => q.duration);
    const total = durations.reduce((sum, duration) => sum + duration, 0);

    return {
      totalSlowQueries: this.slowQueries.length,
      averageDuration: total / this.slowQueries.length,
      maxDuration: Math.max(...durations),
      minDuration: Math.min(...durations)
    };
  }
}

// مثيل مراقب الأداء العام
export const queryMonitor = new QueryPerformanceMonitor();

/**
 * تحليل أداء قاعدة البيانات وتقديم اقتراحات التحسين
 */
export async function analyzeDatabasePerformance(): Promise<{
  performance: any;
  suggestions: OptimizationSuggestion[];
  tableStats: any[];
}> {
  const suggestions: OptimizationSuggestion[] = [];
  const tableStats: any[] = [];

  try {
    // تحليل إحصائيات الجداول
    const tables = [
      'User', 'Customer', 'Product', 'Invoice', 'InvoiceItem',
      'SecurityLog', 'Expense', 'Purchase'
    ];

    for (const table of tables) {
      const stats = await analyzeTablePerformance(table);
      tableStats.push(stats);

      // اقتراحات بناءً على إحصائيات الجدول
      if (stats.recordCount > 10000 && !stats.hasOptimalIndexes) {
        suggestions.push({
          type: 'index',
          priority: 'high',
          description: `جدول ${table} يحتوي على ${stats.recordCount} سجل بدون فهارس محسنة`,
          suggestion: `إضافة فهارس على الأعمدة المستخدمة بكثرة في ${table}`,
          estimatedImprovement: '50-80% تحسن في سرعة الاستعلامات'
        });
      }

      if (stats.averageQueryTime > 500) {
        suggestions.push({
          type: 'query',
          priority: 'medium',
          description: `استعلامات جدول ${table} بطيئة (${stats.averageQueryTime}ms)`,
          suggestion: 'تحسين الاستعلامات وإضافة فهارس مناسبة',
          estimatedImprovement: '30-60% تحسن في الأداء'
        });
      }
    }

    // تحليل الاستعلامات البطيئة
    const slowQueries = queryMonitor.getSlowQueries();
    if (slowQueries.length > 10) {
      suggestions.push({
        type: 'query',
        priority: 'high',
        description: `تم رصد ${slowQueries.length} استعلام بطيء`,
        suggestion: 'مراجعة وتحسين الاستعلامات البطيئة',
        estimatedImprovement: '40-70% تحسن في الأداء العام'
      });
    }

    // اقتراحات عامة
    const dbSize = await getDatabaseSize();
    if (dbSize > 100 * 1024 * 1024) { // أكبر من 100MB
      suggestions.push({
        type: 'schema',
        priority: 'medium',
        description: 'حجم قاعدة البيانات كبير',
        suggestion: 'تنظيف البيانات القديمة وأرشفة السجلات غير المستخدمة',
        estimatedImprovement: 'تحسن في الأداء وتوفير مساحة'
      });
    }

    return {
      performance: queryMonitor.getPerformanceStats(),
      suggestions: suggestions.sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }),
      tableStats
    };

  } catch (error) {
    console.error('Database performance analysis failed:', error);
    return {
      performance: {},
      suggestions: [],
      tableStats: []
    };
  }
}

/**
 * تحليل أداء جدول محدد
 */
async function analyzeTablePerformance(tableName: string): Promise<any> {
  try {
    const startTime = performance.now();

    // الحصول على عدد السجلات
    let recordCount = 0;
    try {
      switch (tableName) {
        case 'User':
          recordCount = await prisma.user.count();
          break;
        case 'Customer':
          recordCount = await prisma.customer.count();
          break;
        case 'Product':
          recordCount = await prisma.product.count();
          break;
        case 'Invoice':
          recordCount = await prisma.invoice.count();
          break;
        case 'InvoiceItem':
          recordCount = await prisma.invoiceItem.count();
          break;
        case 'SecurityLog':
          recordCount = await prisma.securityLog.count();
          break;
        case 'Expense':
          recordCount = await prisma.expense.count();
          break;
        case 'Purchase':
          recordCount = await prisma.purchase.count();
          break;
      }
    } catch (error) {
      recordCount = 0;
    }

    const queryTime = performance.now() - startTime;

    return {
      tableName,
      recordCount,
      averageQueryTime: queryTime,
      hasOptimalIndexes: recordCount < 1000 || queryTime < 100, // تقدير بسيط
      lastAnalyzed: new Date()
    };

  } catch (error) {
    return {
      tableName,
      recordCount: 0,
      averageQueryTime: 0,
      hasOptimalIndexes: false,
      error: error instanceof Error ? error.message : 'Analysis failed'
    };
  }
}

/**
 * الحصول على حجم قاعدة البيانات
 */
async function getDatabaseSize(): Promise<number> {
  try {
    const fs = await import('fs/promises');
    const path = await import('path');

    const dbPath = path.join(process.cwd(), 'prisma', 'dev.db');
    const stats = await fs.stat(dbPath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

/**
 * تنظيف البيانات القديمة
 */
export async function cleanupOldData(options: {
  securityLogsDays?: number;
  expensesDays?: number;
  dryRun?: boolean;
} = {}): Promise<{
  success: boolean;
  cleaned: Record<string, number>;
  errors: string[];
}> {
  const {
    securityLogsDays = 90,
    expensesDays = 365,
    dryRun = false
  } = options;

  const cleaned: Record<string, number> = {};
  const errors: string[] = [];

  try {
    // تنظيف سجلات الأمان القديمة
    const securityLogCutoff = new Date();
    securityLogCutoff.setDate(securityLogCutoff.getDate() - securityLogsDays);

    if (dryRun) {
      const securityLogCount = await prisma.securityLog.count({
        where: {
          timestamp: { lt: securityLogCutoff },
          severity: { in: ['LOW', 'MEDIUM'] }
        }
      });
      cleaned.securityLogs = securityLogCount;
    } else {
      const result = await prisma.securityLog.deleteMany({
        where: {
          timestamp: { lt: securityLogCutoff },
          severity: { in: ['LOW', 'MEDIUM'] } // احتفظ بالسجلات عالية الخطورة
        }
      });
      cleaned.securityLogs = result.count;
    }

    // تسجيل عملية التنظيف
    if (!dryRun && cleaned.securityLogs > 0) {
      await logSecurityEvent({
        type: 'DATA_CLEANUP',
        details: `Cleaned up ${cleaned.securityLogs} old security logs`,
        severity: 'LOW'
      });
    }

    return {
      success: true,
      cleaned,
      errors
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Cleanup failed';
    errors.push(errorMessage);

    return {
      success: false,
      cleaned,
      errors
    };
  }
}
