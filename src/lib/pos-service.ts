import {
  Sale,
  SaleItem,
  Payment,
  POSSettings,
  DEFAULT_POS_SETTINGS,
  calculateSaleSubtotal,
  calculateSaleTaxAmount,
  calculateSaleDiscountAmount,
  calculateSaleTotal,
  calculateSaleAmountDue
} from '@/types/pos';
import { Product } from '@/types/inventory';
import { getProducts, adjustInventoryQuantity } from './inventory-service';
import { getDefaultCurrency, getDefaultTaxSettings } from './settings';
import { generateInvoiceNumber } from './settings';

// الحصول على إعدادات نقاط البيع
export function getPOSSettings(): POSSettings {
  if (typeof window === 'undefined') {
    return DEFAULT_POS_SETTINGS;
  }

  const savedSettings = localStorage.getItem('posSettings');
  if (!savedSettings) {
    return DEFAULT_POS_SETTINGS;
  }

  try {
    return JSON.parse(savedSettings);
  } catch (error) {
    console.error('خطأ في قراءة إعدادات نقاط البيع:', error);
    return DEFAULT_POS_SETTINGS;
  }
}

// حفظ إعدادات نقاط البيع
export function savePOSSettings(settings: POSSettings): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('posSettings', JSON.stringify(settings));
}

// الحصول على جميع المبيعات
export function getSales(): Sale[] {
  if (typeof window === 'undefined') {
    return [];
  }

  const savedSales = localStorage.getItem('sales');
  if (!savedSales) {
    return [];
  }

  try {
    return JSON.parse(savedSales);
  } catch (error) {
    console.error('خطأ في قراءة المبيعات:', error);
    return [];
  }
}

// حفظ المبيعات
export function saveSales(sales: Sale[]): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('sales', JSON.stringify(sales));
}

// الحصول على مبيعة بواسطة المعرف
export function getSaleById(id: string): Sale | undefined {
  const sales = getSales();
  return sales.find(sale => sale.id === id);
}

// إنشاء مبيعة جديدة
export function createSale(customerId?: string, customerName?: string): Sale {
  const sales = getSales();

  const newSale: Sale = {
    id: Math.random().toString(36).substring(2, 15),
    saleNumber: generateInvoiceNumber(),
    customerId,
    customerName,
    status: 'draft',
    items: [],
    payments: [],
    subtotal: 0,
    taxAmount: 0,
    discountAmount: 0,
    total: 0,
    amountPaid: 0,
    amountDue: 0,
    currency: getDefaultCurrency(),
    taxSettings: getDefaultTaxSettings(),
    createdBy: 'current-user',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  sales.push(newSale);
  saveSales(sales);

  return newSale;
}

// إضافة منتج إلى المبيعة
export function addProductToSale(saleId: string, productId: string, quantity: number = 1): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبيعة في حالة مسودة
  if (sale.status !== 'draft') {
    throw new Error('لا يمكن تعديل مبيعة مكتملة');
  }

  // البحث عن المنتج
  const products = getProducts();
  const product = products.find(p => p.id === productId);

  if (!product) {
    throw new Error('المنتج غير موجود');
  }

  // التحقق من وجود المنتج في المبيعة
  const existingItemIndex = sale.items.findIndex(item => item.productId === productId);

  if (existingItemIndex !== -1) {
    // تحديث الكمية إذا كان المنتج موجودًا بالفعل
    const existingItem = sale.items[existingItemIndex];
    const newQuantity = existingItem.quantity + quantity;

    sale.items[existingItemIndex] = {
      ...existingItem,
      quantity: newQuantity,
      subtotal: existingItem.price * newQuantity,
      total: existingItem.price * newQuantity
    };
  } else {
    // إضافة المنتج إذا لم يكن موجودًا
    const newItem: SaleItem = {
      id: Math.random().toString(36).substring(2, 15),
      productId: product.id,
      name: product.name,
      sku: product.sku,
      quantity,
      price: product.price,
      originalPrice: product.price,
      discountAmount: 0,
      discountType: 'percentage',
      discountValue: 0,
      taxable: product.taxable,
      subtotal: product.price * quantity,
      total: product.price * quantity
    };

    sale.items.push(newItem);
  }

  // إعادة حساب المبالغ
  sale.subtotal = calculateSaleSubtotal(sale.items);
  sale.discountAmount = calculateSaleDiscountAmount(sale.items);
  sale.taxAmount = calculateSaleTaxAmount(sale.items, sale.taxSettings);
  sale.total = calculateSaleTotal(sale.subtotal, sale.taxAmount);
  sale.amountDue = calculateSaleAmountDue(sale.total, sale.amountPaid);
  sale.updatedAt = new Date().toISOString();

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// تحديث كمية منتج في المبيعة
export function updateSaleItemQuantity(saleId: string, itemId: string, quantity: number): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبيعة في حالة مسودة
  if (sale.status !== 'draft') {
    throw new Error('لا يمكن تعديل مبيعة مكتملة');
  }

  // البحث عن العنصر
  const itemIndex = sale.items.findIndex(item => item.id === itemId);

  if (itemIndex === -1) {
    throw new Error('العنصر غير موجود في المبيعة');
  }

  const item = sale.items[itemIndex];

  if (quantity <= 0) {
    // إزالة العنصر إذا كانت الكمية صفر أو أقل
    sale.items = sale.items.filter(item => item.id !== itemId);
  } else {
    // تحديث الكمية
    sale.items[itemIndex] = {
      ...item,
      quantity,
      subtotal: item.price * quantity,
      total: item.price * quantity
    };
  }

  // إعادة حساب المبالغ
  sale.subtotal = calculateSaleSubtotal(sale.items);
  sale.discountAmount = calculateSaleDiscountAmount(sale.items);
  sale.taxAmount = calculateSaleTaxAmount(sale.items, sale.taxSettings);
  sale.total = calculateSaleTotal(sale.subtotal, sale.taxAmount);
  sale.amountDue = calculateSaleAmountDue(sale.total, sale.amountPaid);
  sale.updatedAt = new Date().toISOString();

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// تطبيق خصم على منتج في المبيعة
export function applySaleItemDiscount(
  saleId: string,
  itemId: string,
  discountType: 'percentage' | 'fixed',
  discountValue: number
): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبيعة في حالة مسودة
  if (sale.status !== 'draft') {
    throw new Error('لا يمكن تعديل مبيعة مكتملة');
  }

  // البحث عن العنصر
  const itemIndex = sale.items.findIndex(item => item.id === itemId);

  if (itemIndex === -1) {
    throw new Error('العنصر غير موجود في المبيعة');
  }

  const item = sale.items[itemIndex];
  let discountAmount = 0;

  if (discountType === 'percentage') {
    discountAmount = (item.originalPrice * discountValue) / 100;
  } else {
    discountAmount = discountValue;
  }

  // التأكد من أن الخصم لا يتجاوز سعر المنتج
  discountAmount = Math.min(discountAmount, item.originalPrice);

  const price = item.originalPrice - discountAmount;

  sale.items[itemIndex] = {
    ...item,
    price,
    discountAmount,
    discountType,
    discountValue,
    subtotal: price * item.quantity,
    total: price * item.quantity
  };

  // إعادة حساب المبالغ
  sale.subtotal = calculateSaleSubtotal(sale.items);
  sale.discountAmount = calculateSaleDiscountAmount(sale.items);
  sale.taxAmount = calculateSaleTaxAmount(sale.items, sale.taxSettings);
  sale.total = calculateSaleTotal(sale.subtotal, sale.taxAmount);
  sale.amountDue = calculateSaleAmountDue(sale.total, sale.amountPaid);
  sale.updatedAt = new Date().toISOString();

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// إزالة منتج من المبيعة
export function removeSaleItem(saleId: string, itemId: string): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبيعة في حالة مسودة
  if (sale.status !== 'draft') {
    throw new Error('لا يمكن تعديل مبيعة مكتملة');
  }

  // إزالة العنصر
  sale.items = sale.items.filter(item => item.id !== itemId);

  // إعادة حساب المبالغ
  sale.subtotal = calculateSaleSubtotal(sale.items);
  sale.discountAmount = calculateSaleDiscountAmount(sale.items);
  sale.taxAmount = calculateSaleTaxAmount(sale.items, sale.taxSettings);
  sale.total = calculateSaleTotal(sale.subtotal, sale.taxAmount);
  sale.amountDue = calculateSaleAmountDue(sale.total, sale.amountPaid);
  sale.updatedAt = new Date().toISOString();

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// إضافة دفعة إلى المبيعة
export function addPaymentToSale(
  saleId: string,
  method: Payment['method'],
  amount: number,
  reference?: string,
  notes?: string
): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبلغ موجب
  if (amount <= 0) {
    throw new Error('يجب أن يكون مبلغ الدفعة أكبر من صفر');
  }

  // إضافة الدفعة
  const newPayment: Payment = {
    id: Math.random().toString(36).substring(2, 15),
    saleId,
    method,
    amount,
    reference,
    notes,
    createdAt: new Date().toISOString()
  };

  sale.payments.push(newPayment);

  // تحديث المبلغ المدفوع والمبلغ المستحق
  sale.amountPaid = sale.payments.reduce((total, payment) => total + payment.amount, 0);
  sale.amountDue = calculateSaleAmountDue(sale.total, sale.amountPaid);
  sale.updatedAt = new Date().toISOString();

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// إكمال المبيعة
export function completeSale(saleId: string): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبيعة في حالة مسودة
  if (sale.status !== 'draft') {
    throw new Error('المبيعة ليست في حالة مسودة');
  }

  // التحقق من وجود عناصر في المبيعة
  if (sale.items.length === 0) {
    throw new Error('لا يمكن إكمال مبيعة بدون عناصر');
  }

  // تحديث حالة المبيعة
  sale.status = 'completed';
  sale.completedAt = new Date().toISOString();
  sale.updatedAt = new Date().toISOString();

  // تحديث المخزون
  sale.items.forEach(item => {
    if (item.quantity > 0) {
      try {
        // تخفيض المخزون
        adjustInventoryQuantity(
          item.productId,
          'default', // استخدام الموقع الافتراضي
          -item.quantity,
          `sale-${sale.id}`,
          'sale',
          `Sale #${sale.saleNumber}`
        );
      } catch (error) {
        console.error(`خطأ في تحديث المخزون للمنتج ${item.productId}:`, error);
      }
    }
  });

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// إلغاء المبيعة
export function cancelSale(saleId: string): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبيعة في حالة مسودة
  if (sale.status !== 'draft') {
    throw new Error('لا يمكن إلغاء مبيعة مكتملة');
  }

  // تحديث حالة المبيعة
  sale.status = 'cancelled';
  sale.updatedAt = new Date().toISOString();

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// استرجاع المبيعة
export function refundSale(saleId: string, fullRefund: boolean = true): Sale | undefined {
  const sales = getSales();
  const saleIndex = sales.findIndex(sale => sale.id === saleId);

  if (saleIndex === -1) {
    return undefined;
  }

  const sale = sales[saleIndex];

  // التحقق من أن المبيعة مكتملة
  if (sale.status !== 'completed') {
    throw new Error('لا يمكن استرجاع مبيعة غير مكتملة');
  }

  // تحديث حالة المبيعة
  sale.status = fullRefund ? 'refunded' : 'partially_refunded';
  sale.updatedAt = new Date().toISOString();

  // إعادة المخزون (في حالة الاسترجاع الكامل)
  if (fullRefund) {
    sale.items.forEach(item => {
      if (item.quantity > 0) {
        try {
          // زيادة المخزون
          adjustInventoryQuantity(
            item.productId,
            'default', // استخدام الموقع الافتراضي
            item.quantity,
            `refund-${sale.id}`,
            'return',
            `Refund for Sale #${sale.saleNumber}`
          );
        } catch (error) {
          console.error(`خطأ في تحديث المخزون للمنتج ${item.productId}:`, error);
        }
      }
    });
  }

  sales[saleIndex] = sale;
  saveSales(sales);

  return sale;
}

// الحصول على المبيعات حسب الحالة
export function getSalesByStatus(status: Sale['status']): Sale[] {
  const sales = getSales();
  return sales.filter(sale => sale.status === status);
}

// الحصول على المبيعات حسب العميل
export function getSalesByCustomer(customerId: string): Sale[] {
  const sales = getSales();
  return sales.filter(sale => sale.customerId === customerId);
}

// الحصول على المبيعات حسب التاريخ
export function getSalesByDateRange(startDate: Date, endDate: Date): Sale[] {
  const sales = getSales();
  return sales.filter(sale => {
    const saleDate = new Date(sale.createdAt);
    return saleDate >= startDate && saleDate <= endDate;
  });
}

// الحصول على إجمالي المبيعات حسب التاريخ
export function getSalesTotalByDateRange(startDate: Date, endDate: Date): number {
  const sales = getSalesByDateRange(startDate, endDate)
    .filter(sale => sale.status === 'completed');

  return sales.reduce((total, sale) => total + sale.total, 0);
}

// الحصول على عدد المبيعات حسب التاريخ
export function getSalesCountByDateRange(startDate: Date, endDate: Date): number {
  const sales = getSalesByDateRange(startDate, endDate)
    .filter(sale => sale.status === 'completed');

  return sales.length;
}

// الحصول على المنتجات الأكثر مبيعًا
export function getTopSellingProducts(limit: number = 10): { productId: string, name: string, quantity: number, total: number }[] {
  const sales = getSales().filter(sale => sale.status === 'completed');

  // تجميع المنتجات من جميع المبيعات
  const productMap = new Map<string, { productId: string, name: string, quantity: number, total: number }>();

  sales.forEach(sale => {
    sale.items.forEach(item => {
      const existing = productMap.get(item.productId);

      if (existing) {
        existing.quantity += item.quantity;
        existing.total += item.total;
      } else {
        productMap.set(item.productId, {
          productId: item.productId,
          name: item.name,
          quantity: item.quantity,
          total: item.total
        });
      }
    });
  });

  // تحويل الخريطة إلى مصفوفة وترتيبها حسب الكمية
  return Array.from(productMap.values())
    .sort((a, b) => b.quantity - a.quantity)
    .slice(0, limit);
}
