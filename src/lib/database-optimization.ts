/**
 * تحسينات أداء قاعدة البيانات لنظام أمين بلس
 * Database Performance Optimizations for Amin Plus System
 */

import { prisma } from './prisma';

/**
 * تحسين استعلامات العملاء
 * Optimize customer queries
 */
export const optimizedCustomerQueries = {
  // البحث السريع في العملاء
  async searchCustomers(searchTerm: string, limit = 10) {
    return await prisma.customer.findMany({
      where: {
        OR: [
          { name: { contains: searchTerm, mode: 'insensitive' } },
          { email: { contains: searchTerm, mode: 'insensitive' } },
          { phone: { contains: searchTerm, mode: 'insensitive' } },
        ],
        status: 'active'
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        customerType: true,
      },
      take: limit,
      orderBy: { name: 'asc' }
    });
  },

  // العملاء النشطون مع إحصائيات
  async getActiveCustomersWithStats() {
    return await prisma.customer.findMany({
      where: { status: 'active' },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        customerType: true,
        createdAt: true,
        _count: {
          select: { invoices: true }
        },
        invoices: {
          select: {
            total: true,
            status: true,
          },
          where: {
            status: { in: ['paid', 'pending'] }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }
};

/**
 * تحسين استعلامات المنتجات
 * Optimize product queries
 */
export const optimizedProductQueries = {
  // البحث السريع في المنتجات
  async searchProducts(searchTerm: string, limit = 20) {
    return await prisma.product.findMany({
      where: {
        AND: [
          {
            OR: [
              { name: { contains: searchTerm, mode: 'insensitive' } },
              { sku: { contains: searchTerm, mode: 'insensitive' } },
              { barcode: { contains: searchTerm, mode: 'insensitive' } },
            ]
          },
          { status: 'active' }
        ]
      },
      select: {
        id: true,
        name: true,
        sku: true,
        price: true,
        currentStock: true,
        minStock: true,
        unit: true,
        category: true,
      },
      take: limit,
      orderBy: { name: 'asc' }
    });
  },

  // المنتجات منخفضة المخزون
  async getLowStockProducts() {
    return await prisma.product.findMany({
      where: {
        AND: [
          { trackInventory: true },
          { status: 'active' },
          {
            OR: [
              { currentStock: { lte: prisma.product.fields.minStock } },
              { currentStock: { equals: 0 } }
            ]
          }
        ]
      },
      select: {
        id: true,
        name: true,
        sku: true,
        currentStock: true,
        minStock: true,
        unit: true,
        category: true,
      },
      orderBy: { currentStock: 'asc' }
    });
  },

  // أفضل المنتجات مبيعاً
  async getTopSellingProducts(limit = 10) {
    return await prisma.product.findMany({
      where: { status: 'active' },
      select: {
        id: true,
        name: true,
        price: true,
        category: true,
        _count: {
          select: { invoiceItems: true }
        },
        invoiceItems: {
          select: {
            quantity: true,
            total: true,
          }
        }
      },
      orderBy: {
        invoiceItems: {
          _count: 'desc'
        }
      },
      take: limit
    });
  }
};

/**
 * تحسين استعلامات الفواتير
 * Optimize invoice queries
 */
export const optimizedInvoiceQueries = {
  // الفواتير مع التصفية المحسنة
  async getInvoicesWithFilters(filters: {
    status?: string;
    paymentStatus?: string;
    customerId?: number;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};
    
    if (filters.status) where.status = filters.status;
    if (filters.paymentStatus) where.paymentStatus = filters.paymentStatus;
    if (filters.customerId) where.customerId = filters.customerId;
    if (filters.dateFrom || filters.dateTo) {
      where.issueDate = {};
      if (filters.dateFrom) where.issueDate.gte = filters.dateFrom;
      if (filters.dateTo) where.issueDate.lte = filters.dateTo;
    }

    return await prisma.invoice.findMany({
      where,
      select: {
        id: true,
        number: true,
        issueDate: true,
        dueDate: true,
        status: true,
        paymentStatus: true,
        total: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        _count: {
          select: { items: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: filters.limit || 50,
      skip: filters.offset || 0
    });
  },

  // إحصائيات الفواتير السريعة
  async getInvoiceStats() {
    const [totalInvoices, paidInvoices, pendingInvoices, overdueInvoices] = await Promise.all([
      prisma.invoice.count(),
      prisma.invoice.count({ where: { paymentStatus: 'paid' } }),
      prisma.invoice.count({ where: { paymentStatus: 'pending' } }),
      prisma.invoice.count({ 
        where: { 
          paymentStatus: 'pending',
          dueDate: { lt: new Date().toISOString().split('T')[0] }
        } 
      })
    ]);

    const [totalAmount, paidAmount, pendingAmount] = await Promise.all([
      prisma.invoice.aggregate({
        _sum: { total: true }
      }),
      prisma.invoice.aggregate({
        _sum: { total: true },
        where: { paymentStatus: 'paid' }
      }),
      prisma.invoice.aggregate({
        _sum: { total: true },
        where: { paymentStatus: 'pending' }
      })
    ]);

    return {
      counts: {
        total: totalInvoices,
        paid: paidInvoices,
        pending: pendingInvoices,
        overdue: overdueInvoices
      },
      amounts: {
        total: totalAmount._sum.total || 0,
        paid: paidAmount._sum.total || 0,
        pending: pendingAmount._sum.total || 0
      }
    };
  }
};

/**
 * تحسين استعلامات المصروفات
 * Optimize expense queries
 */
export const optimizedExpenseQueries = {
  // المصروفات مع التصفية
  async getExpensesWithFilters(filters: {
    categoryId?: number;
    paymentStatus?: string;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
  }) {
    const where: any = {};
    
    if (filters.categoryId) where.categoryId = filters.categoryId;
    if (filters.paymentStatus) where.paymentStatus = filters.paymentStatus;
    if (filters.dateFrom || filters.dateTo) {
      where.expenseDate = {};
      if (filters.dateFrom) where.expenseDate.gte = new Date(filters.dateFrom);
      if (filters.dateTo) where.expenseDate.lte = new Date(filters.dateTo);
    }

    return await prisma.expense.findMany({
      where,
      select: {
        id: true,
        title: true,
        amount: true,
        totalAmount: true,
        expenseDate: true,
        paymentStatus: true,
        category: {
          select: {
            id: true,
            name: true,
            color: true,
          }
        },
        supplier: {
          select: {
            id: true,
            name: true,
          }
        }
      },
      orderBy: { expenseDate: 'desc' },
      take: filters.limit || 50
    });
  }
};

/**
 * تنظيف قاعدة البيانات
 * Database cleanup utilities
 */
export const databaseCleanup = {
  // حذف السجلات القديمة
  async cleanupOldRecords() {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // حذف سجلات الأمان القديمة
    await prisma.securityLog.deleteMany({
      where: {
        timestamp: { lt: sixMonthsAgo }
      }
    });

    // حذف الإشعارات المقروءة القديمة
    await prisma.notification.deleteMany({
      where: {
        isRead: true,
        createdAt: { lt: sixMonthsAgo }
      }
    });
  },

  // تحديث الإحصائيات
  async updateStatistics() {
    // يمكن إضافة تحديث للإحصائيات المحفوظة هنا
    console.log('Statistics updated');
  }
};
