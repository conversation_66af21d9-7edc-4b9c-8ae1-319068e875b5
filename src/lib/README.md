# مكتبات ووظائف مساعدة

هذا المجلد يحتوي على مكتبات ووظائف مساعدة لتطبيق أمين بلس.

## الملفات الرئيسية

- **electron-utils.ts**: وظائف مساعدة للتفاعل مع Electron.
- **utils.ts**: وظائف مساعدة عامة للتطبيق.

## وظائف electron-utils.ts

### isElectron

التحقق مما إذا كان التطبيق يعمل في بيئة Electron.

```typescript
import { isElectron } from '@/lib/electron-utils';

if (isElectron()) {
  console.log('التطبيق يعمل في بيئة Electron');
} else {
  console.log('التطبيق يعمل في بيئة الويب');
}
```

### showSaveDialog

عرض مربع حوار حفظ الملف.

```typescript
import { showSaveDialog } from '@/lib/electron-utils';

const handleSaveDialog = async () => {
  const result = await showSaveDialog({
    title: 'حفظ الملف',
    defaultPath: 'تقرير.pdf',
    filters: [
      { name: 'PDF', extensions: ['pdf'] },
      { name: 'Excel', extensions: ['xlsx'] },
      { name: 'جميع الملفات', extensions: ['*'] }
    ],
    buttonLabel: 'حفظ'
  });

  if (!result.canceled && result.filePath) {
    console.log(`تم حفظ الملف في: ${result.filePath}`);
  }
};
```

### showOpenDialog

عرض مربع حوار فتح الملف.

```typescript
import { showOpenDialog } from '@/lib/electron-utils';

const handleOpenDialog = async () => {
  const result = await showOpenDialog({
    title: 'فتح ملف',
    filters: [
      { name: 'PDF', extensions: ['pdf'] },
      { name: 'Excel', extensions: ['xlsx'] },
      { name: 'جميع الملفات', extensions: ['*'] }
    ],
    properties: ['openFile'],
    buttonLabel: 'فتح'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    console.log(`تم اختيار الملف: ${result.filePaths[0]}`);
  }
};
```

### showMessageBox

عرض مربع حوار رسالة.

```typescript
import { showMessageBox } from '@/lib/electron-utils';

const handleShowMessageBox = async () => {
  const result = await showMessageBox({
    type: 'info',
    title: 'معلومات',
    message: 'هذه رسالة معلومات',
    detail: 'هذه تفاصيل الرسالة',
    buttons: ['موافق', 'إلغاء']
  });

  if (result.response === 0) {
    console.log('تم النقر على زر موافق');
  } else {
    console.log('تم النقر على زر إلغاء');
  }
};
```

### getSystemInfo

الحصول على معلومات النظام.

```typescript
import { getSystemInfo } from '@/lib/electron-utils';

const handleShowSystemInfo = () => {
  const info = getSystemInfo();
  console.log(`النظام: ${info.platform}`);
  console.log(`المعمارية: ${info.arch}`);
  console.log(`إصدار Electron: ${info.version}`);
  console.log(`إصدار Node.js: ${info.nodeVersion}`);
  console.log(`إصدار Chrome: ${info.chromeVersion}`);
};
```

## وظائف utils.ts

### formatCurrency

تنسيق المبلغ كعملة.

```typescript
import { formatCurrency } from '@/lib/utils';

const amount = 1234.56;
const formattedAmount = formatCurrency(amount); // 1,234.56 ر.س
```

### cn

دمج أسماء الفئات.

```typescript
import { cn } from '@/lib/utils';

const className = cn(
  'text-lg',
  isActive && 'font-bold',
  isDisabled && 'opacity-50'
);
```
