import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export type SessionUser = {
    id: string;
    name: string;
    email: string;
    role: string;
    permissions?: string[];
};

/**
 * Middleware to require authentication for API routes
 */
export function requireAuth(
    handler: (req: NextRequest, user: SessionUser) => Promise<NextResponse>,
    options?: { requiredRole?: string }
) {
    return async (req: NextRequest) => {
        try {
            const token = await getToken({
                req,
                secret: process.env.NEXTAUTH_SECRET
            });

            if (!token) {
                return NextResponse.json(
                    { error: 'Authentication required' },
                    { status: 401 }
                );
            }

            const user: SessionUser = {
                id: token.id as string,
                name: token.name as string,
                email: token.email as string,
                role: token.role as string,
                permissions: token.permissions as string[]
            };

            // Check required role if specified
            if (options?.requiredRole && user.role !== options.requiredRole) {
                return NextResponse.json(
                    { error: 'Insufficient permissions' },
                    { status: 403 }
                );
            }

            return handler(req, user);
        } catch (error) {
            console.error('Auth error:', error);
            return NextResponse.json(
                { error: 'Authentication error' },
                { status: 500 }
            );
        }
    };
}
