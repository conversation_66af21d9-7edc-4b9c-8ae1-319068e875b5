import {formatCurrency as formatCurrencyUtil, formatDate as formatDateUtil, CurrencyCode, DEFAULT_CURRENCY} from './utils';

export type InvoiceStatus = "PAID" | "DRAFT" | "VOID" | "SENT" | "OVERDUE" | "CANCELLED";

// تنسيق العملة
export function formatCurrency(amount: number, currencyCode: CurrencyCode = DEFAULT_CURRENCY): string {
  return formatCurrencyUtil(amount, currencyCode);
}

// تنسيق التاريخ
export function formatDate(date: Date | string): string {
  return formatDateUtil(date);
}

// حساب المجموع الفرعي
export function calculateSubtotal(items: any[]): number {
  return items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
}

// حساب الضريبة (15%)
export function calculateTax(subtotal: number): number {
  return subtotal * 0.15;
}

// حساب المجموع الكلي
export function calculateTotal(subtotal: number, tax: number): number {
  return subtotal + tax;
}

// إنشاء رقم فاتورة جديد
export function generateInvoiceNumber(prefix: string = 'INV'): string {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return `${prefix}-${year}${month}-${random}`;
}

export function getStatusText(status: InvoiceStatus): string {
  switch (status) {
    case "PAID":
      return "مدفوعة | Paid";
    case "DRAFT":
      return "مسودة | Draft";
    case "VOID":
      return "ملغية | Void";
    case "SENT":
      return "مرسلة | Sent";
    case "OVERDUE":
      return "متأخرة | Overdue";
    case "CANCELLED":
      return "ملغاة | Cancelled";
    default:
      return status;
  }
}

export function getStatusClass(status: InvoiceStatus): string {
  switch (status) {
    case "PAID":
      return "bg-green-100 text-green-800";
    case "DRAFT":
      return "bg-gray-100 text-gray-800";
    case "VOID":
      return "bg-red-100 text-red-800";
    case "SENT":
      return "bg-blue-100 text-blue-800";
    case "OVERDUE":
      return "bg-orange-100 text-orange-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}
