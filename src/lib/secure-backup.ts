/**
 * نظام النسخ الاحتياطي الآمن
 * Secure Backup System
 */

import crypto from 'crypto';
import { prisma } from './prisma';
import { AdvancedEncryption } from './advanced-security';
import { logSecurityEvent } from './security-logger';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * مدير النسخ الاحتياطي الآمن
 * Secure Backup Manager
 */
export class SecureBackupManager {
  private static readonly BACKUP_DIR = process.env.BACKUP_DIR || './backups';
  private static readonly MAX_BACKUP_SIZE = 100 * 1024 * 1024; // 100MB
  private static readonly COMPRESSION_LEVEL = 6;

  /**
   * إنشاء نسخة احتياطية شاملة مشفرة
   */
  static async createFullBackup(userId: string): Promise<{
    backupId: string;
    filename: string;
    size: number;
  }> {
    try {
      // جمع جميع البيانات
      const data = await this.collectAllData();
      
      // ضغط البيانات
      const compressed = await this.compressData(JSON.stringify(data));
      
      // التحقق من حجم النسخة الاحتياطية
      if (compressed.length > this.MAX_BACKUP_SIZE) {
        throw new Error('Backup size exceeds maximum allowed size');
      }
      
      // تشفير البيانات المضغوطة
      const encrypted = AdvancedEncryption.encrypt(compressed);
      
      // إنشاء hash للتحقق من السلامة
      const hash = crypto.createHash('sha256')
        .update(compressed)
        .digest('hex');
      
      // إنشاء اسم ملف فريد
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `full_backup_${timestamp}.enc`;
      
      // حفظ النسخة الاحتياطية في قاعدة البيانات
      const backup = await prisma.backup.create({
        data: {
          type: 'FULL',
          userId: parseInt(userId),
          filename,
          size: compressed.length,
          hash,
          encryptedData: JSON.stringify(encrypted),
          metadata: JSON.stringify({
            tables: Object.keys(data),
            recordCounts: Object.fromEntries(
              Object.entries(data).map(([key, value]) => [key, (value as any[]).length])
            ),
            createdAt: new Date().toISOString()
          }),
          createdAt: new Date()
        }
      });

      // حفظ النسخة الاحتياطية في الملف (اختياري)
      await this.saveBackupToFile(filename, JSON.stringify(encrypted));

      await logSecurityEvent({
        type: 'BACKUP_CREATED',
        userId,
        details: `Full encrypted backup created: ${filename} (${this.formatBytes(compressed.length)})`,
        severity: 'LOW'
      });

      return {
        backupId: backup.id.toString(),
        filename,
        size: compressed.length
      };
    } catch (error) {
      console.error('Full backup creation failed:', error);
      throw new Error('Failed to create full encrypted backup');
    }
  }

  /**
   * إنشاء نسخة احتياطية تزايدية
   */
  static async createIncrementalBackup(
    userId: string,
    lastBackupDate: Date
  ): Promise<{
    backupId: string;
    filename: string;
    size: number;
  }> {
    try {
      // جمع البيانات المحدثة منذ آخر نسخة احتياطية
      const data = await this.collectIncrementalData(lastBackupDate);
      
      if (Object.keys(data).length === 0) {
        throw new Error('No changes found since last backup');
      }
      
      // ضغط وتشفير البيانات
      const compressed = await this.compressData(JSON.stringify(data));
      const encrypted = AdvancedEncryption.encrypt(compressed);
      
      // إنشاء hash للتحقق من السلامة
      const hash = crypto.createHash('sha256')
        .update(compressed)
        .digest('hex');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `incremental_backup_${timestamp}.enc`;
      
      const backup = await prisma.backup.create({
        data: {
          type: 'INCREMENTAL',
          userId: parseInt(userId),
          filename,
          size: compressed.length,
          hash,
          encryptedData: JSON.stringify(encrypted),
          metadata: JSON.stringify({
            tables: Object.keys(data),
            recordCounts: Object.fromEntries(
              Object.entries(data).map(([key, value]) => [key, (value as any[]).length])
            ),
            lastBackupDate: lastBackupDate.toISOString(),
            createdAt: new Date().toISOString()
          }),
          createdAt: new Date()
        }
      });

      await this.saveBackupToFile(filename, JSON.stringify(encrypted));

      await logSecurityEvent({
        type: 'INCREMENTAL_BACKUP_CREATED',
        userId,
        details: `Incremental backup created: ${filename} (${this.formatBytes(compressed.length)})`,
        severity: 'LOW'
      });

      return {
        backupId: backup.id.toString(),
        filename,
        size: compressed.length
      };
    } catch (error) {
      console.error('Incremental backup creation failed:', error);
      throw new Error('Failed to create incremental backup');
    }
  }

  /**
   * استعادة النسخة الاحتياطية
   */
  static async restoreBackup(
    backupId: string,
    userId: string,
    options: {
      verifyIntegrity?: boolean;
      createRestorePoint?: boolean;
      selectiveTables?: string[];
    } = {}
  ): Promise<{
    success: boolean;
    restoredTables: string[];
    recordsRestored: number;
  }> {
    try {
      // إنشاء نقطة استعادة قبل البدء
      if (options.createRestorePoint !== false) {
        await this.createRestorePoint(userId);
      }

      const backup = await prisma.backup.findFirst({
        where: {
          id: parseInt(backupId),
          userId: parseInt(userId)
        }
      });

      if (!backup) {
        throw new Error('Backup not found or access denied');
      }

      // فك تشفير البيانات
      const encryptedData = JSON.parse(backup.encryptedData);
      const decrypted = AdvancedEncryption.decrypt(
        encryptedData.encrypted,
        encryptedData.iv,
        encryptedData.tag,
        encryptedData.salt
      );

      // التحقق من سلامة البيانات
      if (options.verifyIntegrity !== false) {
        const hash = crypto.createHash('sha256')
          .update(decrypted)
          .digest('hex');

        if (hash !== backup.hash) {
          throw new Error('Backup integrity check failed - data may be corrupted');
        }
      }

      // إلغاء ضغط البيانات
      const decompressed = await this.decompressData(decrypted);
      const data = JSON.parse(decompressed);

      // استعادة البيانات
      const result = await this.restoreData(data, options.selectiveTables);

      await logSecurityEvent({
        type: 'BACKUP_RESTORED',
        userId,
        details: `Backup restored: ${backup.filename}, Tables: ${result.restoredTables.join(', ')}, Records: ${result.recordsRestored}`,
        severity: 'HIGH'
      });

      return result;
    } catch (error) {
      console.error('Backup restoration failed:', error);
      await logSecurityEvent({
        type: 'BACKUP_RESTORE_FAILED',
        userId,
        details: `Backup restoration failed: ${error.message}`,
        severity: 'HIGH'
      });
      throw new Error(`Failed to restore backup: ${error.message}`);
    }
  }

  /**
   * إنشاء نقطة استعادة
   */
  static async createRestorePoint(userId: string): Promise<string> {
    const restorePoint = await this.createFullBackup(userId);
    
    await prisma.backup.update({
      where: { id: parseInt(restorePoint.backupId) },
      data: { type: 'RESTORE_POINT' }
    });

    return restorePoint.backupId;
  }

  /**
   * جمع جميع البيانات للنسخة الاحتياطية الكاملة
   */
  private static async collectAllData(): Promise<any> {
    const data: any = {};

    // جمع بيانات المستخدمين (بدون كلمات المرور)
    data.users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // جمع بيانات العملاء
    data.customers = await prisma.customer.findMany();

    // جمع بيانات المنتجات
    data.products = await prisma.product.findMany();

    // جمع بيانات الفواتير
    data.invoices = await prisma.invoice.findMany({
      include: {
        items: true,
        customer: true
      }
    });

    // جمع بيانات المصروفات
    data.expenses = await prisma.expense.findMany({
      include: {
        category: true,
        supplier: true
      }
    });

    // جمع بيانات الإعدادات
    data.settings = await prisma.setting.findMany();

    // جمع بيانات الأدوار والصلاحيات
    data.roles = await prisma.role.findMany();

    return data;
  }

  /**
   * جمع البيانات التزايدية
   */
  private static async collectIncrementalData(lastBackupDate: Date): Promise<any> {
    const data: any = {};

    // جمع البيانات المحدثة منذ آخر نسخة احتياطية
    const updatedCustomers = await prisma.customer.findMany({
      where: { updatedAt: { gte: lastBackupDate } }
    });
    if (updatedCustomers.length > 0) data.customers = updatedCustomers;

    const updatedProducts = await prisma.product.findMany({
      where: { updatedAt: { gte: lastBackupDate } }
    });
    if (updatedProducts.length > 0) data.products = updatedProducts;

    const updatedInvoices = await prisma.invoice.findMany({
      where: { updatedAt: { gte: lastBackupDate } },
      include: { items: true, customer: true }
    });
    if (updatedInvoices.length > 0) data.invoices = updatedInvoices;

    const updatedExpenses = await prisma.expense.findMany({
      where: { updatedAt: { gte: lastBackupDate } },
      include: { category: true, supplier: true }
    });
    if (updatedExpenses.length > 0) data.expenses = updatedExpenses;

    return data;
  }

  /**
   * استعادة البيانات
   */
  private static async restoreData(
    data: any,
    selectiveTables?: string[]
  ): Promise<{ restoredTables: string[]; recordsRestored: number }> {
    const restoredTables: string[] = [];
    let recordsRestored = 0;

    // استعادة البيانات حسب الجداول المحددة أو جميع الجداول
    const tablesToRestore = selectiveTables || Object.keys(data);

    for (const table of tablesToRestore) {
      if (data[table] && Array.isArray(data[table])) {
        try {
          // حذف البيانات الموجودة (إذا لزم الأمر)
          // await prisma[table].deleteMany({});

          // إدراج البيانات المستعادة
          for (const record of data[table]) {
            await (prisma as any)[table].upsert({
              where: { id: record.id },
              update: record,
              create: record
            });
            recordsRestored++;
          }

          restoredTables.push(table);
        } catch (error) {
          console.error(`Failed to restore table ${table}:`, error);
        }
      }
    }

    return { restoredTables, recordsRestored };
  }

  /**
   * ضغط البيانات
   */
  private static async compressData(data: string): Promise<string> {
    // في تطبيق حقيقي، استخدم مكتبة ضغط مثل zlib
    const buffer = Buffer.from(data, 'utf8');
    return buffer.toString('base64');
  }

  /**
   * إلغاء ضغط البيانات
   */
  private static async decompressData(data: string): Promise<string> {
    const buffer = Buffer.from(data, 'base64');
    return buffer.toString('utf8');
  }

  /**
   * حفظ النسخة الاحتياطية في ملف
   */
  private static async saveBackupToFile(filename: string, data: string): Promise<void> {
    try {
      // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
      await fs.mkdir(this.BACKUP_DIR, { recursive: true });
      
      const filePath = path.join(this.BACKUP_DIR, filename);
      await fs.writeFile(filePath, data, 'utf8');
    } catch (error) {
      console.error('Failed to save backup to file:', error);
      // لا نرمي خطأ هنا لأن النسخة الاحتياطية محفوظة في قاعدة البيانات
    }
  }

  /**
   * تنسيق حجم الملف
   */
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * الحصول على قائمة النسخ الاحتياطية
   */
  static async getBackupList(userId: string): Promise<any[]> {
    return await prisma.backup.findMany({
      where: { userId: parseInt(userId) },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        type: true,
        filename: true,
        size: true,
        metadata: true,
        createdAt: true
      }
    });
  }

  /**
   * حذف النسخة الاحتياطية
   */
  static async deleteBackup(backupId: string, userId: string): Promise<void> {
    const backup = await prisma.backup.findFirst({
      where: {
        id: parseInt(backupId),
        userId: parseInt(userId)
      }
    });

    if (!backup) {
      throw new Error('Backup not found or access denied');
    }

    // حذف الملف
    try {
      const filePath = path.join(this.BACKUP_DIR, backup.filename);
      await fs.unlink(filePath);
    } catch (error) {
      console.error('Failed to delete backup file:', error);
    }

    // حذف السجل من قاعدة البيانات
    await prisma.backup.delete({
      where: { id: parseInt(backupId) }
    });

    await logSecurityEvent({
      type: 'BACKUP_DELETED',
      userId,
      details: `Backup deleted: ${backup.filename}`,
      severity: 'MEDIUM'
    });
  }
}
