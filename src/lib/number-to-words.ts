/**
 * تحويل الأرقام إلى كلمات باللغة العربية والإنجليزية
 * Convert numbers to words in Arabic and English
 */

// الأرقام العربية من 0 إلى 19
const arabicOnes = [
  '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
  'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
];

// العشرات العربية
const arabicTens = [
  '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
];

// المئات العربية
const arabicHundreds = [
  '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
];

// الأرقام الإنجليزية من 0 إلى 19
const englishOnes = [
  '', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine',
  'ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'
];

// العشرات الإنجليزية
const englishTens = [
  '', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'
];

/**
 * تحويل رقم أقل من 1000 إلى كلمات باللغة العربية
 * @param num الرقم المراد تحويله
 * @returns الرقم بالكلمات العربية
 */
function convertLessThanThousandArabic(num: number): string {
  if (num === 0) {
    return '';
  }

  let result = '';

  // المئات
  if (num >= 100) {
    result = arabicHundreds[Math.floor(num / 100)];
    num %= 100;
    if (num > 0) {
      result += ' و';
    }
  }

  // العشرات والآحاد
  if (num > 0) {
    if (num < 20) {
      result += arabicOnes[num];
    } else {
      const ones = num % 10;
      const tens = Math.floor(num / 10);
      if (ones > 0) {
        result += arabicOnes[ones] + ' و';
      }
      result += arabicTens[tens];
    }
  }

  return result;
}

/**
 * تحويل رقم أقل من 1000 إلى كلمات باللغة الإنجليزية
 * @param num الرقم المراد تحويله
 * @returns الرقم بالكلمات الإنجليزية
 */
function convertLessThanThousandEnglish(num: number): string {
  if (num === 0) {
    return '';
  }

  let result = '';

  // المئات
  if (num >= 100) {
    result = englishOnes[Math.floor(num / 100)] + ' hundred';
    num %= 100;
    if (num > 0) {
      result += ' and ';
    }
  }

  // العشرات والآحاد
  if (num > 0) {
    if (num < 20) {
      result += englishOnes[num];
    } else {
      const ones = num % 10;
      const tens = Math.floor(num / 10);
      result += englishTens[tens];
      if (ones > 0) {
        result += '-' + englishOnes[ones];
      }
    }
  }

  return result;
}

/**
 * تحويل رقم إلى كلمات باللغة العربية
 * @param num الرقم المراد تحويله
 * @param currency عملة الرقم (افتراضياً: درهم)
 * @returns الرقم بالكلمات العربية
 */
export function numberToWordsArabic(num: number, currency: string = 'درهم'): string {
  if (num === 0) {
    return 'صفر ' + currency;
  }

  // تقريب الرقم إلى رقمين عشريين
  num = Math.round(num * 100) / 100;

  // فصل الجزء الصحيح والكسري
  const integerPart = Math.floor(num);
  const decimalPart = Math.round((num - integerPart) * 100);

  // تحويل الجزء الصحيح
  const groups = [];
  let temp = integerPart;
  let groupIndex = 0;

  while (temp > 0) {
    const group = temp % 1000;
    if (group !== 0) {
      let groupStr = convertLessThanThousandArabic(group);

      // إضافة اسم المجموعة (آلاف، ملايين، إلخ)
      if (groupIndex === 1) {
        if (group === 1) {
          groupStr = 'ألف';
        } else if (group === 2) {
          groupStr = 'ألفان';
        } else if (group >= 3 && group <= 10) {
          groupStr += ' آلاف';
        } else {
          groupStr += ' ألف';
        }
      } else if (groupIndex === 2) {
        if (group === 1) {
          groupStr = 'مليون';
        } else if (group === 2) {
          groupStr = 'مليونان';
        } else if (group >= 3 && group <= 10) {
          groupStr += ' ملايين';
        } else {
          groupStr += ' مليون';
        }
      }

      groups.unshift(groupStr);
    }

    temp = Math.floor(temp / 1000);
    groupIndex++;
  }

  let result = groups.join(' و');

  // إضافة العملة
  if (integerPart === 1) {
    result += ' ' + currency;
  } else if (integerPart === 2) {
    result += ' ' + currency + 'ان';
  } else if (integerPart >= 3 && integerPart <= 10) {
    result += ' ' + currency + 'ات';
  } else {
    result += ' ' + currency;
  }

  // إضافة الجزء الكسري
  if (decimalPart > 0) {
    result += ' و';
    if (decimalPart === 1) {
      result += 'فلس واحد';
    } else if (decimalPart === 2) {
      result += 'فلسان';
    } else if (decimalPart >= 3 && decimalPart <= 10) {
      result += convertLessThanThousandArabic(decimalPart) + ' فلوس';
    } else {
      result += convertLessThanThousandArabic(decimalPart) + ' فلساً';
    }
  }

  return result;
}

/**
 * تحويل رقم إلى كلمات باللغة الإنجليزية
 * @param num الرقم المراد تحويله
 * @param currency عملة الرقم (افتراضياً: dirham)
 * @returns الرقم بالكلمات الإنجليزية
 */
export function numberToWordsEnglish(num: number, currency: string = 'dirham'): string {
  if (num === 0) {
    return 'zero ' + currency;
  }

  // تقريب الرقم إلى رقمين عشريين
  num = Math.round(num * 100) / 100;

  // فصل الجزء الصحيح والكسري
  const integerPart = Math.floor(num);
  const decimalPart = Math.round((num - integerPart) * 100);

  // تحويل الجزء الصحيح
  const groups = [];
  let temp = integerPart;
  let groupIndex = 0;

  while (temp > 0) {
    const group = temp % 1000;
    if (group !== 0) {
      let groupStr = convertLessThanThousandEnglish(group);

      // إضافة اسم المجموعة (thousands, millions, etc.)
      if (groupIndex === 1) {
        groupStr += ' thousand';
      } else if (groupIndex === 2) {
        groupStr += ' million';
      } else if (groupIndex === 3) {
        groupStr += ' billion';
      }

      groups.unshift(groupStr);
    }

    temp = Math.floor(temp / 1000);
    groupIndex++;
  }

  let result = groups.join(' ');

  // إضافة العملة
  if (integerPart === 1) {
    result += ' ' + currency;
  } else {
    result += ' ' + currency + 's';
  }

  // إضافة الجزء الكسري
  if (decimalPart > 0) {
    result += ' and ';
    if (decimalPart === 1) {
      result += 'one fils';
    } else {
      result += convertLessThanThousandEnglish(decimalPart) + ' fils';
    }
  }

  return result;
}

/**
 * تحويل رقم إلى كلمات باللغتين العربية والإنجليزية
 * @param num الرقم المراد تحويله
 * @param currency عملة الرقم بالعربية (افتراضياً: درهم)
 * @param currencyEn عملة الرقم بالإنجليزية (افتراضياً: dirham)
 * @returns الرقم بالكلمات باللغتين العربية والإنجليزية
 */
export function numberToWords(
  num: number,
  currency: string = 'درهم',
  currencyEn: string = 'dirham'
): { ar: string; en: string } {
  return {
    ar: numberToWordsArabic(num, currency),
    en: numberToWordsEnglish(num, currencyEn)
  };
}

export default numberToWords;
