/**
 * واجهة برمجة التطبيقات للتفاعل مع Tauri
 * API for interacting with Tauri
 */

// Check if we're in a Tauri environment
const isTauriAvailable = typeof window !== 'undefined' && window.__TAURI_IPC__ !== undefined;

// Mock implementation of invoke for non-Tauri environments
const invoke = async (command: string, args?: any): Promise<any> => {
  console.warn(`Tauri API call to "${command}" not available in this environment`);

  // Return mock data based on the command
  if (command === 'get_app_info') {
    return 'Amin Plus | أمين بلس';
  }

  if (command === 'get_settings') {
    return [];
  }

  if (command === 'get_current_user') {
    return {
      id: 1,
      username: 'admin',
      display_name: 'مدير النظام',
      roles: ['admin']
    };
  }

  // Default fallback
  throw new Error(`Tauri API call to "${command}" not implemented in this environment`);
};

/**
 * واجهة المستخدم
 * User interface
 */
export interface User {
  id: number;
  username: string;
  display_name: string;
  roles: string[];
}

/**
 * واجهة الدور
 * Role interface
 */
export interface Role {
  id: number;
  name: string;
  description: string | null;
}

/**
 * واجهة النسخة الاحتياطية
 * Backup interface
 */
export interface Backup {
  id: number;
  file_name: string;
  file_path: string;
  file_size: number | null;
  created_at: string;
}

/**
 * واجهة سجل التدقيق
 * Audit log interface
 */
export interface AuditLog {
  id: number;
  user_id: number | null;
  username: string | null;
  action: string;
  entity_type: string | null;
  entity_id: number | null;
  details: string | null;
  created_at: string;
}

/**
 * واجهة الإعدادات
 * Settings interface
 */
export interface Settings {
  [key: string]: string;
}

/**
 * الحصول على معلومات التطبيق
 * Get application information
 */
export async function getAppInfo(): Promise<string> {
  try {
    return await invoke('get_app_info');
  } catch (error) {
    console.error('Error getting app info:', error);
    return 'Amin Plus | أمين بلس';
  }
}

/**
 * الحصول على الإعدادات
 * Get settings
 */
export async function getSettings(): Promise<Settings> {
  try {
    const settingsArray = await invoke('get_settings') as [string, string][];
    const settings: Settings = {};

    for (const [key, value] of settingsArray) {
      settings[key] = value;
    }

    return settings;
  } catch (error) {
    console.error('Error getting settings:', error);
    // Return default settings
    return {
      'app_name': 'أمين بلس',
      'app_name_en': 'Amin Plus',
      'currency': 'درهم',
      'currency_symbol': 'د.إ',
      'tax_rate': '5'
    };
  }
}

/**
 * تحديث إعداد
 * Update setting
 */
export async function updateSetting(key: string, value: string): Promise<void> {
  return invoke('update_setting', { key, value });
}

/**
 * الحصول على المستخدم الحالي
 * Get current user
 */
export async function getCurrentUser(): Promise<User> {
  try {
    return await invoke('get_current_user') as User;
  } catch (error) {
    console.error('Error getting current user:', error);
    // Return a default user
    return {
      id: 1,
      username: 'admin',
      display_name: 'مدير النظام',
      roles: ['admin']
    };
  }
}

/**
 * الحصول على جميع المستخدمين
 * Get all users
 */
export async function getUsers(): Promise<User[]> {
  return invoke('get_users');
}

/**
 * إضافة مستخدم جديد
 * Add new user
 */
export async function addUser(username: string, display_name: string, roles: string[]): Promise<number> {
  return invoke('add_user', { username, display_name, roles });
}

/**
 * تحديث مستخدم
 * Update user
 */
export async function updateUser(id: number, display_name: string, roles: string[]): Promise<void> {
  return invoke('update_user', { id, display_name, roles });
}

/**
 * حذف مستخدم
 * Delete user
 */
export async function deleteUser(id: number): Promise<void> {
  return invoke('delete_user', { id });
}

/**
 * الحصول على جميع الأدوار
 * Get all roles
 */
export async function getRoles(): Promise<Role[]> {
  return invoke('get_roles');
}

/**
 * إضافة دور جديد
 * Add new role
 */
export async function addRole(name: string, description: string | null): Promise<number> {
  return invoke('add_role', { name, description });
}

/**
 * تحديث دور
 * Update role
 */
export async function updateRole(id: number, name: string, description: string | null): Promise<void> {
  return invoke('update_role', { id, name, description });
}

/**
 * حذف دور
 * Delete role
 */
export async function deleteRole(id: number): Promise<void> {
  return invoke('delete_role', { id });
}

/**
 * التحقق من صلاحية المستخدم
 * Check user permission
 */
export async function checkPermission(permission: string): Promise<boolean> {
  return invoke('check_permission', { permission });
}

/**
 * إضافة سجل تدقيق
 * Add audit log
 */
export async function addAuditLog(
  action: string,
  entity_type?: string,
  entity_id?: number,
  details?: string
): Promise<number> {
  return invoke('add_audit_log', { action, entity_type, entity_id, details });
}

/**
 * الحصول على سجلات التدقيق
 * Get audit logs
 */
export async function getAuditLogs(limit?: number, offset?: number): Promise<AuditLog[]> {
  return invoke('get_audit_logs', { limit, offset });
}

/**
 * إنشاء نسخة احتياطية
 * Create backup
 */
export async function createBackup(): Promise<Backup> {
  return invoke('create_backup');
}

/**
 * استعادة نسخة احتياطية
 * Restore backup
 */
export async function restoreBackup(backup_id: number): Promise<void> {
  return invoke('restore_backup', { backup_id });
}

/**
 * تصدير نسخة احتياطية
 * Export backup
 */
export async function exportBackup(backup_id: number): Promise<string> {
  return invoke('export_backup', { backup_id });
}

/**
 * استيراد نسخة احتياطية
 * Import backup
 */
export async function importBackup(): Promise<Backup> {
  return invoke('import_backup');
}

/**
 * حذف نسخة احتياطية
 * Delete backup
 */
export async function deleteBackup(backup_id: number): Promise<void> {
  return invoke('delete_backup', { backup_id });
}

/**
 * الحصول على جميع النسخ الاحتياطية
 * Get all backups
 */
export async function getBackups(): Promise<Backup[]> {
  return invoke('get_backups');
}
