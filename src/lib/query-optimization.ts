/**
 * تحسين الاستعلامات والأداء
 * Query Optimization and Performance
 */

import { prisma } from './prisma';

/**
 * مدير التخزين المؤقت للاستعلامات
 * Query Cache Manager
 */
class QueryCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl = 300000) { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  clear() {
    this.cache.clear();
  }
  
  clearExpired() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

export const queryCache = new QueryCache();

/**
 * مساعد للاستعلامات المحسنة
 * Optimized Query Helper
 */
export class OptimizedQuery {
  /**
   * تنفيذ استعلام مع تخزين مؤقت
   * Execute query with caching
   */
  static async withCache<T>(
    key: string,
    queryFn: () => Promise<T>,
    ttl = 300000
  ): Promise<T> {
    const cached = queryCache.get(key);
    if (cached) return cached;
    
    const result = await queryFn();
    queryCache.set(key, result, ttl);
    return result;
  }
  
  /**
   * تنفيذ استعلامات متوازية
   * Execute parallel queries
   */
  static async parallel<T extends Record<string, Promise<any>>>(
    queries: T
  ): Promise<{ [K in keyof T]: Awaited<T[K]> }> {
    const keys = Object.keys(queries) as (keyof T)[];
    const promises = Object.values(queries);
    
    const results = await Promise.all(promises);
    
    return keys.reduce((acc, key, index) => {
      acc[key] = results[index];
      return acc;
    }, {} as any);
  }
  
  /**
   * تنفيذ استعلام مع إعادة المحاولة
   * Execute query with retry
   */
  static async withRetry<T>(
    queryFn: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await queryFn();
      } catch (error) {
        lastError = error as Error;
        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
        }
      }
    }
    
    throw lastError!;
  }
}

/**
 * استعلامات لوحة التحكم المحسنة
 * Optimized Dashboard Queries
 */
export const dashboardQueries = {
  /**
   * إحصائيات لوحة التحكم الرئيسية
   */
  async getMainStats() {
    return OptimizedQuery.withCache('dashboard:main-stats', async () => {
      return OptimizedQuery.parallel({
        // إحصائيات العملاء
        customers: prisma.customer.aggregate({
          _count: { id: true },
          where: { status: 'active' }
        }),
        
        // إحصائيات المنتجات
        products: prisma.product.aggregate({
          _count: { id: true },
          where: { status: 'active' }
        }),
        
        // إحصائيات الفواتير
        invoices: prisma.invoice.aggregate({
          _count: { id: true },
          _sum: { total: true }
        }),
        
        // الفواتير المدفوعة
        paidInvoices: prisma.invoice.aggregate({
          _count: { id: true },
          _sum: { total: true },
          where: { paymentStatus: 'paid' }
        }),
        
        // الفواتير المعلقة
        pendingInvoices: prisma.invoice.aggregate({
          _count: { id: true },
          _sum: { total: true },
          where: { paymentStatus: 'pending' }
        }),
        
        // المصروفات
        expenses: prisma.expense.aggregate({
          _sum: { totalAmount: true },
          where: {
            expenseDate: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        })
      });
    }, 60000); // Cache for 1 minute
  },
  
  /**
   * الفواتير الأخيرة
   */
  async getRecentInvoices(limit = 5) {
    return OptimizedQuery.withCache(`dashboard:recent-invoices:${limit}`, async () => {
      return prisma.invoice.findMany({
        select: {
          id: true,
          number: true,
          issueDate: true,
          total: true,
          status: true,
          paymentStatus: true,
          customer: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit
      });
    }, 30000); // Cache for 30 seconds
  },
  
  /**
   * المنتجات منخفضة المخزون
   */
  async getLowStockProducts(limit = 10) {
    return OptimizedQuery.withCache(`dashboard:low-stock:${limit}`, async () => {
      return prisma.product.findMany({
        where: {
          AND: [
            { trackInventory: true },
            { status: 'active' },
            {
              currentStock: {
                lte: prisma.product.fields.minStock
              }
            }
          ]
        },
        select: {
          id: true,
          name: true,
          sku: true,
          currentStock: true,
          minStock: true,
          unit: true
        },
        orderBy: { currentStock: 'asc' },
        take: limit
      });
    }, 120000); // Cache for 2 minutes
  },
  
  /**
   * إحصائيات المبيعات الشهرية
   */
  async getMonthlySalesStats() {
    return OptimizedQuery.withCache('dashboard:monthly-sales', async () => {
      const currentYear = new Date().getFullYear();
      const months = Array.from({ length: 12 }, (_, i) => {
        const month = i + 1;
        const startDate = new Date(currentYear, i, 1);
        const endDate = new Date(currentYear, i + 1, 0);
        
        return {
          month,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        };
      });
      
      const salesData = await Promise.all(
        months.map(async ({ month, startDate, endDate }) => {
          const result = await prisma.invoice.aggregate({
            _sum: { total: true },
            _count: { id: true },
            where: {
              issueDate: {
                gte: startDate.split('T')[0],
                lte: endDate.split('T')[0]
              },
              status: { not: 'cancelled' }
            }
          });
          
          return {
            month,
            total: result._sum.total || 0,
            count: result._count.id || 0
          };
        })
      );
      
      return salesData;
    }, 300000); // Cache for 5 minutes
  }
};

/**
 * تحسين استعلامات البحث
 * Optimized Search Queries
 */
export const searchQueries = {
  /**
   * البحث الشامل
   */
  async globalSearch(term: string, limit = 20) {
    const searchTerm = term.toLowerCase();
    
    return OptimizedQuery.parallel({
      customers: prisma.customer.findMany({
        where: {
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { email: { contains: searchTerm, mode: 'insensitive' } },
            { phone: { contains: searchTerm } }
          ],
          status: 'active'
        },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true
        },
        take: Math.floor(limit / 3)
      }),
      
      products: prisma.product.findMany({
        where: {
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { sku: { contains: searchTerm, mode: 'insensitive' } },
            { barcode: { contains: searchTerm } }
          ],
          status: 'active'
        },
        select: {
          id: true,
          name: true,
          sku: true,
          price: true,
          currentStock: true
        },
        take: Math.floor(limit / 3)
      }),
      
      invoices: prisma.invoice.findMany({
        where: {
          OR: [
            { number: { contains: searchTerm, mode: 'insensitive' } },
            { customer: { name: { contains: searchTerm, mode: 'insensitive' } } }
          ]
        },
        select: {
          id: true,
          number: true,
          total: true,
          status: true,
          customer: {
            select: { name: true }
          }
        },
        take: Math.floor(limit / 3)
      })
    });
  }
};

/**
 * تنظيف التخزين المؤقت دورياً
 * Periodic cache cleanup
 */
setInterval(() => {
  queryCache.clearExpired();
}, 60000); // Clean every minute
