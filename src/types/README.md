# أنواع TypeScript

هذا المجلد يحتوي على تعريفات أنواع TypeScript لتطبيق أمين بلس.

## الملفات الرئيسية

- **electron.d.ts**: تعريفات TypeScript لواجهة برمجة تطبيقات Electron.
- **next-auth.d.ts**: تعريفات TypeScript لـ NextAuth.js.

## تعريفات electron.d.ts

هذا الملف يحتوي على تعريفات TypeScript لواجهة برمجة تطبيقات Electron. يتم استخدام هذه التعريفات لتوفير اقتراحات التلقائية (IntelliSense) عند استخدام واجهة برمجة تطبيقات Electron في التطبيق.

```typescript
interface ElectronAPI {
  send: (channel: string, data: any) => void;
  receive: (channel: string, func: (...args: any[]) => void) => void;
  invoke: (channel: string, data: any) => Promise<any>;
  showSaveDialog: (options: any) => Promise<{ canceled: boolean; filePath?: string }>;
  showOpenDialog: (options: any) => Promise<{ canceled: boolean; filePaths: string[] }>;
  showMessageBox: (options: any) => Promise<{ response: number; checkboxChecked: boolean }>;
  getSystemInfo: () => {
    platform: string;
    arch: string;
    version: string;
    nodeVersion: string;
    chromeVersion: string;
  };
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}
```

## تعريفات next-auth.d.ts

هذا الملف يحتوي على تعريفات TypeScript لـ NextAuth.js. يتم استخدام هذه التعريفات لتوسيع أنواع NextAuth.js الافتراضية لتشمل معلومات إضافية مثل الأدوار والصلاحيات.

```typescript
import 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
      permissions: string[];
    };
  }

  interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    permissions: string[];
  }
}
```

## كيفية استخدام الأنواع

### استخدام أنواع Electron

```typescript
// التحقق مما إذا كان التطبيق يعمل في بيئة Electron
if (typeof window !== 'undefined' && typeof window.electron !== 'undefined') {
  // استخدام واجهة برمجة تطبيقات Electron
  window.electron.showSaveDialog({
    title: 'حفظ الملف',
    defaultPath: 'تقرير.pdf',
    filters: [
      { name: 'PDF', extensions: ['pdf'] },
      { name: 'Excel', extensions: ['xlsx'] },
      { name: 'جميع الملفات', extensions: ['*'] }
    ],
    buttonLabel: 'حفظ'
  });
}
```

### استخدام أنواع NextAuth.js

```typescript
import { useSession } from 'next-auth/react';

export default function ProfilePage() {
  const { data: session } = useSession();

  if (!session) {
    return <div>يرجى تسجيل الدخول</div>;
  }

  return (
    <div>
      <h1>الملف الشخصي</h1>
      <p>الاسم: {session.user.name}</p>
      <p>البريد الإلكتروني: {session.user.email}</p>
      <p>الدور: {session.user.role}</p>
      <p>الصلاحيات: {session.user.permissions.join(', ')}</p>
    </div>
  );
}
```
