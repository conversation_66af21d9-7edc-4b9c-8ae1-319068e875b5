/**
 * تعريفات TypeScript لواجهة برمجة تطبيقات Electron
 * TypeScript definitions for Electron API
 */

interface ElectronAPI {
  /**
   * إرسال رسالة إلى العملية الرئيسية
   * Send a message to the main process
   */
  send: (channel: string, data: any) => void;

  /**
   * استقبال رسالة من العملية الرئيسية
   * Receive a message from the main process
   */
  receive: (channel: string, func: (...args: any[]) => void) => void;

  /**
   * استدعاء وظيفة في العملية الرئيسية وانتظار الرد
   * Invoke a function in the main process and wait for a response
   */
  invoke: (channel: string, data: any) => Promise<any>;

  /**
   * عرض مربع حوار حفظ الملف
   * Show save file dialog
   */
  showSaveDialog: (options: any) => Promise<{ canceled: boolean; filePath?: string }>;

  /**
   * عرض مربع حوار فتح الملف
   * Show open file dialog
   */
  showOpenDialog: (options: any) => Promise<{ canceled: boolean; filePaths: string[] }>;

  /**
   * عرض مربع حوار رسالة
   * Show message box
   */
  showMessageBox: (options: any) => Promise<{ response: number; checkboxChecked: boolean }>;

  /**
   * الحصول على معلومات النظام
   * Get system information
   */
  getSystemInfo: () => {
    platform: string;
    arch: string;
    version: string;
    nodeVersion: string;
    chromeVersion: string;
  };
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}

export {};
