/**
 * تعريفات TypeScript لـ NextAuth.js
 * TypeScript definitions for NextAuth.js
 */

import 'next-auth';

declare module 'next-auth' {
  /**
   * توسيع واجهة الجلسة
   * Extending the Session interface
   */
  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
      permissions: string[];
    };
  }

  /**
   * توسيع واجهة المستخدم
   * Extending the User interface
   */
  interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    permissions: string[];
  }
}

declare module 'next-auth/jwt' {
  /**
   * توسيع واجهة JWT
   * Extending the JWT interface
   */
  interface JWT {
    id: string;
    name: string;
    email: string;
    role: string;
    permissions: string[];
  }
}
