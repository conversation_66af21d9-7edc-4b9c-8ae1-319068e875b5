import { CurrencyCode } from '@/lib/utils';
import { TaxSettings } from '@/components/ui/tax-settings';

// حالة المبيعة
export type SaleStatus = 'draft' | 'completed' | 'refunded' | 'partially_refunded' | 'cancelled';

// طريقة الدفع
export type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'check' | 'credit' | 'multiple';

// نوع المبيعة
export interface Sale {
  id: string;
  saleNumber: string;
  customerId?: string;
  customerName?: string;
  status: SaleStatus;
  items: SaleItem[];
  payments: Payment[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  total: number;
  amountPaid: number;
  amountDue: number;
  currency: CurrencyCode;
  taxSettings: TaxSettings;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

// عنصر المبيعة
export interface SaleItem {
  id: string;
  productId: string;
  name: string;
  sku: string;
  quantity: number;
  price: number;
  originalPrice: number;
  discountAmount: number;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  taxable: boolean;
  subtotal: number;
  total: number;
}

// الدفعة
export interface Payment {
  id: string;
  saleId: string;
  method: PaymentMethod;
  amount: number;
  reference?: string;
  notes?: string;
  createdAt: string;
}

// إعدادات نقاط البيع
export interface POSSettings {
  defaultCustomerId?: string;
  defaultPaymentMethod: PaymentMethod;
  showProductImages: boolean;
  allowDiscounts: boolean;
  allowPriceChange: boolean;
  requireCustomerForCredit: boolean;
  printReceipt: boolean;
  receiptFooter: string;
  receiptHeader: string;
}

// الإعدادات الافتراضية لنقاط البيع
export const DEFAULT_POS_SETTINGS: POSSettings = {
  defaultPaymentMethod: 'cash',
  showProductImages: true,
  allowDiscounts: true,
  allowPriceChange: true,
  requireCustomerForCredit: true,
  printReceipt: true,
  receiptFooter: 'شكراً لتسوقكم معنا | Thank you for shopping with us',
  receiptHeader: 'فاتورة مبيعات | Sales Receipt'
};

// حساب المجموع الفرعي لعنصر المبيعة
export function calculateSaleItemSubtotal(item: SaleItem): number {
  return item.price * item.quantity;
}

// حساب المجموع الكلي لعنصر المبيعة
export function calculateSaleItemTotal(item: SaleItem): number {
  return item.subtotal;
}

// حساب المجموع الفرعي للمبيعة
export function calculateSaleSubtotal(items: SaleItem[]): number {
  return items.reduce((total, item) => total + item.subtotal, 0);
}

// حساب مبلغ الضريبة للمبيعة
export function calculateSaleTaxAmount(items: SaleItem[], taxSettings: TaxSettings): number {
  if (!taxSettings.enabled) return 0;
  
  const taxableAmount = items
    .filter(item => item.taxable)
    .reduce((total, item) => total + item.subtotal, 0);
  
  if (taxSettings.inclusive) {
    // الضريبة مضمنة في السعر
    return taxableAmount - (taxableAmount / (1 + taxSettings.rate / 100));
  } else {
    // الضريبة غير مضمنة في السعر
    return taxableAmount * (taxSettings.rate / 100);
  }
}

// حساب مبلغ الخصم للمبيعة
export function calculateSaleDiscountAmount(items: SaleItem[]): number {
  return items.reduce((total, item) => total + item.discountAmount * item.quantity, 0);
}

// حساب المجموع الكلي للمبيعة
export function calculateSaleTotal(subtotal: number, taxAmount: number): number {
  return subtotal + taxAmount;
}

// حساب المبلغ المستحق للمبيعة
export function calculateSaleAmountDue(total: number, amountPaid: number): number {
  return total - amountPaid;
}

// تطبيق خصم على عنصر المبيعة
export function applySaleItemDiscount(
  item: SaleItem,
  discountType: 'percentage' | 'fixed',
  discountValue: number
): SaleItem {
  let discountAmount = 0;
  
  if (discountType === 'percentage') {
    discountAmount = (item.originalPrice * discountValue) / 100;
  } else {
    discountAmount = discountValue;
  }
  
  // التأكد من أن الخصم لا يتجاوز سعر المنتج
  discountAmount = Math.min(discountAmount, item.originalPrice);
  
  const price = item.originalPrice - discountAmount;
  const subtotal = price * item.quantity;
  
  return {
    ...item,
    price,
    discountAmount,
    discountType,
    discountValue,
    subtotal,
    total: subtotal
  };
}
