'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Copy } from 'lucide-react';

interface Product {
  id: number;
  name: string;
}

interface CopyRecipeDialogProps {
  sourceProductId: number;
  onCopySuccess?: () => void;
}

export function CopyRecipeDialog({ sourceProductId, onCopySuccess }: CopyRecipeDialogProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [fetchingProducts, setFetchingProducts] = useState(true);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (open) {
      fetchProducts();
    }
  }, [open]);

  const fetchProducts = async () => {
    try {
      setFetchingProducts(true);
      const response = await fetch('/api/products');
      if (!response.ok) {
        throw new Error('فشل في جلب المنتجات');
      }
      const data = await response.json();
      
      // استبعاد المنتج المصدر
      const filteredProducts = data.filter((product: Product) => product.id !== sourceProductId);
      
      setProducts(filteredProducts);
      setFetchingProducts(false);
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      toast.error('حدث خطأ أثناء جلب المنتجات');
      setFetchingProducts(false);
    }
  };

  const handleCopyRecipe = async () => {
    if (!selectedProductId) {
      toast.error('الرجاء اختيار منتج هدف');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/recipes/copy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceProductId,
          targetProductId: selectedProductId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في نسخ المقادير');
      }

      toast.success('تم نسخ المقادير بنجاح');
      setOpen(false);
      setSelectedProductId('');
      
      if (onCopySuccess) {
        onCopySuccess();
      }
    } catch (error) {
      console.error('خطأ في نسخ المقادير:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء نسخ المقادير');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Copy className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
          نسخ التقادير
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>نسخ التقادير إلى منتج آخر</DialogTitle>
          <DialogDescription>
            اختر المنتج الذي تريد نسخ التقادير إليه. سيتم استبدال أي تقادير موجودة في المنتج الهدف.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="targetProduct">المنتج الهدف</Label>
            <Select
              value={selectedProductId}
              onValueChange={setSelectedProductId}
              disabled={fetchingProducts}
            >
              <SelectTrigger id="targetProduct">
                <SelectValue placeholder="اختر المنتج الهدف" />
              </SelectTrigger>
              <SelectContent>
                {fetchingProducts ? (
                  <div className="flex justify-center p-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  </div>
                ) : products.length === 0 ? (
                  <div className="p-2 text-center text-gray-500">لا توجد منتجات متاحة</div>
                ) : (
                  products.map((product) => (
                    <SelectItem key={product.id} value={product.id.toString()}>
                      {product.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            إلغاء
          </Button>
          <Button onClick={handleCopyRecipe} disabled={loading || !selectedProductId}>
            {loading ? 'جاري النسخ...' : 'نسخ التقادير'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
