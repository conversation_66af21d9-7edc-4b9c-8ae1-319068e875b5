'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { AlertTriangle, AlertCircle, RefreshCw } from 'lucide-react';

interface IngredientAlert {
  id: number;
  name: string;
  stock: number;
  minStock: number;
  usedIn: { id: number; name: string }[];
}

interface StockAlertsProps {
  onViewIngredient?: (id: number) => void;
}

export function IngredientsStockAlerts({ onViewIngredient }: StockAlertsProps) {
  const [lowStockIngredients, setLowStockIngredients] = useState<IngredientAlert[]>([]);
  const [outOfStockIngredients, setOutOfStockIngredients] = useState<IngredientAlert[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    checkIngredientsStock();
  }, []);

  const checkIngredientsStock = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/inventory/check-ingredients-stock');
      
      if (!response.ok) {
        throw new Error('فشل في جلب تنبيهات المخزون');
      }
      
      const data = await response.json();
      setLowStockIngredients(data.lowStockIngredients);
      setOutOfStockIngredients(data.outOfStockIngredients);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب تنبيهات المخزون:', error);
      toast.error('حدث خطأ أثناء جلب تنبيهات المخزون');
      setLoading(false);
    }
  };

  const handleViewIngredient = (id: number) => {
    if (onViewIngredient) {
      onViewIngredient(id);
    } else {
      window.location.href = `/dashboard/products/${id}`;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>تنبيهات مخزون المكونات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center p-4">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (lowStockIngredients.length === 0 && outOfStockIngredients.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>تنبيهات مخزون المكونات</CardTitle>
            <Button variant="ghost" size="sm" onClick={checkIngredientsStock}>
              <RefreshCw className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
              تحديث
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-gray-500">
            لا توجد تنبيهات حالية للمخزون
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>تنبيهات مخزون المكونات</CardTitle>
          <Button variant="ghost" size="sm" onClick={checkIngredientsStock}>
            <RefreshCw className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            تحديث
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {outOfStockIngredients.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-medium flex items-center text-red-600">
              <AlertCircle className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
              مكونات نفدت من المخزون ({outOfStockIngredients.length})
            </h3>
            <div className="space-y-2">
              {outOfStockIngredients.map((ingredient) => (
                <div key={ingredient.id} className="border rounded-md p-3 bg-red-50">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium">{ingredient.name}</h4>
                      <p className="text-sm text-gray-500">
                        المخزون الحالي: <span className="text-red-600 font-bold">{ingredient.stock}</span> | 
                        الحد الأدنى: {ingredient.minStock}
                      </p>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewIngredient(ingredient.id)}
                    >
                      عرض المكون
                    </Button>
                  </div>
                  <div className="mt-2 text-sm">
                    <p className="text-gray-600">يستخدم في:</p>
                    <ul className="list-disc list-inside mt-1">
                      {ingredient.usedIn.map((product) => (
                        <li key={product.id}>{product.name}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {lowStockIngredients.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-medium flex items-center text-amber-600">
              <AlertTriangle className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
              مكونات منخفضة في المخزون ({lowStockIngredients.length})
            </h3>
            <div className="space-y-2">
              {lowStockIngredients.map((ingredient) => (
                <div key={ingredient.id} className="border rounded-md p-3 bg-amber-50">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium">{ingredient.name}</h4>
                      <p className="text-sm text-gray-500">
                        المخزون الحالي: <span className="text-amber-600 font-bold">{ingredient.stock}</span> | 
                        الحد الأدنى: {ingredient.minStock}
                      </p>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewIngredient(ingredient.id)}
                    >
                      عرض المكون
                    </Button>
                  </div>
                  <div className="mt-2 text-sm">
                    <p className="text-gray-600">يستخدم في:</p>
                    <ul className="list-disc list-inside mt-1">
                      {ingredient.usedIn.map((product) => (
                        <li key={product.id}>{product.name}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
