'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Bell, CheckCircle2, AlertTriangle, Package, Calendar, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { useI18n } from '@/lib/i18n';

interface Notification {
  id: number;
  title: string;
  message: string;
  type: string;
  relatedId?: string;
  relatedType?: string;
  isRead: boolean;
  createdAt: string;
}

export function NotificationsDropdown() {
  const { t, language } = useI18n();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('all');

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      // تعطيل طلب API الإشعارات مؤقتًا حتى يتم إصلاح المشكلة
      // const response = await fetch('/api/notifications?limit=20');
      // if (!response.ok) {
      //   throw new Error('فشل في جلب الإشعارات');
      // }
      // const data = await response.json();
      // setNotifications(data.notifications);
      // setUnreadCount(data.notifications.filter((n: Notification) => !n.isRead).length);

      // استخدام بيانات وهمية بدلاً من ذلك
      // Using mock data instead
      throw new Error(t('notifications.useMockData'));
    } catch (error) {
      console.error(t('notifications.usingMockData'));
      // استخدام بيانات وهمية للعرض
      // Using mock data for display
      const mockNotifications = [
        {
          id: 1,
          title: language === 'ar' ? 'منتج جديد' : 'New Product',
          message: language === 'ar' ? 'تم إضافة منتج جديد بنجاح' : 'New product added successfully',
          type: 'PRODUCTION',
          isRead: false,
          createdAt: new Date().toISOString()
        },
        {
          id: 2,
          title: language === 'ar' ? 'تنبيه المخزون' : 'Inventory Alert',
          message: language === 'ar' ? 'المخزون منخفض لبعض المكونات' : 'Low stock for some components',
          type: 'INVENTORY',
          isRead: true,
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          title: language === 'ar' ? 'فحص الجودة' : 'Quality Check',
          message: language === 'ar' ? 'تم اجتياز فحص الجودة بنجاح' : 'Quality check passed successfully',
          type: 'QUALITY',
          isRead: false,
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      setNotifications(mockNotifications);
      setUnreadCount(mockNotifications.filter(n => !n.isRead).length);
      setLoading(false);
    }
  };

  const markAsRead = async (id: number) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          isRead: true
        }),
      });

      if (!response.ok) {
        throw new Error(t('notifications.errors.updateFailed'));
      }

      // تحديث حالة الإشعارات محليًا
      // Update notifications state locally
      setNotifications(notifications.map(notification =>
        notification.id === id ? { ...notification, isRead: true } : notification
      ));
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error(t('notifications.errors.updateError'), error);
      // تحديث حالة الإشعارات محليًا على أي حال للعرض
      // Update notifications state locally anyway for display purposes
      setNotifications(notifications.map(notification =>
        notification.id === id ? { ...notification, isRead: true } : notification
      ));
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const markAllAsRead = async () => {
    try {
      // في الواقع، يجب إنشاء API لتحديث جميع الإشعارات دفعة واحدة
      // لكن للعرض، سنقوم بتحديث الحالة محليًا
      // For display purposes, we'll update the state locally
      setNotifications(notifications.map(notification => ({ ...notification, isRead: true })));
      setUnreadCount(0);
      toast.success(t('notifications.allMarkedAsRead'));
    } catch (error) {
      console.error(t('notifications.errors.updateAllError'), error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'PRODUCTION':
        return <Package className="h-5 w-5 text-blue-500" />;
      case 'QUALITY':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'INVENTORY':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'EXPIRY':
        return <Calendar className="h-5 w-5 text-red-500" />;
      default:
        return <Settings className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: language === 'ar' ? ar : enUS
      });
    } catch (error) {
      return t('notifications.unknownTime');
    }
  };

  const filteredNotifications = activeTab === 'all'
    ? notifications
    : activeTab === 'unread'
      ? notifications.filter(n => !n.isRead)
      : notifications.filter(n => n.type === activeTab);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-medium">{t('notifications.title')}</h3>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" onClick={markAllAsRead}>
              {t('notifications.markAllAsRead')}
            </Button>
          )}
        </div>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 p-1 m-1">
            <TabsTrigger value="all">{t('notifications.tabs.all')}</TabsTrigger>
            <TabsTrigger value="unread">{t('notifications.tabs.unread')}</TabsTrigger>
            <TabsTrigger value="PRODUCTION">{t('notifications.tabs.production')}</TabsTrigger>
            <TabsTrigger value="INVENTORY">{t('notifications.tabs.inventory')}</TabsTrigger>
            <TabsTrigger value="QUALITY">{t('notifications.tabs.quality')}</TabsTrigger>
          </TabsList>
          <TabsContent value={activeTab} className="max-h-[300px] overflow-y-auto">
            {loading ? (
              <div className="flex justify-center p-4">
                <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="text-center py-6 text-gray-500">
                {t('notifications.noNotifications')}
              </div>
            ) : (
              <div>
                {filteredNotifications.map((notification) => (
                  <button
                    key={notification.id}
                    className={`p-3 border-b hover:bg-gray-50 cursor-pointer w-full text-left ${!notification.isRead ? 'bg-blue-50' : ''}`}
                    onClick={() => !notification.isRead && markAsRead(notification.id)}
                    onKeyDown={(e) => e.key === 'Enter' && !notification.isRead && markAsRead(notification.id)}

                    tabIndex={0}
                    aria-label={`${notification.title} - ${notification.isRead ? t('notifications.read') : t('notifications.unread')}`}
                  >
                    <div className="flex items-start">
                      <div className="mr-3 rtl:ml-3 rtl:mr-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium">{notification.title}</h4>
                        <p className="text-xs text-gray-500 mt-1">{notification.message}</p>
                        <p className="text-xs text-gray-400 mt-1">{getTimeAgo(notification.createdAt)}</p>
                      </div>
                      {!notification.isRead && (
                        <div className="h-2 w-2 rounded-full bg-blue-500 mt-1"></div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
        <div className="p-2 border-t text-center">
          <Button
            variant="ghost"
            size="sm"
            className="w-full"
            onClick={() => toast.info(t('notifications.featureComingSoon'))}
          >
            {t('notifications.viewAll')}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
