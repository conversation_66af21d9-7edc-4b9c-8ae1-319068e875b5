'use client';

import { useState, useEffect } from 'react';
import { Bell, Check, Clock, FileText, X, ChevronDown, ChevronUp, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

// أنواع البيانات
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'invoice' | 'payment' | 'reminder' | 'system';
  read: boolean;
  date: string;
  link?: string;
}

interface NotificationSettings {
  email: boolean;
  push: boolean;
  invoiceCreated: boolean;
  paymentReceived: boolean;
  invoiceDue: boolean;
  invoiceOverdue: boolean;
}

export function NotificationsCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    email: true,
    push: true,
    invoiceCreated: true,
    paymentReceived: true,
    invoiceDue: true,
    invoiceOverdue: true,
  });

  useEffect(() => {
    // محاكاة جلب الإشعارات
    const mockNotifications: Notification[] = [
      {
        id: '1',
        title: 'فاتورة جديدة',
        message: 'تم إنشاء فاتورة جديدة برقم INV-001 للعميل شركة الأمل',
        type: 'invoice',
        read: false,
        date: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // قبل 30 دقيقة
        link: '/dashboard/invoices/1',
      },
      {
        id: '2',
        title: 'تم استلام دفعة',
        message: 'تم استلام دفعة بقيمة 1,200 درهم للفاتورة رقم INV-002',
        type: 'payment',
        read: false,
        date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // قبل ساعتين
        link: '/dashboard/invoices/2',
      },
      {
        id: '3',
        title: 'فاتورة مستحقة',
        message: 'الفاتورة رقم INV-003 مستحقة الدفع اليوم',
        type: 'reminder',
        read: true,
        date: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // قبل 5 ساعات
        link: '/dashboard/invoices/3',
      },
      {
        id: '4',
        title: 'فاتورة متأخرة',
        message: 'الفاتورة رقم INV-004 متأخرة عن موعد الدفع بـ 3 أيام',
        type: 'reminder',
        read: false,
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // قبل يوم
        link: '/dashboard/invoices/4',
      },
      {
        id: '5',
        title: 'تحديث النظام',
        message: 'تم تحديث النظام إلى الإصدار الجديد 2.0.0',
        type: 'system',
        read: true,
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // قبل يومين
      },
    ];

    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter(n => !n.read).length);
  }, []);

  const handleMarkAsRead = (id: string) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    ));
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleMarkAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, read: true })));
    setUnreadCount(0);
  };

  const handleDeleteNotification = (id: string) => {
    const notification = notifications.find(n => n.id === id);
    setNotifications(notifications.filter(notification => notification.id !== id));
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const handleClearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      handleMarkAsRead(notification.id);
    }
    
    if (notification.link) {
      window.location.href = notification.link;
    }
    
    setIsOpen(false);
  };

  const handleSaveSettings = () => {
    // محاكاة حفظ الإعدادات
    setSettingsOpen(false);
  };

  // تصفية الإشعارات حسب علامة التبويب النشطة
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    return notification.type === activeTab;
  });

  // الحصول على أيقونة الإشعار حسب النوع
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'invoice': return <FileText className="h-5 w-5 text-blue-500" />;
      case 'payment': return <Check className="h-5 w-5 text-green-500" />;
      case 'reminder': return <Clock className="h-5 w-5 text-amber-500" />;
      case 'system': return <Bell className="h-5 w-5 text-purple-500" />;
      default: return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  // تنسيق التاريخ بالعربية
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: ar });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {unreadCount}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 md:w-96 p-0" align="end">
          <div className="flex items-center justify-between p-4 border-b">
            <h3 className="font-medium">الإشعارات</h3>
            <div className="flex gap-1">
              <Button variant="ghost" size="icon" onClick={handleMarkAllAsRead} title="تعيين الكل كمقروء">
                <Check className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={handleClearAll} title="حذف الكل">
                <X className="h-4 w-4" />
              </Button>
              <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" title="إعدادات الإشعارات">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>إعدادات الإشعارات</DialogTitle>
                    <DialogDescription>
                      قم بتخصيص إعدادات الإشعارات حسب احتياجاتك
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <h4 className="font-medium">قنوات الإشعارات</h4>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="email-notifications">إشعارات البريد الإلكتروني</Label>
                      <Switch
                        id="email-notifications"
                        checked={notificationSettings.email}
                        onCheckedChange={(checked) => setNotificationSettings({ ...notificationSettings, email: checked })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="push-notifications">إشعارات الويب</Label>
                      <Switch
                        id="push-notifications"
                        checked={notificationSettings.push}
                        onCheckedChange={(checked) => setNotificationSettings({ ...notificationSettings, push: checked })}
                      />
                    </div>
                    <div className="border-t pt-4 mt-2">
                      <h4 className="font-medium mb-2">أنواع الإشعارات</h4>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="invoice-created">إنشاء فاتورة جديدة</Label>
                        <Switch
                          id="invoice-created"
                          checked={notificationSettings.invoiceCreated}
                          onCheckedChange={(checked) => setNotificationSettings({ ...notificationSettings, invoiceCreated: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <Label htmlFor="payment-received">استلام دفعة</Label>
                        <Switch
                          id="payment-received"
                          checked={notificationSettings.paymentReceived}
                          onCheckedChange={(checked) => setNotificationSettings({ ...notificationSettings, paymentReceived: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <Label htmlFor="invoice-due">فاتورة مستحقة</Label>
                        <Switch
                          id="invoice-due"
                          checked={notificationSettings.invoiceDue}
                          onCheckedChange={(checked) => setNotificationSettings({ ...notificationSettings, invoiceDue: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <Label htmlFor="invoice-overdue">فاتورة متأخرة</Label>
                        <Switch
                          id="invoice-overdue"
                          checked={notificationSettings.invoiceOverdue}
                          onCheckedChange={(checked) => setNotificationSettings({ ...notificationSettings, invoiceOverdue: checked })}
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleSaveSettings}>حفظ الإعدادات</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
          
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-5 p-0 bg-transparent">
              <TabsTrigger value="all" className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none">
                الكل
              </TabsTrigger>
              <TabsTrigger value="unread" className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none">
                غير مقروء
              </TabsTrigger>
              <TabsTrigger value="invoice" className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none">
                فواتير
              </TabsTrigger>
              <TabsTrigger value="payment" className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none">
                مدفوعات
              </TabsTrigger>
              <TabsTrigger value="reminder" className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none">
                تذكيرات
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value={activeTab} className="m-0">
              <ScrollArea className="h-[300px]">
                {filteredNotifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-[300px] text-gray-500">
                    <Bell className="h-8 w-8 mb-2 text-gray-300" />
                    <p>لا توجد إشعارات</p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {filteredNotifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 hover:bg-gray-50 cursor-pointer flex gap-3 ${!notification.read ? 'bg-blue-50' : ''}`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-grow">
                          <div className="flex justify-between">
                            <h4 className="font-medium text-sm">{notification.title}</h4>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteNotification(notification.id);
                              }}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                          <p className="text-xs text-gray-400 mt-1">{formatDate(notification.date)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>
          
          <div className="p-2 border-t text-center">
            <Button variant="link" size="sm" asChild>
              <a href="/dashboard/notifications">عرض كل الإشعارات</a>
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
