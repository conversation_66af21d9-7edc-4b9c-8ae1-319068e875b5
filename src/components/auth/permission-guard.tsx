'use client'

import { ReactNode } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { usePermission } from '@/hooks/usePermission'

// Adjusted the permission type to match the expected type
type Permission = 'MANAGE_USERS' | 'MANAGE_SETTINGS' | 'VIEW_REPORTS' | 'MANAGE_CUSTOMERS' | 'MANAGE_INVOICES' | 'MANAGE_PRODUCTS'

interface PermissionGuardProps {
    readonly children: ReactNode
    readonly permission: Permission | Permission[]
    readonly fallback?: ReactNode
}

export function PermissionGuard({ children, permission, fallback = null }: PermissionGuardProps) {
    const { status } = useSession()
    const router = useRouter()
    const { hasPermission, hasAnyPermission } = usePermission()

    // إذا كان المستخدم غير مسجل الدخول، قم بتوجيهه إلى صفحة تسجيل الدخول
    if (status === 'unauthenticated') {
        router.push('/auth/login')
        return null
    }

    // إذا كانت الجلسة قيد التحميل، اعرض حالة التحميل
    if (status === 'loading') {
        return <div className="p-4 text-center">جاري التحميل...</div>
    }

    // التحقق من الصلاحيات
    const userHasPermission = Array.isArray(permission)
        ? hasAnyPermission(permission)
        : hasPermission(permission)

    // إذا لم يكن لدى المستخدم الصلاحية المطلوبة، اعرض المحتوى البديل
    if (!userHasPermission) {
        return fallback ? <>{fallback}</> : <div className="p-4 text-center">غير مصرح لك بالوصول إلى هذه الصفحة</div>
    }

    // إذا كان لدى المستخدم الصلاحية المطلوبة، اعرض المحتوى الأصلي
    return <div data-testid="protected-content">{children}</div>
}
