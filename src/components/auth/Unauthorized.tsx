// src/components/auth/Unauthorized.tsx
import { Button } from "@/components/ui/button";
import Link from "next/link";

export const Unauthorized = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-4 text-center">
      <div className="w-16 h-16 mb-4 text-red-500">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      </div>
      <h1 className="mb-2 text-2xl font-bold">غير مصرح</h1>
      <p className="mb-6 text-gray-600">
        ليس لديك الصلاحيات الكافية للوصول إلى هذه الصفحة.
      </p>
      <div className="space-x-4 rtl:space-x-reverse">
        <Button variant="outline" asChild>
          <Link href="/dashboard">العودة إلى لوحة التحكم</Link>
        </Button>
        <Button asChild>
          <Link href="/auth/login">تسجيل الدخول بحساب آخر</Link>
        </Button>
      </div>
    </div>
  );
};