'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { isElectron } from '@/lib/electron';

/**
 * واجهة سياق Electron
 * Electron context interface
 */
interface ElectronContextType {
  isElectronApp: boolean;
}

/**
 * سياق Electron
 * Electron context
 */
const ElectronContext = createContext<ElectronContextType>({
  isElectronApp: false,
});

/**
 * خطاف استخدام سياق Electron
 * Hook to use Electron context
 */
export const useElectron = () => useContext(ElectronContext);

/**
 * واجهة خصائص مزود Electron
 * Electron provider props interface
 */
interface ElectronProviderProps {
  children: ReactNode;
}

/**
 * مزود سياق Electron
 * Electron context provider
 */
export function ElectronProvider({ children }: ElectronProviderProps) {
  // التحقق مما إذا كان التطبيق يعمل في بيئة Electron
  // Check if the app is running in an Electron environment
  const isElectronApp = isElectron();

  return (
    <ElectronContext.Provider value={{ isElectronApp }}>
      {children}
    </ElectronContext.Provider>
  );
}
