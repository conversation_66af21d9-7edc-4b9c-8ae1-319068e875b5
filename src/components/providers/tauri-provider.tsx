'use client';

import React, { createContext, useContext, ReactNode, useEffect, useState, useMemo } from 'react';

/**
 * واجهة سياق Tauri
 * Tauri context interface
 */
interface TauriContextType {
  isTauriApp: boolean;
  platform: string | null;
  version: string | null;
}

/**
 * سياق Tauri
 * Tauri context
 */
const TauriContext = createContext<TauriContextType>({
  isTauriApp: false,
  platform: null,
  version: null,
});

/**
 * خطاف استخدام سياق Tauri
 * Hook to use Tauri context
 */
export const useTauri = () => useContext(TauriContext);

/**
 * واجهة خصائص مزود Tauri
 * Tauri provider props interface
 */
interface TauriProviderProps {
  readonly children: ReactNode;
}

/**
 * التحقق مما إذا كان التطبيق يعمل في بيئة Tauri
 * Check if the app is running in a Tauri environment
 */
const isTauri = (): boolean => {
  return typeof window !== 'undefined' && window.__TAURI_IPC__ !== undefined;
};

/**
 * مزود سياق Tauri
 * Tauri context provider
 */
export function TauriProvider({ children }: TauriProviderProps) {
  const [platform, setPlatform] = useState<string | null>(null);
  const [version, setVersion] = useState<string | null>(null);
  const [isTauriApp, setIsTauriApp] = useState<boolean>(false);

  useEffect(() => {
    const checkTauri = async () => {
      try {
        // التحقق مما إذا كان التطبيق يعمل في بيئة Tauri
        // Check if the app is running in a Tauri environment
        const isTauriEnvironment = typeof window !== 'undefined' && window.__TAURI_IPC__ !== undefined;
        setIsTauriApp(isTauriEnvironment);

        // تعيين قيم افتراضية للمنصة والإصدار
        // Set default values for platform and version
        setPlatform(isTauriEnvironment ? 'desktop' : 'web');
        setVersion('1.0.0');
      } catch (error) {
        // معالجة أي أخطاء أثناء الكشف عن Tauri
        // Handle any errors during Tauri detection
        console.error('Error checking Tauri environment:', error);
        setIsTauriApp(false);
        setPlatform('web');
        setVersion('1.0.0');
      }
    };

    checkTauri();
  }, []);

  // استخدام useMemo لتجنب إعادة إنشاء كائن القيمة في كل عملية تقديم
  // Use useMemo to avoid recreating the value object on every render
  const contextValue = useMemo(() => ({
    isTauriApp,
    platform,
    version
  }), [isTauriApp, platform, version]);

  return (
    <TauriContext.Provider value={contextValue}>
      {children}
    </TauriContext.Provider>
  );
}
