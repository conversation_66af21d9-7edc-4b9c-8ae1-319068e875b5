'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { BilingualLabel } from './bilingual-text';
import { BilingualButton } from './bilingual-button';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';

interface BilingualInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  labelAr: React.ReactNode;
  labelEn: React.ReactNode;
  valueAr: string;
  valueEn: string;
  onChange: (valueAr: string, valueEn: string) => void;
  primaryLanguage?: SupportedLanguage;
  placeholderAr?: string;
  placeholderEn?: string;
  errorAr?: string;
  errorEn?: string;
  required?: boolean;
  showBothInputs?: boolean;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
}

/**
 * مكون حقل إدخال ثنائي اللغة
 * Bilingual Input Component
 */
export function BilingualInput({
  labelAr,
  labelEn,
  valueAr,
  valueEn,
  onChange,
  primaryLanguage,
  placeholderAr = 'أدخل النص بالعربية',
  placeholderEn = 'Enter text in English',
  errorAr,
  errorEn,
  required = false,
  showBothInputs = true,
  className = '',
  inputClassName = '',
  labelClassName = '',
  errorClassName = '',
  id,
  ...props
}: Readonly<BilingualInputProps>) {
  const { language } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  // إنشاء معرف فريد إذا لم يتم توفيره
  const inputId = id || `bilingual-input-${Math.random().toString(36).substring(2, 9)}`;
  
  // معالجة تغيير القيمة العربية
  const handleArabicChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value, valueEn);
  };
  
  // معالجة تغيير القيمة الإنجليزية
  const handleEnglishChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(valueAr, e.target.value);
  };
  
  // إذا كان عرض حقل إدخال واحد فقط
  if (!showBothInputs) {
    return (
      <div className={cn('bilingual-input-container', className)}>
        <BilingualLabel
          ar={labelAr}
          en={labelEn}
          htmlFor={inputId}
          required={required}
          className={labelClassName}
          primaryLanguage={effectivePrimaryLanguage}
        />
        
        <Input
          id={inputId}
          value={effectivePrimaryLanguage === 'ar' ? valueAr : valueEn}
          onChange={effectivePrimaryLanguage === 'ar' ? handleArabicChange : handleEnglishChange}
          placeholder={effectivePrimaryLanguage === 'ar' ? placeholderAr : placeholderEn}
          dir={effectivePrimaryLanguage === 'ar' ? 'rtl' : 'ltr'}
          className={cn(inputClassName, {
            'text-right': effectivePrimaryLanguage === 'ar',
            'text-left': effectivePrimaryLanguage === 'en'
          })}
          required={required}
          {...props}
        />
        
        {((effectivePrimaryLanguage === 'ar' && errorAr) || (effectivePrimaryLanguage === 'en' && errorEn)) && (
          <p className={cn('text-sm text-red-500 mt-1', errorClassName)}>
            {effectivePrimaryLanguage === 'ar' ? errorAr : errorEn}
          </p>
        )}
      </div>
    );
  }
  
  // عرض حقلي إدخال (عربي وإنجليزي)
  return (
    <div className={cn('bilingual-input-container', className)}>
      <BilingualLabel
        ar={labelAr}
        en={labelEn}
        htmlFor={`${inputId}-ar`}
        required={required}
        className={labelClassName}
        primaryLanguage={effectivePrimaryLanguage}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div>
          <Input
            id={`${inputId}-ar`}
            value={valueAr}
            onChange={handleArabicChange}
            placeholder={placeholderAr}
            dir="rtl"
            className={cn(inputClassName, 'text-right')}
            required={required}
            {...props}
          />
          {errorAr && (
            <p className={cn('text-sm text-red-500 mt-1 text-right', errorClassName)}>
              {errorAr}
            </p>
          )}
        </div>
        
        <div>
          <Input
            id={`${inputId}-en`}
            value={valueEn}
            onChange={handleEnglishChange}
            placeholder={placeholderEn}
            dir="ltr"
            className={cn(inputClassName, 'text-left')}
            required={required}
            {...props}
          />
          {errorEn && (
            <p className={cn('text-sm text-red-500 mt-1', errorClassName)}>
              {errorEn}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * مكون منطقة نص ثنائية اللغة
 * Bilingual Textarea Component
 */
export function BilingualTextarea({
  labelAr,
  labelEn,
  valueAr,
  valueEn,
  onChange,
  primaryLanguage,
  placeholderAr = 'أدخل النص بالعربية',
  placeholderEn = 'Enter text in English',
  errorAr,
  errorEn,
  required = false,
  showBothInputs = true,
  className = '',
  inputClassName = '',
  labelClassName = '',
  errorClassName = '',
  id,
  ...props
}: Readonly<Omit<BilingualInputProps, 'type'> & React.TextareaHTMLAttributes<HTMLTextAreaElement>>) {
  const { language } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  // إنشاء معرف فريد إذا لم يتم توفيره
  const textareaId = id || `bilingual-textarea-${Math.random().toString(36).substring(2, 9)}`;
  
  // معالجة تغيير القيمة العربية
  const handleArabicChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value, valueEn);
  };
  
  // معالجة تغيير القيمة الإنجليزية
  const handleEnglishChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(valueAr, e.target.value);
  };
  
  // إذا كان عرض منطقة نص واحدة فقط
  if (!showBothInputs) {
    return (
      <div className={cn('bilingual-textarea-container', className)}>
        <BilingualLabel
          ar={labelAr}
          en={labelEn}
          htmlFor={textareaId}
          required={required}
          className={labelClassName}
          primaryLanguage={effectivePrimaryLanguage}
        />
        
        <Textarea
          id={textareaId}
          value={effectivePrimaryLanguage === 'ar' ? valueAr : valueEn}
          onChange={effectivePrimaryLanguage === 'ar' ? handleArabicChange : handleEnglishChange}
          placeholder={effectivePrimaryLanguage === 'ar' ? placeholderAr : placeholderEn}
          dir={effectivePrimaryLanguage === 'ar' ? 'rtl' : 'ltr'}
          className={cn(inputClassName, {
            'text-right': effectivePrimaryLanguage === 'ar',
            'text-left': effectivePrimaryLanguage === 'en'
          })}
          required={required}
          {...props}
        />
        
        {((effectivePrimaryLanguage === 'ar' && errorAr) || (effectivePrimaryLanguage === 'en' && errorEn)) && (
          <p className={cn('text-sm text-red-500 mt-1', errorClassName)}>
            {effectivePrimaryLanguage === 'ar' ? errorAr : errorEn}
          </p>
        )}
      </div>
    );
  }
  
  // عرض منطقتي نص (عربي وإنجليزي)
  return (
    <div className={cn('bilingual-textarea-container', className)}>
      <BilingualLabel
        ar={labelAr}
        en={labelEn}
        htmlFor={`${textareaId}-ar`}
        required={required}
        className={labelClassName}
        primaryLanguage={effectivePrimaryLanguage}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div>
          <Textarea
            id={`${textareaId}-ar`}
            value={valueAr}
            onChange={handleArabicChange}
            placeholder={placeholderAr}
            dir="rtl"
            className={cn(inputClassName, 'text-right')}
            required={required}
            {...props}
          />
          {errorAr && (
            <p className={cn('text-sm text-red-500 mt-1 text-right', errorClassName)}>
              {errorAr}
            </p>
          )}
        </div>
        
        <div>
          <Textarea
            id={`${textareaId}-en`}
            value={valueEn}
            onChange={handleEnglishChange}
            placeholder={placeholderEn}
            dir="ltr"
            className={cn(inputClassName, 'text-left')}
            required={required}
            {...props}
          />
          {errorEn && (
            <p className={cn('text-sm text-red-500 mt-1', errorClassName)}>
              {errorEn}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * مكون نموذج ثنائي اللغة
 * Bilingual Form Component
 */
export function BilingualForm({
  onSubmit,
  submitTextAr = 'حفظ',
  submitTextEn = 'Save',
  cancelTextAr = 'إلغاء',
  cancelTextEn = 'Cancel',
  onCancel,
  loading = false,
  primaryLanguage,
  className = '',
  children,
  ...props
}: Readonly<React.FormHTMLAttributes<HTMLFormElement> & {
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  submitTextAr?: React.ReactNode;
  submitTextEn?: React.ReactNode;
  cancelTextAr?: React.ReactNode;
  cancelTextEn?: React.ReactNode;
  onCancel?: () => void;
  loading?: boolean;
  primaryLanguage?: SupportedLanguage;
}>) {
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    onSubmit(e);
  };
  
  return (
    <form 
      onSubmit={handleSubmit} 
      className={cn('bilingual-form space-y-4', className)}
      {...props}
    >
      {children}
      
      <div className="flex justify-end space-x-2 space-x-reverse">
        {onCancel && (
          <BilingualButton
            textAr={cancelTextAr}
            textEn={cancelTextEn}
            variant="outline"
            type="button"
            onClick={onCancel}
            primaryLanguage={primaryLanguage}
          />
        )}
        
        <BilingualButton
          textAr={submitTextAr}
          textEn={submitTextEn}
          type="submit"
          loading={loading}
          primaryLanguage={primaryLanguage}
        />
      </div>
    </form>
  );
}
