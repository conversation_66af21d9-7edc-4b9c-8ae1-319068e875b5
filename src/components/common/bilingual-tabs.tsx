'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';
import { BilingualText } from './bilingual-text';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface BilingualTabProps {
  id: string;
  titleAr: React.ReactNode;
  titleEn: React.ReactNode;
  contentAr?: React.ReactNode;
  contentEn?: React.ReactNode;
  icon?: React.ReactNode;
}

interface BilingualTabsProps {
  tabs: BilingualTabProps[];
  defaultTabId?: string;
  primaryLanguage?: SupportedLanguage;
  className?: string;
  tabsListClassName?: string;
  tabsTriggerClassName?: string;
  tabsContentClassName?: string;
  showBothLanguages?: boolean;
  showBothContents?: boolean;
  onChange?: (tabId: string) => void;
  orientation?: 'horizontal' | 'vertical';
}

/**
 * مكون تبويب ثنائي اللغة
 * Bilingual Tabs Component
 */
export function BilingualTabs({
  tabs,
  defaultTabId,
  primaryLanguage,
  className = '',
  tabsListClassName = '',
  tabsTriggerClassName = '',
  tabsContentClassName = '',
  showBothLanguages = false,
  showBothContents = false,
  onChange,
  orientation = 'horizontal',
}: Readonly<BilingualTabsProps>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  // تحديد التبويب الافتراضي
  const defaultTab = defaultTabId ?? (tabs.length > 0 ? tabs[0].id : '');

  // حالة التبويب النشط
  const [activeTab, setActiveTab] = React.useState(defaultTab);

  // معالجة تغيير التبويب
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    if (onChange) {
      onChange(tabId);
    }
  };

  return (
    <Tabs
      defaultValue={defaultTab}
      value={activeTab}
      onValueChange={handleTabChange}
      className={cn(
        'bilingual-tabs',
        orientation === 'vertical' ? 'flex flex-row space-x-4 rtl:space-x-reverse' : '',
        className
      )}
    >
      <TabsList
        className={cn(
          'bilingual-tabs-list',
          orientation === 'vertical'
            ? 'flex-col h-auto space-y-1 space-x-0'
            : orientation === 'horizontal' && tabs.length > 2
              ? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6'
              : 'grid grid-cols-2',
          tabsListClassName
        )}
      >
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            className={cn('bilingual-tab-trigger', tabsTriggerClassName)}
          >
            <div className="flex items-center">
              {tab.icon && (
                <span className="tab-icon mr-2">
                  {tab.icon}
                </span>
              )}
              <BilingualText
                ar={tab.titleAr}
                en={tab.titleEn}
                primaryLanguage={effectivePrimaryLanguage}
                showBothLanguages={showBothLanguages}
              />
            </div>
          </TabsTrigger>
        ))}
      </TabsList>

      <div className={cn(
        'bilingual-tabs-content-container',
        orientation === 'vertical' ? 'flex-1' : 'mt-2'
      )}>
        {tabs.map((tab) => (
          <TabsContent
            key={tab.id}
            value={tab.id}
            className={cn('bilingual-tab-content', tabsContentClassName)}
          >
            {showBothContents ? (
              <>
                {tab.contentAr && (
                  <div className="bilingual-tab-content-ar mb-4" dir="rtl">
                    {tab.contentAr}
                  </div>
                )}
                {tab.contentEn && (
                  <div className="bilingual-tab-content-en" dir="ltr">
                    {tab.contentEn}
                  </div>
                )}
              </>
            ) : (
              <div className={cn('bilingual-tab-content-single', {
                'text-right': effectivePrimaryLanguage === 'ar',
                'text-left': effectivePrimaryLanguage === 'en'
              })}>
                {effectivePrimaryLanguage === 'ar' ? tab.contentAr : tab.contentEn}
              </div>
            )}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  );
}

interface BilingualTabsCardProps extends BilingualTabsProps {
  titleAr?: React.ReactNode;
  titleEn?: React.ReactNode;
  descriptionAr?: React.ReactNode;
  descriptionEn?: React.ReactNode;
  cardClassName?: string;
  headerClassName?: string;
}

/**
 * مكون بطاقة تبويب ثنائية اللغة
 * Bilingual Tabs Card Component
 */
export function BilingualTabsCard({
  tabs,
  defaultTabId,
  primaryLanguage,
  className = '',
  tabsListClassName = '',
  tabsTriggerClassName = '',
  tabsContentClassName = '',
  showBothLanguages = false,
  showBothContents = false,
  onChange,
  orientation = 'horizontal',
  titleAr,
  titleEn,
  descriptionAr,
  descriptionEn,
  cardClassName = '',
  headerClassName = '',
}: Readonly<BilingualTabsCardProps>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  return (
    <div className={cn('bilingual-tabs-card rounded-lg border bg-card text-card-foreground shadow-sm', cardClassName)}>
      {(titleAr || titleEn || descriptionAr || descriptionEn) && (
        <div className={cn('p-6 pb-3', headerClassName)}>
          {(titleAr || titleEn) && (
            <h3 className="text-lg font-semibold leading-none tracking-tight">
              <BilingualText
                ar={titleAr ?? ''}
                en={titleEn ?? ''}
                primaryLanguage={effectivePrimaryLanguage}
                showBothLanguages={showBothLanguages}
                separator={<br />}
              />
            </h3>
          )}
          {(descriptionAr || descriptionEn) && (
            <p className="text-sm text-muted-foreground mt-2">
              <BilingualText
                ar={descriptionAr ?? ''}
                en={descriptionEn ?? ''}
                primaryLanguage={effectivePrimaryLanguage}
                showBothLanguages={showBothLanguages}
                separator={<br />}
              />
            </p>
          )}
        </div>
      )}

      <div className={cn('p-6 pt-3', className)}>
        <BilingualTabs
          tabs={tabs}
          defaultTabId={defaultTabId}
          primaryLanguage={primaryLanguage}
          tabsListClassName={tabsListClassName}
          tabsTriggerClassName={tabsTriggerClassName}
          tabsContentClassName={tabsContentClassName}
          showBothLanguages={showBothLanguages}
          showBothContents={showBothContents}
          onChange={onChange}
          orientation={orientation}
        />
      </div>
    </div>
  );
}
