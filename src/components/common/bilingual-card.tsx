'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';
import { BilingualText, BilingualHeading, BilingualDescription } from './bilingual-text';
import { BilingualButton } from './bilingual-button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slot } from '@radix-ui/react-slot';

interface BilingualCardProps {
  titleAr: React.ReactNode;
  titleEn: React.ReactNode;
  descriptionAr?: React.ReactNode;
  descriptionEn?: React.ReactNode;
  contentAr?: React.ReactNode;
  contentEn?: React.ReactNode;
  footerAr?: React.ReactNode;
  footerEn?: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footerClassName?: string;
  showBothLanguages?: boolean;
  icon?: React.ReactNode;
  iconClassName?: string;
  asChild?: boolean;
  href?: string;
  onClick?: () => void;
  actions?: React.ReactNode;
}

/**
 * مكون بطاقة ثنائية اللغة
 * Bilingual Card Component
 */
export function BilingualCard({
  titleAr,
  titleEn,
  descriptionAr,
  descriptionEn,
  contentAr,
  contentEn,
  footerAr,
  footerEn,
  primaryLanguage,
  className = '',
  headerClassName = '',
  contentClassName = '',
  footerClassName = '',
  showBothLanguages = true,
  icon,
  iconClassName = '',
  asChild = false,
  href,
  onClick,
  actions,
}: Readonly<BilingualCardProps>) {
  const { language } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  // إنشاء محتوى البطاقة
  const cardContent = (
    <>
      <CardHeader className={cn('bilingual-card-header', headerClassName)}>
        {icon && (
          <div className={cn('bilingual-card-icon flex justify-center mb-2', iconClassName)}>
            {icon}
          </div>
        )}
        <CardTitle>
          <BilingualText
            ar={titleAr}
            en={titleEn}
            primaryLanguage={effectivePrimaryLanguage}
            showBothLanguages={showBothLanguages}
            separator={<br />}
          />
        </CardTitle>
        {(descriptionAr || descriptionEn) && (
          <CardDescription>
            <BilingualText
              ar={descriptionAr}
              en={descriptionEn}
              primaryLanguage={effectivePrimaryLanguage}
              showBothLanguages={showBothLanguages}
              separator={<br />}
            />
          </CardDescription>
        )}
      </CardHeader>
      
      {(contentAr || contentEn || (showBothLanguages ? false : true)) && (
        <CardContent className={cn('bilingual-card-content', contentClassName)}>
          {showBothLanguages ? (
            <>
              {contentAr && (
                <div className="bilingual-card-content-ar mb-2" dir="rtl">
                  {contentAr}
                </div>
              )}
              {contentEn && (
                <div className="bilingual-card-content-en" dir="ltr">
                  {contentEn}
                </div>
              )}
            </>
          ) : (
            <div className={cn('bilingual-card-content-single', {
              'text-right': effectivePrimaryLanguage === 'ar',
              'text-left': effectivePrimaryLanguage === 'en'
            })}>
              {effectivePrimaryLanguage === 'ar' ? contentAr : contentEn}
            </div>
          )}
        </CardContent>
      )}
      
      {(footerAr || footerEn || actions) && (
        <CardFooter className={cn('bilingual-card-footer', footerClassName)}>
          {actions ? (
            actions
          ) : (
            <BilingualText
              ar={footerAr}
              en={footerEn}
              primaryLanguage={effectivePrimaryLanguage}
              showBothLanguages={showBothLanguages}
              separator={<br />}
            />
          )}
        </CardFooter>
      )}
    </>
  );
  
  // إذا كان هناك رابط، نستخدم عنصر <a>
  if (href) {
    return (
      <Card 
        className={cn('bilingual-card transition-all hover:shadow-md', className)}
        asChild
      >
        <a href={href}>
          {cardContent}
        </a>
      </Card>
    );
  }
  
  // إذا كان هناك حدث نقر، نضيف خاصية onClick
  if (onClick) {
    return (
      <Card 
        className={cn('bilingual-card transition-all hover:shadow-md cursor-pointer', className)}
        onClick={onClick}
      >
        {cardContent}
      </Card>
    );
  }
  
  // إذا كان asChild=true، نستخدم Slot
  if (asChild) {
    return (
      <Slot className={cn('bilingual-card', className)}>
        <Card>
          {cardContent}
        </Card>
      </Slot>
    );
  }
  
  // الحالة الافتراضية
  return (
    <Card className={cn('bilingual-card', className)}>
      {cardContent}
    </Card>
  );
}

/**
 * مكون بطاقة إحصائية ثنائية اللغة
 * Bilingual Stat Card Component
 */
export function BilingualStatCard({
  titleAr,
  titleEn,
  valueAr,
  valueEn,
  changeAr,
  changeEn,
  changeType = 'neutral',
  icon,
  primaryLanguage,
  className = '',
  valueClassName = '',
  changeClassName = '',
  showBothLanguages = false,
}: Readonly<{
  titleAr: React.ReactNode;
  titleEn: React.ReactNode;
  valueAr: React.ReactNode;
  valueEn: React.ReactNode;
  changeAr?: React.ReactNode;
  changeEn?: React.ReactNode;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon?: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  className?: string;
  valueClassName?: string;
  changeClassName?: string;
  showBothLanguages?: boolean;
}>) {
  const { language } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  // تحديد لون التغيير
  const changeColor = {
    positive: 'text-green-600',
    negative: 'text-red-600',
    neutral: 'text-gray-600',
  }[changeType];
  
  return (
    <BilingualCard
      titleAr={titleAr}
      titleEn={titleEn}
      primaryLanguage={effectivePrimaryLanguage}
      showBothLanguages={showBothLanguages}
      icon={icon}
      className={cn('bilingual-stat-card', className)}
      contentAr={
        <div className="space-y-1">
          <div className={cn('text-2xl font-bold', valueClassName)}>{valueAr}</div>
          {changeAr && (
            <div className={cn('text-sm', changeColor, changeClassName)}>{changeAr}</div>
          )}
        </div>
      }
      contentEn={
        <div className="space-y-1">
          <div className={cn('text-2xl font-bold', valueClassName)}>{valueEn}</div>
          {changeEn && (
            <div className={cn('text-sm', changeColor, changeClassName)}>{changeEn}</div>
          )}
        </div>
      }
    />
  );
}

/**
 * مكون بطاقة إجراء ثنائية اللغة
 * Bilingual Action Card Component
 */
export function BilingualActionCard({
  titleAr,
  titleEn,
  descriptionAr,
  descriptionEn,
  buttonTextAr,
  buttonTextEn,
  onClick,
  href,
  icon,
  buttonVariant = 'default',
  primaryLanguage,
  className = '',
  buttonClassName = '',
  showBothLanguages = true,
}: Readonly<{
  titleAr: React.ReactNode;
  titleEn: React.ReactNode;
  descriptionAr?: React.ReactNode;
  descriptionEn?: React.ReactNode;
  buttonTextAr: React.ReactNode;
  buttonTextEn: React.ReactNode;
  onClick?: () => void;
  href?: string;
  icon?: React.ReactNode;
  buttonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  primaryLanguage?: SupportedLanguage;
  className?: string;
  buttonClassName?: string;
  showBothLanguages?: boolean;
}>) {
  return (
    <BilingualCard
      titleAr={titleAr}
      titleEn={titleEn}
      descriptionAr={descriptionAr}
      descriptionEn={descriptionEn}
      primaryLanguage={primaryLanguage}
      showBothLanguages={showBothLanguages}
      icon={icon}
      className={cn('bilingual-action-card', className)}
      actions={
        <div className="w-full flex justify-end">
          <BilingualButton
            textAr={buttonTextAr}
            textEn={buttonTextEn}
            onClick={onClick}
            variant={buttonVariant}
            className={buttonClassName}
            asChild={!!href}
          >
            {href && <a href={href}></a>}
          </BilingualButton>
        </div>
      }
    />
  );
}
