'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';
import { BilingualText } from './bilingual-text';
import { Check, X } from 'lucide-react';

interface BilingualListItemProps {
  textAr: React.ReactNode;
  textEn: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  icon?: React.ReactNode;
  className?: string;
  showBothLanguages?: boolean;
}

/**
 * مكون عنصر قائمة ثنائي اللغة
 * Bilingual List Item Component
 */
export function BilingualListItem({
  textAr,
  textEn,
  primaryLanguage,
  icon,
  className = '',
  showBothLanguages = false,
}: Readonly<BilingualListItemProps>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  return (
    <li className={cn('bilingual-list-item flex items-start', className)}>
      {icon && (
        <span className="bilingual-list-item-icon flex-shrink-0 mr-2">
          {icon}
        </span>
      )}
      <span className="bilingual-list-item-text">
        <BilingualText
          ar={textAr}
          en={textEn}
          primaryLanguage={effectivePrimaryLanguage}
          showBothLanguages={showBothLanguages}
          separator={showBothLanguages ? <br /> : ' | '}
        />
      </span>
    </li>
  );
}

interface BilingualListProps {
  items: {
    textAr: React.ReactNode;
    textEn: React.ReactNode;
    icon?: React.ReactNode;
  }[];
  primaryLanguage?: SupportedLanguage;
  className?: string;
  itemClassName?: string;
  showBothLanguages?: boolean;
  type?: 'ul' | 'ol';
  iconType?: 'none' | 'bullet' | 'check' | 'custom';
  customIcon?: React.ReactNode;
}

/**
 * مكون قائمة ثنائية اللغة
 * Bilingual List Component
 */
export function BilingualList({
  items,
  primaryLanguage,
  className = '',
  itemClassName = '',
  showBothLanguages = false,
  type = 'ul',
  iconType = 'none',
  customIcon,
}: Readonly<BilingualListProps>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  // تحديد أيقونة العنصر
  const getItemIcon = (index: number) => {
    switch (iconType) {
      case 'bullet':
        return <span className="h-2 w-2 rounded-full bg-current mt-2" />;
      case 'check':
        return <Check className="h-4 w-4 text-green-500 mt-1" />;
      case 'custom':
        return customIcon;
      default:
        return undefined;
    }
  };

  // إنشاء عناصر القائمة
  const listItems = items.map((item, index) => (
    <BilingualListItem
      key={index}
      textAr={item.textAr}
      textEn={item.textEn}
      primaryLanguage={effectivePrimaryLanguage}
      icon={item.icon || getItemIcon(index)}
      className={itemClassName}
      showBothLanguages={showBothLanguages}
    />
  ));

  // عرض القائمة حسب النوع
  if (type === 'ol') {
    return (
      <ol className={cn('bilingual-list space-y-2', className)}>
        {listItems}
      </ol>
    );
  }

  return (
    <ul className={cn('bilingual-list space-y-2', className)}>
      {listItems}
    </ul>
  );
}

interface BilingualFeatureListProps {
  features: {
    titleAr: React.ReactNode;
    titleEn: React.ReactNode;
    descriptionAr?: React.ReactNode;
    descriptionEn?: React.ReactNode;
    included?: boolean;
  }[];
  primaryLanguage?: SupportedLanguage;
  className?: string;
  itemClassName?: string;
  showBothLanguages?: boolean;
}

/**
 * مكون قائمة ميزات ثنائية اللغة
 * Bilingual Feature List Component
 */
export function BilingualFeatureList({
  features,
  primaryLanguage,
  className = '',
  itemClassName = '',
  showBothLanguages = false,
}: Readonly<BilingualFeatureListProps>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  return (
    <ul className={cn('bilingual-feature-list space-y-4', className)}>
      {features.map((feature, index) => (
        <li
          key={index}
          className={cn('bilingual-feature-item flex items-start',
            feature.included === false ? 'opacity-60' : '',
            itemClassName
          )}
        >
          <span className="bilingual-feature-icon flex-shrink-0 mr-3 mt-1">
            {feature.included === false ? (
              <X className="h-5 w-5 text-red-500" />
            ) : (
              <Check className="h-5 w-5 text-green-500" />
            )}
          </span>
          <div className="bilingual-feature-content">
            <div className="bilingual-feature-title font-medium">
              <BilingualText
                ar={feature.titleAr}
                en={feature.titleEn}
                primaryLanguage={effectivePrimaryLanguage}
                showBothLanguages={showBothLanguages}
                separator={showBothLanguages ? <br /> : ' | '}
              />
            </div>
            {(feature.descriptionAr || feature.descriptionEn) && (
              <div className="bilingual-feature-description text-sm text-muted-foreground mt-1">
                <BilingualText
                  ar={feature.descriptionAr}
                  en={feature.descriptionEn}
                  primaryLanguage={effectivePrimaryLanguage}
                  showBothLanguages={showBothLanguages}
                  separator={showBothLanguages ? <br /> : ' | '}
                />
              </div>
            )}
          </div>
        </li>
      ))}
    </ul>
  );
}
