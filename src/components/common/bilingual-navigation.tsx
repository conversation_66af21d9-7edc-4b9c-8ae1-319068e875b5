'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';
import { BilingualText } from './bilingual-text';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, ChevronDown } from 'lucide-react';

interface BilingualBreadcrumbItemProps {
  labelAr: React.ReactNode;
  labelEn: React.ReactNode;
  href?: string;
  isActive?: boolean;
  primaryLanguage?: SupportedLanguage;
  className?: string;
  showBothLanguages?: boolean;
  icon?: React.ReactNode;
}

/**
 * مكون عنصر مسار التنقل ثنائي اللغة
 * Bilingual Breadcrumb Item Component
 */
export function BilingualBreadcrumbItem({
  labelAr,
  labelEn,
  href,
  isActive = false,
  primaryLanguage,
  className = '',
  showBothLanguages = false,
  icon,
}: Readonly<BilingualBreadcrumbItemProps>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  // إنشاء محتوى العنصر
  const itemContent = (
    <>
      {icon && (
        <span className="breadcrumb-icon mr-1">
          {icon}
        </span>
      )}
      <BilingualText
        ar={labelAr}
        en={labelEn}
        primaryLanguage={effectivePrimaryLanguage}
        showBothLanguages={showBothLanguages}
        className={cn(
          'transition-colors',
          isActive ? 'font-medium text-foreground' : 'text-muted-foreground hover:text-foreground'
        )}
      />
    </>
  );

  return (
    <li className={cn('bilingual-breadcrumb-item flex items-center', className)}>
      {href && !isActive ? (
        <Link href={href} className="flex items-center">
          {itemContent}
        </Link>
      ) : (
        <span className="flex items-center">
          {itemContent}
        </span>
      )}
    </li>
  );
}

interface BilingualBreadcrumbProps {
  items: {
    labelAr: React.ReactNode;
    labelEn: React.ReactNode;
    href?: string;
    icon?: React.ReactNode;
  }[];
  primaryLanguage?: SupportedLanguage;
  className?: string;
  itemClassName?: string;
  separatorClassName?: string;
  showBothLanguages?: boolean;
  separator?: React.ReactNode;
}

/**
 * مكون مسار التنقل ثنائي اللغة
 * Bilingual Breadcrumb Component
 */
export function BilingualBreadcrumb({
  items,
  primaryLanguage,
  className = '',
  itemClassName = '',
  separatorClassName = '',
  showBothLanguages = false,
  separator,
}: Readonly<BilingualBreadcrumbProps>) {
  const { language, isRTL } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  // تحديد الفاصل بين العناصر
  const defaultSeparator = isRTL ? (
    <ChevronLeft className="h-4 w-4 mx-2 text-muted-foreground" />
  ) : (
    <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
  );

  const breadcrumbSeparator = separator || defaultSeparator;

  return (
    <nav className={cn('bilingual-breadcrumb', className)} aria-label="Breadcrumb">
      <ol className="flex items-center flex-wrap">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <BilingualBreadcrumbItem
              labelAr={item.labelAr}
              labelEn={item.labelEn}
              href={item.href}
              isActive={index === items.length - 1}
              primaryLanguage={effectivePrimaryLanguage}
              className={itemClassName}
              showBothLanguages={showBothLanguages}
              icon={item.icon}
            />
            {index < items.length - 1 && (
              <li className={cn('bilingual-breadcrumb-separator', separatorClassName)}>
                {breadcrumbSeparator}
              </li>
            )}
          </React.Fragment>
        ))}
      </ol>
    </nav>
  );
}

interface BilingualNavItemProps {
  labelAr: React.ReactNode;
  labelEn: React.ReactNode;
  href: string;
  icon?: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  className?: string;
  activeClassName?: string;
  showBothLanguages?: boolean;
  exact?: boolean;
  onClick?: () => void;
}

/**
 * مكون عنصر قائمة التنقل ثنائي اللغة
 * Bilingual Navigation Item Component
 */
export function BilingualNavItem({
  labelAr,
  labelEn,
  href,
  icon,
  primaryLanguage,
  className = '',
  activeClassName = '',
  showBothLanguages = false,
  exact = false,
  onClick,
}: Readonly<BilingualNavItemProps>) {
  const { language } = useLanguage();
  const pathname = usePathname();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  // تحديد ما إذا كان العنصر نشطًا
  const isActive = exact
    ? pathname === href
    : pathname === href || pathname.startsWith(`${href}/`);

  return (
    <li className={cn('bilingual-nav-item', className)}>
      <Link
        href={href}
        className={cn(
          'flex items-center px-3 py-2 rounded-md transition-colors',
          isActive
            ? cn('bg-primary/10 text-primary', activeClassName)
            : 'text-muted-foreground hover:text-foreground hover:bg-accent'
        )}
        onClick={onClick}
      >
        {icon && (
          <span className="nav-icon mr-2">
            {icon}
          </span>
        )}
        <BilingualText
          ar={labelAr}
          en={labelEn}
          primaryLanguage={effectivePrimaryLanguage}
          showBothLanguages={showBothLanguages}
        />
      </Link>
    </li>
  );
}

interface BilingualNavGroupProps {
  labelAr: React.ReactNode;
  labelEn: React.ReactNode;
  icon?: React.ReactNode;
  items: {
    labelAr: React.ReactNode;
    labelEn: React.ReactNode;
    href: string;
    icon?: React.ReactNode;
  }[];
  primaryLanguage?: SupportedLanguage;
  className?: string;
  itemClassName?: string;
  showBothLanguages?: boolean;
  defaultOpen?: boolean;
}

/**
 * مكون مجموعة قائمة التنقل ثنائية اللغة
 * Bilingual Navigation Group Component
 */
export function BilingualNavGroup({
  labelAr,
  labelEn,
  icon,
  items,
  primaryLanguage,
  className = '',
  itemClassName = '',
  showBothLanguages = false,
  defaultOpen = false,
}: Readonly<BilingualNavGroupProps>) {
  const { language } = useLanguage();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = React.useState(defaultOpen);

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  // تحديد ما إذا كان أي عنصر في المجموعة نشطًا
  const isAnyItemActive = items.some(item =>
    pathname === item.href || pathname.startsWith(`${item.href}/`)
  );

  // فتح المجموعة تلقائيًا إذا كان أي عنصر نشطًا
  React.useEffect(() => {
    if (isAnyItemActive) {
      setIsOpen(true);
    }
  }, [isAnyItemActive]);

  return (
    <li className={cn('bilingual-nav-group', className)}>
      <button
        className={cn(
          'flex items-center justify-between w-full px-3 py-2 rounded-md transition-colors',
          isAnyItemActive
            ? 'text-primary'
            : 'text-muted-foreground hover:text-foreground hover:bg-accent'
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="flex items-center">
          {icon && (
            <span className="nav-group-icon mr-2">
              {icon}
            </span>
          )}
          <BilingualText
            ar={labelAr}
            en={labelEn}
            primaryLanguage={effectivePrimaryLanguage}
            showBothLanguages={showBothLanguages}
          />
        </span>
        <ChevronDown
          className={cn(
            'h-4 w-4 transition-transform',
            isOpen ? 'transform rotate-180' : ''
          )}
        />
      </button>

      {isOpen && (
        <ul className="bilingual-nav-group-items mt-1 ml-6 space-y-1">
          {items.map((item, index) => (
            <BilingualNavItem
              key={index}
              labelAr={item.labelAr}
              labelEn={item.labelEn}
              href={item.href}
              icon={item.icon}
              primaryLanguage={effectivePrimaryLanguage}
              className={itemClassName}
              showBothLanguages={showBothLanguages}
            />
          ))}
        </ul>
      )}
    </li>
  );
}

interface BilingualNavProps {
  items: ({
    type: 'item';
    labelAr: React.ReactNode;
    labelEn: React.ReactNode;
    href: string;
    icon?: React.ReactNode;
  } | {
    type: 'group';
    labelAr: React.ReactNode;
    labelEn: React.ReactNode;
    icon?: React.ReactNode;
    items: {
      labelAr: React.ReactNode;
      labelEn: React.ReactNode;
      href: string;
      icon?: React.ReactNode;
    }[];
  })[];
  primaryLanguage?: SupportedLanguage;
  className?: string;
  itemClassName?: string;
  groupClassName?: string;
  showBothLanguages?: boolean;
}

/**
 * مكون قائمة التنقل ثنائية اللغة
 * Bilingual Navigation Component
 */
export function BilingualNav({
  items,
  primaryLanguage,
  className = '',
  itemClassName = '',
  groupClassName = '',
  showBothLanguages = false,
}: Readonly<BilingualNavProps>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  return (
    <nav className={cn('bilingual-nav', className)}>
      <ul className="space-y-1">
        {items.map((item, index) => {
          if (item.type === 'group') {
            return (
              <BilingualNavGroup
                key={index}
                labelAr={item.labelAr}
                labelEn={item.labelEn}
                icon={item.icon}
                items={item.items}
                primaryLanguage={effectivePrimaryLanguage}
                className={groupClassName}
                itemClassName={itemClassName}
                showBothLanguages={showBothLanguages}
              />
            );
          }

          return (
            <BilingualNavItem
              key={index}
              labelAr={item.labelAr}
              labelEn={item.labelEn}
              href={item.href}
              icon={item.icon}
              primaryLanguage={effectivePrimaryLanguage}
              className={itemClassName}
              showBothLanguages={showBothLanguages}
            />
          );
        })}
      </ul>
    </nav>
  );
}
