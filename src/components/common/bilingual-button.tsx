'use client';

import React from 'react';
import { Button } from '@/components/ui/enhanced-button';
import { cn } from '@/lib/utils';
import { BilingualText } from './bilingual-text';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';
import { Slot } from '@radix-ui/react-slot';

interface BilingualButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  textAr: React.ReactNode;
  textEn: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  separator?: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showBothLanguages?: boolean;
  asChild?: boolean;
  loading?: boolean;
  loadingTextAr?: React.ReactNode;
  loadingTextEn?: React.ReactNode;
  icon?: React.ReactNode;
  iconPosition?: 'start' | 'end';
}

/**
 * مكون زر ثنائي اللغة
 * Bilingual Button Component
 */
export function BilingualButton({
  textAr,
  textEn,
  primaryLanguage,
  separator = ' | ',
  variant = 'default',
  size = 'default',
  showBothLanguages = false,
  asChild = false,
  loading = false,
  loadingTextAr,
  loadingTextEn,
  icon,
  iconPosition = 'start',
  className,
  ...props
}: Readonly<BilingualButtonProps>) {
  const { language } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  // تحديد النص المعروض أثناء التحميل
  const displayLoadingTextAr = loadingTextAr ?? 'جاري التنفيذ...';
  const displayLoadingTextEn = loadingTextEn ?? 'Loading...';
  
  // تحديد النص المعروض
  const displayTextAr = loading ? displayLoadingTextAr : textAr;
  const displayTextEn = loading ? displayLoadingTextEn : textEn;
  
  // إنشاء محتوى الزر
  const buttonContent = (
    <>
      {icon && iconPosition === 'start' && (
        <span className={cn('mr-2', { 'ml-2 mr-0': effectivePrimaryLanguage === 'ar' })}>
          {icon}
        </span>
      )}
      
      <BilingualText
        ar={displayTextAr}
        en={displayTextEn}
        primaryLanguage={effectivePrimaryLanguage}
        separator={separator}
        showBothLanguages={showBothLanguages}
      />
      
      {icon && iconPosition === 'end' && (
        <span className={cn('ml-2', { 'mr-2 ml-0': effectivePrimaryLanguage === 'ar' })}>
          {icon}
        </span>
      )}
    </>
  );
  
  const Comp = asChild ? Slot : Button;
  
  return (
    <Comp
      variant={variant}
      size={size}
      className={cn('bilingual-button', className)}
      disabled={loading || props.disabled}
      {...props}
    >
      {buttonContent}
    </Comp>
  );
}

/**
 * مكون زر رابط ثنائي اللغة
 * Bilingual Link Button Component
 */
export function BilingualLinkButton({
  textAr,
  textEn,
  href,
  primaryLanguage,
  separator = ' | ',
  variant = 'default',
  size = 'default',
  showBothLanguages = false,
  icon,
  iconPosition = 'start',
  className,
  ...props
}: Readonly<Omit<BilingualButtonProps, 'asChild' | 'loading' | 'loadingTextAr' | 'loadingTextEn'> & {
  href: string;
}>) {
  return (
    <BilingualButton
      textAr={textAr}
      textEn={textEn}
      primaryLanguage={primaryLanguage}
      separator={separator}
      variant={variant}
      size={size}
      showBothLanguages={showBothLanguages}
      icon={icon}
      iconPosition={iconPosition}
      className={className}
      asChild
      {...props}
    >
      <a href={href}></a>
    </BilingualButton>
  );
}
