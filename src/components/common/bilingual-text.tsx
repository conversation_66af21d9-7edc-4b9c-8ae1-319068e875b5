'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';

interface BilingualTextProps {
  ar: React.ReactNode;
  en: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  separator?: React.ReactNode;
  className?: string;
  arClassName?: string;
  enClassName?: string;
  separatorClassName?: string;
  showBothLanguages?: boolean;
  reverseOnRTL?: boolean;
}

/**
 * مكون لعرض النص بشكل ثنائي اللغة (العربية والإنجليزية)
 * Bilingual Text Component for displaying text in both Arabic and English
 */
export function BilingualText({
  ar,
  en,
  primaryLanguage,
  separator = ' | ',
  className = '',
  arClassName = '',
  enClassName = '',
  separatorClassName = '',
  showBothLanguages = true,
  reverseOnRTL = false,
}: Readonly<BilingualTextProps>) {
  const { language, isRTL } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  // إذا كان عرض لغة واحدة فقط
  if (!showBothLanguages) {
    return (
      <span className={cn('bilingual-text', className)}>
        {effectivePrimaryLanguage === 'ar' ? (
          <span className={cn('text-ar', arClassName)}>{ar}</span>
        ) : (
          <span className={cn('text-en', enClassName)}>{en}</span>
        )}
      </span>
    );
  }
  
  // تحديد ترتيب اللغات
  const shouldReverse = reverseOnRTL && isRTL;
  
  // عرض النص بكلتا اللغتين
  return (
    <span className={cn('bilingual-text', className)}>
      {(effectivePrimaryLanguage === 'ar' && !shouldReverse) || (effectivePrimaryLanguage === 'en' && shouldReverse) ? (
        <>
          <span className={cn('text-ar', arClassName)}>{ar}</span>
          <span className={cn('text-separator', separatorClassName)}>{separator}</span>
          <span className={cn('text-en', enClassName)}>{en}</span>
        </>
      ) : (
        <>
          <span className={cn('text-en', enClassName)}>{en}</span>
          <span className={cn('text-separator', separatorClassName)}>{separator}</span>
          <span className={cn('text-ar', arClassName)}>{ar}</span>
        </>
      )}
    </span>
  );
}

/**
 * مكون لعرض العنوان بشكل ثنائي اللغة
 * Bilingual Heading Component
 */
export function BilingualHeading({
  ar,
  en,
  as: Component = 'h2',
  primaryLanguage,
  separator = <br />,
  className = '',
  arClassName = '',
  enClassName = '',
  separatorClassName = '',
  showBothLanguages = true,
  reverseOnRTL = false,
}: Readonly<BilingualTextProps & {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | React.ComponentType<any>;
}>) {
  const { language, isRTL } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  // إذا كان عرض لغة واحدة فقط
  if (!showBothLanguages) {
    return (
      <Component className={cn('bilingual-heading', className)}>
        {effectivePrimaryLanguage === 'ar' ? (
          <span className={cn('heading-ar', arClassName)}>{ar}</span>
        ) : (
          <span className={cn('heading-en', enClassName)}>{en}</span>
        )}
      </Component>
    );
  }
  
  // تحديد ترتيب اللغات
  const shouldReverse = reverseOnRTL && isRTL;
  
  // عرض العنوان بكلتا اللغتين
  return (
    <Component className={cn('bilingual-heading', className)}>
      {(effectivePrimaryLanguage === 'ar' && !shouldReverse) || (effectivePrimaryLanguage === 'en' && shouldReverse) ? (
        <>
          <span className={cn('heading-ar', arClassName)}>{ar}</span>
          {separator && <span className={cn('heading-separator', separatorClassName)}>{separator}</span>}
          <span className={cn('heading-en', enClassName)}>{en}</span>
        </>
      ) : (
        <>
          <span className={cn('heading-en', enClassName)}>{en}</span>
          {separator && <span className={cn('heading-separator', separatorClassName)}>{separator}</span>}
          <span className={cn('heading-ar', arClassName)}>{ar}</span>
        </>
      )}
    </Component>
  );
}

/**
 * مكون لعرض الوصف بشكل ثنائي اللغة
 * Bilingual Description Component
 */
export function BilingualDescription({
  ar,
  en,
  primaryLanguage,
  separator = <br />,
  className = '',
  arClassName = '',
  enClassName = '',
  separatorClassName = '',
  showBothLanguages = true,
  reverseOnRTL = false,
}: Readonly<BilingualTextProps>) {
  return (
    <p className={cn('bilingual-description text-muted-foreground', className)}>
      <BilingualText
        ar={ar}
        en={en}
        primaryLanguage={primaryLanguage}
        separator={separator}
        arClassName={cn('description-ar', arClassName)}
        enClassName={cn('description-en', enClassName)}
        separatorClassName={separatorClassName}
        showBothLanguages={showBothLanguages}
        reverseOnRTL={reverseOnRTL}
      />
    </p>
  );
}

/**
 * مكون لعرض التسمية بشكل ثنائي اللغة
 * Bilingual Label Component
 */
export function BilingualLabel({
  ar,
  en,
  htmlFor,
  primaryLanguage,
  separator = ' | ',
  className = '',
  arClassName = '',
  enClassName = '',
  separatorClassName = '',
  showBothLanguages = true,
  reverseOnRTL = false,
  required = false,
}: Readonly<BilingualTextProps & {
  htmlFor?: string;
  required?: boolean;
}>) {
  return (
    <label 
      htmlFor={htmlFor}
      className={cn('bilingual-label block text-sm font-medium', className)}
    >
      <BilingualText
        ar={<>{ar}{required && <span className="text-red-500 mr-1">*</span>}</>}
        en={<>{en}{required && <span className="text-red-500 ml-1">*</span>}</>}
        primaryLanguage={primaryLanguage}
        separator={separator}
        arClassName={cn('label-ar', arClassName)}
        enClassName={cn('label-en', enClassName)}
        separatorClassName={separatorClassName}
        showBothLanguages={showBothLanguages}
        reverseOnRTL={reverseOnRTL}
      />
    </label>
  );
}
