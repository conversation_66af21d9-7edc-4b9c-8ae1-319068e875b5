'use client';

import { useEffect } from 'react';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage, DEFAULT_LANGUAGE } from '@/config/languages';

interface LanguageDetectorProps {
  defaultLanguage?: SupportedLanguage;
  persistLanguage?: boolean;
  storageKey?: string;
}

/**
 * مكون للكشف عن لغة المتصفح وتعيين اللغة الافتراضية للتطبيق
 * Component to detect browser language and set default application language
 */
export function LanguageDetector({
  defaultLanguage = DEFAULT_LANGUAGE,
  persistLanguage = true,
  storageKey = 'language',
}: Readonly<LanguageDetectorProps>) {
  const { setLanguage } = useLanguage();
  
  useEffect(() => {
    // محاولة استرداد اللغة المحفوظة
    if (persistLanguage && typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem(storageKey) as SupportedLanguage | null;
      
      if (savedLanguage) {
        // استخدام اللغة المحفوظة
        setLanguage(savedLanguage);
        return;
      }
    }
    
    // محاولة الكشف عن لغة المتصفح
    if (typeof window !== 'undefined' && navigator.language) {
      const browserLanguage = navigator.language.split('-')[0].toLowerCase();
      
      // التحقق مما إذا كانت لغة المتصفح مدعومة
      if (browserLanguage === 'ar') {
        setLanguage('ar');
        return;
      } else if (browserLanguage === 'en') {
        setLanguage('en');
        return;
      }
    }
    
    // استخدام اللغة الافتراضية إذا لم يتم العثور على لغة محفوظة أو لغة متصفح مدعومة
    setLanguage(defaultLanguage);
  }, [defaultLanguage, persistLanguage, storageKey, setLanguage]);
  
  // هذا المكون لا يعرض أي شيء
  return null;
}
