'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';
import { BilingualText } from './bilingual-text';

interface BilingualPageTitleProps {
  titleAr: React.ReactNode;
  titleEn: React.ReactNode;
  descriptionAr?: React.ReactNode;
  descriptionEn?: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  iconClassName?: string;
  actionsClassName?: string;
  showBothLanguages?: boolean;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | React.ComponentType<any>;
}

/**
 * مكون عنوان الصفحة ثنائي اللغة
 * Bilingual Page Title Component
 * 
 * مكون موحد لعناوين الصفحات يدعم العربية والإنجليزية مع وصف اختياري وأيقونة وإجراءات
 * A unified component for page titles that supports Arabic and English with optional description, icon, and actions
 */
export function BilingualPageTitle({
  titleAr,
  titleEn,
  descriptionAr,
  descriptionEn,
  primaryLanguage,
  icon,
  actions,
  className = '',
  titleClassName = '',
  descriptionClassName = '',
  iconClassName = '',
  actionsClassName = '',
  showBothLanguages = true,
  as: Component = 'h1',
}: Readonly<BilingualPageTitleProps>) {
  const { language } = useLanguage();
  
  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;
  
  return (
    <div className={cn('bilingual-page-title flex flex-col space-y-2 mb-6', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {icon && (
            <div className={cn('mr-2 flex-shrink-0', iconClassName)}>
              {icon}
            </div>
          )}
          <Component className={cn('text-2xl font-bold', titleClassName)}>
            <BilingualText
              ar={titleAr}
              en={titleEn}
              primaryLanguage={effectivePrimaryLanguage}
              showBothLanguages={showBothLanguages}
              separator=" | "
              arClassName="font-bold"
              enClassName={effectivePrimaryLanguage === 'ar' ? 'text-muted-foreground text-xl' : 'font-bold'}
            />
          </Component>
        </div>
        
        {actions && (
          <div className={cn('flex items-center space-x-2 rtl:space-x-reverse', actionsClassName)}>
            {actions}
          </div>
        )}
      </div>
      
      {(descriptionAr || descriptionEn) && (
        <div className={cn('text-muted-foreground', descriptionClassName)}>
          <BilingualText
            ar={descriptionAr}
            en={descriptionEn}
            primaryLanguage={effectivePrimaryLanguage}
            showBothLanguages={showBothLanguages}
            separator=" | "
            arClassName={effectivePrimaryLanguage === 'en' ? 'text-sm' : ''}
            enClassName={effectivePrimaryLanguage === 'ar' ? 'text-sm' : ''}
          />
        </div>
      )}
    </div>
  );
}

/**
 * مكون عنوان القسم ثنائي اللغة
 * Bilingual Section Title Component
 * 
 * مكون موحد لعناوين الأقسام داخل الصفحة
 * A unified component for section titles within a page
 */
export function BilingualSectionTitle({
  titleAr,
  titleEn,
  descriptionAr,
  descriptionEn,
  primaryLanguage,
  icon,
  actions,
  className = '',
  titleClassName = '',
  descriptionClassName = '',
  iconClassName = '',
  actionsClassName = '',
  showBothLanguages = true,
}: Readonly<Omit<BilingualPageTitleProps, 'as'>>) {
  return (
    <BilingualPageTitle
      titleAr={titleAr}
      titleEn={titleEn}
      descriptionAr={descriptionAr}
      descriptionEn={descriptionEn}
      primaryLanguage={primaryLanguage}
      icon={icon}
      actions={actions}
      className={cn('mb-4', className)}
      titleClassName={cn('text-xl', titleClassName)}
      descriptionClassName={descriptionClassName}
      iconClassName={iconClassName}
      actionsClassName={actionsClassName}
      showBothLanguages={showBothLanguages}
      as="h2"
    />
  );
}

/**
 * مكون عنوان البطاقة ثنائي اللغة
 * Bilingual Card Title Component
 * 
 * مكون موحد لعناوين البطاقات
 * A unified component for card titles
 */
export function BilingualCardTitle({
  titleAr,
  titleEn,
  descriptionAr,
  descriptionEn,
  primaryLanguage,
  icon,
  actions,
  className = '',
  titleClassName = '',
  descriptionClassName = '',
  iconClassName = '',
  actionsClassName = '',
  showBothLanguages = true,
}: Readonly<Omit<BilingualPageTitleProps, 'as'>>) {
  return (
    <BilingualPageTitle
      titleAr={titleAr}
      titleEn={titleEn}
      descriptionAr={descriptionAr}
      descriptionEn={descriptionEn}
      primaryLanguage={primaryLanguage}
      icon={icon}
      actions={actions}
      className={cn('mb-2', className)}
      titleClassName={cn('text-lg', titleClassName)}
      descriptionClassName={cn('text-sm', descriptionClassName)}
      iconClassName={iconClassName}
      actionsClassName={actionsClassName}
      showBothLanguages={showBothLanguages}
      as="h3"
    />
  );
}
