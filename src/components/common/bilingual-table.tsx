'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import { SupportedLanguage } from '@/config/languages';
import { BilingualText } from './bilingual-text';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface BilingualColumn<T extends object> {
  accessorKey: keyof T;
  headerAr: React.ReactNode;
  headerEn: React.ReactNode;
  cell?: (item: T) => React.ReactNode;
  footerAr?: React.ReactNode;
  footerEn?: React.ReactNode;
  className?: string;
}

interface BilingualTableProps<T extends object> {
  data: T[];
  columns: BilingualColumn<T>[];
  captionAr?: React.ReactNode;
  captionEn?: React.ReactNode;
  primaryLanguage?: SupportedLanguage;
  className?: string;
  tableClassName?: string;
  headerClassName?: string;
  rowClassName?: (item: T, index: number) => string | undefined;
  cellClassName?: string;
  footerClassName?: string;
  emptyStateAr?: React.ReactNode;
  emptyStateEn?: React.ReactNode;
  isLoading?: boolean;
  loadingStateAr?: React.ReactNode;
  loadingStateEn?: React.ReactNode;
  pagination?: {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    onPageChange: (page: number) => void;
  };
  paginationLabelsAr?: {
    previous: string;
    next: string;
    first: string;
    last: string;
    page: string;
    of: string;
  };
  paginationLabelsEn?: {
    previous: string;
    next: string;
    first: string;
    last: string;
    page: string;
    of: string;
  };
}

/**
 * مكون جدول ثنائي اللغة
 * Bilingual Table Component
 */
export function BilingualTable<T extends object>({
  data,
  columns,
  captionAr,
  captionEn,
  primaryLanguage,
  className = '',
  tableClassName = '',
  headerClassName = '',
  rowClassName,
  cellClassName = '',
  footerClassName = '',
  emptyStateAr = 'لا توجد بيانات',
  emptyStateEn = 'No data available',
  isLoading = false,
  loadingStateAr = 'جاري التحميل...',
  loadingStateEn = 'Loading...',
  pagination,
  paginationLabelsAr = {
    previous: 'السابق',
    next: 'التالي',
    first: 'الأول',
    last: 'الأخير',
    page: 'صفحة',
    of: 'من'
  },
  paginationLabelsEn = {
    previous: 'Previous',
    next: 'Next',
    first: 'First',
    last: 'Last',
    page: 'Page',
    of: 'of'
  }
}: Readonly<BilingualTableProps<T>>) {
  const { language } = useLanguage();

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  // عرض حالة التحميل
  if (isLoading) {
    return (
      <div className={cn('bilingual-table-loading flex justify-center items-center p-8', className)}>
        <BilingualText
          ar={loadingStateAr}
          en={loadingStateEn}
          primaryLanguage={effectivePrimaryLanguage}
          className="text-muted-foreground"
        />
      </div>
    );
  }

  // عرض حالة عدم وجود بيانات
  if (!data || data.length === 0) {
    return (
      <div className={cn('bilingual-table-empty flex justify-center items-center p-8 border rounded-lg', className)}>
        <BilingualText
          ar={emptyStateAr}
          en={emptyStateEn}
          primaryLanguage={effectivePrimaryLanguage}
          className="text-muted-foreground"
        />
      </div>
    );
  }

  // استخدام مكون صفحات الجدول
  const renderPagination = () => {
    if (!pagination) return null;

    const { pageIndex, pageCount, onPageChange } = pagination;

    return (
      <div className="flex items-center justify-between px-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          <BilingualText
            ar={`${paginationLabelsAr.page} ${pageIndex + 1} ${paginationLabelsAr.of} ${pageCount}`}
            en={`${paginationLabelsEn.page} ${pageIndex + 1} ${paginationLabelsEn.of} ${pageCount}`}
            primaryLanguage={effectivePrimaryLanguage}
          />
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(0)}
            disabled={pageIndex === 0}
            className="hidden sm:flex"
          >
            {effectivePrimaryLanguage === 'ar' ? <ChevronsRight className="h-4 w-4" /> : <ChevronsLeft className="h-4 w-4" />}
            <span className="sr-only">
              {effectivePrimaryLanguage === 'ar' ? paginationLabelsAr.first : paginationLabelsEn.first}
            </span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pageIndex - 1)}
            disabled={pageIndex === 0}
          >
            {effectivePrimaryLanguage === 'ar' ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            <span className="sr-only">
              {effectivePrimaryLanguage === 'ar' ? paginationLabelsAr.previous : paginationLabelsEn.previous}
            </span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pageIndex + 1)}
            disabled={pageIndex === pageCount - 1}
          >
            {effectivePrimaryLanguage === 'ar' ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            <span className="sr-only">
              {effectivePrimaryLanguage === 'ar' ? paginationLabelsAr.next : paginationLabelsEn.next}
            </span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pageCount - 1)}
            disabled={pageIndex === pageCount - 1}
            className="hidden sm:flex"
          >
            {effectivePrimaryLanguage === 'ar' ? <ChevronsLeft className="h-4 w-4" /> : <ChevronsRight className="h-4 w-4" />}
            <span className="sr-only">
              {effectivePrimaryLanguage === 'ar' ? paginationLabelsAr.last : paginationLabelsEn.last}
            </span>
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className={cn('bilingual-table-container', className)}>
      <Table className={cn('bilingual-table', tableClassName)}>
        {(captionAr || captionEn) && (
          <TableCaption>
            <BilingualText
              ar={captionAr}
              en={captionEn}
              primaryLanguage={effectivePrimaryLanguage}
            />
          </TableCaption>
        )}

        <TableHeader>
          <TableRow className={headerClassName}>
            {columns.map((column, index) => (
              <TableHead key={`header-${String(column.accessorKey)}-${index}`} className={cn(column.className)}>
                <BilingualText
                  ar={column.headerAr}
                  en={column.headerEn}
                  primaryLanguage={effectivePrimaryLanguage}
                />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>

        <TableBody>
          {data.map((item, rowIndex) => (
            <TableRow
              key={`row-${rowIndex}-${'id' in item ? (item as any).id : rowIndex}`}
              className={cn(rowClassName ? rowClassName(item, rowIndex) : undefined)}
            >
              {columns.map((column, colIndex) => (
                <TableCell
                  key={`cell-${String(column.accessorKey)}-${colIndex}`}
                  className={cn(cellClassName, column.className)}
                >
                  {column.cell ? (
                    column.cell(item)
                  ) : (
                    <span>{String(item[column.accessorKey] || '')}</span>
                  )}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>

        {/* عرض تذييل الجدول إذا كان هناك محتوى */}
        {columns.some((col): boolean => Boolean(col.footerAr || col.footerEn)) && (
          <TableFooter>
            <TableRow className={footerClassName}>
              {columns.map((column, index) => (
                <TableCell key={`footer-${String(column.accessorKey)}-${index}`} className={cn(column.className)}>
                  {(column.footerAr || column.footerEn) ? (
                    <BilingualText
                      ar={column.footerAr}
                      en={column.footerEn}
                      primaryLanguage={effectivePrimaryLanguage}
                    />
                  ) : null}
                </TableCell>
              ))}
            </TableRow>
          </TableFooter>
        )}
      </Table>

      {pagination && renderPagination()}
    </div>
  );
}
