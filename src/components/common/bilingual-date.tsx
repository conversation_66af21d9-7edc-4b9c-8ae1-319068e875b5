'use client';

import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import {SupportedLanguage} from '@/config/languages';

interface BilingualDateProps {
  date: Date | string;
  showHijri?: boolean;
  format?: 'short' | 'medium' | 'long' | 'full';
  separator?: string;
  primaryLanguage?: 'ar' | 'en';
  className?: string;
}

/**
 * مكون لعرض التاريخ بتنسيق ثنائي اللغة (العربية والإنجليزية)
 * Bilingual Date Component for displaying dates in both Arabic and English
 */
export function BilingualDate({
  date,
  showHijri = false,
  format = 'medium',
  separator = ' | ',
  primaryLanguage,
  className = '',
}: Readonly<BilingualDateProps>) {
  const { language } = useLanguage();
  // استخدام اللغة الحالية إذا لم يتم تحديد لغة أساسية
  const effectivePrimaryLanguage = primaryLanguage || language;
  // تحويل التاريخ إلى كائن Date إذا كان نصًا
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // خيارات تنسيق التاريخ بالعربية
  const arOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: format === 'short' ? 'numeric' : 'long',
    day: 'numeric',
    weekday: format === 'full' ? 'long' : undefined,
  };

  // خيارات تنسيق التاريخ بالإنجليزية
  const enOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: format === 'short' ? 'short' : 'long',
    day: 'numeric',
    weekday: format === 'full' ? 'long' : undefined,
  };

  // تنسيق التاريخ بالعربية
  const arDate = new Intl.DateTimeFormat('ar-SA', arOptions).format(dateObj);

  // تنسيق التاريخ بالإنجليزية
  const enDate = new Intl.DateTimeFormat('en-US', enOptions).format(dateObj);

  // تنسيق التاريخ الهجري (إذا كان مطلوبًا)
  const hijriDate = showHijri
    ? new Intl.DateTimeFormat('ar-SA-u-ca-islamic', arOptions).format(dateObj)
    : null;

  return (
    <div className={cn('bilingual-date', className)}>
      {(primaryLanguage ?? language) === 'ar' ? (
        <>
          <span className="date-ar">{arDate}</span>
          {separator}
          <span className="date-en">{enDate}</span>
          {showHijri && hijriDate && (
            <div className="hijri-date text-sm text-gray-500 mt-1">
              {hijriDate} (هجري)
            </div>
          )}
        </>
      ) : (
        <>
          <span className="date-en">{enDate}</span>
          {separator}
          <span className="date-ar">{arDate}</span>
          {showHijri && hijriDate && (
            <div className="hijri-date text-sm text-gray-500 mt-1">
              {hijriDate} (Hijri)
            </div>
          )}
        </>
      )}
    </div>
  );
}

/**
 * مكون لعرض التاريخ النسبي بتنسيق ثنائي اللغة
 * Bilingual Relative Date Component
 */
export function BilingualRelativeDate({
  date,
  language,
  className = '',
}: Readonly<{
  date: Date | string;
  language?: SupportedLanguage;
  className?: string;
}>) {
  const { language: contextLanguage } = useLanguage();
  // استخدام اللغة الحالية إذا لم يتم تحديد لغة
  const effectiveLanguage = language ?? contextLanguage;
  // تحويل التاريخ إلى كائن Date إذا كان نصًا
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // حساب الفرق بالمللي ثانية
  const diffMs = Date.now() - dateObj.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  const diffMonths = Math.floor(diffDays / 30);
  const diffYears = Math.floor(diffDays / 365);

  // تحديد النص المناسب حسب اللغة والفترة الزمنية
  const getArabicRelativeText = (): string => {
    if (diffSeconds < 60) return 'الآن';
    if (diffMinutes < 60) return `منذ ${diffMinutes} ${diffMinutes === 1 ? 'دقيقة' : diffMinutes < 11 ? 'دقائق' : 'دقيقة'}`;
    if (diffHours < 24) return `منذ ${diffHours} ${diffHours === 1 ? 'ساعة' : diffHours < 11 ? 'ساعات' : 'ساعة'}`;
    if (diffDays < 30) return `منذ ${diffDays} ${diffDays === 1 ? 'يوم' : diffDays < 11 ? 'أيام' : 'يومًا'}`;
    if (diffMonths < 12) return `منذ ${diffMonths} ${diffMonths === 1 ? 'شهر' : diffMonths < 11 ? 'أشهر' : 'شهرًا'}`;
    return `منذ ${diffYears} ${diffYears === 1 ? 'سنة' : diffYears < 11 ? 'سنوات' : 'سنة'}`;
  };

  const getEnglishRelativeText = (): string => {
    if (diffSeconds < 60) return 'just now';
    if (diffMinutes < 60) return `${diffMinutes} ${diffMinutes === 1 ? 'minute' : 'minutes'} ago`;
    if (diffHours < 24) return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    if (diffDays < 30) return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    if (diffMonths < 12) return `${diffMonths} ${diffMonths === 1 ? 'month' : 'months'} ago`;
    return `${diffYears} ${diffYears === 1 ? 'year' : 'years'} ago`;
  };

  // اختيار النص المناسب حسب اللغة
  const relativeText = (language ?? contextLanguage) === 'ar'
    ? getArabicRelativeText()
    : getEnglishRelativeText();

  return (
    <span
      className={cn('relative-date', className)}
      title={dateObj.toLocaleString((language ?? contextLanguage) === 'ar' ? 'ar-SA' : 'en-US')}
    >
      {relativeText}
    </span>
  );
}

/**
 * مكون لعرض نطاق تاريخ ثنائي اللغة
 * Bilingual Date Range Component
 */
export function BilingualDateRange({
  startDate,
  endDate,
  format = 'medium',
  language,
  className = '',
}: Readonly<{
  startDate: Date | string;
  endDate: Date | string;
  format?: 'short' | 'medium' | 'long';
  language?: SupportedLanguage;
  className?: string;
}>) {
  const { language: contextLanguage } = useLanguage();
  // تحويل التواريخ إلى كائنات Date إذا كانت نصوصًا
  const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;

  // خيارات تنسيق التاريخ
  const dateOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: format === 'short' ? 'numeric' : 'long',
    day: 'numeric',
  };

  // تحديد اللغة الفعلية
  const effectiveLanguage = language ?? contextLanguage;

  // تنسيق التواريخ حسب اللغة
  const formattedStartDate = startDateObj.toLocaleDateString(
    effectiveLanguage === 'ar' ? 'ar-SA' : 'en-US',
    dateOptions
  );

  const formattedEndDate = endDateObj.toLocaleDateString(
    effectiveLanguage === 'ar' ? 'ar-SA' : 'en-US',
    dateOptions
  );

  // النص الفاصل حسب اللغة
  const rangeSeparator = effectiveLanguage === 'ar' ? ' إلى ' : ' to ';

  return (
    <span className={cn('date-range', className)}>
      {formattedStartDate}{rangeSeparator}{formattedEndDate}
    </span>
  );
}
