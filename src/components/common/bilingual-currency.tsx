'use client';

import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/language-context';
import {SupportedLanguage} from '@/config/languages';

interface BilingualCurrencyProps {
  amount: number;
  currency?: string;
  currencyDisplay?: 'symbol' | 'code' | 'name';
  showInWords?: boolean;
  primaryLanguage?: SupportedLanguage;
  className?: string;
  amountClassName?: string;
  inWordsClassName?: string;
}

/**
 * مكون لعرض المبالغ المالية بتنسيق ثنائي اللغة (العربية والإنجليزية)
 * Bilingual Currency Component for displaying monetary amounts in both Arabic and English
 */
export function BilingualCurrency({
  amount,
  currency = 'AED',
  currencyDisplay = 'symbol',
  showInWords = false,
  primaryLanguage,
  className = '',
  amountClassName = '',
  inWordsClassName = '',
}: Readonly<BilingualCurrencyProps>) {
  const { language } = useLanguage();
  // خيارات تنسيق العملة
  const currencyOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currency,
    currencyDisplay: currencyDisplay,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  };

  // تنسيق المبلغ بالعربية
  const arAmount = new Intl.NumberFormat('ar-AE', currencyOptions).format(amount);

  // تنسيق المبلغ بالإنجليزية
  const enAmount = new Intl.NumberFormat('en-AE', currencyOptions).format(amount);

  // تحويل المبلغ إلى كلمات بالعربية
  const getAmountInArabicWords = (num: number): string => {
    if (num === 0) return 'صفر';

    const units = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة'];
    const teens = ['', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
    const tens = ['', 'عشرة', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];

    const numStr = num.toString();
    const decimalParts = numStr.split('.');
    const wholePart = parseInt(decimalParts[0]);
    const decimalPart = decimalParts.length > 1 ? parseInt(decimalParts[1]) : 0;

    let result = '';

    // تحويل الجزء الصحيح
    if (wholePart === 0) {
      result = 'صفر';
    } else if (wholePart < 11) {
      result = units[wholePart];
    } else if (wholePart < 20) {
      result = teens[wholePart - 10];
    } else if (wholePart < 100) {
      const unit = wholePart % 10;
      const ten = Math.floor(wholePart / 10);
      result = unit > 0 ? `${units[unit]} و${tens[ten]}` : tens[ten];
    } else if (wholePart < 1000) {
      const hundred = Math.floor(wholePart / 100);
      const remainder = wholePart % 100;
      result = hundreds[hundred];
      if (remainder > 0) {
        result += ' و' + getAmountInArabicWords(remainder);
      }
    } else {
      // للأرقام الأكبر، يمكن إضافة المزيد من المنطق هنا
      result = numStr;
    }

    // إضافة الجزء العشري إذا وجد
    if (decimalPart > 0) {
      result += ` و${getAmountInArabicWords(decimalPart)} ${getCurrencyFractionName(currency, decimalPart !== 1)}`;
    }

    // إضافة اسم العملة
    result += ` ${getCurrencyName(currency, wholePart !== 1)}`;

    return result;
  };

  // تحويل المبلغ إلى كلمات بالإنجليزية
  const getAmountInEnglishWords = (num: number): string => {
    if (num === 0) return 'zero';

    const units = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
    const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];

    const numStr = num.toString();
    const decimalParts = numStr.split('.');
    const wholePart = parseInt(decimalParts[0]);
    const decimalPart = decimalParts.length > 1 ? parseInt(decimalParts[1]) : 0;

    const convertLessThanThousand = (n: number): string => {
      if (n === 0) return '';

      if (n < 20) {
        return units[n];
      }

      if (n < 100) {
        return tens[Math.floor(n / 10)] + (n % 10 !== 0 ? '-' + units[n % 10] : '');
      }

      return units[Math.floor(n / 100)] + ' hundred' + (n % 100 !== 0 ? ' and ' + convertLessThanThousand(n % 100) : '');
    };

    let result = '';

    // تحويل الجزء الصحيح
    if (wholePart === 0) {
      result = 'zero';
    } else {
      // للأرقام الأكبر، يمكن إضافة المزيد من المنطق هنا
      result = convertLessThanThousand(wholePart);
    }

    // إضافة الجزء العشري إذا وجد
    if (decimalPart > 0) {
      result += ` and ${convertLessThanThousand(decimalPart)} ${getCurrencyFractionNameEn(currency, decimalPart !== 1)}`;
    }

    // إضافة اسم العملة
    result += ` ${getCurrencyNameEn(currency, wholePart !== 1)}`;

    return result;
  };

  // الحصول على اسم العملة بالعربية
  const getCurrencyName = (currencyCode: string, plural: boolean): string => {
    switch (currencyCode) {
      case 'AED':
        return plural ? 'دراهم إماراتية' : 'درهم إماراتي';
      case 'SAR':
        return plural ? 'ريالات سعودية' : 'ريال سعودي';
      case 'USD':
        return plural ? 'دولارات أمريكية' : 'دولار أمريكي';
      case 'EUR':
        return plural ? 'يوروهات' : 'يورو';
      default:
        return currencyCode;
    }
  };

  // الحصول على اسم الكسور بالعربية
  const getCurrencyFractionName = (currencyCode: string, plural: boolean): string => {
    switch (currencyCode) {
      case 'AED':
      case 'SAR':
        return plural ? 'فلسات' : 'فلس';
      case 'USD':
        return plural ? 'سنتات' : 'سنت';
      case 'EUR':
        return plural ? 'سنتات' : 'سنت';
      default:
        return '';
    }
  };

  // الحصول على اسم العملة بالإنجليزية
  const getCurrencyNameEn = (currencyCode: string, plural: boolean): string => {
    switch (currencyCode) {
      case 'AED':
        return plural ? 'UAE Dirhams' : 'UAE Dirham';
      case 'SAR':
        return plural ? 'Saudi Riyals' : 'Saudi Riyal';
      case 'USD':
        return plural ? 'US Dollars' : 'US Dollar';
      case 'EUR':
        return plural ? 'Euros' : 'Euro';
      default:
        return currencyCode;
    }
  };

  // الحصول على اسم الكسور بالإنجليزية
  const getCurrencyFractionNameEn = (currencyCode: string, plural: boolean): string => {
    switch (currencyCode) {
      case 'AED':
      case 'SAR':
        return plural ? 'fils' : 'fil';
      case 'USD':
        return plural ? 'cents' : 'cent';
      case 'EUR':
        return plural ? 'cents' : 'cent';
      default:
        return '';
    }
  };

  // تحويل المبلغ إلى كلمات
  const amountInArabicWords = getAmountInArabicWords(amount);
  const amountInEnglishWords = getAmountInEnglishWords(amount);

  // تحديد اللغة الأساسية
  const effectivePrimaryLanguage = primaryLanguage ?? language;

  return (
    <div className={cn('bilingual-currency', className)}>
      <div className={cn('amount', amountClassName)}>
        {effectivePrimaryLanguage === 'ar' ? (
          <>
            <span className="amount-ar">{arAmount}</span>
            <span className="amount-separator mx-1">|</span>
            <span className="amount-en">{enAmount}</span>
          </>
        ) : (
          <>
            <span className="amount-en">{enAmount}</span>
            <span className="amount-separator mx-1">|</span>
            <span className="amount-ar">{arAmount}</span>
          </>
        )}
      </div>

      {showInWords && (
        <div className={cn('amount-in-words text-sm text-gray-600 mt-1', inWordsClassName)}>
          {effectivePrimaryLanguage === 'ar' ? (
            <>
              <div className="words-ar">{amountInArabicWords}</div>
              <div className="words-en">{amountInEnglishWords}</div>
            </>
          ) : (
            <>
              <div className="words-en">{amountInEnglishWords}</div>
              <div className="words-ar">{amountInArabicWords}</div>
            </>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * مكون لعرض المبالغ المالية بتنسيق بسيط
 * Simple Currency Component
 */
export function SimpleCurrency({
  amount,
  currency = 'AED',
  language,
  className = '',
}: Readonly<{
  amount: number;
  currency?: string;
  language?: SupportedLanguage;
  className?: string;
}>) {
  const { language: contextLanguage } = useLanguage();
  // خيارات تنسيق العملة
  const currencyOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  };

  // تحديد اللغة الفعلية
  const effectiveLanguage = language ?? contextLanguage;

  // تنسيق المبلغ حسب اللغة
  const formattedAmount = new Intl.NumberFormat(
    effectiveLanguage === 'ar' ? 'ar-AE' : 'en-AE',
    currencyOptions
  ).format(amount);

  return (
    <span className={cn('simple-currency', className)}>
      {formattedAmount}
    </span>
  );
}
