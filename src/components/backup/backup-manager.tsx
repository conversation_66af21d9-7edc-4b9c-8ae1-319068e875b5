'use client';

import { useState, useEffect } from 'react';
import { useTauriAPI } from '@/components/providers/tauri-api-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/toast';
import { useTranslation } from '@/lib/i18n';
import { Backup } from '@/lib/tauri-api';

/**
 * مكون إدارة النسخ الاحتياطية
 * Backup manager component
 */
export function BackupManager() {
  const { api } = useTauriAPI();
  const [backups, setBackups] = useState<Backup[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [restoreDialogOpen, setRestoreDialogOpen] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState<Backup | null>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  // تحميل النسخ الاحتياطية
  // Load backups
  const loadBackups = async () => {
    setLoading(true);
    try {
      const backupsData = await api.getBackups();
      setBackups(backupsData);
    } catch (error) {
      console.error('Error loading backups:', error);
      toast({
        title: t('backup.loadError'),
        description: String(error),
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBackups();
  }, []);

  // إنشاء نسخة احتياطية جديدة
  // Create new backup
  const handleCreateBackup = async () => {
    setCreating(true);
    try {
      await api.createBackup();
      toast({
        title: t('backup.createSuccess'),
        description: t('backup.createSuccessDescription'),
        type: 'success',
      });
      await loadBackups();
    } catch (error) {
      console.error('Error creating backup:', error);
      toast({
        title: t('backup.createError'),
        description: String(error),
        type: 'error',
      });
    } finally {
      setCreating(false);
    }
  };

  // استيراد نسخة احتياطية
  // Import backup
  const handleImportBackup = async () => {
    try {
      await api.importBackup();
      toast({
        title: t('backup.importSuccess'),
        description: t('backup.importSuccessDescription'),
        type: 'success',
      });
      await loadBackups();
    } catch (error) {
      if (String(error) !== 'Import cancelled') {
        console.error('Error importing backup:', error);
        toast({
          title: t('backup.importError'),
          description: String(error),
          type: 'error',
        });
      }
    }
  };

  // تصدير نسخة احتياطية
  // Export backup
  const handleExportBackup = async (backup: Backup) => {
    try {
      await api.exportBackup(backup.id);
      toast({
        title: t('backup.exportSuccess'),
        description: t('backup.exportSuccessDescription'),
        type: 'success',
      });
    } catch (error) {
      if (String(error) !== 'Export cancelled') {
        console.error('Error exporting backup:', error);
        toast({
          title: t('backup.exportError'),
          description: String(error),
          type: 'error',
        });
      }
    }
  };

  // فتح مربع حوار استعادة النسخة الاحتياطية
  // Open restore backup dialog
  const handleOpenRestoreDialog = (backup: Backup) => {
    setSelectedBackup(backup);
    setRestoreDialogOpen(true);
  };

  // استعادة نسخة احتياطية
  // Restore backup
  const handleRestoreBackup = async () => {
    if (!selectedBackup) return;

    try {
      await api.restoreBackup(selectedBackup.id);
      setRestoreDialogOpen(false);
      toast({
        title: t('backup.restoreSuccess'),
        description: t('backup.restoreSuccessDescription'),
        type: 'success',
      });

      // إعادة تحميل الصفحة بعد الاستعادة
      // Reload page after restoration
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error restoring backup:', error);
      toast({
        title: t('backup.restoreError'),
        description: String(error),
        type: 'error',
      });
    }
  };

  // حذف نسخة احتياطية
  // Delete backup
  const handleDeleteBackup = async (backup: Backup) => {
    if (!confirm(t('backup.confirmDelete'))) {
      return;
    }

    try {
      await api.deleteBackup(backup.id);
      toast({
        title: t('backup.deleteSuccess'),
        description: t('backup.deleteSuccessDescription'),
        type: 'success',
      });
      await loadBackups();
    } catch (error) {
      console.error('Error deleting backup:', error);
      toast({
        title: t('backup.deleteError'),
        description: String(error),
        type: 'error',
      });
    }
  };

  // تنسيق حجم الملف
  // Format file size
  const formatFileSize = (size: number | null) => {
    if (size === null) return '-';

    if (size < 1024) {
      return `${size} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    } else {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  // تنسيق التاريخ
  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('ar-AE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);
    } catch (error) {
      return dateString;
    }
  };

  if (loading && backups.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-40">
            <p>{t('common.loading')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{t('backup.title')}</CardTitle>
            <CardDescription>{t('backup.description')}</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleImportBackup}>
              {t('backup.import')}
            </Button>
            <Button onClick={handleCreateBackup} disabled={creating}>
              {creating ? t('backup.creating') : t('backup.create')}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/50 font-medium">
                <th className="py-3 px-4 text-right">{t('backup.fileName')}</th>
                <th className="py-3 px-4 text-right">{t('backup.fileSize')}</th>
                <th className="py-3 px-4 text-right">{t('backup.createdAt')}</th>
                <th className="py-3 px-4 text-center">{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {backups.length === 0 ? (
                <tr>
                  <td colSpan={4} className="py-6 text-center text-muted-foreground">
                    {t('backup.noBackups')}
                  </td>
                </tr>
              ) : (
                backups.map((backup) => (
                  <tr key={backup.id} className="border-b">
                    <td className="py-3 px-4">{backup.file_name}</td>
                    <td className="py-3 px-4">{formatFileSize(backup.file_size)}</td>
                    <td className="py-3 px-4">{formatDate(backup.created_at)}</td>
                    <td className="py-3 px-4">
                      <div className="flex justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExportBackup(backup)}
                        >
                          {t('backup.export')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleOpenRestoreDialog(backup)}
                        >
                          {t('backup.restore')}
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteBackup(backup)}
                        >
                          {t('common.delete')}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </CardContent>

      {/* مربع حوار استعادة النسخة الاحتياطية */}
      {/* Restore backup dialog */}
      <Dialog open={restoreDialogOpen} onOpenChange={setRestoreDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('backup.restoreTitle')}</DialogTitle>
            <DialogDescription>
              {t('backup.restoreDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <p className="text-destructive font-semibold">
              {t('backup.restoreWarning')}
            </p>
            <p className="mt-2">
              {t('backup.restoreConfirmation')}
            </p>
            {selectedBackup && (
              <div className="mt-4 p-3 border rounded-md bg-muted/50">
                <p><strong>{t('backup.fileName')}:</strong> {selectedBackup.file_name}</p>
                <p><strong>{t('backup.createdAt')}:</strong> {formatDate(selectedBackup.created_at)}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setRestoreDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button variant="destructive" onClick={handleRestoreBackup}>
              {t('backup.confirmRestore')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
