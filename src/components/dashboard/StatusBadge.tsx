import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: "PAID" | "PENDING" | "OVERDUE" | "CANCELLED";
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  return (
    <span
      className={cn(
        "px-2 py-1 text-xs font-medium rounded-full",
        status === "PAID" && "bg-green-100 text-green-800",
        status === "PENDING" && "bg-yellow-100 text-yellow-800",
        status === "OVERDUE" && "bg-red-100 text-red-800",
        status === "CANCELLED" && "bg-gray-100 text-gray-800",
        className
      )}
    >
      {status === "PAID" && "مدفوعة"}
      {status === "PENDING" && "قيد الانتظار"}
      {status === "OVERDUE" && "متأخرة"}
      {status === "CANCELLED" && "ملغاة"}
    </span>
  );
}
