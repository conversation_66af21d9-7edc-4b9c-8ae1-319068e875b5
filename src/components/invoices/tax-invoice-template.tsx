'use client';

import React from 'react';
import Image from 'next/image';
import { cn, formatCurrency, formatDate } from '@/lib/utils';
import { getCompanyInfo } from '@/lib/settings';
import { TaxInvoiceQR } from './tax-invoice-qr';
import { BilingualText } from '@/components/common/bilingual-text';
import { useLanguage } from '@/contexts/language-context';

interface TaxInvoiceTemplateProps {
  invoice: {
    id: string;
    invoiceNumber: string;
    issueDate: string;
    dueDate?: string;
    customer: {
      id: string;
      name: string;
      address?: string;
      taxNumber?: string;
      phone?: string;
      email?: string;
    };
    items: Array<{
      id: string;
      description: string;
      quantity: number;
      price: number;
      discount?: number;
      tax?: number;
      total: number;
    }>;
    subtotal: number;
    taxAmount: number;
    total: number;
    notes?: string;
    terms?: string;
    status: string;
    paymentStatus?: string;
    paymentMethod?: string;
    paymentDate?: string;
  };
  showWatermark?: boolean;
  showBackground?: boolean;
  backgroundType?: 'pattern' | 'gradient' | 'solid';
  backgroundOpacity?: number;
  watermarkText?: string;
  watermarkOpacity?: number;
  className?: string;
}

/**
 * مكون قالب الفاتورة الضريبية
 * Tax Invoice Template Component
 */
export function TaxInvoiceTemplate({
  invoice,
  showWatermark = true,
  showBackground = true,
  backgroundType = 'pattern',
  backgroundOpacity = 0.05,
  watermarkText = 'فاتورة ضريبية | TAX INVOICE',
  watermarkOpacity = 0.1,
  className = '',
}: Readonly<TaxInvoiceTemplateProps>) {
  const { language } = useLanguage();
  const companyInfo = getCompanyInfo();

  // تحديد نمط الخلفية
  const backgroundStyle = React.useMemo(() => {
    if (!showBackground) return {};

    switch (backgroundType) {
      case 'pattern':
        return {
          backgroundImage: 'url("/images/invoice-pattern.png")',
          backgroundRepeat: 'repeat',
          backgroundSize: '200px',
          opacity: backgroundOpacity,
        };
      case 'gradient':
        return {
          background: 'linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0) 100%)',
          opacity: backgroundOpacity,
        };
      case 'solid':
        return {
          backgroundColor: `rgba(0, 123, 255, ${backgroundOpacity})`,
        };
      default:
        return {};
    }
  }, [showBackground, backgroundType, backgroundOpacity]);

  // تحديد حالة الفاتورة
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'PAID':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'OVERDUE':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'SENT':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className={cn('tax-invoice-template relative bg-white rounded-lg overflow-hidden', className)}>
      {/* خلفية الفاتورة */}
      {showBackground && (
        <div
          className="absolute inset-0 pointer-events-none z-0"
          style={backgroundStyle}
        />
      )}

      {/* العلامة المائية */}
      {showWatermark && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-0 overflow-hidden">
          <div
            className="text-4xl font-bold transform rotate-45 text-center whitespace-nowrap"
            style={{
              opacity: watermarkOpacity,
              color: 'rgba(0, 0, 0, 0.1)',
              fontSize: '6rem',
              letterSpacing: '0.5rem'
            }}
          >
            {watermarkText}
          </div>
        </div>
      )}

      {/* محتوى الفاتورة */}
      <div className="relative z-10 p-8">
        {/* رأس الفاتورة */}
        <div className="flex justify-between items-start mb-8">
          {/* معلومات الشركة */}
          <div className="flex items-center">
            <div className="w-16 h-16 mr-4 relative">
              <Image
                src={companyInfo.logo || "/logo.png"}
                alt={companyInfo.name}
                width={64}
                height={64}
                className="object-contain"
              />
            </div>
            <div>
              <h2 className="text-xl font-bold">{companyInfo.name}</h2>
              <p className="text-sm text-gray-600">{companyInfo.nameEn}</p>
              <p className="text-sm mt-1">{companyInfo.address}</p>
              <p className="text-sm">
                <BilingualText
                  ar={`الرقم الضريبي: ${companyInfo.taxNumber}`}
                  en={`Tax Number: ${companyInfo.taxNumber}`}
                  showBothLanguages={true}
                  separator=" | "
                />
              </p>
            </div>
          </div>

          {/* عنوان الفاتورة والرمز الضريبي */}
          <div className="text-left flex flex-col items-end">
            <h1 className="text-2xl font-bold mb-2">
              <BilingualText
                ar="فاتورة ضريبية"
                en="TAX INVOICE"
                showBothLanguages={true}
                separator=" | "
              />
            </h1>
            <div className="text-sm">
              <BilingualText
                ar={`رقم الفاتورة: ${invoice.invoiceNumber}`}
                en={`Invoice Number: ${invoice.invoiceNumber}`}
                showBothLanguages={true}
                separator=" | "
              />
            </div>
            <div className="text-sm">
              <BilingualText
                ar={`تاريخ الإصدار: ${formatDate(invoice.issueDate)}`}
                en={`Issue Date: ${formatDate(invoice.issueDate)}`}
                showBothLanguages={true}
                separator=" | "
              />
            </div>
            {invoice.dueDate && (
              <div className="text-sm">
                <BilingualText
                  ar={`تاريخ الاستحقاق: ${formatDate(invoice.dueDate)}`}
                  en={`Due Date: ${formatDate(invoice.dueDate)}`}
                  showBothLanguages={true}
                  separator=" | "
                />
              </div>
            )}
            <div className="mt-2">
              <span className={cn('px-3 py-1 rounded-full text-xs border', getStatusColor(invoice.status))}>
                {invoice.status === 'PAID' ? 'مدفوعة | Paid' :
                 invoice.status === 'OVERDUE' ? 'متأخرة | Overdue' :
                 invoice.status === 'DRAFT' ? 'مسودة | Draft' :
                 invoice.status === 'SENT' ? 'مرسلة | Sent' :
                 invoice.status === 'CANCELLED' ? 'ملغاة | Cancelled' : invoice.status}
              </span>
            </div>
          </div>
        </div>

        {/* معلومات العميل */}
        <div className="mb-8 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">
            <BilingualText
              ar="معلومات العميل"
              en="Customer Information"
              showBothLanguages={true}
              separator=" | "
            />
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="font-medium">{invoice.customer.name}</p>
              {invoice.customer.address && <p className="text-sm text-gray-600">{invoice.customer.address}</p>}
              {invoice.customer.phone && <p className="text-sm text-gray-600">{invoice.customer.phone}</p>}
              {invoice.customer.email && <p className="text-sm text-gray-600">{invoice.customer.email}</p>}
            </div>
            <div className="text-left">
              {invoice.customer.taxNumber && (
                <p className="text-sm">
                  <BilingualText
                    ar={`الرقم الضريبي: ${invoice.customer.taxNumber}`}
                    en={`Tax Number: ${invoice.customer.taxNumber}`}
                    showBothLanguages={true}
                    separator=" | "
                  />
                </p>
              )}
            </div>
          </div>
        </div>

        {/* جدول العناصر */}
        <div className="mb-8 overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100 text-gray-700">
                <th className="p-3 text-right border">#</th>
                <th className="p-3 text-right border">
                  <BilingualText
                    ar="الوصف"
                    en="Description"
                    showBothLanguages={false}
                  />
                </th>
                <th className="p-3 text-center border">
                  <BilingualText
                    ar="الكمية"
                    en="Quantity"
                    showBothLanguages={false}
                  />
                </th>
                <th className="p-3 text-center border">
                  <BilingualText
                    ar="السعر"
                    en="Price"
                    showBothLanguages={false}
                  />
                </th>
                {invoice.items.some(item => (item.discount ?? 0) > 0) && (
                  <th className="p-3 text-center border">
                    <BilingualText
                      ar="الخصم"
                      en="Discount"
                      showBothLanguages={false}
                    />
                  </th>
                )}
                <th className="p-3 text-center border">
                  <BilingualText
                    ar="الضريبة"
                    en="Tax"
                    showBothLanguages={false}
                  />
                </th>
                <th className="p-3 text-center border">
                  <BilingualText
                    ar="المجموع"
                    en="Total"
                    showBothLanguages={false}
                  />
                </th>
              </tr>
            </thead>
            <tbody>
              {invoice.items.map((item, index) => (
                <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="p-3 text-right border">{index + 1}</td>
                  <td className="p-3 text-right border">{item.description}</td>
                  <td className="p-3 text-center border">{item.quantity}</td>
                  <td className="p-3 text-center border">{formatCurrency(item.price)}</td>
                  {invoice.items.some(item => (item.discount ?? 0) > 0) && (
                    <td className="p-3 text-center border">
                      {item.discount ? `${item.discount}%` : '-'}
                    </td>
                  )}
                  <td className="p-3 text-center border">
                    {item.tax ? `${item.tax}%` : '-'}
                  </td>
                  <td className="p-3 text-center border">{formatCurrency(item.total)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* ملخص الفاتورة */}
        <div className="flex justify-between mb-8">
          {/* معلومات الضريبة والرمز الضريبي */}
          <div className="w-1/3">
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">
                <BilingualText
                  ar="معلومات الضريبة"
                  en="Tax Information"
                  showBothLanguages={true}
                  separator=" | "
                />
              </h3>
              <div className="text-sm">
                <p>
                  <BilingualText
                    ar="ضريبة القيمة المضافة"
                    en="Value Added Tax (VAT)"
                    showBothLanguages={true}
                    separator=" | "
                  />
                </p>
                <p>
                  <BilingualText
                    ar={`الرقم الضريبي: ${companyInfo.taxNumber}`}
                    en={`Tax Number: ${companyInfo.taxNumber}`}
                    showBothLanguages={true}
                    separator=" | "
                  />
                </p>
              </div>
            </div>

            {/* رمز QR الضريبي */}
            <div className="w-24 h-24">
              <TaxInvoiceQR
                invoiceTimestamp={invoice.issueDate}
                invoiceTotal={invoice.total}
                taxAmount={invoice.taxAmount}
                width={96}
                showBorder={true}
                showLabel={true}
              />
            </div>
          </div>

          {/* ملخص المبالغ */}
          <div className="w-1/3">
            <div className="border rounded-lg overflow-hidden">
              <div className="p-3 border-b bg-gray-50">
                <BilingualText
                  ar="ملخص الفاتورة"
                  en="Invoice Summary"
                  showBothLanguages={true}
                  separator=" | "
                />
              </div>
              <div className="p-3">
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">
                    <BilingualText
                      ar="المجموع الفرعي"
                      en="Subtotal"
                      showBothLanguages={false}
                    />
                  </span>
                  <span>{formatCurrency(invoice.subtotal)}</span>
                </div>
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">
                    <BilingualText
                      ar="ضريبة القيمة المضافة"
                      en="VAT"
                      showBothLanguages={false}
                    />
                  </span>
                  <span>{formatCurrency(invoice.taxAmount)}</span>
                </div>
                <div className="flex justify-between py-1 font-bold border-t mt-1 pt-2">
                  <span>
                    <BilingualText
                      ar="الإجمالي"
                      en="Total"
                      showBothLanguages={false}
                    />
                  </span>
                  <span>{formatCurrency(invoice.total)}</span>
                </div>

                {/* معلومات الدفع إذا كانت متوفرة */}
                {invoice.paymentStatus === 'PAID' && invoice.paymentDate && (
                  <div className="mt-3 pt-2 border-t text-sm">
                    <div className="flex justify-between py-1">
                      <span className="text-gray-600">
                        <BilingualText
                          ar="طريقة الدفع"
                          en="Payment Method"
                          showBothLanguages={false}
                        />
                      </span>
                      <span>{invoice.paymentMethod || '-'}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-gray-600">
                        <BilingualText
                          ar="تاريخ الدفع"
                          en="Payment Date"
                          showBothLanguages={false}
                        />
                      </span>
                      <span>{formatDate(invoice.paymentDate)}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* الشروط والملاحظات */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          {invoice.terms && (
            <div>
              <h3 className="text-lg font-semibold mb-2">
                <BilingualText
                  ar="الشروط والأحكام"
                  en="Terms & Conditions"
                  showBothLanguages={true}
                  separator=" | "
                />
              </h3>
              <p className="text-sm text-gray-600">{invoice.terms}</p>
            </div>
          )}

          {invoice.notes && (
            <div>
              <h3 className="text-lg font-semibold mb-2">
                <BilingualText
                  ar="ملاحظات"
                  en="Notes"
                  showBothLanguages={true}
                  separator=" | "
                />
              </h3>
              <p className="text-sm text-gray-600">{invoice.notes}</p>
            </div>
          )}
        </div>

        {/* تذييل الفاتورة */}
        <div className="text-center text-sm text-gray-500 pt-4 border-t">
          <p>
            <BilingualText
              ar="شكراً لتعاملكم معنا"
              en="Thank you for your business"
              showBothLanguages={true}
              separator=" | "
            />
          </p>
          <p className="mt-1">
            <BilingualText
              ar={`${companyInfo.website} | ${companyInfo.email} | ${companyInfo.phone}`}
              en={`${companyInfo.website} | ${companyInfo.email} | ${companyInfo.phone}`}
              showBothLanguages={false}
            />
          </p>
        </div>
      </div>
    </div>
  );
}
