'use client';

/**
 * مكون مربع حوار الدفع - يوفر واجهة منبثقة لمعالجة المدفوعات
 * Payment Dialog Component - Provides a popup interface for payment processing
 *
 * ملاحظة: هذا المكون تم استبداله بمكون PaymentDialog الأكثر تطوراً في مجلد payments
 * Note: This component has been replaced by the more advanced PaymentDialog component in the payments folder
 */

import { PaymentDialog as NewPaymentDialog } from '@/components/payments/payment-dialog';

interface PaymentDialogProps {
  invoiceId: string;
  invoiceNumber: string;
  invoiceTotal: number;
  customerName?: string;
  onPaymentSuccess?: () => void;
}

/**
 * مكون مربع حوار الدفع - يوفر واجهة منبثقة لمعالجة المدفوعات
 * Payment Dialog Component - Provides a popup interface for payment processing
 *
 * @deprecated استخدم مكون PaymentDialog من مجلد payments بدلاً من ذلك
 * @deprecated Use PaymentDialog component from payments folder instead
 */
export function PaymentDialog(props: Readonly<PaymentDialogProps>) {
  // استخدام المكون الجديد مع نفس الخصائص
  // Use the new component with the same props
  return <NewPaymentDialog {...props} buttonLabel="دفع الفاتورة" />;
}
