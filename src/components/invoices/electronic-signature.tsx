'use client';

import { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import {Pen, Upload, Trash2, CheckCircle, FileText} from 'lucide-react';

interface ElectronicSignatureProps {
  documentId: string;
  documentName: string;
  onSignatureComplete?: (signatureData: SignatureData) => void;
}

interface SignatureData {
  type: 'draw' | 'type' | 'upload';
  data: string;
  name: string;
  date: string;
  ip: string;
}

export function ElectronicSignature({
  documentId,
  documentName,
  onSignatureComplete,
}: ElectronicSignatureProps) {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('draw');
  const [loading, setLoading] = useState(false);
  const [signatureComplete, setSignatureComplete] = useState(false);
  const [typedName, setTypedName] = useState('');
  const [typedFont, setTypedFont] = useState('Dancing Script');
  const [signerName, setSignerName] = useState('');
  const [signerEmail, setSignerEmail] = useState('');
  const [uploadedSignature, setUploadedSignature] = useState<string | null>(null);
  const [signatureData, setSignatureData] = useState<SignatureData | null>(null);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [canvasEmpty, setCanvasEmpty] = useState(true);

  // تهيئة Canvas للرسم
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // تعيين حجم Canvas
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // تعيين خصائص الرسم
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.strokeStyle = '#000';

    // مسح Canvas
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }, [activeTab]);

  // وظائف الرسم
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    setIsDrawing(true);

    // الحصول على إحداثيات الماوس أو اللمس
    let clientX, clientY;
    if ('touches' in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    const rect = canvas.getBoundingClientRect();
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // الحصول على إحداثيات الماوس أو اللمس
    let clientX, clientY;
    if ('touches' in e) {
      e.preventDefault(); // منع التمرير أثناء الرسم على الأجهزة اللمسية
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    const rect = canvas.getBoundingClientRect();
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    ctx.lineTo(x, y);
    ctx.stroke();

    setCanvasEmpty(false);
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    setCanvasEmpty(true);
  };

  // وظيفة لتحويل Canvas إلى صورة
  const getCanvasImage = (): string => {
    const canvas = canvasRef.current;
    if (!canvas) return '';

    return canvas.toDataURL('image/png');
  };

  // وظيفة لتحميل صورة التوقيع
  const handleUploadSignature = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      toast.error('يرجى تحميل صورة صالحة');
      return;
    }

    // التحقق من حجم الملف (الحد الأقصى 2 ميجابايت)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('حجم الصورة كبير جدًا. يرجى تحميل صورة أصغر من 2 ميجابايت');
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target && typeof event.target.result === 'string') {
        setUploadedSignature(event.target.result);
      }
    };
    reader.readAsDataURL(file);
  };

  // وظيفة لإكمال التوقيع
  const handleCompleteSignature = async () => {
    // التحقق من صحة البيانات
    if (!signerName) {
      toast.error('يرجى إدخال اسم الموقع');
      return;
    }

    if (!signerEmail) {
      toast.error('يرجى إدخال البريد الإلكتروني للموقع');
      return;
    }

    // التحقق من صحة التوقيع حسب الطريقة المختارة
    if (activeTab === 'draw' && canvasEmpty) {
      toast.error('يرجى رسم توقيعك');
      return;
    }

    if (activeTab === 'type' && !typedName) {
      toast.error('يرجى كتابة اسمك');
      return;
    }

    if (activeTab === 'upload' && !uploadedSignature) {
      toast.error('يرجى تحميل صورة التوقيع');
      return;
    }

    try {
      setLoading(true);

      // الحصول على بيانات التوقيع حسب الطريقة المختارة
      let signatureImageData = '';

      if (activeTab === 'draw') {
        signatureImageData = getCanvasImage();
      } else if (activeTab === 'type') {
        // إنشاء صورة من النص المكتوب
        const canvas = document.createElement('canvas');
        canvas.width = 300;
        canvas.height = 100;

        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = '#fff';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          ctx.font = `30px ${typedFont}`;
          ctx.fillStyle = '#000';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(typedName, canvas.width / 2, canvas.height / 2);

          signatureImageData = canvas.toDataURL('image/png');
        }
      } else if (activeTab === 'upload' && uploadedSignature) {
        signatureImageData = uploadedSignature;
      }

      // محاكاة إرسال البيانات إلى الخادم
      await new Promise(resolve => setTimeout(resolve, 1500));

      // إنشاء كائن بيانات التوقيع
      const newSignatureData: SignatureData = {
        type: activeTab as 'draw' | 'type' | 'upload',
        data: signatureImageData,
        name: signerName,
        date: new Date().toISOString(),
        ip: '***********', // عنوان IP وهمي
      };

      setSignatureData(newSignatureData);
      setSignatureComplete(true);

      // استدعاء دالة الاستجابة إذا كانت موجودة
      if (onSignatureComplete) {
        onSignatureComplete(newSignatureData);
      }

      toast.success('تم توقيع المستند بنجاح');
    } catch (error) {
      console.error('خطأ في توقيع المستند:', error);
      toast.error('حدث خطأ أثناء توقيع المستند');
    } finally {
      setLoading(false);
    }
  };

  // وظيفة لإعادة تعيين التوقيع
  const handleResetSignature = () => {
    setSignatureComplete(false);
    setSignatureData(null);
    setTypedName('');
    setUploadedSignature(null);
    clearCanvas();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <Pen className="h-4 w-4 ml-1" />
          <span>توقيع المستند</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        {signatureComplete ? (
          <div className="flex flex-col items-center py-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h2 className="text-xl font-bold mb-2">تم توقيع المستند بنجاح!</h2>
            <p className="text-gray-500 mb-4 text-center">
              تم توقيع {documentName} بنجاح.
            </p>

            <div className="bg-gray-50 p-4 rounded-md w-full mb-4">
              <div className="flex justify-between mb-2">
                <span className="text-gray-500">اسم الموقع:</span>
                <span className="font-medium">{signatureData?.name}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-500">تاريخ التوقيع:</span>
                <span className="font-medium">{new Date(signatureData?.date || '').toLocaleString('ar-AE')}</span>
              </div>
              <div className="flex justify-between mb-4">
                <span className="text-gray-500">طريقة التوقيع:</span>
                <span className="font-medium">
                  {signatureData?.type === 'draw' ? 'رسم التوقيع' :
                   signatureData?.type === 'type' ? 'كتابة الاسم' : 'تحميل صورة'}
                </span>
              </div>

              <div className="border rounded-md p-2 bg-white">
                <div className="text-center text-sm text-gray-500 mb-2">التوقيع</div>
                <div className="flex justify-center">
                  <img
                    src={signatureData?.data}
                    alt="التوقيع"
                    className="max-h-20 object-contain"
                  />
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                إغلاق
              </Button>
              <Button variant="outline" onClick={handleResetSignature}>
                إعادة التوقيع
              </Button>
              <Button variant="outline" onClick={() => window.open(`/dashboard/documents/${documentId}`, '_blank')}>
                عرض المستند
              </Button>
            </div>
          </div>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>توقيع المستند إلكترونياً</DialogTitle>
              <DialogDescription>
                قم بتوقيع {documentName} باستخدام إحدى الطرق التالية
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="signer_name">اسم الموقع</Label>
                <Input
                  id="signer_name"
                  placeholder="أدخل اسمك الكامل"
                  value={signerName}
                  onChange={(e) => setSignerName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="signer_email">البريد الإلكتروني</Label>
                <Input
                  id="signer_email"
                  type="email"
                  placeholder="أدخل بريدك الإلكتروني"
                  value={signerEmail}
                  onChange={(e) => setSignerEmail(e.target.value)}
                />
              </div>

              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid grid-cols-3 mb-4">
                  <TabsTrigger value="draw" className="flex items-center gap-1">
                    <Pen className="h-4 w-4" />
                    رسم
                  </TabsTrigger>
                  <TabsTrigger value="type" className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    كتابة
                  </TabsTrigger>
                  <TabsTrigger value="upload" className="flex items-center gap-1">
                    <Upload className="h-4 w-4" />
                    تحميل
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="draw" className="space-y-4">
                  <div className="border rounded-md p-2">
                    <canvas
                      ref={canvasRef}
                      className="w-full h-32 border rounded cursor-crosshair touch-none"
                      onMouseDown={startDrawing}
                      onMouseMove={draw}
                      onMouseUp={endDrawing}
                      onMouseLeave={endDrawing}
                      onTouchStart={startDrawing}
                      onTouchMove={draw}
                      onTouchEnd={endDrawing}
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button variant="outline" size="sm" onClick={clearCanvas}>
                      <Trash2 className="h-4 w-4 ml-1" />
                      مسح
                    </Button>
                  </div>

                  <p className="text-sm text-gray-500">
                    قم برسم توقيعك باستخدام الماوس أو شاشة اللمس
                  </p>
                </TabsContent>

                <TabsContent value="type" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="typed_name">اكتب اسمك</Label>
                    <Input
                      id="typed_name"
                      placeholder="اكتب اسمك كما تريده أن يظهر في التوقيع"
                      value={typedName}
                      onChange={(e) => setTypedName(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="font_style">نمط الخط</Label>
                    <Select value={typedFont} onValueChange={setTypedFont}>
                      <SelectTrigger id="font_style">
                        <SelectValue placeholder="اختر نمط الخط" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Dancing Script">خط التوقيع</SelectItem>
                        <SelectItem value="Pacifico">خط مزخرف</SelectItem>
                        <SelectItem value="Satisfy">خط انسيابي</SelectItem>
                        <SelectItem value="Caveat">خط يدوي</SelectItem>
                        <SelectItem value="Arial">خط عادي</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="border rounded-md p-4 min-h-20 flex items-center justify-center">
                    {typedName ? (
                      <div style={{ fontFamily: typedFont, fontSize: '24px' }}>
                        {typedName}
                      </div>
                    ) : (
                      <div className="text-gray-400">معاينة التوقيع</div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="upload" className="space-y-4">
                  <div className="border rounded-md p-4 flex flex-col items-center justify-center">
                    {uploadedSignature ? (
                      <div className="space-y-2 w-full">
                        <img
                          src={uploadedSignature}
                          alt="التوقيع المحمل"
                          className="max-h-32 mx-auto object-contain"
                        />
                        <div className="flex justify-center">
                          <Button variant="outline" size="sm" onClick={() => setUploadedSignature(null)}>
                            <Trash2 className="h-4 w-4 ml-1" />
                            حذف
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4 text-center">
                        <Upload className="h-12 w-12 mx-auto text-gray-400" />
                        <p className="text-sm text-gray-500">
                          قم بتحميل صورة التوقيع الخاص بك
                        </p>
                        <Input
                          id="signature_upload"
                          type="file"
                          accept="image/*"
                          onChange={handleUploadSignature}
                          className="max-w-xs mx-auto"
                        />
                      </div>
                    )}
                  </div>

                  <p className="text-sm text-gray-500">
                    يجب أن تكون الصورة بتنسيق JPG أو PNG وبحجم أقل من 2 ميجابايت
                  </p>
                </TabsContent>
              </Tabs>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleCompleteSignature} disabled={loading}>
                {loading ? 'جاري التوقيع...' : 'توقيع المستند'}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}

// مكون Select المستخدم في الكود
function Select({ children, value, onValueChange }: { children: React.ReactNode, value: string, onValueChange: (value: string) => void }) {
  return (
    <div className="relative">
      <select
        value={value}
        onChange={(e) => onValueChange(e.target.value)}
        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
      >
        {children}
      </select>
    </div>
  );
}

function SelectTrigger({ children, id }: { children: React.ReactNode, id: string }) {
  return <div id={id}>{children}</div>;
}

function SelectValue({ children, placeholder }: { children: React.ReactNode, placeholder: string }) {
  return <>{children || placeholder}</>;
}

function SelectContent({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

function SelectItem({ children, value }: { children: React.ReactNode, value: string }) {
  return <option value={value}>{children}</option>;
}
