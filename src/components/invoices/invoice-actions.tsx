"use client";

import { useState } from 'react';
import { MoreHorizontal, Eye, Edit, Trash, Send, Download, CheckCircle } from 'lucide-react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

interface InvoiceActionsProps {
    invoice: {
        id: string;
        status: string;
    };
    onView?: (id: string) => void;
    onEdit?: (id: string) => void;
    onDelete?: (id: string) => void;
    onSend?: (id: string) => void;
    onMarkAsPaid?: (id: string) => void;
}

export default function InvoiceActions({
    invoice,
    onView,
    onEdit,
    onDelete,
    onSend,
    onMarkAsPaid
}: InvoiceActionsProps) {
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = () => {
        if (window.confirm('هل أنت متأكد من رغبتك في حذف هذه الفاتورة؟') && onDelete) {
            setIsDeleting(true);
            onDelete(invoice.id);
            setIsDeleting(false);
        }
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">فتح القائمة | Open Menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuLabel>إجراءات | Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onView?.(invoice.id)}>
                    <Eye className="ml-2 h-4 w-4" />
                    <span>عرض الفاتورة | View Invoice</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit?.(invoice.id)}>
                    <Edit className="ml-2 h-4 w-4" />
                    <span>تعديل الفاتورة | Edit Invoice</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onSend?.(invoice.id)}>
                    <Send className="ml-2 h-4 w-4" />
                    <span>إرسال بالبريد الإلكتروني | Send Email</span>
                </DropdownMenuItem>
                {invoice.status !== 'PAID' && (
                    <DropdownMenuItem onClick={() => onMarkAsPaid?.(invoice.id)}>
                        <CheckCircle className="ml-2 h-4 w-4" />
                        <span>تحديد كمدفوعة | Mark as Paid</span>
                    </DropdownMenuItem>
                )}
                <DropdownMenuItem>
                    <Download className="ml-2 h-4 w-4" />
                    <span>تنزيل PDF | Download PDF</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-red-600 focus:text-red-500"
                    disabled={isDeleting}
                >
                    <Trash className="ml-2 h-4 w-4" />
                    <span>{isDeleting ? 'جاري الحذف... | Deleting...' : 'حذف الفاتورة | Delete Invoice'}</span>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}