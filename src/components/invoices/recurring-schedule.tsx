'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Clock, AlertCircle, Info } from 'lucide-react';
import { format, addDays, addWeeks, addMonths, addYears } from 'date-fns';
import { ar } from 'date-fns/locale';

interface RecurringScheduleProps {
  initialFrequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  initialStartDate?: Date;
  initialEndDate?: Date | null;
  initialEndAfterOccurrences?: number | null;
  initialDayOfMonth?: number | null;
  initialDayOfWeek?: number | null;
  onScheduleChange?: (schedule: RecurringScheduleData) => void;
}

export interface RecurringScheduleData {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  startDate: Date;
  endType: 'never' | 'after_date' | 'after_occurrences';
  endDate: Date | null;
  endAfterOccurrences: number | null;
  dayOfMonth: number | null;
  dayOfWeek: number | null;
  monthsInterval: number;
  weeksInterval: number;
  daysInterval: number;
  nextDates: Date[];
}

export function RecurringSchedule({
  initialFrequency = 'monthly',
  initialStartDate = new Date(),
  initialEndDate = null,
  initialEndAfterOccurrences = null,
  initialDayOfMonth = null,
  initialDayOfWeek = null,
  onScheduleChange,
}: RecurringScheduleProps) {
  const [frequency, setFrequency] = useState<'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'>(initialFrequency);
  const [startDate, setStartDate] = useState<Date>(initialStartDate);
  const [endType, setEndType] = useState<'never' | 'after_date' | 'after_occurrences'>(
    initialEndDate ? 'after_date' : initialEndAfterOccurrences ? 'after_occurrences' : 'never'
  );
  const [endDate, setEndDate] = useState<Date | null>(initialEndDate);
  const [endAfterOccurrences, setEndAfterOccurrences] = useState<number | null>(initialEndAfterOccurrences || 12);
  const [dayOfMonth, setDayOfMonth] = useState<number | null>(initialDayOfMonth || new Date().getDate());
  const [dayOfWeek, setDayOfWeek] = useState<number | null>(initialDayOfWeek || new Date().getDay());
  const [monthsInterval, setMonthsInterval] = useState(1);
  const [weeksInterval, setWeeksInterval] = useState(1);
  const [daysInterval, setDaysInterval] = useState(1);
  const [activeTab, setActiveTab] = useState<'schedule' | 'preview'>('schedule');

  // حساب التواريخ القادمة بناءً على الجدول الزمني
  const calculateNextDates = (): Date[] => {
    const dates: Date[] = [];
    let currentDate = new Date(startDate);
    
    // تحديد عدد التواريخ المراد عرضها
    const maxDates = 10;
    
    // حساب التواريخ حسب التكرار
    for (let i = 0; i < maxDates; i++) {
      if (endType === 'after_date' && endDate && currentDate > endDate) {
        break;
      }
      
      if (endType === 'after_occurrences' && endAfterOccurrences && i >= endAfterOccurrences) {
        break;
      }
      
      dates.push(new Date(currentDate));
      
      // حساب التاريخ التالي حسب التكرار
      switch (frequency) {
        case 'daily':
          currentDate = addDays(currentDate, daysInterval);
          break;
        case 'weekly':
          currentDate = addWeeks(currentDate, weeksInterval);
          break;
        case 'monthly':
          currentDate = addMonths(currentDate, monthsInterval);
          // ضبط اليوم من الشهر إذا كان محدداً
          if (dayOfMonth) {
            currentDate.setDate(Math.min(dayOfMonth, new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()));
          }
          break;
        case 'quarterly':
          currentDate = addMonths(currentDate, 3 * monthsInterval);
          // ضبط اليوم من الشهر إذا كان محدداً
          if (dayOfMonth) {
            currentDate.setDate(Math.min(dayOfMonth, new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()));
          }
          break;
        case 'yearly':
          currentDate = addYears(currentDate, 1);
          break;
      }
    }
    
    return dates;
  };

  // حساب التواريخ القادمة
  const nextDates = calculateNextDates();

  // إرسال التغييرات إلى المكون الأب
  const handleChange = () => {
    if (onScheduleChange) {
      onScheduleChange({
        frequency,
        startDate,
        endType,
        endDate,
        endAfterOccurrences,
        dayOfMonth,
        dayOfWeek,
        monthsInterval,
        weeksInterval,
        daysInterval,
        nextDates,
      });
    }
  };

  // تحديث البيانات عند تغيير أي قيمة
  const updateSchedule = () => {
    handleChange();
  };

  // أيام الأسبوع بالعربية
  const weekDays = [
    'الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
  ];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>الجدول الزمني للفاتورة المتكررة</CardTitle>
        <CardDescription>
          قم بتحديد تكرار وتوقيت إنشاء الفواتير
        </CardDescription>
      </CardHeader>
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'schedule' | 'preview')}>
        <TabsList className="grid grid-cols-2 mx-4">
          <TabsTrigger value="schedule">الجدول الزمني</TabsTrigger>
          <TabsTrigger value="preview">معاينة التواريخ</TabsTrigger>
        </TabsList>
        <TabsContent value="schedule">
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="frequency">تكرار الفاتورة</Label>
                <Select
                  value={frequency}
                  onValueChange={(value) => {
                    setFrequency(value as any);
                    updateSchedule();
                  }}
                >
                  <SelectTrigger id="frequency">
                    <SelectValue placeholder="اختر التكرار" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">يومي</SelectItem>
                    <SelectItem value="weekly">أسبوعي</SelectItem>
                    <SelectItem value="monthly">شهري</SelectItem>
                    <SelectItem value="quarterly">ربع سنوي</SelectItem>
                    <SelectItem value="yearly">سنوي</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="start-date">تاريخ البدء</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-right"
                      id="start-date"
                    >
                      <CalendarIcon className="ml-2 h-4 w-4" />
                      {startDate ? format(startDate, 'PPP', { locale: ar }) : 'اختر تاريخ'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={(date) => {
                        if (date) {
                          setStartDate(date);
                          updateSchedule();
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            {frequency === 'daily' && (
              <div className="space-y-2">
                <Label htmlFor="days-interval">كل</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="days-interval"
                    type="number"
                    min={1}
                    max={30}
                    value={daysInterval}
                    onChange={(e) => {
                      setDaysInterval(parseInt(e.target.value) || 1);
                      updateSchedule();
                    }}
                    className="w-20"
                  />
                  <span>يوم</span>
                </div>
              </div>
            )}
            
            {frequency === 'weekly' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="weeks-interval">كل</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="weeks-interval"
                      type="number"
                      min={1}
                      max={12}
                      value={weeksInterval}
                      onChange={(e) => {
                        setWeeksInterval(parseInt(e.target.value) || 1);
                        updateSchedule();
                      }}
                      className="w-20"
                    />
                    <span>أسبوع</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>في يوم</Label>
                  <Select
                    value={dayOfWeek?.toString() || '0'}
                    onValueChange={(value) => {
                      setDayOfWeek(parseInt(value));
                      updateSchedule();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر يوم الأسبوع" />
                    </SelectTrigger>
                    <SelectContent>
                      {weekDays.map((day, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {day}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
            
            {(frequency === 'monthly' || frequency === 'quarterly') && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="months-interval">كل</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="months-interval"
                      type="number"
                      min={1}
                      max={12}
                      value={monthsInterval}
                      onChange={(e) => {
                        setMonthsInterval(parseInt(e.target.value) || 1);
                        updateSchedule();
                      }}
                      className="w-20"
                    />
                    <span>{frequency === 'monthly' ? 'شهر' : 'ربع سنة'}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>في اليوم</Label>
                  <Select
                    value={dayOfMonth?.toString() || '1'}
                    onValueChange={(value) => {
                      setDayOfMonth(parseInt(value));
                      updateSchedule();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر يوم الشهر" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                        <SelectItem key={day} value={day.toString()}>
                          {day}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
            
            <div className="space-y-2 pt-2 border-t">
              <Label>انتهاء التكرار</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="radio"
                    id="end-never"
                    name="end-type"
                    checked={endType === 'never'}
                    onChange={() => {
                      setEndType('never');
                      updateSchedule();
                    }}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="end-never" className="cursor-pointer">لا ينتهي أبداً</Label>
                </div>
                
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="radio"
                    id="end-after-occurrences"
                    name="end-type"
                    checked={endType === 'after_occurrences'}
                    onChange={() => {
                      setEndType('after_occurrences');
                      updateSchedule();
                    }}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="end-after-occurrences" className="cursor-pointer">بعد</Label>
                  <Input
                    type="number"
                    min={1}
                    max={100}
                    value={endAfterOccurrences || 12}
                    onChange={(e) => {
                      setEndAfterOccurrences(parseInt(e.target.value) || 12);
                      updateSchedule();
                    }}
                    disabled={endType !== 'after_occurrences'}
                    className="w-20"
                  />
                  <span>مرة</span>
                </div>
                
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="radio"
                    id="end-after-date"
                    name="end-type"
                    checked={endType === 'after_date'}
                    onChange={() => {
                      setEndType('after_date');
                      if (!endDate) {
                        const nextYear = new Date();
                        nextYear.setFullYear(nextYear.getFullYear() + 1);
                        setEndDate(nextYear);
                      }
                      updateSchedule();
                    }}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="end-after-date" className="cursor-pointer">في تاريخ</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="justify-start text-right"
                        disabled={endType !== 'after_date'}
                      >
                        <CalendarIcon className="ml-2 h-4 w-4" />
                        {endDate ? format(endDate, 'PPP', { locale: ar }) : 'اختر تاريخ'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={endDate || undefined}
                        onSelect={(date) => {
                          setEndDate(date);
                          updateSchedule();
                        }}
                        disabled={(date) => date < startDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </CardContent>
        </TabsContent>
        
        <TabsContent value="preview">
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-amber-600">
                <AlertCircle className="h-5 w-5" />
                <p className="text-sm">هذه معاينة للتواريخ المتوقعة للفواتير المتكررة بناءً على الإعدادات الحالية.</p>
              </div>
              
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-right">#</th>
                      <th className="px-4 py-2 text-right">التاريخ</th>
                      <th className="px-4 py-2 text-right">اليوم</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {nextDates.map((date, index) => (
                      <tr key={index} className={index === 0 ? 'bg-blue-50' : ''}>
                        <td className="px-4 py-2">{index + 1}</td>
                        <td className="px-4 py-2">{format(date, 'PPP', { locale: ar })}</td>
                        <td className="px-4 py-2">{format(date, 'EEEE', { locale: ar })}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {nextDates.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد تواريخ متوقعة. يرجى مراجعة إعدادات الجدول الزمني.
                </div>
              )}
              
              <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-md">
                <Info className="h-5 w-5 flex-shrink-0" />
                <p className="text-sm">
                  سيتم إنشاء الفاتورة الأولى في {nextDates.length > 0 ? format(nextDates[0], 'PPP', { locale: ar }) : 'تاريخ البدء'}.
                  {endType === 'never' && ' وستستمر بدون تاريخ انتهاء محدد.'}
                  {endType === 'after_occurrences' && endAfterOccurrences && ` وستنتهي بعد ${endAfterOccurrences} فاتورة.`}
                  {endType === 'after_date' && endDate && ` وستنتهي في ${format(endDate, 'PPP', { locale: ar })}.`}
                </p>
              </div>
            </div>
          </CardContent>
        </TabsContent>
      </Tabs>
      <CardFooter className="flex justify-between border-t pt-4">
        <Button variant="outline" onClick={() => setActiveTab(activeTab === 'schedule' ? 'preview' : 'schedule')}>
          {activeTab === 'schedule' ? 'معاينة التواريخ' : 'تعديل الجدول الزمني'}
        </Button>
        <Button onClick={updateSchedule}>تطبيق</Button>
      </CardFooter>
    </Card>
  );
}
