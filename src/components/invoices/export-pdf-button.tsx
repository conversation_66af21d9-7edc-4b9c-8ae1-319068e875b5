'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/enhanced-button';
import { FileDown } from 'lucide-react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { toast } from 'sonner';

interface ExportPDFButtonProps {
  invoiceId: string;
  invoiceNumber: string;
}

export function ExportPDFButton({ invoiceId, invoiceNumber }: Readonly<ExportPDFButtonProps>) {
  const [loading, setLoading] = useState(false);

  const handleExportPDF = async () => {
    try {
      setLoading(true);
      toast.info('جاري تحضير ملف PDF...');

      // فتح صفحة الطباعة في نافذة جديدة
      const printWindow = window.open(`/dashboard/invoices/${invoiceId}/print`, '_blank');

      if (!printWindow) {
        throw new Error('تم منع النوافذ المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
      }

      // انتظار تحميل الصفحة
      const waitForLoad = () => {
        return new Promise<void>((resolve) => {
          printWindow.onload = () => {
            // انتظار ثانيتين للتأكد من تحميل الصفحة بالكامل
            setTimeout(resolve, 2000);
          };
        });
      };

      await waitForLoad();

      // تحويل الصفحة إلى صورة
      const canvas = await html2canvas(printWindow.document.body, {
        scale: 2, // جودة أعلى
        useCORS: true,
        allowTaint: true,
        logging: false,
      });

      // إنشاء ملف PDF
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      // حساب النسبة بين عرض وارتفاع الصورة
      const imgWidth = 210; // عرض صفحة A4 بالملم
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // إضافة الصورة إلى ملف PDF
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      // حفظ ملف PDF
      pdf.save(`فاتورة_${invoiceNumber}.pdf`);

      // إغلاق النافذة المنبثقة
      printWindow.close();

      toast.success('تم تصدير الفاتورة بنجاح');
      setLoading(false);
    } catch (error) {
      console.error('خطأ في تصدير الفاتورة:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تصدير الفاتورة');
      setLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleExportPDF}
      disabled={loading}
      className="flex items-center gap-1"
    >
      <FileDown className="h-4 w-4" />
      <span>{loading ? 'جاري التصدير...' : 'تصدير PDF'}</span>
    </Button>
  );
}
