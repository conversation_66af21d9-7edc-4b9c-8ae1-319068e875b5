'use client';

import { useEffect, useState } from 'react';
import QRCode from 'qrcode';
import { createTaxInvoiceQRData, getTaxRegistrationNumber } from '@/lib/services/tax-service';
import { getCompanyInfo } from '@/lib/settings';

/**
 * واجهة خصائص مكون رمز الاستجابة السريعة للفاتورة الضريبية
 * Tax Invoice QR Component Props Interface
 */
interface TaxInvoiceQRProps {
  // الخصائص الأساسية - Basic properties
  readonly invoiceTimestamp: string;
  readonly invoiceTotal: number;
  readonly taxAmount: number;

  // الخصائص الاختيارية - Optional properties
  readonly sellerName?: string;
  readonly sellerTaxNumber?: string;
  readonly className?: string;
  readonly width?: number;
  readonly showBorder?: boolean;
  readonly showLabel?: boolean;
  readonly darkColor?: string;
  readonly lightColor?: string;
}

/**
 * مكون رمز الاستجابة السريعة للفاتورة الضريبية
 * Tax Invoice QR Code Component
 *
 * يقوم بإنشاء رمز الاستجابة السريعة وفقًا لمتطلبات الهيئة الاتحادية للضرائب في الإمارات
 * Creates QR code according to UAE Federal Tax Authority requirements
 */
export function TaxInvoiceQR({
  invoiceTimestamp,
  invoiceTotal,
  taxAmount,
  sellerName,
  sellerTaxNumber,
  className = '',
  width = 200,
  showBorder = true,
  showLabel = true,
  darkColor = '#0c4a6e',
  lightColor = '#ffffff'
}: Readonly<TaxInvoiceQRProps>) {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');

  useEffect(() => {
    const generateQRCode = async () => {
      try {
        // الحصول على معلومات الشركة ورقم التسجيل الضريبي إذا لم يتم توفيرها
        // Get company information and tax registration number if not provided
        const companyInfo = getCompanyInfo();
22        const finalSellerName = sellerName || companyInfo?.name || 'Unknown Seller';
        const trn = sellerTaxNumber || getTaxRegistrationNumber() || companyInfo?.taxNumber || 'Unknown TRN';

        // إنشاء بيانات رمز الاستجابة السريعة
        // Create QR code data
        const qrData = [
          finalSellerName,
          trn,
          invoiceTimestamp,
          invoiceTotal.toFixed(2),
          taxAmount.toFixed(2),
        ].join('\n');

        // توليد رمز QR
        // Generate QR code
        const dataUrl = await QRCode.toDataURL(qrData, {
          errorCorrectionLevel: 'M',
          margin: 1,
          width,
          color: {
            dark: darkColor,
            light: lightColor,
          },
        });

        setQrCodeDataUrl(dataUrl);
      } catch (error) {
        console.error('خطأ في إنشاء رمز QR:', error);
        setQrCodeDataUrl('error'); // Set error state
      }
    };

    generateQRCode();
  }, [invoiceTimestamp, invoiceTotal, taxAmount, sellerName, sellerTaxNumber, width, darkColor, lightColor]);

  if (qrCodeDataUrl === 'error') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <p className="text-red-500">خطأ في إنشاء رمز QR | Error generating QR Code</p>
      </div>
    );
  }

  if (!qrCodeDataUrl) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className={`tax-invoice-qr ${className}`}>
      <div className={`flex flex-col items-center ${showBorder ? 'border border-gray-200 p-2 rounded-md' : ''}`}>
        <img
          src={qrCodeDataUrl}
          alt="رمز QR للفاتورة الضريبية | Tax Invoice QR Code"
          className="object-contain"
        />

        {showLabel && (
          <div className="text-center mt-2 text-xs text-gray-500">
            <p>رمز الاستجابة السريعة للفاتورة الضريبية</p>
            <p>Tax Invoice QR Code</p>
          </div>
        )}
      </div>
    </div>
  );
}
