'use client';

/**
 * مكون بوابات الدفع - يوفر واجهة لمعالجة المدفوعات
 * Payment Gateways Component - Provides an interface for payment processing
 *
 * ملاحظة: هذا المكون تم استبداله بمكون PaymentForm الأكثر تطوراً
 * Note: This component has been replaced by the more advanced PaymentForm component
 */

import { PaymentForm } from './payment-form';

interface PaymentGatewaysProps {
  invoiceId: string;
  invoiceNumber: string;
  invoiceTotal: number;
  customerName?: string;
  onPaymentSuccess?: () => void;
}

/**
 * مكون بوابات الدفع - يوفر واجهة لمعالجة المدفوعات
 * Payment Gateways Component - Provides an interface for payment processing
 *
 * @deprecated استخدم مكون PaymentForm بدلاً من ذلك
 * @deprecated Use PaymentForm component instead
 */
export function PaymentGateways(props: Readonly<PaymentGatewaysProps>) {
  // استخدام المكون الجديد مع نفس الخصائص
  // Use the new component with the same props
  return <PaymentForm {...props} />;
}
