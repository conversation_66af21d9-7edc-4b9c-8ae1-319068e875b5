'use client';

/**
 * مكون نموذج الدفع - يوفر واجهة موحدة لمعالجة المدفوعات
 * Payment Form Component - Provides a unified interface for payment processing
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { CreditCard, DollarSign, CreditCardIcon, CheckCircle, Shield, AlertCircle, Building, Wallet } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { useI18n } from '@/lib/i18n';
import { 
  processPayment, 
  formatCardNumber, 
  formatExpiryDate,
  getBankAccountInfo,
  getCashPaymentInfo,
  PaymentMethodType
} from '@/lib/services/payment-service';

// واجهة خصائص نموذج الدفع
// Payment form props interface
interface PaymentFormProps {
  invoiceId: string;
  invoiceNumber: string;
  invoiceTotal: number;
  customerName?: string;
  onPaymentSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

/**
 * مكون نموذج الدفع - يوفر واجهة موحدة لمعالجة المدفوعات
 * Payment Form Component - Provides a unified interface for payment processing
 */
export function PaymentForm({
  invoiceId,
  invoiceNumber,
  invoiceTotal,
  customerName = '',
  onPaymentSuccess,
  onCancel,
  className,
}: PaymentFormProps) {
  const { t } = useI18n();
  const [activeTab, setActiveTab] = useState<PaymentMethodType>('credit_card');
  const [loading, setLoading] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [transactionId, setTransactionId] = useState('');

  // بيانات بطاقة الائتمان
  // Credit card data
  const [cardNumber, setCardNumber] = useState('');
  const [cardName, setCardName] = useState('');
  const [cardExpiry, setCardExpiry] = useState('');
  const [cardCvv, setCardCvv] = useState('');

  // بيانات PayPal
  // PayPal data
  const [paypalEmail, setPaypalEmail] = useState('');
  const [paypalPassword, setPaypalPassword] = useState('');

  // بيانات المحفظة الإلكترونية
  // E-wallet data
  const [walletSelected, setWalletSelected] = useState(false);

  // بيانات التحويل البنكي
  // Bank transfer data
  const [bankTransferReference, setBankTransferReference] = useState('');
  const [bankTransferDate, setBankTransferDate] = useState('');
  const [bankTransferProof, setBankTransferProof] = useState<File | null>(null);

  // معلومات الحساب البنكي
  // Bank account information
  const bankInfo = getBankAccountInfo();
  
  // معلومات الدفع النقدي
  // Cash payment information
  const cashInfo = getCashPaymentInfo();

  // معالجة الدفع
  // Process payment
  const handlePayment = async () => {
    try {
      setLoading(true);

      // إعداد بيانات الدفع
      // Prepare payment data
      const paymentData = {
        invoiceId,
        invoiceNumber,
        amount: invoiceTotal,
        paymentMethod: activeTab,
        creditCard: activeTab === 'credit_card' ? {
          cardNumber,
          cardName,
          cardExpiry,
          cardCvv
        } : undefined,
        bankTransfer: activeTab === 'bank_transfer' ? {
          reference: bankTransferReference,
          date: bankTransferDate,
          proof: bankTransferProof || undefined
        } : undefined,
        paypal: activeTab === 'paypal' ? {
          email: paypalEmail,
          password: paypalPassword
        } : undefined,
        walletSelected: activeTab === 'wallet' ? walletSelected : undefined
      };

      // معالجة الدفع
      // Process payment
      const result = await processPayment(paymentData);

      if (!result.success) {
        toast.error(result.error || t('payments.genericError') || 'Payment failed');
        setLoading(false);
        return;
      }

      // تعيين معرف المعاملة
      // Set transaction ID
      setTransactionId(result.transactionId || '');
      setPaymentSuccess(true);
      toast.success(t('payments.success') || 'Payment successful!');

      // استدعاء دالة النجاح إذا كانت موجودة
      // Call success callback if provided
      if (onPaymentSuccess) {
        onPaymentSuccess();
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      toast.error(t('payments.genericError') || 'An error occurred during payment');
    } finally {
      setLoading(false);
    }
  };

  // إغلاق النموذج
  // Close form
  const handleClose = () => {
    if (paymentSuccess) {
      // إعادة تحميل الصفحة بعد إغلاق النافذة في حالة نجاح الدفع
      // Reload page after closing the window in case of successful payment
      window.location.reload();
    }
    
    if (onCancel) {
      onCancel();
    }
  };

  // عرض شاشة نجاح الدفع
  // Display payment success screen
  if (paymentSuccess) {
    return (
      <div className="flex flex-col items-center justify-center py-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-xl font-bold mb-2">{t('payments.successTitle') || 'Payment Successful!'}</h2>
        <p className="text-gray-500 mb-4 text-center">
          {t('payments.successMessage', { invoiceNumber, amount: formatCurrency(invoiceTotal) }) || 
           `Invoice #${invoiceNumber} has been paid successfully for the amount of ${formatCurrency(invoiceTotal)}.`}
        </p>
        <div className="bg-gray-50 p-4 rounded-md w-full max-w-md mb-4">
          <div className="flex justify-between mb-2">
            <span className="text-gray-500">{t('payments.transactionId') || 'Transaction ID'}:</span>
            <span className="font-medium">{transactionId}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-500">{t('payments.paymentDate') || 'Payment Date'}:</span>
            <span className="font-medium">{new Date().toLocaleDateString()}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-500">{t('payments.paymentMethod') || 'Payment Method'}:</span>
            <span className="font-medium">
              {activeTab === 'credit_card' ? t('payments.methods.creditCard') || 'Credit Card' :
               activeTab === 'paypal' ? 'PayPal' :
               activeTab === 'wallet' ? t('payments.methods.wallet') || 'E-Wallet' : 
               activeTab === 'bank_transfer' ? t('payments.methods.bankTransfer') || 'Bank Transfer' :
               t('payments.methods.cash') || 'Cash'}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">{t('payments.amount') || 'Amount'}:</span>
            <span className="font-medium">{formatCurrency(invoiceTotal)}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleClose}>
            {t('common.close') || 'Close'}
          </Button>
          <Button variant="outline" onClick={() => window.open(`/dashboard/invoices/${invoiceId}/print`, '_blank')}>
            {t('payments.printReceipt') || 'Print Receipt'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Card className={`w-full max-w-3xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle>{t('payments.title') || 'Pay Invoice'}</CardTitle>
        <CardDescription>
          {t('payments.description', { invoiceNumber, customerName, amount: formatCurrency(invoiceTotal) }) || 
           `Invoice #${invoiceNumber} for ${customerName}, amount ${formatCurrency(invoiceTotal)}`}
        </CardDescription>
      </CardHeader>
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as PaymentMethodType)}>
        <TabsList className="grid grid-cols-4 mx-4">
          <TabsTrigger value="credit_card" className="flex flex-col items-center gap-1 py-3">
            <CreditCardIcon className="h-4 w-4" />
            <span className="text-xs">{t('payments.methods.creditCard') || 'Credit Card'}</span>
          </TabsTrigger>
          <TabsTrigger value="paypal" className="flex flex-col items-center gap-1 py-3">
            <div className="h-4 w-4 flex items-center justify-center">
              <span className="text-blue-600 font-bold text-xs">P</span>
            </div>
            <span className="text-xs">PayPal</span>
          </TabsTrigger>
          <TabsTrigger value="wallet" className="flex flex-col items-center gap-1 py-3">
            <DollarSign className="h-4 w-4" />
            <span className="text-xs">{t('payments.methods.wallet') || 'E-Wallet'}</span>
          </TabsTrigger>
          <TabsTrigger value="bank_transfer" className="flex flex-col items-center gap-1 py-3">
            <Building className="h-4 w-4" />
            <span className="text-xs">{t('payments.methods.bankTransfer') || 'Bank Transfer'}</span>
          </TabsTrigger>
        </TabsList>

        {/* بطاقة الائتمان */}
        {/* Credit Card */}
        <TabsContent value="credit_card">
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                <CreditCardIcon className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium">{t('payments.creditCardTitle') || 'Pay with Credit Card'}</h3>
              </div>
              <div className="flex gap-2">
                <div className="w-8 h-5 bg-gray-200 rounded"></div>
                <div className="w-8 h-5 bg-gray-200 rounded"></div>
                <div className="w-8 h-5 bg-gray-200 rounded"></div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="card_number">{t('payments.cardNumber') || 'Card Number'}</Label>
                <Input
                  id="card_number"
                  placeholder="0000 0000 0000 0000"
                  value={cardNumber}
                  onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                  maxLength={19}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="card_name">{t('payments.cardName') || 'Name on Card'}</Label>
                <Input
                  id="card_name"
                  placeholder={t('payments.cardNamePlaceholder') || 'Name as it appears on the card'}
                  value={cardName}
                  onChange={(e) => setCardName(e.target.value)}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="card_expiry">{t('payments.cardExpiry') || 'Expiry Date'}</Label>
                  <Input
                    id="card_expiry"
                    placeholder="MM/YY"
                    value={cardExpiry}
                    onChange={(e) => setCardExpiry(formatExpiryDate(e.target.value))}
                    maxLength={5}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="card_cvv">{t('payments.cardCvv') || 'CVV Code'}</Label>
                  <Input
                    id="card_cvv"
                    placeholder="123"
                    value={cardCvv}
                    onChange={(e) => setCardCvv(e.target.value.replace(/\D/g, ''))}
                    maxLength={3}
                    type="password"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2 text-amber-600 bg-amber-50 p-3 rounded-md">
              <Shield className="h-5 w-5 flex-shrink-0" />
              <p className="text-sm">
                {t('payments.securityMessage') || 'Your card data is secure and encrypted. We use Stripe as a secure payment provider.'}
              </p>
            </div>
          </CardContent>
        </TabsContent>

        {/* PayPal */}
        <TabsContent value="paypal">
          <CardContent className="space-y-4">
            <div className="flex justify-center mb-6">
              <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-2xl">PayPal</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="paypal_email">{t('payments.paypalEmail') || 'PayPal Email'}</Label>
                <Input
                  id="paypal_email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={paypalEmail}
                  onChange={(e) => setPaypalEmail(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="paypal_password">{t('payments.paypalPassword') || 'PayPal Password'}</Label>
                <Input
                  id="paypal_password"
                  type="password"
                  placeholder={t('payments.password') || 'Password'}
                  value={paypalPassword}
                  onChange={(e) => setPaypalPassword(e.target.value)}
                />
              </div>
            </div>

            <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-md">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <p className="text-sm">
                {t('payments.paypalNote') || 'Note: This is a simulation interface. In a real app, you would be redirected to PayPal to complete the payment.'}
              </p>
            </div>
          </CardContent>
        </TabsContent>

        {/* المحفظة الإلكترونية */}
        {/* E-Wallet */}
        <TabsContent value="wallet">
          <CardContent className="space-y-4">
            <div className="flex justify-center mb-6">
              <div className="flex gap-4">
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-black font-bold">Apple Pay</span>
                </div>
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-black font-bold">Google Pay</span>
                </div>
              </div>
            </div>

            <div className="border rounded-md p-4">
              <RadioGroup value={walletSelected ? 'confirm' : ''} onValueChange={(value) => setWalletSelected(value === 'confirm')}>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <RadioGroupItem value="confirm" id="confirm" />
                  <Label htmlFor="confirm" className="cursor-pointer">
                    {t('payments.walletConfirm') || 'Confirm payment using e-wallet'}
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="flex items-center gap-2 text-amber-600 bg-amber-50 p-3 rounded-md">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <p className="text-sm">
                {t('payments.walletNote') || 'Note: This is a simulation interface. In a real app, the e-wallet window would open to complete the payment.'}
              </p>
            </div>
          </CardContent>
        </TabsContent>

        {/* التحويل البنكي */}
        {/* Bank Transfer */}
        <TabsContent value="bank_transfer">
          <CardContent className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-md mb-4">
              <h3 className="font-medium mb-2">{t('payments.bankAccountInfo') || 'Bank Account Information'}</h3>
              <p className="text-sm mb-1">{t('payments.bankName') || 'Bank Name'}: {bankInfo.bankName}</p>
              <p className="text-sm mb-1">{t('payments.accountNumber') || 'Account Number'}: {bankInfo.accountNumber}</p>
              <p className="text-sm mb-1">IBAN: {bankInfo.iban}</p>
              <p className="text-sm mb-1">{t('payments.beneficiaryName') || 'Beneficiary Name'}: {bankInfo.beneficiaryName}</p>
              <p className="text-sm mt-3 text-gray-500">
                {t('payments.transferInstructions', { amount: formatCurrency(invoiceTotal) }) || 
                 `Please transfer the amount ${formatCurrency(invoiceTotal)} to the account mentioned above.`}
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="transfer_reference">{t('payments.transferReference') || 'Transfer Reference Number'}</Label>
                <Input
                  id="transfer_reference"
                  placeholder={t('payments.transferReferencePlaceholder') || 'Enter the transfer reference number'}
                  value={bankTransferReference}
                  onChange={(e) => setBankTransferReference(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="transfer_date">{t('payments.transferDate') || 'Transfer Date'}</Label>
                <Input
                  id="transfer_date"
                  type="date"
                  value={bankTransferDate}
                  onChange={(e) => setBankTransferDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="transfer_proof">{t('payments.transferProof') || 'Transfer Proof'} ({t('common.optional') || 'Optional'})</Label>
                <Input
                  id="transfer_proof"
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const files = e.target.files;
                    if (files && files.length > 0) {
                      setBankTransferProof(files[0]);
                    }
                  }}
                />
                <p className="text-xs text-gray-500">
                  {t('payments.transferProofNote') || 'You can upload an image or PDF file of the transfer receipt'}
                </p>
              </div>
            </div>
          </CardContent>
        </TabsContent>

        <CardFooter className="border-t pt-4">
          <div className="flex justify-between w-full">
            <Button variant="outline" onClick={handleClose}>
              {t('common.cancel') || 'Cancel'}
            </Button>
            <Button onClick={handlePayment} disabled={loading}>
              {loading ? 
                (t('payments.processing') || 'Processing...') : 
                (t('payments.payAmount', { amount: formatCurrency(invoiceTotal) }) || `Pay ${formatCurrency(invoiceTotal)}`)}
            </Button>
          </div>
        </CardFooter>
      </Tabs>
    </Card>
  );
}
