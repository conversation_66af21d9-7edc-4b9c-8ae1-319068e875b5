'use client';

/**
 * مكون مربع حوار الدفع - يوفر واجهة منبثقة لمعالجة المدفوعات
 * Payment Dialog Component - Provides a popup interface for payment processing
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CreditCardIcon } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useI18n } from '@/lib/i18n';
import { PaymentForm } from './payment-form';

// واجهة خصائص مربع حوار الدفع
// Payment dialog props interface
interface PaymentDialogProps {
  invoiceId: string;
  invoiceNumber: string;
  invoiceTotal: number;
  customerName?: string;
  buttonLabel?: string;
  buttonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  buttonSize?: 'default' | 'sm' | 'lg' | 'icon';
  buttonClassName?: string;
  onPaymentSuccess?: () => void;
}

/**
 * مكون مربع حوار الدفع - يوفر واجهة منبثقة لمعالجة المدفوعات
 * Payment Dialog Component - Provides a popup interface for payment processing
 */
export function PaymentDialog({
  invoiceId,
  invoiceNumber,
  invoiceTotal,
  customerName = '',
  buttonLabel,
  buttonVariant = 'default',
  buttonSize = 'sm',
  buttonClassName = '',
  onPaymentSuccess,
}: PaymentDialogProps) {
  const { t } = useI18n();
  const [open, setOpen] = useState(false);

  // معالجة نجاح الدفع
  // Handle payment success
  const handlePaymentSuccess = () => {
    if (onPaymentSuccess) {
      onPaymentSuccess();
    }

    // إغلاق مربع الحوار بعد فترة قصيرة
    // Close dialog after a short delay
    setTimeout(() => {
      setOpen(false);
    }, 5000);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          className={`flex items-center gap-1 ${buttonClassName}`}
        >
          <CreditCardIcon className="h-4 w-4 mr-1" />
          <span>{buttonLabel || t('payments.payInvoice') || 'Pay Invoice'}</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] p-0">
        <PaymentForm
          invoiceId={invoiceId}
          invoiceNumber={invoiceNumber}
          invoiceTotal={invoiceTotal}
          customerName={customerName}
          onPaymentSuccess={handlePaymentSuccess}
          onCancel={() => setOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
