'use client';

import { useState, useEffect } from 'react';
import { useTauriAPI } from '@/components/providers/tauri-api-provider';
import { Button } from '@/components/ui/button';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/toast';
import { useTranslation } from '@/lib/i18n';
import { User, Role } from '@/lib/tauri-api';

/**
 * مكون جدول المستخدمين
 * Users table component
 */
export function UsersTable() {
  const { api, reload } = useTauriAPI();
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    username: '',
    display_name: '',
    roles: [] as string[],
  });
  const { toast } = useToast();
  const { t } = useTranslation();

  // تحميل المستخدمين والأدوار
  // Load users and roles
  const loadData = async () => {
    setLoading(true);
    try {
      const [usersData, rolesData] = await Promise.all([
        api.getUsers(),
        api.getRoles(),
      ]);
      setUsers(usersData);
      setRoles(rolesData);
    } catch (error) {
      console.error('Error loading users data:', error);
      toast({
        title: t('users.loadError'),
        description: String(error),
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // فتح مربع حوار إضافة مستخدم جديد
  // Open add user dialog
  const handleAddUser = () => {
    setEditingUser(null);
    setFormData({
      username: '',
      display_name: '',
      roles: [],
    });
    setDialogOpen(true);
  };

  // فتح مربع حوار تعديل مستخدم
  // Open edit user dialog
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setFormData({
      username: user.username,
      display_name: user.display_name,
      roles: user.roles,
    });
    setDialogOpen(true);
  };

  // تحديث قيمة الحقل
  // Update field value
  const handleChange = (field: string, value: string | string[]) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // تبديل تحديد الدور
  // Toggle role selection
  const toggleRole = (roleName: string) => {
    const roles = [...formData.roles];
    const index = roles.indexOf(roleName);

    if (index === -1) {
      roles.push(roleName);
    } else {
      roles.splice(index, 1);
    }

    handleChange('roles', roles);
  };

  // حفظ المستخدم
  // Save user
  const handleSave = async () => {
    try {
      if (editingUser) {
        // تحديث مستخدم موجود
        // Update existing user
        await api.updateUser(editingUser.id, formData.display_name, formData.roles);
        toast({
          title: t('users.updateSuccess'),
          description: t('users.updateSuccessDescription'),
          type: 'success',
        });
      } else {
        // إضافة مستخدم جديد
        // Add new user
        await api.addUser(formData.username, formData.display_name, formData.roles);
        toast({
          title: t('users.addSuccess'),
          description: t('users.addSuccessDescription'),
          type: 'success',
        });
      }

      // إغلاق مربع الحوار وإعادة تحميل البيانات
      // Close dialog and reload data
      setDialogOpen(false);
      await loadData();
    } catch (error) {
      console.error('Error saving user:', error);
      toast({
        title: editingUser ? t('users.updateError') : t('users.addError'),
        description: String(error),
        type: 'error',
      });
    }
  };

  // حذف مستخدم
  // Delete user
  const handleDeleteUser = async (user: User) => {
    if (!confirm(t('users.confirmDelete'))) {
      return;
    }

    try {
      await api.deleteUser(user.id);
      toast({
        title: t('users.deleteSuccess'),
        description: t('users.deleteSuccessDescription'),
        type: 'success',
      });
      await loadData();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: t('users.deleteError'),
        description: String(error),
        type: 'error',
      });
    }
  };

  if (loading && users.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-40">
            <p>{t('common.loading')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{t('users.title')}</CardTitle>
            <CardDescription>{t('users.description')}</CardDescription>
          </div>
          <Button onClick={handleAddUser}>{t('users.addUser')}</Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/50 font-medium">
                <th className="py-3 px-4 text-right">{t('users.username')}</th>
                <th className="py-3 px-4 text-right">{t('users.displayName')}</th>
                <th className="py-3 px-4 text-right">{t('users.roles')}</th>
                <th className="py-3 px-4 text-center">{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id} className="border-b">
                  <td className="py-3 px-4">{user.username}</td>
                  <td className="py-3 px-4">{user.display_name}</td>
                  <td className="py-3 px-4">
                    <div className="flex flex-wrap gap-1">
                      {user.roles.map((role) => (
                        <span
                          key={role}
                          className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold"
                        >
                          {role}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex justify-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditUser(user)}
                      >
                        {t('common.edit')}
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteUser(user)}
                      >
                        {t('common.delete')}
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>

      {/* مربع حوار إضافة/تعديل مستخدم */}
      {/* Add/Edit user dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingUser ? t('users.editUser') : t('users.addUser')}
            </DialogTitle>
            <DialogDescription>
              {editingUser
                ? t('users.editUserDescription')
                : t('users.addUserDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="username">{t('users.username')}</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => handleChange('username', e.target.value)}
                disabled={!!editingUser}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="display_name">{t('users.displayName')}</Label>
              <Input
                id="display_name"
                value={formData.display_name}
                onChange={(e) => handleChange('display_name', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>{t('users.roles')}</Label>
              <div className="grid grid-cols-2 gap-2">
                {roles.map((role) => (
                  <div key={role.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`role-${role.id}`}
                      checked={formData.roles.includes(role.name)}
                      onChange={() => toggleRole(role.name)}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <label htmlFor={`role-${role.id}`} className="text-sm">
                      {role.description || role.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleSave}>
              {editingUser ? t('common.update') : t('common.add')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
