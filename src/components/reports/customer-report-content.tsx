'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Download } from 'lucide-react'
import {
    <PERSON><PERSON><PERSON>,
    Pie,
    Cell,
    ResponsiveContainer,
    Legend,
    Tooltip
} from 'recharts'

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#9370DB'];

// بيانات تجريبية لتوزيع العملاء
const customerData = [
    { name: 'عملاء جدد', value: 25 },
    { name: 'عملاء منتظمون', value: 45 },
    { name: 'عملاء متقطعون', value: 20 },
    { name: 'عملاء غير نشطين', value: 10 },
];

export function CustomerReportContent() {

    return (
        <>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>توزيع العملاء</CardTitle>
                        <CardDescription>
                            تصنيف العملاء حسب نشاطهم
                        </CardDescription>
                    </div>
                    <div>
                        <Button variant="outline" size="sm">
                            <Download className="ml-2 h-4 w-4" />
                            تصدير
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                                <Pie
                                    data={customerData}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                    outerRadius={100}
                                    fill="#8884d8"
                                    dataKey="value"
                                >
                                    {customerData.map((entry) => (
                                        <Cell key={entry.name} fill={COLORS[customerData.indexOf(entry) % COLORS.length]} />
                                    ))}
                                </Pie>
                                <Tooltip />
                                <Legend />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>أفضل العملاء</CardTitle>
                    <CardDescription>
                        العملاء الأكثر شراءً حسب قيمة المبيعات
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                            <thead>
                                <tr className="border-b">
                                    <th className="py-2 px-4 text-right font-medium text-gray-500">العميل</th>
                                    <th className="py-2 px-4 text-right font-medium text-gray-500">عدد الفواتير</th>
                                    <th className="py-2 px-4 text-right font-medium text-gray-500">إجمالي المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr className="border-b">
                                    <td className="py-2 px-4">شركة الرياض التجارية</td>
                                    <td className="py-2 px-4">12</td>
                                    <td className="py-2 px-4">٤٨٬٦٠٠ ر.س</td>
                                </tr>
                                <tr className="border-b">
                                    <td className="py-2 px-4">مؤسسة المدينة</td>
                                    <td className="py-2 px-4">8</td>
                                    <td className="py-2 px-4">٣٥٬٢٠٠ ر.س</td>
                                </tr>
                                <tr className="border-b">
                                    <td className="py-2 px-4">شركة جدة المحدودة</td>
                                    <td className="py-2 px-4">5</td>
                                    <td className="py-2 px-4">٢٢٬٨٠٠ ر.س</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </CardContent>
            </Card>
        </>
    )
}
