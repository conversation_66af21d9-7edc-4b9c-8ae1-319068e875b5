'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    BarChart,
    Bar
} from 'recharts'
import { Download, Filter } from 'lucide-react'

// بيانات تجريبية للرسوم البيانية
const salesData = [
    { month: 'يناير', amount: 12000 },
    { month: 'فبراير', amount: 19000 },
    { month: 'مارس', amount: 15000 },
    { month: 'أبريل', amount: 22000 },
    { month: 'مايو', amount: 18000 },
    { month: 'يونيو', amount: 25000 },
];

export function SalesReportContent() {
    const [dateRange, setDateRange] = useState('year')
    const [reportData, setReportData] = useState<any[]>([])
    const [summary, setSummary] = useState({
        totalSales: 0,
        averageSale: 0,
        invoiceCount: 0
    })

    useEffect(() => {
        // محاكاة طلب API لجلب بيانات التقرير
        setTimeout(() => {
            setReportData(salesData)
            setSummary({
                totalSales: salesData.reduce((sum, item) => sum + item.amount, 0),
                averageSale: salesData.reduce((sum, item) => sum + item.amount, 0) / salesData.length,
                invoiceCount: 42
            })
        }, 500)
    }, [dateRange])

    const handleExport = () => {
        // منطق تصدير التقرير إلى Excel أو PDF
        alert('سيتم تنفيذ وظيفة التصدير في الإصدار القادم')
    }

    return (
        <>
            <div className="grid gap-4 md:grid-cols-3 mb-4">
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-500">
                            إجمالي المبيعات
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(summary.totalSales)}
                        </div>
                        <p className="text-xs text-gray-500">
                            خلال الفترة المحددة
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-500">
                            متوسط قيمة الفاتورة
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(summary.averageSale)}
                        </div>
                        <p className="text-xs text-gray-500">
                            لكل فاتورة
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-500">
                            عدد الفواتير
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {summary.invoiceCount}
                        </div>
                        <p className="text-xs text-gray-500">
                            فاتورة صادرة
                        </p>
                    </CardContent>
                </Card>
            </div>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>تقرير المبيعات</CardTitle>
                        <CardDescription>
                            عرض إجماليات المبيعات خلال فترة زمنية محددة
                        </CardDescription>
                    </div>
                    <div className="flex space-x-2 space-x-reverse">
                        <Button variant="outline" size="sm" onClick={handleExport}>
                            <Download className="ml-2 h-4 w-4" />
                            تصدير
                        </Button>
                        <Button variant="outline" size="sm">
                            <Filter className="ml-2 h-4 w-4" />
                            تصفية
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4">
                        <div className="flex justify-center mb-4">
                            <div className="flex gap-2 p-1 bg-gray-100 rounded-md">
                                <Button
                                    variant={dateRange === 'month' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => setDateRange('month')}
                                >
                                    شهري
                                </Button>
                                <Button
                                    variant={dateRange === 'quarter' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => setDateRange('quarter')}
                                >
                                    ربع سنوي
                                </Button>
                                <Button
                                    variant={dateRange === 'year' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => setDateRange('year')}
                                >
                                    سنوي
                                </Button>
                            </div>
                        </div>

                        <div className="h-[300px]">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart data={reportData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="month" />
                                    <YAxis />
                                    <Tooltip
                                        formatter={(value) =>
                                            new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(Number(value))
                                        }
                                    />
                                    <Legend />
                                    <Bar dataKey="amount" name="المبيعات" fill="#3B82F6" />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </>
    )
}
