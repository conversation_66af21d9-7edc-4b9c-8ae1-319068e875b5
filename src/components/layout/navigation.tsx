'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { useI18n } from '@/lib/i18n';

const navigationItems = [
  {
    name: 'nav.dashboard',
    href: '/dashboard',
    icon: '🏠'
  },
  {
    name: 'nav.customers',
    href: '/dashboard/customers',
    icon: '👥'
  },
  {
    name: 'nav.products',
    href: '/dashboard/products',
    icon: '📦'
  },
  {
    name: 'nav.invoices',
    href: '/dashboard/invoices',
    icon: '📄'
  },
  {
    name: 'nav.reports',
    href: '/dashboard/reports',
    icon: '📊'
  },
  {
    name: 'nav.settings',
    href: '/dashboard/settings',
    icon: '⚙️'
  }
];

export function Navigation() {
  const pathname = usePathname();
  const { data: session } = useSession();
  const { t } = useI18n();

  if (!session) {
    return null;
  }

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <h1 className="text-xl font-bold text-gray-900">أمين بلس</h1>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navigationItems.map((item) => {
                const isActive = pathname === item.href ||
                  (item.href !== '/dashboard' && pathname.startsWith(item.href));

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                      isActive
                        ? 'border-blue-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    }`}
                  >
                    <span className="ml-2">{item.icon}</span>
                    {t(item.name)}
                  </Link>
                );
              })}
            </div>
          </div>

          <div className="flex items-center">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                مرحباً، {session.user?.name || session.user?.email}
              </span>
              <button
                onClick={() => signOut({ callbackUrl: '/auth/login' })}
                className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className="sm:hidden">
        <div className="pt-2 pb-3 space-y-1">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href ||
              (item.href !== '/dashboard' && pathname.startsWith(item.href));

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${
                  isActive
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'
                }`}
              >
                <span className="ml-2">{item.icon}</span>
                {t(item.name)}
              </Link>
            );
          })}
        </div>
      </div>
    </nav>
  );
}
