'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import {
    LayoutDashboard,
    Users,
    FileText,
    Settings,
    CreditCard,
    Package,
    LogOut,
    Monitor,
    BarChart3,
    ChevronRight,
    User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui';
import AppLogo from '@/components/ui/app-logo';
import { useI18n } from '@/lib/i18n';

interface SidebarProps {
    readonly isOpen: boolean;
}

export default function Sidebar({ isOpen }: SidebarProps) {
    const pathname = usePathname();
    const { t, language } = useI18n();
    const [hoveredItem, setHoveredItem] = useState<string | null>(null);

    // قائمة الروابط المحسنة - Enhanced navigation links
    const navItems = [
        {
            title: language === 'ar' ? 'لوحة التحكم' : 'Dashboard',
            href: '/dashboard',
            icon: LayoutDashboard,
            badge: null,
            description: language === 'ar' ? 'نظرة عامة على النظام' : 'System overview'
        },
        {
            title: language === 'ar' ? 'العملاء' : 'Customers',
            href: '/dashboard/customers',
            icon: Users,
            badge: '124',
            description: language === 'ar' ? 'إدارة العملاء' : 'Manage customers'
        },
        {
            title: language === 'ar' ? 'المنتجات' : 'Products',
            href: '/dashboard/products',
            icon: Package,
            badge: '89',
            description: language === 'ar' ? 'إدارة المنتجات' : 'Manage products'
        },
        {
            title: language === 'ar' ? 'الفواتير' : 'Invoices',
            href: '/dashboard/invoices',
            icon: FileText,
            badge: '56',
            description: language === 'ar' ? 'إدارة الفواتير' : 'Manage invoices'
        },
        {
            title: language === 'ar' ? 'التقارير' : 'Reports',
            href: '/dashboard/reports',
            icon: BarChart3,
            badge: null,
            description: language === 'ar' ? 'التقارير والإحصائيات' : 'Reports & analytics'
        },
        {
            title: language === 'ar' ? 'الإعدادات' : 'Settings',
            href: '/dashboard/settings',
            icon: Settings,
            badge: null,
            description: language === 'ar' ? 'إعدادات النظام' : 'System settings'
        },
    ];

    const isActive = (path: string) => {
        // للتحقق من أن المسار الحالي هو المسار النشط
        // Check if current path is the active path
        if (path === '/dashboard' && pathname === '/dashboard') {
            return true;
        }

        // للتحقق من أن المسار الحالي هو أحد المسارات الفرعية للمسار الرئيسي
        // Check if current path is a subpath of the main path
        return path !== '/dashboard' && pathname.startsWith(path);
    };

    return (
        <aside
            className={cn(
                `fixed inset-y-0 ${language === 'ar' ? 'right-0' : 'left-0'} z-50 flex h-full w-72 flex-col ${language === 'ar' ? 'border-l' : 'border-r'} border-border bg-card/95 backdrop-blur-sm transition-all-smooth shadow-lg`,
                isOpen ? "translate-x-0" : language === 'ar' ? "translate-x-full" : "-translate-x-full"
            )}
        >
            {/* رأس الشريط الجانبي المحسن - Enhanced sidebar header */}
            <div className="flex h-16 items-center justify-between border-b border-border px-6 bg-gradient-to-r from-primary/5 to-primary/10">
                <AppLogo />
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse-soft" title="متصل" />
            </div>

            {/* قائمة التنقل المحسنة - Enhanced navigation menu */}
            <nav className="flex-1 overflow-y-auto p-4 space-y-2">
                {navItems.map((item, index) => (
                    <div key={item.href} className={`animate-fade-in-up animate-delay-${(index + 1) * 100}`}>
                        <Link
                            href={item.href}
                            className={cn(
                                "group flex items-center rounded-xl px-4 py-3 text-sm font-medium transition-all-smooth hover-lift relative overflow-hidden",
                                isActive(item.href)
                                    ? "bg-primary text-primary-foreground shadow-md"
                                    : "text-foreground hover:bg-accent hover:text-accent-foreground"
                            )}
                            onMouseEnter={() => setHoveredItem(item.href)}
                            onMouseLeave={() => setHoveredItem(null)}
                        >
                            {/* خلفية متحركة للعنصر النشط */}
                            {isActive(item.href) && (
                                <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 animate-fade-in" />
                            )}

                            <div className="relative flex items-center w-full">
                                <item.icon className={cn(
                                    `${language === 'ar' ? 'ml-3' : 'mr-3'} h-5 w-5 transition-transform-smooth`,
                                    hoveredItem === item.href && "scale-110"
                                )} />

                                <div className="flex-1">
                                    <div className="flex items-center justify-between">
                                        <span className="font-medium">{item.title}</span>
                                        {item.badge && (
                                            <span className={cn(
                                                "px-2 py-0.5 text-xs rounded-full font-medium",
                                                isActive(item.href)
                                                    ? "bg-white/20 text-white"
                                                    : "bg-primary/10 text-primary"
                                            )}>
                                                {item.badge}
                                            </span>
                                        )}
                                    </div>
                                    {hoveredItem === item.href && (
                                        <p className="text-xs opacity-75 mt-1 animate-fade-in">
                                            {item.description}
                                        </p>
                                    )}
                                </div>

                                <ChevronRight className={cn(
                                    `h-4 w-4 transition-transform-smooth opacity-0 group-hover:opacity-100`,
                                    language === 'ar' && "rotate-180",
                                    isActive(item.href) && "opacity-100"
                                )} />
                            </div>
                        </Link>
                    </div>
                ))}
            </nav>

            {/* تذييل الشريط الجانبي المحسن - Enhanced sidebar footer */}
            <div className="border-t border-border p-4 bg-gradient-to-t from-muted/50 to-transparent">
                <div className="mb-4 p-3 rounded-xl bg-card border border-border hover-lift transition-all-smooth">
                    <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center text-white font-semibold text-sm">
                            <User className="h-5 w-5" />
                        </div>
                        <div className={`${language === 'ar' ? 'mr-3' : 'ml-3'} flex-1`}>
                            <p className="text-sm font-medium text-foreground">
                                {language === 'ar' ? 'مستخدم تجريبي' : 'Test User'}
                            </p>
                            <p className="text-xs text-muted-foreground"><EMAIL></p>
                        </div>
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse-soft" />
                    </div>
                </div>

                <Button
                    variant="outline"
                    className="w-full justify-start hover-lift group"
                    onClick={() => {
                        // تسجيل الخروج
                        window.location.href = '/api/auth/signout';
                    }}
                >
                    <LogOut className={cn(
                        `${language === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 transition-transform-smooth group-hover:scale-110`
                    )} />
                    <span>{language === 'ar' ? 'تسجيل الخروج' : 'Sign Out'}</span>
                </Button>
            </div>
        </aside>
    );
}
