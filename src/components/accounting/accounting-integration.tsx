'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import {Check, RefreshCw, Settings, Shield, Unlink} from 'lucide-react';

interface AccountingIntegrationProps {
  onConnect?: (provider: string, credentials: any) => void;
  onDisconnect?: () => void;
  onSync?: () => void;
}

export function AccountingIntegration({
  onConnect,
  onDisconnect,
  onSync,
}: AccountingIntegrationProps) {
  const [activeTab, setActiveTab] = useState('providers');
  const [selectedProvider, setSelectedProvider] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [syncSettings, setSyncSettings] = useState({
    autoSync: true,
    syncInvoices: true,
    syncPayments: true,
    syncCustomers: true,
    syncProducts: true,
    syncTaxes: true,
  });
  const [credentials, setCredentials] = useState({
    apiKey: '',
    clientId: '',
    clientSecret: '',
    username: '',
    password: '',
  });
  const [lastSyncDate, setLastSyncDate] = useState<string | null>(null);

  const handleConnect = async () => {
    if (!selectedProvider) {
      toast.error('يرجى اختيار مزود خدمة المحاسبة');
      return;
    }

    if (selectedProvider === 'quickbooks' && (!credentials.clientId || !credentials.clientSecret)) {
      toast.error('يرجى إدخال معرف العميل والرمز السري');
      return;
    }

    if (selectedProvider === 'xero' && !credentials.apiKey) {
      toast.error('يرجى إدخال مفتاح API');
      return;
    }

    if (selectedProvider === 'zohobooks' && (!credentials.username || !credentials.password)) {
      toast.error('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    try {
      setLoading(true);

      // محاكاة الاتصال بمزود خدمة المحاسبة
      await new Promise(resolve => setTimeout(resolve, 2000));

      setIsConnected(true);
      setLastSyncDate(new Date().toISOString());
      toast.success(`تم الاتصال بنجاح بـ ${getProviderName(selectedProvider)}`);

      if (onConnect) {
        onConnect(selectedProvider, credentials);
      }

      // الانتقال إلى علامة التبويب "الإعدادات" بعد الاتصال
      setActiveTab('settings');
    } catch (error) {
      console.error('خطأ في الاتصال:', error);
      toast.error('حدث خطأ أثناء الاتصال بمزود خدمة المحاسبة');
    } finally {
      setLoading(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      setLoading(true);

      // محاكاة قطع الاتصال بمزود خدمة المحاسبة
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsConnected(false);
      setSelectedProvider('');
      setCredentials({
        apiKey: '',
        clientId: '',
        clientSecret: '',
        username: '',
        password: '',
      });

      toast.success('تم قطع الاتصال بنجاح');

      if (onDisconnect) {
        onDisconnect();
      }

      // الانتقال إلى علامة التبويب "مزودي الخدمة" بعد قطع الاتصال
      setActiveTab('providers');
    } catch (error) {
      console.error('خطأ في قطع الاتصال:', error);
      toast.error('حدث خطأ أثناء قطع الاتصال بمزود خدمة المحاسبة');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async () => {
    try {
      setLoading(true);

      // محاكاة المزامنة مع مزود خدمة المحاسبة
      await new Promise(resolve => setTimeout(resolve, 3000));

      setLastSyncDate(new Date().toISOString());
      toast.success('تمت المزامنة بنجاح');

      if (onSync) {
        onSync();
      }
    } catch (error) {
      console.error('خطأ في المزامنة:', error);
      toast.error('حدث خطأ أثناء المزامنة مع مزود خدمة المحاسبة');
    } finally {
      setLoading(false);
    }
  };

  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'quickbooks': return 'QuickBooks';
      case 'xero': return 'Xero';
      case 'zohobooks': return 'Zoho Books';
      case 'sage': return 'Sage';
      case 'wave': return 'Wave';
      default: return provider;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('ar-AE', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>التكامل مع نظام المحاسبة</CardTitle>
        <CardDescription>
          قم بربط نظام الفواتير مع نظام المحاسبة الخاص بك لمزامنة البيانات تلقائياً
        </CardDescription>
      </CardHeader>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mx-4">
          <TabsTrigger value="providers">مزودي الخدمة</TabsTrigger>
          <TabsTrigger value="settings" disabled={!isConnected}>الإعدادات</TabsTrigger>
          <TabsTrigger value="history" disabled={!isConnected}>سجل المزامنة</TabsTrigger>
        </TabsList>

        <TabsContent value="providers">
          <CardContent className="space-y-4">
            {isConnected ? (
              <div className="bg-green-50 p-4 rounded-md flex items-start gap-3">
                <Check className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-green-800">متصل بنجاح</h3>
                  <p className="text-sm text-green-700">
                    أنت متصل حالياً بـ {getProviderName(selectedProvider)}.
                    {lastSyncDate && ` آخر مزامنة: ${formatDate(lastSyncDate)}`}
                  </p>
                  <div className="mt-3 flex gap-2">
                    <Button variant="outline" size="sm" onClick={handleDisconnect} disabled={loading}>
                      <Unlink className="h-4 w-4 ml-1" />
                      قطع الاتصال
                    </Button>
                    <Button size="sm" onClick={handleSync} disabled={loading}>
                      <RefreshCw className="h-4 w-4 ml-1" />
                      مزامنة الآن
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <>
                <div className="space-y-2">
                  <Label htmlFor="provider">اختر مزود خدمة المحاسبة</Label>
                  <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                    <SelectTrigger id="provider">
                      <SelectValue placeholder="اختر مزود الخدمة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="quickbooks">QuickBooks</SelectItem>
                      <SelectItem value="xero">Xero</SelectItem>
                      <SelectItem value="zohobooks">Zoho Books</SelectItem>
                      <SelectItem value="sage">Sage</SelectItem>
                      <SelectItem value="wave">Wave</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {selectedProvider && (
                  <div className="space-y-4 p-4 border rounded-md">
                    <h3 className="font-medium">بيانات الاتصال بـ {getProviderName(selectedProvider)}</h3>

                    {selectedProvider === 'quickbooks' && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="client-id">معرف العميل (Client ID)</Label>
                          <Input
                            id="client-id"
                            value={credentials.clientId}
                            onChange={(e) => setCredentials({ ...credentials, clientId: e.target.value })}
                            placeholder="أدخل معرف العميل"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="client-secret">الرمز السري (Client Secret)</Label>
                          <Input
                            id="client-secret"
                            type="password"
                            value={credentials.clientSecret}
                            onChange={(e) => setCredentials({ ...credentials, clientSecret: e.target.value })}
                            placeholder="أدخل الرمز السري"
                          />
                        </div>
                      </>
                    )}

                    {selectedProvider === 'xero' && (
                      <div className="space-y-2">
                        <Label htmlFor="api-key">مفتاح API</Label>
                        <Input
                          id="api-key"
                          value={credentials.apiKey}
                          onChange={(e) => setCredentials({ ...credentials, apiKey: e.target.value })}
                          placeholder="أدخل مفتاح API"
                        />
                      </div>
                    )}

                    {selectedProvider === 'zohobooks' && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="username">اسم المستخدم</Label>
                          <Input
                            id="username"
                            value={credentials.username}
                            onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
                            placeholder="أدخل اسم المستخدم"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="password">كلمة المرور</Label>
                          <Input
                            id="password"
                            type="password"
                            value={credentials.password}
                            onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                            placeholder="أدخل كلمة المرور"
                          />
                        </div>
                      </>
                    )}

                    <div className="flex items-center gap-2 text-amber-600 bg-amber-50 p-3 rounded-md mt-4">
                      <Shield className="h-5 w-5 flex-shrink-0" />
                      <p className="text-sm">
                        بيانات الاعتماد الخاصة بك آمنة ومشفرة. لن نشاركها مع أي طرف ثالث.
                      </p>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>

          {!isConnected && (
            <CardFooter className="border-t pt-4">
              <Button onClick={handleConnect} disabled={loading || !selectedProvider}>
                {loading ? 'جاري الاتصال...' : 'اتصال'}
              </Button>
            </CardFooter>
          )}
        </TabsContent>

        <TabsContent value="settings">
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h3 className="font-medium">إعدادات المزامنة</h3>
              <p className="text-sm text-gray-500">
                قم بتخصيص كيفية مزامنة البيانات بين نظام الفواتير ونظام المحاسبة
              </p>
            </div>

            <div className="space-y-3 pt-2">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-sync">المزامنة التلقائية</Label>
                  <p className="text-xs text-gray-500">مزامنة البيانات تلقائياً كل ساعة</p>
                </div>
                <Switch
                  id="auto-sync"
                  checked={syncSettings.autoSync}
                  onCheckedChange={(checked) => setSyncSettings({ ...syncSettings, autoSync: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sync-invoices">مزامنة الفواتير</Label>
                  <p className="text-xs text-gray-500">مزامنة الفواتير مع نظام المحاسبة</p>
                </div>
                <Switch
                  id="sync-invoices"
                  checked={syncSettings.syncInvoices}
                  onCheckedChange={(checked) => setSyncSettings({ ...syncSettings, syncInvoices: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sync-payments">مزامنة المدفوعات</Label>
                  <p className="text-xs text-gray-500">مزامنة المدفوعات مع نظام المحاسبة</p>
                </div>
                <Switch
                  id="sync-payments"
                  checked={syncSettings.syncPayments}
                  onCheckedChange={(checked) => setSyncSettings({ ...syncSettings, syncPayments: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sync-customers">مزامنة العملاء</Label>
                  <p className="text-xs text-gray-500">مزامنة بيانات العملاء مع نظام المحاسبة</p>
                </div>
                <Switch
                  id="sync-customers"
                  checked={syncSettings.syncCustomers}
                  onCheckedChange={(checked) => setSyncSettings({ ...syncSettings, syncCustomers: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sync-products">مزامنة المنتجات</Label>
                  <p className="text-xs text-gray-500">مزامنة المنتجات والخدمات مع نظام المحاسبة</p>
                </div>
                <Switch
                  id="sync-products"
                  checked={syncSettings.syncProducts}
                  onCheckedChange={(checked) => setSyncSettings({ ...syncSettings, syncProducts: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sync-taxes">مزامنة الضرائب</Label>
                  <p className="text-xs text-gray-500">مزامنة إعدادات الضرائب مع نظام المحاسبة</p>
                </div>
                <Switch
                  id="sync-taxes"
                  checked={syncSettings.syncTaxes}
                  onCheckedChange={(checked) => setSyncSettings({ ...syncSettings, syncTaxes: checked })}
                />
              </div>
            </div>

            <div className="pt-4 border-t">
              <Button variant="outline" size="sm" onClick={handleSync} disabled={loading} className="w-full">
                <RefreshCw className="h-4 w-4 ml-1" />
                مزامنة الآن
              </Button>
              {lastSyncDate && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  آخر مزامنة: {formatDate(lastSyncDate)}
                </p>
              )}
            </div>
          </CardContent>
        </TabsContent>

        <TabsContent value="history">
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">سجل المزامنة</h3>
                <p className="text-sm text-gray-500">
                  عرض سجل عمليات المزامنة السابقة
                </p>
              </div>

              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-right">التاريخ</th>
                      <th className="px-4 py-2 text-right">النوع</th>
                      <th className="px-4 py-2 text-right">الحالة</th>
                      <th className="px-4 py-2 text-right">التفاصيل</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {lastSyncDate ? (
                      <>
                        <tr>
                          <td className="px-4 py-2">{formatDate(lastSyncDate)}</td>
                          <td className="px-4 py-2">مزامنة كاملة</td>
                          <td className="px-4 py-2">
                            <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                              ناجحة
                            </span>
                          </td>
                          <td className="px-4 py-2">تمت مزامنة 15 فاتورة و 8 مدفوعات</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2">{formatDate(new Date(new Date(lastSyncDate).getTime() - 24 * 60 * 60 * 1000).toISOString())}</td>
                          <td className="px-4 py-2">مزامنة تلقائية</td>
                          <td className="px-4 py-2">
                            <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                              ناجحة
                            </span>
                          </td>
                          <td className="px-4 py-2">تمت مزامنة 3 فواتير و 2 مدفوعات</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2">{formatDate(new Date(new Date(lastSyncDate).getTime() - 2 * 24 * 60 * 60 * 1000).toISOString())}</td>
                          <td className="px-4 py-2">مزامنة يدوية</td>
                          <td className="px-4 py-2">
                            <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                              فشلت
                            </span>
                          </td>
                          <td className="px-4 py-2">خطأ في الاتصال بالخادم</td>
                        </tr>
                      </>
                    ) : (
                      <tr>
                        <td colSpan={4} className="px-4 py-8 text-center text-gray-500">
                          لا توجد عمليات مزامنة سابقة
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </TabsContent>
      </Tabs>
    </Card>
  );
}
