'use client';

/**
 * مكون محول العملات - يوفر واجهة لتحويل المبالغ بين العملات المختلفة
 * Currency Converter Component - Provides an interface for converting amounts between different currencies
 */

import { useState, useEffect } from 'react';
import {CurrencyCode, cn} from '@/lib/utils';
import { useI18n } from '@/lib/i18n';
import { ArrowRightLeft, RotateCw, History, X, Clock, ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CurrencySelector from './currency-selector';
import {
  convertCurrency,
  getExchangeRate,
  updateExchangeRates,
  getConversionHistory,
  addConversionToHistory,
  clearConversionHistory,
  ConversionHistoryEntry,
  formatCurrencyAmount
} from '@/lib/services/currency-service';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

// واجهة خصائص محول العملات
// Currency converter props interface
interface CurrencyConverterProps {
  className?: string;
  variant?: 'default' | 'compact' | 'minimal';
  defaultFromCurrency?: CurrencyCode;
  defaultToCurrency?: CurrencyCode;
  defaultAmount?: number;
  showHistory?: boolean;
  showHeader?: boolean;
  onConvert?: (from: CurrencyCode, to: CurrencyCode, amount: number, result: number) => void;
}

/**
 * مكون محول العملات - يوفر واجهة لتحويل المبالغ بين العملات المختلفة
 * Currency Converter Component - Provides an interface for converting amounts between different currencies
 */
export default function CurrencyConverter({
  className = '',
  variant = 'default',
  defaultFromCurrency = 'AED',
  defaultToCurrency = 'USD',
  defaultAmount = 100,
  showHistory = true,
  showHeader = true,
  onConvert
}: CurrencyConverterProps) {
  const { t, language } = useI18n();
  const [fromCurrency, setFromCurrency] = useState<CurrencyCode>(defaultFromCurrency);
  const [toCurrency, setToCurrency] = useState<CurrencyCode>(defaultToCurrency);
  const [amount, setAmount] = useState<string>(defaultAmount.toString());
  const [convertedAmount, setConvertedAmount] = useState<number>(0);
  const [exchangeRate, setExchangeRate] = useState<number>(0);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('converter');
  const [history, setHistory] = useState<ConversionHistoryEntry[]>([]);
  const [historyDialogOpen, setHistoryDialogOpen] = useState<boolean>(false);

  // تحميل سجل التحويلات عند تحميل المكون
  // Load conversion history when component mounts
  useEffect(() => {
    if (showHistory) {
      setHistory(getConversionHistory());
    }
  }, [showHistory]);

  // تحديث سعر الصرف عند تغيير العملات
  // Update exchange rate when currencies change
  useEffect(() => {
    updateExchangeRateInfo();
  }, [fromCurrency, toCurrency]);

  // تحويل المبلغ عند تغيير المدخلات
  // Convert amount when inputs change
  useEffect(() => {
    handleConvert();
  }, [amount, fromCurrency, toCurrency, exchangeRate]);

  // تحديث معلومات سعر الصرف
  // Update exchange rate information
  const updateExchangeRateInfo = async () => {
    try {
      // الحصول على سعر الصرف الحالي
      // Get current exchange rate
      const rate = getExchangeRate(fromCurrency, toCurrency);
      setExchangeRate(rate);

      // تحديث المبلغ المحول
      // Update converted amount
      const amountValue = parseFloat(amount) || 0;
      const converted = convertCurrency(amountValue, fromCurrency, toCurrency);
      setConvertedAmount(converted);
    } catch (error) {
      console.error('Error updating exchange rate info:', error);
    }
  };

  // تحديث أسعار الصرف من المصدر
  // Update exchange rates from source
  const handleUpdateRates = async () => {
    try {
      setIsUpdating(true);
      await updateExchangeRates();
      setLastUpdated(new Date());
      updateExchangeRateInfo();
    } catch (error) {
      console.error('Error updating exchange rates:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  // تبديل العملات
  // Swap currencies
  const handleSwapCurrencies = () => {
    const temp = fromCurrency;
    setFromCurrency(toCurrency);
    setToCurrency(temp);
  };

  // معالجة تحويل المبلغ
  // Handle amount conversion
  const handleConvert = () => {
    try {
      const amountValue = parseFloat(amount) || 0;
      const converted = convertCurrency(amountValue, fromCurrency, toCurrency);
      setConvertedAmount(converted);

      // استدعاء دالة التحويل إذا كانت موجودة
      // Call convert callback if provided
      if (onConvert) {
        onConvert(fromCurrency, toCurrency, amountValue, converted);
      }
    } catch (error) {
      console.error('Error converting amount:', error);
    }
  };

  // حفظ التحويل في السجل
  // Save conversion to history
  const saveToHistory = () => {
    try {
      const amountValue = parseFloat(amount) || 0;
      addConversionToHistory(fromCurrency, toCurrency, amountValue, convertedAmount);
      setHistory(getConversionHistory());
    } catch (error) {
      console.error('Error saving to history:', error);
    }
  };

  // مسح سجل التحويلات
  // Clear conversion history
  const handleClearHistory = () => {
    try {
      clearConversionHistory();
      setHistory([]);
    } catch (error) {
      console.error('Error clearing history:', error);
    }
  };

  // تنسيق التاريخ
  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(language === 'ar' ? 'ar-AE' : 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  };

  // عرض محول العملات المدمج
  // Render compact currency converter
  if (variant === 'compact') {
    return (
      <div className={cn('currency-converter-compact', className)}>
        <div className="flex flex-col gap-2">
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0"
                className="text-right"
              />
            </div>
            <CurrencySelector
              value={fromCurrency}
              onChange={setFromCurrency}
              variant="compact"
              showSymbol={false}
              className="w-24"
            />
          </div>

          <div className="flex items-center justify-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleSwapCurrencies}
              className="h-6 w-6"
            >
              <ArrowRightLeft className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                type="text"
                value={formatCurrencyAmount(convertedAmount, toCurrency, language, { maximumFractionDigits: 4 })}
                readOnly
                className="text-right bg-muted"
              />
            </div>
            <CurrencySelector
              value={toCurrency}
              onChange={setToCurrency}
              variant="compact"
              showSymbol={false}
              className="w-24"
            />
          </div>

          <div className="text-xs text-muted-foreground text-center mt-1">
            {t('currency.exchangeRate', { rate: exchangeRate.toFixed(4), from: fromCurrency, to: toCurrency }) ||
             `1 ${fromCurrency} = ${exchangeRate.toFixed(4)} ${toCurrency}`}
          </div>
        </div>
      </div>
    );
  }

  // عرض محول العملات المصغر
  // Render minimal currency converter
  if (variant === 'minimal') {
    return (
      <div className={cn('currency-converter-minimal flex items-center gap-2', className)}>
        <Input
          type="number"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="0"
          className="w-24"
        />

        <CurrencySelector
          value={fromCurrency}
          onChange={setFromCurrency}
          variant="minimal"
          showSymbol={false}
          className="w-16"
        />

        <ArrowRightLeft className="h-4 w-4 text-muted-foreground" />

        <Input
          type="text"
          value={formatCurrencyAmount(convertedAmount, toCurrency, language, { maximumFractionDigits: 4 })}
          readOnly
          className="w-24 bg-muted"
        />

        <CurrencySelector
          value={toCurrency}
          onChange={setToCurrency}
          variant="minimal"
          showSymbol={false}
          className="w-16"
        />
      </div>
    );
  }

  // عرض محول العملات الافتراضي
  // Render default currency converter
  return (
    <Card className={cn('currency-converter', className)}>
      {showHeader && (
        <CardHeader>
          <CardTitle>{t('currency.converter') || 'Currency Converter'}</CardTitle>
          <CardDescription>
            {t('currency.converterDescription') || 'Convert between different currencies with real-time exchange rates'}
          </CardDescription>
        </CardHeader>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="converter">{t('currency.converter') || 'Converter'}</TabsTrigger>
          {showHistory && (
            <TabsTrigger value="history">{t('currency.history') || 'History'}</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="converter">
          <CardContent className="space-y-4 pt-4">
            <div className="flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                {t('currency.lastUpdated') || 'Last updated'}: {formatDate(lastUpdated.toISOString())}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleUpdateRates}
                disabled={isUpdating}
                className="h-8"
              >
                {isUpdating ? (
                  <RotateCw className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <RotateCw className="h-4 w-4 mr-1" />
                )}
                {t('currency.update') || 'Update'}
              </Button>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="amount">{t('currency.amount') || 'Amount'}</Label>
                <div className="flex gap-2">
                  <Input
                    id="amount"
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0"
                    className="flex-1"
                  />
                  <CurrencySelector
                    value={fromCurrency}
                    onChange={setFromCurrency}
                    variant="dropdown"
                    showSymbol={true}
                    showFlag={true}
                    className="w-[180px]"
                  />
                </div>
              </div>

              <div className="flex justify-center">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleSwapCurrencies}
                  className="h-8 w-8 rounded-full"
                >
                  <ArrowDown className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor="converted">{t('currency.convertedAmount') || 'Converted Amount'}</Label>
                <div className="flex gap-2">
                  <Input
                    id="converted"
                    type="text"
                    value={formatCurrencyAmount(convertedAmount, toCurrency, language, { maximumFractionDigits: 4 })}
                    readOnly
                    className="flex-1 bg-muted"
                  />
                  <CurrencySelector
                    value={toCurrency}
                    onChange={setToCurrency}
                    variant="dropdown"
                    showSymbol={true}
                    showFlag={true}
                    className="w-[180px]"
                  />
                </div>
              </div>

              <div className="p-3 bg-muted rounded-md">
                <div className="flex justify-between items-center">
                  <div className="text-sm">
                    {t('currency.exchangeRate') || 'Exchange Rate'}:
                  </div>
                  <div className="font-medium">
                    1 {fromCurrency} = {exchangeRate.toFixed(4)} {toCurrency}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-end gap-2">
            {showHistory && (
              <Button
                variant="outline"
                onClick={saveToHistory}
                disabled={parseFloat(amount) <= 0}
              >
                <History className="h-4 w-4 mr-2" />
                {t('currency.saveToHistory') || 'Save to History'}
              </Button>
            )}

            <Button
              onClick={handleConvert}
              disabled={parseFloat(amount) <= 0}
            >
              {t('currency.convert') || 'Convert'}
            </Button>
          </CardFooter>
        </TabsContent>

        {showHistory && (
          <TabsContent value="history">
            <CardContent className="pt-4">
              {history.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="text-sm font-medium">
                      {t('currency.recentConversions') || 'Recent Conversions'}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClearHistory}
                      className="h-8 text-destructive hover:text-destructive"
                    >
                      <X className="h-4 w-4 mr-1" />
                      {t('currency.clearHistory') || 'Clear History'}
                    </Button>
                  </div>

                  <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
                    {history.map((entry) => (
                      <div
                        key={entry.id}
                        className="p-3 border rounded-md hover:bg-accent transition-colors"
                      >
                        <div className="flex justify-between items-center">
                          <div className="font-medium">
                            {formatCurrencyAmount(entry.fromAmount, entry.fromCurrency, language)} →{' '}
                            {formatCurrencyAmount(entry.toAmount, entry.toCurrency, language)}
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDate(entry.date)}
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {t('currency.rate') || 'Rate'}: 1 {entry.fromCurrency} = {entry.rate.toFixed(4)} {entry.toCurrency}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <History className="h-12 w-12 mx-auto mb-4 opacity-20" />
                  <p>{t('currency.noHistory') || 'No conversion history yet'}</p>
                  <p className="text-sm mt-2">
                    {t('currency.startConverting') || 'Start converting currencies to build your history'}
                  </p>
                </div>
              )}
            </CardContent>
          </TabsContent>
        )}
      </Tabs>
    </Card>
  );
}
