'use client';

/**
 * مكون اختيار العملة المحسن - يوفر واجهة متقدمة لاختيار العملات
 * Enhanced Currency Selector Component - Provides an advanced interface for currency selection
 */

import { useState, useEffect } from 'react';
import { CurrencyCode, DEFAULT_CURRENCY, CURRENCIES, cn } from '@/lib/utils';
import { useI18n } from '@/lib/i18n';
import { Check, ChevronDown } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Button } from '@/components/ui';

import { Label } from '@/components/ui/label';

// واجهة خصائص مكون اختيار العملة
// Currency selector props interface
interface CurrencySelectorProps {
  readonly onChange: (currency: CurrencyCode) => void;
  readonly value?: CurrencyCode;
  readonly className?: string;
  readonly variant?: 'default' | 'compact' | 'minimal' | 'dropdown' | 'combobox';
  readonly showSymbol?: boolean;
  readonly showFlag?: boolean;
  readonly showSearch?: boolean;
  readonly showLabel?: boolean;
  readonly label?: string;
  readonly placeholder?: string;
  readonly disabled?: boolean;
  readonly error?: string;
  readonly required?: boolean;
  readonly favoritesCurrencies?: CurrencyCode[];
}

/**
 * مكون اختيار العملة - يسمح للمستخدم باختيار العملة من قائمة العملات المدعومة
 * Currency Selector Component - Allows user to select currency from supported currencies list
 */
export default function CurrencySelector({
  onChange,
  value = DEFAULT_CURRENCY,
  className = '',
  variant = 'default',
  showSymbol = true,
  showFlag = true,
  showSearch = false,
  showLabel = false,
  label = '',
  placeholder = '',
  disabled = false,
  error = '',
  required = false,
  favoritesCurrencies = ['AED', 'SAR', 'USD', 'EUR']
}: CurrencySelectorProps) {
  const { t, language } = useI18n();
  const [selectedCurrency, setSelectedCurrency] = useState<CurrencyCode>(value);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // تحديث العملة المحددة عند تغيير القيمة من الخارج
  // Update selected currency when value changes from outside
  useEffect(() => {
    setSelectedCurrency(value);
  }, [value]);

  // معالجة تغيير العملة
  // Handle currency change
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newCurrency = e.target.value as CurrencyCode;
    setSelectedCurrency(newCurrency);
    onChange(newCurrency);
  };

  // معالجة اختيار العملة من القائمة المنسدلة
  // Handle currency selection from dropdown
  const handleSelect = (currencyCode: CurrencyCode) => {
    setSelectedCurrency(currencyCode);
    onChange(currencyCode);
    setOpen(false);
  };

  // تحديد أنماط العرض المختلفة
  // Define different display styles
  const selectStyles = {
    default: "p-2 border border-gray-300 rounded-md text-sm transition-colors hover:border-primary focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 dark:bg-gray-800 dark:border-gray-700 dark:text-white",
    compact: "p-1 border border-gray-300 rounded-md text-xs transition-colors hover:border-primary focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary/20 dark:bg-gray-800 dark:border-gray-700 dark:text-white",
    minimal: "bg-transparent text-sm focus:outline-none focus:ring-0 border-none p-0 dark:text-white",
    dropdown: "sr-only",
    combobox: "sr-only"
  };

  // الحصول على رموز العملات للعرض
  // Get currency flags for display
  const getCurrencyFlag = (code: string): string => {
    switch (code) {
      case 'AED': return '🇦🇪';
      case 'SAR': return '🇸🇦';
      case 'QAR': return '🇶🇦';
      case 'BHD': return '🇧🇭';
      case 'KWD': return '🇰🇼';
      case 'OMR': return '🇴🇲';
      case 'EGP': return '🇪🇬';
      case 'JOD': return '🇯🇴';
      case 'LBP': return '🇱🇧';
      case 'USD': return '🇺🇸';
      case 'EUR': return '🇪🇺';
      case 'GBP': return '🇬🇧';
      default: return '';
    }
  };

  // الحصول على معلومات العملة المحددة
  // Get selected currency info
  const selectedCurrencyInfo = CURRENCIES.find(c => c.code === selectedCurrency) || CURRENCIES[0];

  // تصفية العملات حسب البحث
  // Filter currencies based on search
  const filteredCurrencies = CURRENCIES.filter(currency => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      currency.code.toLowerCase().includes(query) ||
      currency.nameAr.toLowerCase().includes(query) ||
      currency.nameEn.toLowerCase().includes(query) ||
      currency.symbol.toLowerCase().includes(query)
    );
  });

  // تقسيم العملات إلى مفضلة وأخرى
  // Split currencies into favorites and others
  const favoriteCurrencies = filteredCurrencies.filter(c => favoritesCurrencies.includes(c.code));
  const otherCurrencies = filteredCurrencies.filter(c => !favoritesCurrencies.includes(c.code));

  // عرض مكون اختيار العملة حسب النوع المحدد
  // Render currency selector based on selected variant
  if (variant === 'dropdown') {
    return (
      <div className={cn('currency-selector-dropdown', className)}>
        {showLabel && (
          <Label htmlFor="currency-selector" className="mb-2 block">
            {label || t('common.currency') || 'Currency'}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}

        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              aria-haspopup="listbox"
              aria-expanded={open}
              className={cn(
                "w-full justify-between",
                error && "border-destructive",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              disabled={disabled}
            >
              <div className="flex items-center">
                {showFlag && (
                  <span className="mr-2 text-lg">{getCurrencyFlag(selectedCurrency)}</span>
                )}
                <span>
                  {language === 'ar' ? selectedCurrencyInfo.nameAr : selectedCurrencyInfo.nameEn}
                  {showSymbol && ` (${selectedCurrencyInfo.symbol})`}
                </span>
              </div>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-0" align={language === 'ar' ? 'end' : 'start'}>
            <div className="max-h-[300px] overflow-auto">
              {showSearch && (
                <div className="p-2 border-b">
                  <input
                    className="w-full p-2 text-sm border rounded-md"
                    placeholder={t('common.search') || 'Search'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              )}

              <div className="py-1">
                {filteredCurrencies.map(currency => (
                  <button
                    key={currency.code}
                    type="button"
                    className={cn(
                      "flex items-center w-full px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 text-left",
                      selectedCurrency === currency.code && "bg-gray-100 dark:bg-gray-800"
                    )}
                    onClick={() => handleSelect(currency.code)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleSelect(currency.code);
                      }
                    }}
                  >
                    {showFlag && (
                      <span className="mr-2 text-lg">{getCurrencyFlag(currency.code)}</span>
                    )}
                    <span className="flex-1">
                      {language === 'ar' ? currency.nameAr : currency.nameEn}
                      {showSymbol && ` (${currency.symbol})`}
                    </span>
                    {selectedCurrency === currency.code && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {error && (
          <p className="text-destructive text-sm mt-1">{error}</p>
        )}
      </div>
    );
  }

  if (variant === 'combobox') {
    return (
      <div className={cn('currency-selector-combobox', className)}>
        {showLabel && (
          <Label htmlFor="currency-selector" className="mb-2 block">
            {label || t('common.currency') || 'Currency'}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}

        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              aria-haspopup="listbox"
              aria-expanded={open}
              className={cn(
                "w-full justify-between",
                error && "border-destructive",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              disabled={disabled}
            >
              <div className="flex items-center">
                {showFlag && (
                  <span className="mr-2 text-lg">{getCurrencyFlag(selectedCurrency)}</span>
                )}
                <span>
                  {language === 'ar' ? selectedCurrencyInfo.nameAr : selectedCurrencyInfo.nameEn}
                  {showSymbol && ` (${selectedCurrencyInfo.symbol})`}
                </span>
              </div>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0" align={language === 'ar' ? 'end' : 'start'}>
            <Command>
              <CommandInput
                placeholder={placeholder || t('common.searchCurrency') || "Search currency..."}
                onValueChange={setSearchQuery}
                className="h-9"
              />
              <CommandList>
                {favoriteCurrencies.length > 0 && (
                  <CommandGroup heading={t('common.favorites') || "Favorites"}>
                    {favoriteCurrencies.map(currency => (
                      <CommandItem
                        key={currency.code}
                        value={currency.code}
                        onSelect={() => handleSelect(currency.code)}
                      >
                        <div className="flex items-center">
                          {showFlag && (
                            <span className="mr-2 text-lg">{getCurrencyFlag(currency.code)}</span>
                          )}
                          <span className="flex-1">
                            {language === 'ar' ? currency.nameAr : currency.nameEn}
                            {showSymbol && (
                              <span className="text-muted-foreground ml-1">
                                ({currency.symbol})
                              </span>
                            )}
                          </span>
                          <Check
                            className={cn(
                              "ml-2 h-4 w-4",
                              selectedCurrency === currency.code ? "opacity-100" : "opacity-0"
                            )}
                          />
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}

                {otherCurrencies.length > 0 && (
                  <CommandGroup heading={t('common.allCurrencies') || "All Currencies"}>
                    {otherCurrencies.map(currency => (
                      <CommandItem
                        key={currency.code}
                        value={currency.code}
                        onSelect={() => handleSelect(currency.code)}
                      >
                        <div className="flex items-center">
                          {showFlag && (
                            <span className="mr-2 text-lg">{getCurrencyFlag(currency.code)}</span>
                          )}
                          <span className="flex-1">
                            {language === 'ar' ? currency.nameAr : currency.nameEn}
                            {showSymbol && (
                              <span className="text-muted-foreground ml-1">
                                ({currency.symbol})
                              </span>
                            )}
                          </span>
                          <Check
                            className={cn(
                              "ml-2 h-4 w-4",
                              selectedCurrency === currency.code ? "opacity-100" : "opacity-0"
                            )}
                          />
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}

                <CommandEmpty>
                  {t('common.noCurrenciesFound') || "No currencies found."}
                </CommandEmpty>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {error && (
          <p className="text-destructive text-sm mt-1">{error}</p>
        )}
      </div>
    );
  }

  // عرض مكون اختيار العملة التقليدي (select)
  // Render traditional currency selector (select)
  return (
    <div className={cn('currency-selector', className)}>
      {showLabel && (
        <Label htmlFor="currency-selector" className="mb-2 block">
          {label || t('common.currency') || 'Currency'}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}

      <select
        id="currency-selector"
        value={selectedCurrency}
        onChange={handleChange}
        className={cn(
          selectStyles[variant],
          error && "border-destructive"
        )}
        aria-label={t('common.currency')}
        disabled={disabled}
        required={required}
      >
        {CURRENCIES.map(currency => (
          <option key={currency.code} value={currency.code}>
            {showFlag && `${getCurrencyFlag(currency.code)} `}
            {language === 'ar' ? currency.nameAr : currency.nameEn}
            {showSymbol && ` (${currency.symbol})`}
          </option>
        ))}
      </select>

      {error && (
        <p className="text-destructive text-sm mt-1">{error}</p>
      )}
    </div>
  );
}
