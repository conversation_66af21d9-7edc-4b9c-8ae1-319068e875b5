'use client';

import { useState, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Step {
  title: string;
  description?: string;
  content: ReactNode;
}

interface FormWizardProps {
  steps: Step[];
  onComplete: () => void;
  className?: string;
}

/**
 * مكون معالج النماذج لتقسيم النماذج الكبيرة إلى خطوات
 * يساعد في تحسين تجربة المستخدم وأداء النماذج الكبيرة
 */
export function FormWizard({ steps, onComplete, className }: FormWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  // التحقق من اكتمال الخطوة
  const isStepCompleted = (stepIndex: number) => completedSteps.includes(stepIndex);

  // الانتقال إلى الخطوة التالية
  const goToNextStep = () => {
    // تحديث الخطوات المكتملة
    if (!isStepCompleted(currentStep)) {
      setCompletedSteps([...completedSteps, currentStep]);
    }

    // الانتقال إلى الخطوة التالية أو إكمال المعالج
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  // الانتقال إلى الخطوة السابقة
  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // الانتقال إلى خطوة محددة
  const goToStep = (stepIndex: number) => {
    // يمكن الانتقال فقط إلى الخطوات المكتملة أو الخطوة التالية
    if (stepIndex <= currentStep || isStepCompleted(stepIndex - 1)) {
      setCurrentStep(stepIndex);
    }
  };

  return (
    <div className={cn('space-y-8', className)}>
      {/* مؤشر التقدم */}
      <div className="flex items-center justify-center">
        {steps.map((step, index) => (
          <div key={index} className="flex items-center">
            {/* رقم الخطوة */}
            <button
              type="button"
              onClick={() => goToStep(index)}
              disabled={index > currentStep && !isStepCompleted(index - 1)}
              className={cn(
                'flex h-10 w-10 items-center justify-center rounded-full border-2 text-sm font-medium transition-colors',
                index === currentStep
                  ? 'border-primary bg-primary text-white'
                  : isStepCompleted(index)
                  ? 'border-primary bg-primary/10 text-primary'
                  : 'border-gray-300 bg-white text-gray-500'
              )}
            >
              {index + 1}
            </button>

            {/* خط الاتصال بين الخطوات */}
            {index < steps.length - 1 && (
              <div
                className={cn(
                  'h-1 w-10 transition-colors',
                  isStepCompleted(index) ? 'bg-primary' : 'bg-gray-300'
                )}
              />
            )}
          </div>
        ))}
      </div>

      {/* عنوان ووصف الخطوة الحالية */}
      <div className="text-center">
        <h3 className="text-lg font-medium">{steps[currentStep].title}</h3>
        {steps[currentStep].description && (
          <p className="mt-1 text-sm text-gray-500">{steps[currentStep].description}</p>
        )}
      </div>

      {/* محتوى الخطوة الحالية */}
      <div className="rounded-lg border p-6">{steps[currentStep].content}</div>

      {/* أزرار التنقل */}
      <div className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={goToPreviousStep}
          disabled={currentStep === 0}
        >
          السابق
        </Button>

        <Button type="button" onClick={goToNextStep}>
          {currentStep === steps.length - 1 ? 'إنهاء' : 'التالي'}
        </Button>
      </div>
    </div>
  );
}
