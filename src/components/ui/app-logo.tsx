'use client';

import Image from 'next/image';
import { cn } from '@/lib/utils';

// استيراد متغيرات البيئة للواجهة الأمامية
// Import environment variables for frontend
const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME ?? 'أمين بلس';
const APP_NAME_EN = process.env.NEXT_PUBLIC_APP_NAME_EN ?? 'Amin Plus';

interface AppLogoProps {
  readonly showName?: boolean;
  readonly showEnglishName?: boolean;
  readonly className?: string;
  readonly imageSize?: number;
  readonly textSize?: 'small' | 'medium' | 'large';
  readonly variant?: 'default' | 'stacked' | 'horizontal';
}

/**
 * شعار التطبيق مع اسم التطبيق بالعربية والإنجليزية
 * Application logo with app name in Arabic and English
 */
export default function AppLogo({
  showName = true,
  showEnglishName = true,
  className = '',
  imageSize = 32,
  textSize = 'medium',
  variant = 'default'
}: AppLogoProps) {
  // تحديد أحجام النص بناءً على الخيار المحدد
  // Define text sizes based on selected option
  const arabicTextSizeClasses = {
    small: 'text-base font-semibold leading-tight',
    medium: 'text-lg font-semibold leading-tight',
    large: 'text-xl font-bold leading-tight'
  };

  const englishTextSizeClasses = {
    small: 'text-xs text-gray-500',
    medium: 'text-sm text-gray-500',
    large: 'text-base text-gray-600'
  };

  // تحديد حجم الشعار بناءً على حجم النص
  // Define logo size based on text size
  const logoSizeMap = {
    small: Math.max(24, imageSize),
    medium: Math.max(32, imageSize),
    large: Math.max(40, imageSize)
  };

  const actualLogoSize = logoSizeMap[textSize];

  // تحديد تنسيق العرض بناءً على الخيار المحدد
  // Define layout based on selected variant
  const containerClasses = {
    default: 'flex items-center',
    stacked: 'flex flex-col items-center text-center',
    horizontal: 'flex items-center'
  };

  const logoContainerClasses = {
    default: 'relative overflow-hidden rounded-md bg-primary/10 mr-2',
    stacked: 'relative overflow-hidden rounded-md bg-primary/10 mb-2',
    horizontal: 'relative overflow-hidden rounded-md bg-primary/10 mr-3'
  };

  const logoSizeClasses = {
    small: 'h-6 w-6',
    medium: 'h-8 w-8',
    large: 'h-10 w-10'
  };

  return (
    <div className={cn(containerClasses[variant], className)}>
      {/* الشعار - Logo */}
      <div className={cn(logoContainerClasses[variant], logoSizeClasses[textSize])}>
        <Image
          src="/logo.svg"
          alt={`${APP_NAME} | ${APP_NAME_EN}`}
          width={actualLogoSize}
          height={actualLogoSize}
          className="object-contain"
          onError={(e) => {
            // إذا فشل تحميل الصورة، نعرض الحرف الأول من اسم التطبيق
            // If image loading fails, display the first letter of the app name
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            target.parentElement!.innerHTML = '<div class="flex h-full w-full items-center justify-center bg-primary text-white font-bold">أ</div>';
          }}
        />
      </div>

      {showName && (
        <div className={variant === 'stacked' ? 'flex flex-col items-center' : 'flex flex-col'}>
          <h2 className={arabicTextSizeClasses[textSize]}>
            {APP_NAME}
          </h2>
          {showEnglishName && (
            <span className={englishTextSizeClasses[textSize]}>
              {APP_NAME_EN}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
