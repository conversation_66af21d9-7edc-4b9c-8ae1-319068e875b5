import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 button-text relative overflow-hidden group active:scale-95",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-soft hover:shadow-medium hover:scale-[1.02]",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-soft hover:shadow-colored-red hover:scale-[1.02]",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-soft hover:shadow-medium hover:scale-[1.02]",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-soft hover:shadow-medium hover:scale-[1.02]",
        ghost: "hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] transition-all duration-200",
        link: "text-primary underline-offset-4 hover:underline hover:scale-[1.02] transition-all duration-200",
        gradient: "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-colored-blue hover:shadow-strong hover:scale-[1.02] hover:from-blue-600 hover:to-purple-700 before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        success: "bg-green-500 text-white hover:bg-green-600 shadow-colored-green hover:shadow-strong hover:scale-[1.02]",
        warning: "bg-yellow-500 text-white hover:bg-yellow-600 shadow-soft hover:shadow-medium hover:scale-[1.02]",
        info: "bg-blue-500 text-white hover:bg-blue-600 shadow-colored-blue hover:shadow-strong hover:scale-[1.02]",
        glass: "glass text-foreground hover:bg-white/20 backdrop-blur-md border border-white/20 hover:scale-[1.02]",
        neon: "bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-glow hover:shadow-strong hover:scale-[1.02] animate-glow",
        royal: "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-colored-purple hover:shadow-strong hover:scale-[1.02]",
        ocean: "bg-gradient-to-r from-blue-400 to-cyan-400 text-white shadow-colored-blue hover:shadow-strong hover:scale-[1.02]",
      },
      size: {
        default: "h-11 px-6 py-2.5",
        sm: "h-9 rounded-lg px-4 text-xs",
        lg: "h-13 rounded-xl px-8 text-base",
        xl: "h-16 rounded-2xl px-12 text-lg font-semibold",
        icon: "h-11 w-11",
        "icon-sm": "h-9 w-9",
        "icon-lg": "h-13 w-13",
        "icon-xl": "h-16 w-16",
      },
      loading: {
        true: "cursor-not-allowed",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      loading: false,
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant,
    size,
    asChild = false,
    loading = false,
    leftIcon,
    rightIcon,
    children,
    disabled,
    onClick,
    onKeyDown,
    ...props
  }, ref) => {
    const Comp = asChild ? Slot : "button"

    // معالج keyboard events
    const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        onClick?.(event as any);
      }
      onKeyDown?.(event);
    };

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, loading, className }))}
        ref={ref}
        disabled={disabled || loading}
        aria-busy={loading ? "true" : undefined}
        onClick={onClick}
        onKeyDown={handleKeyDown}
        {...props}
      >
        {loading && (
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        )}
        {!loading && leftIcon && (
          <span className="mr-2 flex items-center">
            {leftIcon}
          </span>
        )}
        {children}
        {!loading && rightIcon && (
          <span className="ml-2 flex items-center">
            {rightIcon}
          </span>
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

// مكون مجموعة أزرار
interface ButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: "horizontal" | "vertical"
  size?: VariantProps<typeof buttonVariants>["size"]
  variant?: VariantProps<typeof buttonVariants>["variant"]
}

const ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ className, orientation = "horizontal", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="group"
        className={cn(
          "flex",
          orientation === "horizontal" ? "flex-row" : "flex-col",
          "[&>button]:rounded-none [&>button:first-child]:rounded-l-lg [&>button:last-child]:rounded-r-lg",
          orientation === "vertical" && "[&>button:first-child]:rounded-t-lg [&>button:first-child]:rounded-l-none [&>button:last-child]:rounded-b-lg [&>button:last-child]:rounded-r-none",
          "[&>button:not(:first-child)]:border-l-0",
          orientation === "vertical" && "[&>button:not(:first-child)]:border-l [&>button:not(:first-child)]:border-t-0",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ButtonGroup.displayName = "ButtonGroup"

// مكون زر عائم (Floating Action Button)
interface FABProps extends ButtonProps {
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
}

const FloatingActionButton = React.forwardRef<HTMLButtonElement, FABProps>(
  ({ className, position = "bottom-right", size = "icon-lg", variant = "default", ...props }, ref) => {
    const positionClasses = {
      "bottom-right": "fixed bottom-6 right-6",
      "bottom-left": "fixed bottom-6 left-6",
      "top-right": "fixed top-6 right-6",
      "top-left": "fixed top-6 left-6",
    }

    return (
      <Button
        ref={ref}
        className={cn(
          positionClasses[position],
          "rounded-full shadow-lg hover:shadow-xl z-50 animate-scale-in",
          className
        )}
        size={size}
        variant={variant}
        {...props}
      />
    )
  }
)
FloatingActionButton.displayName = "FloatingActionButton"

// مكون زر مع tooltip
interface TooltipButtonProps extends ButtonProps {
  tooltip?: string
  tooltipSide?: "top" | "bottom" | "left" | "right"
}

const TooltipButton = React.forwardRef<HTMLButtonElement, TooltipButtonProps>(
  ({ tooltip, tooltipSide = "top", ...props }, ref) => {
    if (!tooltip) {
      return <Button ref={ref} {...props} />
    }

    return (
      <div className="relative group">
        <Button ref={ref} {...props} />
        <div
          className={cn(
            "absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap",
            {
              "bottom-full left-1/2 transform -translate-x-1/2 mb-2": tooltipSide === "top",
              "top-full left-1/2 transform -translate-x-1/2 mt-2": tooltipSide === "bottom",
              "right-full top-1/2 transform -translate-y-1/2 mr-2": tooltipSide === "left",
              "left-full top-1/2 transform -translate-y-1/2 ml-2": tooltipSide === "right",
            }
          )}
        >
          {tooltip}
        </div>
      </div>
    )
  }
)
TooltipButton.displayName = "TooltipButton"

export { Button, ButtonGroup, FloatingActionButton, TooltipButton, buttonVariants }
