import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-lg border p-4 pr-8 shadow-lg transition-all-smooth animate-slide-in-right",
  {
    variants: {
      variant: {
        default: "border bg-background text-foreground",
        destructive: "destructive group border-destructive bg-destructive text-destructive-foreground",
        success: "border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-200",
        warning: "border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200",
        info: "border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const ToastProvider = React.createContext<{
  toasts: ToastType[]
  addToast: (toast: Omit<ToastType, 'id'>) => void
  removeToast: (id: string) => void
}>({
  toasts: [],
  addToast: () => {},
  removeToast: () => {},
})

export interface ToastType {
  id: string
  title?: string
  description?: string
  variant?: VariantProps<typeof toastVariants>["variant"]
  duration?: number
  action?: React.ReactNode
  onClose?: () => void
}

interface ToastProps extends React.HTMLAttributes<HTMLDivElement>, ToastType {
  onClose: () => void
}

const Toast = React.forwardRef<HTMLDivElement, ToastProps>(
  ({ className, variant, title, description, action, onClose, duration = 5000, ...props }, ref) => {
    const [isVisible, setIsVisible] = React.useState(true)
    const timeoutRef = React.useRef<NodeJS.Timeout>()

    React.useEffect(() => {
      if (duration > 0) {
        timeoutRef.current = setTimeout(() => {
          setIsVisible(false)
          setTimeout(onClose, 300) // Wait for animation to complete
        }, duration)
      }

      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
      }
    }, [duration, onClose])

    const handleClose = () => {
      setIsVisible(false)
      setTimeout(onClose, 300)
    }

    const getIcon = () => {
      switch (variant) {
        case "success":
          return <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
        case "destructive":
          return <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
        case "warning":
          return <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
        case "info":
          return <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        default:
          return null
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          toastVariants({ variant }),
          !isVisible && "animate-slide-out-right",
          className
        )}
        {...props}
      >
        <div className="flex items-start space-x-3">
          {getIcon()}
          <div className="flex-1 space-y-1">
            {title && (
              <div className="text-sm font-semibold leading-none tracking-tight">
                {title}
              </div>
            )}
            {description && (
              <div className="text-sm opacity-90 leading-relaxed">
                {description}
              </div>
            )}
          </div>
        </div>
        
        {action && (
          <div className="flex items-center space-x-2">
            {action}
          </div>
        )}
        
        <button
          type="button"
          onClick={handleClose}
          className="absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100"
          aria-label="Close notification"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    )
  }
)
Toast.displayName = "Toast"

// Toast Container
interface ToastContainerProps {
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left" | "top-center" | "bottom-center"
}

const ToastContainer: React.FC<ToastContainerProps> = ({ position = "top-right" }) => {
  const { toasts, removeToast } = React.useContext(ToastProvider)

  const positionClasses = {
    "top-right": "fixed top-4 right-4 z-50",
    "top-left": "fixed top-4 left-4 z-50",
    "bottom-right": "fixed bottom-4 right-4 z-50",
    "bottom-left": "fixed bottom-4 left-4 z-50",
    "top-center": "fixed top-4 left-1/2 transform -translate-x-1/2 z-50",
    "bottom-center": "fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50",
  }

  return (
    <div className={cn(positionClasses[position], "flex flex-col space-y-2 w-96 max-w-sm")}>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          {...toast}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </div>
  )
}

// Toast Provider Component
interface ToastProviderProps {
  children: React.ReactNode
}

const ToastProviderComponent: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = React.useState<ToastType[]>([])

  const addToast = React.useCallback((toast: Omit<ToastType, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    setToasts((prev) => [...prev, { ...toast, id }])
  }, [])

  const removeToast = React.useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }, [])

  return (
    <ToastProvider.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastContainer />
    </ToastProvider.Provider>
  )
}

// Hook to use toast
export const useToast = () => {
  const context = React.useContext(ToastProvider)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

// Convenience functions
export const toast = {
  success: (title: string, description?: string) => {
    // This would need to be implemented with a global toast context
    console.log("Success toast:", title, description)
  },
  error: (title: string, description?: string) => {
    console.log("Error toast:", title, description)
  },
  warning: (title: string, description?: string) => {
    console.log("Warning toast:", title, description)
  },
  info: (title: string, description?: string) => {
    console.log("Info toast:", title, description)
  },
}

export { Toast, ToastContainer, ToastProviderComponent as ToastProvider, toastVariants }
