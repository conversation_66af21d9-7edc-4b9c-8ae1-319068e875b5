import * as React from "react"
import { cn } from "@/lib/utils"
import {cva} from "class-variance-authority"

const cardVariants = cva(
  "rounded-2xl border bg-card text-card-foreground shadow-soft transition-all duration-300 relative overflow-hidden group",
  {
    variants: {
      variant: {
        default: "border-border hover:shadow-medium hover:scale-[1.01]",
        elevated: "shadow-strong hover:shadow-glow hover:scale-[1.02] hover:-translate-y-1",
        outlined: "border-2 border-primary/20 hover:border-primary/40 hover:shadow-medium",
        ghost: "border-transparent shadow-none hover:bg-accent/50",
        gradient: "bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 hover:from-primary/10 hover:to-primary/20",
        glass: "glass border-white/20 hover:bg-white/20",
        neon: "border-cyan-500/50 shadow-glow hover:shadow-strong bg-gradient-to-br from-cyan-500/5 to-blue-500/5",
        premium: "bg-gradient-to-br from-purple-500/5 to-pink-500/5 border-purple-200 dark:border-purple-800 shadow-colored-purple",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
        xl: "p-12",
      },
      interactive: {
        true: "cursor-pointer hover:scale-[1.02] hover:-translate-y-1 focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      interactive: false,
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, interactive, asChild = false, ...props }, ref) => {
    const Comp = asChild ? React.Fragment : "div"
    return (
      <Comp
        className={cn(cardVariants({ variant, size, interactive, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 pb-4", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-semibold leading-none tracking-tight text-foreground",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground leading-relaxed", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center pt-4", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

// مكون إحصائية محسن
interface StatCardProps extends CardProps {
  title: string
  value: string | number
  description?: string
  icon?: React.ReactNode
  trend?: {
    value: number
    label: string
    isPositive?: boolean
  }
  color?: "blue" | "green" | "purple" | "orange" | "red"
}

const StatCard = React.forwardRef<HTMLDivElement, StatCardProps>(
  ({ title, value, description, icon, trend, color = "blue", className, ...props }, ref) => {
    const colorClasses = {
      blue: "from-blue-500/10 to-blue-600/10 border-blue-200 dark:border-blue-800",
      green: "from-green-500/10 to-green-600/10 border-green-200 dark:border-green-800",
      purple: "from-purple-500/10 to-purple-600/10 border-purple-200 dark:border-purple-800",
      orange: "from-orange-500/10 to-orange-600/10 border-orange-200 dark:border-orange-800",
      red: "from-red-500/10 to-red-600/10 border-red-200 dark:border-red-800",
    }

    const iconColorClasses = {
      blue: "bg-blue-500 text-white",
      green: "bg-green-500 text-white",
      purple: "bg-purple-500 text-white",
      orange: "bg-orange-500 text-white",
      red: "bg-red-500 text-white",
    }

    return (
      <Card
        ref={ref}
        variant="gradient"
        className={cn(
          "bg-gradient-to-br",
          colorClasses[color],
          "animate-fade-in-up",
          className
        )}
        {...props}
      >
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm font-medium text-muted-foreground">
              {title}
            </div>
            {icon && (
              <div className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center",
                iconColorClasses[color]
              )}>
                {icon}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <div className="text-2xl font-bold text-foreground tabular-nums">
              {value}
            </div>

            {description && (
              <div className="text-xs text-muted-foreground">
                {description}
              </div>
            )}

            {trend && (
              <div className="flex items-center text-xs">
                <span className={cn(
                  "font-medium",
                  trend.isPositive !== false ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                )}>
                  {trend.isPositive !== false ? "+" : ""}{trend.value}%
                </span>
                <span className="text-muted-foreground ml-1">
                  {trend.label}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }
)
StatCard.displayName = "StatCard"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, StatCard }
