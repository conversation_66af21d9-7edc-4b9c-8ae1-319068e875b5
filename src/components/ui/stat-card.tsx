'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'yellow';
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
  };
  className?: string;
  onClick?: () => void;
}

const colorVariants = {
  blue: {
    bg: 'bg-blue-50 dark:bg-blue-950/20',
    icon: 'text-blue-600 dark:text-blue-400',
    border: 'border-blue-200 dark:border-blue-800',
    trend: 'text-blue-600 dark:text-blue-400'
  },
  green: {
    bg: 'bg-green-50 dark:bg-green-950/20',
    icon: 'text-green-600 dark:text-green-400',
    border: 'border-green-200 dark:border-green-800',
    trend: 'text-green-600 dark:text-green-400'
  },
  purple: {
    bg: 'bg-purple-50 dark:bg-purple-950/20',
    icon: 'text-purple-600 dark:text-purple-400',
    border: 'border-purple-200 dark:border-purple-800',
    trend: 'text-purple-600 dark:text-purple-400'
  },
  orange: {
    bg: 'bg-orange-50 dark:bg-orange-950/20',
    icon: 'text-orange-600 dark:text-orange-400',
    border: 'border-orange-200 dark:border-orange-800',
    trend: 'text-orange-600 dark:text-orange-400'
  },
  red: {
    bg: 'bg-red-50 dark:bg-red-950/20',
    icon: 'text-red-600 dark:text-red-400',
    border: 'border-red-200 dark:border-red-800',
    trend: 'text-red-600 dark:text-red-400'
  },
  yellow: {
    bg: 'bg-yellow-50 dark:bg-yellow-950/20',
    icon: 'text-yellow-600 dark:text-yellow-400',
    border: 'border-yellow-200 dark:border-yellow-800',
    trend: 'text-yellow-600 dark:text-yellow-400'
  }
};

export function StatCard({
  title,
  value,
  icon,
  color = 'blue',
  trend,
  className,
  onClick
}: StatCardProps) {
  const colors = colorVariants[color];

  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-2xl border bg-card p-6 shadow-soft transition-all duration-300 group',
        'hover:shadow-strong hover:scale-[1.03] hover:-translate-y-1',
        onClick && 'cursor-pointer',
        colors.border,
        className
      )}
      onClick={onClick}
    >
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
        <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-current animate-float" />
        <div className="absolute -bottom-4 -left-4 h-16 w-16 rounded-full bg-current animate-float" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 h-12 w-12 rounded-full bg-current opacity-30 animate-pulse" />
      </div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* Glass Effect */}
      <div className="absolute inset-0 backdrop-blur-[1px] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="relative z-10">
        {/* Enhanced Header */}
        <div className="flex items-center justify-between mb-6">
          <div className={cn(
            'rounded-xl p-3 shadow-soft group-hover:shadow-medium transition-all duration-300 group-hover:scale-110',
            colors.bg
          )}>
            <div className={cn('h-6 w-6 transition-transform duration-300 group-hover:rotate-12', colors.icon)}>
              {icon}
            </div>
          </div>
          {trend && (
            <div className={cn(
              'flex items-center gap-1.5 text-xs font-semibold px-2 py-1 rounded-full transition-all duration-300 group-hover:scale-105',
              trend.isPositive
                ? 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20'
                : 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20'
            )}>
              {trend.isPositive ? (
                <TrendingUp className="h-3.5 w-3.5 animate-bounce" />
              ) : (
                <TrendingDown className="h-3.5 w-3.5 animate-bounce" />
              )}
              {trend.value}%
            </div>
          )}
        </div>

        {/* Enhanced Content */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider">
            {title}
          </h3>
          <p className="text-3xl font-bold text-foreground group-hover:text-gradient transition-all duration-300">
            {value}
          </p>
          {trend && (
            <p className="text-xs text-muted-foreground font-medium">
              {trend.label}
            </p>
          )}
        </div>

        {/* Progress Bar (if trend exists) */}
        {trend && (
          <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 overflow-hidden">
            <div
              className={cn(
                'h-full rounded-full transition-all duration-1000 ease-out',
                trend.isPositive ? 'bg-green-500' : 'bg-red-500'
              )}
              style={{
                width: `${Math.min(Math.abs(trend.value), 100)}%`,
                animationDelay: '0.5s'
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default StatCard;
