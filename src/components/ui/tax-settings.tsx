'use client';

import { useState, useEffect } from 'react';

interface TaxSettingsProps {
  readonly onChange: (settings: TaxSettings) => void;
  readonly initialSettings?: TaxSettings;
  readonly className?: string;
}

export interface TaxSettings {
  readonly enabled: boolean;
  readonly rate: number;
  readonly inclusive: boolean;
}

export const DEFAULT_TAX_SETTINGS: TaxSettings = {
  enabled: true,
  rate: 5, // 5% ضريبة القيمة المضافة الافتراضية
  inclusive: false
};

export default function TaxSettings({ onChange, initialSettings = DEFAULT_TAX_SETTINGS, className = '' }: TaxSettingsProps) {
  const [settings, setSettings] = useState<TaxSettings>(initialSettings);

  // تحديث الإعدادات عند تغيير القيمة من الخارج
  useEffect(() => {
    setSettings(initialSettings);
  }, [initialSettings]);

  // معالجة تغيير تفعيل الضريبة
  const handleEnabledChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSettings = { ...settings, enabled: e.target.checked };
    setSettings(newSettings);
    onChange(newSettings);
  };

  // معالجة تغيير نسبة الضريبة
  const handleRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rate = parseFloat(e.target.value);
    if (!isNaN(rate) && rate >= 0 && rate <= 100) {
      const newSettings = { ...settings, rate };
      setSettings(newSettings);
      onChange(newSettings);
    }
  };

  // معالجة تغيير نوع الضريبة (شاملة أو غير شاملة)
  const handleInclusiveChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSettings = { ...settings, inclusive: e.target.checked };
    setSettings(newSettings);
    onChange(newSettings);
  };

  return (
    <div className={`tax-settings ${className}`}>
      <div className="p-4 border border-gray-200 rounded-md bg-gray-50">
        <h3 className="text-lg font-medium mb-3">
          <span>إعدادات الضريبة</span>
          <span className="text-xs text-gray-500 mr-1">Tax Settings</span>
        </h3>

        <div className="space-y-3">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="tax-enabled"
              checked={settings.enabled}
              onChange={handleEnabledChange}
              className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-2 focus:ring-primary/20 focus:ring-offset-0"
            />
            <label htmlFor="tax-enabled" className="mr-2 text-sm">
              <span>تفعيل الضريبة</span>
              <span className="text-xs text-gray-500 mr-1">Enable Tax</span>
            </label>
          </div>

          {settings.enabled && (
            <>
              <div className="flex flex-col">
                <label htmlFor="tax-rate" className="text-sm mb-1">
                  <span>نسبة الضريبة (%)</span>
                  <span className="text-xs text-gray-500 mr-1">Tax Rate (%)</span>
                </label>
                <div className="flex items-center">
                  <input
                    type="number"
                    id="tax-rate"
                    value={settings.rate}
                    onChange={handleRateChange}
                    min="0"
                    max="100"
                    step="0.01"
                    className="p-2 border border-gray-300 rounded-md w-24 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                  <span className="mr-2">%</span>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="tax-inclusive"
                  checked={settings.inclusive}
                  onChange={handleInclusiveChange}
                  className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-2 focus:ring-primary/20 focus:ring-offset-0"
                />
                <label htmlFor="tax-inclusive" className="mr-2 text-sm">
                  <span>الأسعار شاملة الضريبة</span>
                  <span className="text-xs text-gray-500 mr-1">Prices Include Tax</span>
                </label>
              </div>

              <div className="mt-2 p-2 bg-blue-50 border border-blue-100 rounded-md text-sm text-blue-800">
                {settings.inclusive ? (
                  <p>
                    <span>الأسعار المدخلة تشمل الضريبة بالفعل. سيتم حساب قيمة الضريبة من السعر الإجمالي.</span>
                    <br />
                    <span className="text-xs">Entered prices already include tax. Tax amount will be calculated from the total price.</span>
                  </p>
                ) : (
                  <p>
                    <span>سيتم إضافة الضريبة إلى الأسعار المدخلة. المجموع النهائي سيكون السعر + الضريبة.</span>
                    <br />
                    <span className="text-xs">Tax will be added to entered prices. Final total will be price + tax.</span>
                  </p>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
