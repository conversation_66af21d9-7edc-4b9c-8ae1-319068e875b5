'use client';

import { useState } from 'react';
import { Product } from '@/types/inventory';
import { formatCurrency } from '@/lib/utils';

interface ProductCardProps {
  product: Product;
  inStock: boolean;
  showImage?: boolean;
  onAddToCart: (productId: string) => void;
  onViewDetails?: (productId: string) => void;
}

export function ProductCard({
  product,
  inStock,
  showImage = true,
  onAddToCart,
  onViewDetails
}: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const handleAddToCart = () => {
    if (inStock) {
      onAddToCart(product.id);
    }
  };

  return (
    <div
      className={`relative p-3 border rounded-md transition-all ${
        isHovered && inStock ? 'shadow-md border-blue-300' : 'border-gray-300'
      } ${inStock ? 'cursor-pointer' : 'opacity-70 bg-gray-50'}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleAddToCart}
    >
      {showImage && product.images && product.images.length > 0 ? (
        <div className="h-24 flex items-center justify-center mb-2 overflow-hidden">
          <img
            src={product.images[0]}
            alt={product.name}
            className="max-h-full max-w-full object-contain"
          />
        </div>
      ) : (
        <div className="h-24 flex items-center justify-center mb-2 bg-gray-100 rounded-md">
          <span className="text-gray-400 text-3xl">
            {product.name.charAt(0).toUpperCase()}
          </span>
        </div>
      )}

      <div className="text-sm font-medium line-clamp-2 min-h-[2.5rem]">{product.name}</div>
      <div className="text-xs text-gray-500 mb-2">{product.sku}</div>

      <div className="flex justify-between items-center">
        <span className="font-bold text-primary">{formatCurrency(product.price)}</span>
        {!inStock && (
          <span className="text-xs text-red-600 font-medium">نفذ من المخزون</span>
        )}
      </div>

      {isHovered && inStock && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center flex-col gap-2 p-3">
          <button
            className="w-full py-1.5 bg-primary text-white rounded-md text-sm hover:bg-primary-dark transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onAddToCart(product.id);
            }}
          >
            إضافة للسلة
          </button>

          {onViewDetails && (
            <button
              className="w-full py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                onViewDetails(product.id);
              }}
            >
              التفاصيل
            </button>
          )}
        </div>
      )}
    </div>
  );
}
