import React from 'react';
import { cn } from '@/lib/utils';

// Enhanced FormField Component
interface FormFieldProps {
  id: string;
  label?: string;
  helpText?: string;
  error?: string;
  required?: boolean;
  children: React.ReactNode;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  id,
  label,
  helpText,
  error,
  required,
  children,
  className
}) => {
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="space-y-1">
          <label
            htmlFor={id}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </label>
        </div>
      )}

      {React.cloneElement(children as React.ReactElement, { id })}

      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}

      {helpText && !error && (
        <p className="text-sm text-muted-foreground">{helpText}</p>
      )}
    </div>
  );
};

// Form Actions مع alignment صحيح
interface FormActionsProps {
  children: React.ReactNode;
  align?: 'start' | 'center' | 'end';
  className?: string;
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  align = 'end',
  className
}) => {
  const alignClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end'
  };

  return (
    <div className={cn(
      "flex items-center space-x-4 pt-4",
      alignClasses[align],
      className
    )}>
      {children}
    </div>
  );
};

// Form Section مع collapsible
interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  collapsible?: boolean;
  defaultOpen?: boolean;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  collapsible = false,
  defaultOpen = true
}) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);

  return (
    <div className="space-y-4">
      <div className="space-y-1">
        {collapsible ? (
          <button
            type="button"
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center justify-between w-full text-left"
            aria-expanded={isOpen ? "true" : "false"}
          >
            <div>
              <h3 className="text-lg font-semibold text-foreground heading">
                {title}
              </h3>
              {description && (
                <p className="text-sm text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
            <span className="ml-2">
              {isOpen ? '−' : '+'}
            </span>
          </button>
        ) : (
          <div>
            <h3 className="text-lg font-semibold text-foreground heading">
              {title}
            </h3>
            {description && (
              <p className="text-sm text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        )}
      </div>

      {(!collapsible || isOpen) && (
        <div data-testid="section-content">
          {children}
        </div>
      )}
    </div>
  );
};
