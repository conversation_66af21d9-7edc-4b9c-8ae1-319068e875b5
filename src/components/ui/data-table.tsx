'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from './button';
import { Input } from './input';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, Search, ArrowUpDown } from 'lucide-react';

interface Column<T> {
  key: string;
  header: string;
  cell: (item: T) => React.ReactNode;
  sortable?: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  searchable?: boolean;
  searchKeys?: string[];
  pagination?: boolean;
  pageSize?: number;
  className?: string;
}

/**
 * مكون جدول بيانات محسن مع دعم للبحث والترتيب والتصفح
 */
export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  searchable = true,
  searchKeys = [],
  pagination = true,
  pageSize = 10,
  className,
}: DataTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // إعادة تعيين الصفحة الحالية عند تغيير البيانات
  useEffect(() => {
    setCurrentPage(1);
  }, [data]);

  // تصفية البيانات حسب مصطلح البحث
  const filteredData = useMemo(() => {
    if (!searchTerm) return data;

    return data.filter((item) => {
      // إذا لم يتم تحديد مفاتيح البحث، ابحث في جميع الحقول
      const keysToSearch = searchKeys.length > 0 ? searchKeys : Object.keys(item);

      return keysToSearch.some((key) => {
        const value = item[key];
        if (value == null) return false;
        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });
  }, [data, searchTerm, searchKeys]);

  // ترتيب البيانات
  const sortedData = useMemo(() => {
    if (!sortConfig) return filteredData;

    return [...filteredData].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;
      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return sortConfig.direction === 'asc'
        ? aValue - bValue
        : bValue - aValue;
    });
  }, [filteredData, sortConfig]);

  // تقسيم البيانات إلى صفحات
  const paginatedData = useMemo(() => {
    if (!pagination) return sortedData;

    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, currentPage, pageSize, pagination]);

  // إجمالي عدد الصفحات
  const totalPages = useMemo(() => {
    return Math.ceil(sortedData.length / pageSize);
  }, [sortedData, pageSize]);

  // تغيير ترتيب العمود
  const handleSort = (key: string) => {
    setSortConfig((prevConfig) => {
      if (prevConfig?.key === key) {
        return prevConfig.direction === 'asc'
          ? { key, direction: 'desc' }
          : null;
      }
      return { key, direction: 'asc' };
    });
  };

  // عرض صف في الجدول
  const renderTableRow = (item: T, index: number) => (
    <tr
      key={index}
      className={cn(
        'border-b transition-colors hover:bg-gray-50 dark:hover:bg-gray-800',
        index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800/50'
      )}
    >
      {columns.map((column) => (
        <td key={column.key} className="px-4 py-3 text-sm">
          {column.cell(item)}
        </td>
      ))}
    </tr>
  );

  return (
    <div className={cn('space-y-4', className)}>
      {/* شريط البحث */}
      {searchable && (
        <div className="relative">
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
          <Input
            type="search"
            placeholder="بحث..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>
      )}

      {/* الجدول */}
      <div className="overflow-hidden rounded-lg border">
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b bg-gray-100 dark:bg-gray-800">
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className="px-4 py-3 text-right text-sm font-medium text-gray-500"
                  >
                    {column.sortable ? (
                      <button
                        className="flex items-center gap-1"
                        onClick={() => handleSort(column.key)}
                      >
                        {column.header}
                        <ArrowUpDown
                          className={cn(
                            'h-4 w-4',
                            sortConfig?.key === column.key
                              ? 'text-primary'
                              : 'text-gray-400'
                          )}
                        />
                      </button>
                    ) : (
                      column.header
                    )}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {paginatedData.length === 0 ? (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-4 py-8 text-center text-gray-500"
                  >
                    لا توجد بيانات
                  </td>
                </tr>
              ) : (
                paginatedData.map(renderTableRow)
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* شريط التصفح */}
      {pagination && totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            عرض {(currentPage - 1) * pageSize + 1} إلى{' '}
            {Math.min(currentPage * pageSize, sortedData.length)} من{' '}
            {sortedData.length} سجل
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <span className="text-sm">
              {currentPage} من {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
