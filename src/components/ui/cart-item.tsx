'use client';

import { SaleItem } from '@/types/pos';
import { formatCurrency } from '@/lib/utils';

interface CartItemProps {
  item: SaleItem;
  allowDiscount?: boolean;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onOpenDiscountModal?: (itemId: string) => void;
}

export function CartItem({
  item,
  allowDiscount = true,
  onUpdateQuantity,
  onRemoveItem,
  onOpenDiscountModal
}: CartItemProps) {
  return (
    <div className="flex flex-col border-b py-2">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="font-medium text-sm">{item.name}</div>
          <div className="text-xs text-gray-500">{item.sku}</div>
          {item.discountAmount > 0 && (
            <div className="text-xs text-green-600 mt-0.5">
              خصم: {item.discountType === 'percentage'
                ? `${item.discountValue}%`
                : formatCurrency(item.discountAmount)}
            </div>
          )}
        </div>
        <button
          onClick={() => onRemoveItem(item.id)}
          className="text-red-500 hover:text-red-700 p-1"
          title="حذف"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="flex justify-between items-center mt-2">
        <div className="flex items-center">
          <button
            onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
            className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full hover:bg-gray-300"
          >
            -
          </button>
          <span className="mx-2 min-w-[1.5rem] text-center">{item.quantity}</span>
          <button
            onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
            className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full hover:bg-gray-300"
          >
            +
          </button>
        </div>

        <div className="flex items-center">
          <span className="font-medium">{formatCurrency(item.subtotal)}</span>
          {allowDiscount && onOpenDiscountModal && (
            <button
              onClick={() => onOpenDiscountModal(item.id)}
              className="ml-2 text-blue-600 hover:text-blue-800 p-1"
              title="خصم"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
