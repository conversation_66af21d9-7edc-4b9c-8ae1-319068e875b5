'use client';

import { useState, useEffect } from 'react';
import { TaxType, DEFAULT_TAX_TYPE, TAXES, getTaxInfo, cn } from '@/lib/utils';
import { useI18n } from '@/lib/i18n';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { InfoCircledIcon } from '@radix-ui/react-icons';

export interface EnhancedTaxSettings {
  readonly enabled: boolean;
  readonly type: TaxType;
  readonly rate: number;
  readonly inclusive: boolean;
  readonly registrationNumber: string;
  readonly exemptionCategories: string[];
  readonly zeroRatedCategories: string[];
  readonly standardRatedCategories: string[];
  readonly effectiveDate: string;
  readonly showOnInvoices: boolean;
  readonly applyToAllItems: boolean;
}

export const DEFAULT_ENHANCED_TAX_SETTINGS: EnhancedTaxSettings = {
  enabled: true,
  type: DEFAULT_TAX_TYPE,
  rate: 5, // 5% ضريبة القيمة المضافة الافتراضية في الإمارات
  inclusive: false,
  registrationNumber: '',
  exemptionCategories: [],
  zeroRatedCategories: [],
  standardRatedCategories: [],
  effectiveDate: new Date().toISOString().split('T')[0],
  showOnInvoices: true,
  applyToAllItems: true
};

interface EnhancedTaxSettingsProps {
  readonly onChange: (settings: EnhancedTaxSettings) => void;
  readonly initialSettings?: EnhancedTaxSettings;
  readonly className?: string;
}

export default function EnhancedTaxSettings({
  onChange,
  initialSettings = DEFAULT_ENHANCED_TAX_SETTINGS,
  className = ''
}: EnhancedTaxSettingsProps) {
  const { t, language } = useI18n();
  const [settings, setSettings] = useState<EnhancedTaxSettings>(initialSettings);
  const [activeTab, setActiveTab] = useState('general');
  const [newCategory, setNewCategory] = useState('');
  const [categoryType, setCategoryType] = useState<'exempt' | 'zeroRated' | 'standardRated'>('exempt');

  // تحديث الإعدادات عند تغيير القيمة من الخارج
  // Update settings when value changes from outside
  useEffect(() => {
    setSettings(initialSettings);
  }, [initialSettings]);

  // تحديث الإعدادات وإرسالها للأعلى
  // Update settings and send them up
  const updateSettings = (newSettings: Partial<EnhancedTaxSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    onChange(updatedSettings);
  };

  // معالجة تغيير تفعيل الضريبة
  // Handle tax enable change
  const handleEnabledChange = (checked: boolean) => {
    updateSettings({ enabled: checked });
  };

  // معالجة تغيير نوع الضريبة
  // Handle tax type change
  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newType = e.target.value as TaxType;
    const taxInfo = getTaxInfo(newType);
    updateSettings({ 
      type: newType,
      rate: taxInfo.defaultRate
    });
  };

  // معالجة تغيير نسبة الضريبة
  // Handle tax rate change
  const handleRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rate = parseFloat(e.target.value);
    if (!isNaN(rate) && rate >= 0 && rate <= 100) {
      updateSettings({ rate });
    }
  };

  // معالجة تغيير نوع الضريبة (شاملة أو غير شاملة)
  // Handle tax inclusion change
  const handleInclusiveChange = (checked: boolean) => {
    updateSettings({ inclusive: checked });
  };

  // معالجة تغيير رقم التسجيل الضريبي
  // Handle tax registration number change
  const handleRegistrationNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateSettings({ registrationNumber: e.target.value });
  };

  // معالجة تغيير تاريخ سريان الضريبة
  // Handle tax effective date change
  const handleEffectiveDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateSettings({ effectiveDate: e.target.value });
  };

  // معالجة تغيير إظهار الضريبة على الفواتير
  // Handle show on invoices change
  const handleShowOnInvoicesChange = (checked: boolean) => {
    updateSettings({ showOnInvoices: checked });
  };

  // معالجة تغيير تطبيق الضريبة على جميع العناصر
  // Handle apply to all items change
  const handleApplyToAllItemsChange = (checked: boolean) => {
    updateSettings({ applyToAllItems: checked });
  };

  // إضافة فئة جديدة
  // Add new category
  const handleAddCategory = () => {
    if (!newCategory.trim()) return;

    let updatedSettings: EnhancedTaxSettings;

    if (categoryType === 'exempt') {
      updatedSettings = {
        ...settings,
        exemptionCategories: [...settings.exemptionCategories, newCategory.trim()]
      };
    } else if (categoryType === 'zeroRated') {
      updatedSettings = {
        ...settings,
        zeroRatedCategories: [...settings.zeroRatedCategories, newCategory.trim()]
      };
    } else {
      updatedSettings = {
        ...settings,
        standardRatedCategories: [...settings.standardRatedCategories, newCategory.trim()]
      };
    }

    setSettings(updatedSettings);
    onChange(updatedSettings);
    setNewCategory('');
  };

  // حذف فئة
  // Remove category
  const handleRemoveCategory = (category: string, type: 'exempt' | 'zeroRated' | 'standardRated') => {
    let updatedSettings: EnhancedTaxSettings;

    if (type === 'exempt') {
      updatedSettings = {
        ...settings,
        exemptionCategories: settings.exemptionCategories.filter(c => c !== category)
      };
    } else if (type === 'zeroRated') {
      updatedSettings = {
        ...settings,
        zeroRatedCategories: settings.zeroRatedCategories.filter(c => c !== category)
      };
    } else {
      updatedSettings = {
        ...settings,
        standardRatedCategories: settings.standardRatedCategories.filter(c => c !== category)
      };
    }

    setSettings(updatedSettings);
    onChange(updatedSettings);
  };

  return (
    <div className={cn('enhanced-tax-settings', className)}>
      <Card>
        <CardHeader>
          <CardTitle>
            {t('tax.settings') || 'إعدادات الضريبة'}
            <span className="text-sm text-muted-foreground mr-2">Tax Settings</span>
          </CardTitle>
          <CardDescription>
            {t('tax.settingsDescription') || 'إدارة إعدادات الضريبة وفقًا لمتطلبات الهيئة الاتحادية للضرائب'}
            <span className="text-xs block mt-1">Manage tax settings according to Federal Tax Authority requirements</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 space-x-reverse mb-6">
            <Switch
              id="tax-enabled"
              checked={settings.enabled}
              onCheckedChange={handleEnabledChange}
            />
            <Label htmlFor="tax-enabled" className="text-base">
              {t('tax.enableTax') || 'تفعيل الضريبة'}
              <span className="text-sm text-muted-foreground mr-2">Enable Tax</span>
            </Label>
          </div>

          {settings.enabled && (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="general">
                  {t('tax.general') || 'عام'}
                  <span className="text-xs block">General</span>
                </TabsTrigger>
                <TabsTrigger value="categories">
                  {t('tax.categories') || 'الفئات'}
                  <span className="text-xs block">Categories</span>
                </TabsTrigger>
                <TabsTrigger value="advanced">
                  {t('tax.advanced') || 'متقدم'}
                  <span className="text-xs block">Advanced</span>
                </TabsTrigger>
              </TabsList>

              {/* علامة التبويب العامة */}
              {/* General Tab */}
              <TabsContent value="general" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="tax-type">
                      {t('tax.type') || 'نوع الضريبة'}
                      <span className="text-sm text-muted-foreground mr-2">Tax Type</span>
                    </Label>
                    <select
                      id="tax-type"
                      value={settings.type}
                      onChange={handleTypeChange}
                      className="w-full p-2 border border-input bg-background rounded-md"
                    >
                      {TAXES.map(tax => (
                        <option key={tax.type} value={tax.type}>
                          {language === 'ar' ? tax.nameAr : tax.nameEn}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tax-rate">
                      {t('tax.rate') || 'نسبة الضريبة (%)'}
                      <span className="text-sm text-muted-foreground mr-2">Tax Rate (%)</span>
                    </Label>
                    <div className="flex items-center">
                      <Input
                        id="tax-rate"
                        type="number"
                        value={settings.rate}
                        onChange={handleRateChange}
                        min="0"
                        max="100"
                        step="0.01"
                        className="w-24"
                      />
                      <span className="mr-2">%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tax-registration-number">
                    {t('tax.registrationNumber') || 'رقم التسجيل الضريبي'}
                    <span className="text-sm text-muted-foreground mr-2">Tax Registration Number (TRN)</span>
                  </Label>
                  <Input
                    id="tax-registration-number"
                    value={settings.registrationNumber}
                    onChange={handleRegistrationNumberChange}
                    placeholder="123456789012345"
                  />
                  <p className="text-sm text-muted-foreground">
                    {t('tax.registrationNumberDescription') || 'رقم التسجيل الضريبي الخاص بك لدى الهيئة الاتحادية للضرائب'}
                    <span className="text-xs block">Your tax registration number with the Federal Tax Authority</span>
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tax-effective-date">
                    {t('tax.effectiveDate') || 'تاريخ سريان الضريبة'}
                    <span className="text-sm text-muted-foreground mr-2">Tax Effective Date</span>
                  </Label>
                  <Input
                    id="tax-effective-date"
                    type="date"
                    value={settings.effectiveDate}
                    onChange={handleEffectiveDateChange}
                  />
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <Switch
                    id="tax-inclusive"
                    checked={settings.inclusive}
                    onCheckedChange={handleInclusiveChange}
                  />
                  <Label htmlFor="tax-inclusive">
                    {t('tax.inclusive') || 'الأسعار شاملة الضريبة'}
                    <span className="text-sm text-muted-foreground mr-2">Prices Include Tax</span>
                  </Label>
                </div>

                <div className="bg-blue-50 border border-blue-100 rounded-md p-4 text-blue-800 text-sm mt-4">
                  <div className="flex">
                    <InfoCircledIcon className="h-5 w-5 ml-2 flex-shrink-0" />
                    <div>
                      {settings.inclusive ? (
                        <>
                          <p className="font-medium">
                            {t('tax.inclusiveExplanation') || 'الأسعار المدخلة تشمل الضريبة بالفعل'}
                          </p>
                          <p className="mt-1">
                            {t('tax.inclusiveDetails') || 'سيتم حساب قيمة الضريبة من السعر الإجمالي. مثال: سعر بقيمة 105 درهم يتضمن 5 دراهم ضريبة (بنسبة 5٪).'}
                          </p>
                          <p className="text-xs mt-2">
                            Entered prices already include tax. Tax amount will be calculated from the total price. Example: A price of AED 105 includes AED 5 tax (at 5%).
                          </p>
                        </>
                      ) : (
                        <>
                          <p className="font-medium">
                            {t('tax.exclusiveExplanation') || 'الأسعار المدخلة لا تشمل الضريبة'}
                          </p>
                          <p className="mt-1">
                            {t('tax.exclusiveDetails') || 'سيتم إضافة الضريبة إلى الأسعار المدخلة. مثال: سعر بقيمة 100 درهم + 5 دراهم ضريبة (بنسبة 5٪) = 105 دراهم إجمالي.'}
                          </p>
                          <p className="text-xs mt-2">
                            Entered prices do not include tax. Tax will be added to entered prices. Example: A price of AED 100 + AED 5 tax (at 5%) = AED 105 total.
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* علامة تبويب الفئات */}
              {/* Categories Tab */}
              <TabsContent value="categories" className="space-y-4 mt-4">
                <div className="bg-amber-50 border border-amber-100 rounded-md p-4 text-amber-800 text-sm">
                  <div className="flex">
                    <InfoCircledIcon className="h-5 w-5 ml-2 flex-shrink-0" />
                    <div>
                      <p className="font-medium">
                        {t('tax.categoriesExplanation') || 'فئات الضريبة المختلفة'}
                      </p>
                      <p className="mt-1">
                        {t('tax.categoriesDetails') || 'حدد الفئات المعفاة من الضريبة، والفئات ذات النسبة الصفرية، والفئات ذات النسبة القياسية وفقًا لمتطلبات الهيئة الاتحادية للضرائب.'}
                      </p>
                      <p className="text-xs mt-2">
                        Define tax-exempt categories, zero-rated categories, and standard-rated categories according to Federal Tax Authority requirements.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Input
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      placeholder={t('tax.newCategory') || 'فئة جديدة...'}
                      className="flex-1"
                    />
                    <select
                      value={categoryType}
                      onChange={(e) => setCategoryType(e.target.value as any)}
                      className="p-2 border border-input bg-background rounded-md"
                    >
                      <option value="exempt">{t('tax.exempt') || 'معفاة'}</option>
                      <option value="zeroRated">{t('tax.zeroRated') || 'نسبة صفرية'}</option>
                      <option value="standardRated">{t('tax.standardRated') || 'نسبة قياسية'}</option>
                    </select>
                    <button
                      type="button"
                      onClick={handleAddCategory}
                      className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
                      disabled={!newCategory.trim()}
                    >
                      {t('common.add') || 'إضافة'}
                    </button>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* الفئات المعفاة */}
                    {/* Exempt Categories */}
                    <div>
                      <h3 className="font-medium mb-2">
                        {t('tax.exemptCategories') || 'الفئات المعفاة'}
                        <span className="text-xs block text-muted-foreground">Exempt Categories</span>
                      </h3>
                      <ul className="space-y-2">
                        {settings.exemptionCategories.length === 0 ? (
                          <li className="text-sm text-muted-foreground">
                            {t('tax.noCategories') || 'لا توجد فئات'}
                          </li>
                        ) : (
                          settings.exemptionCategories.map((category, index) => (
                            <li key={index} className="flex justify-between items-center bg-gray-50 p-2 rounded-md">
                              <span>{category}</span>
                              <button
                                type="button"
                                onClick={() => handleRemoveCategory(category, 'exempt')}
                                className="text-red-500 hover:text-red-700"
                              >
                                ×
                              </button>
                            </li>
                          ))
                        )}
                      </ul>
                    </div>

                    {/* الفئات ذات النسبة الصفرية */}
                    {/* Zero-Rated Categories */}
                    <div>
                      <h3 className="font-medium mb-2">
                        {t('tax.zeroRatedCategories') || 'الفئات ذات النسبة الصفرية'}
                        <span className="text-xs block text-muted-foreground">Zero-Rated Categories</span>
                      </h3>
                      <ul className="space-y-2">
                        {settings.zeroRatedCategories.length === 0 ? (
                          <li className="text-sm text-muted-foreground">
                            {t('tax.noCategories') || 'لا توجد فئات'}
                          </li>
                        ) : (
                          settings.zeroRatedCategories.map((category, index) => (
                            <li key={index} className="flex justify-between items-center bg-gray-50 p-2 rounded-md">
                              <span>{category}</span>
                              <button
                                type="button"
                                onClick={() => handleRemoveCategory(category, 'zeroRated')}
                                className="text-red-500 hover:text-red-700"
                              >
                                ×
                              </button>
                            </li>
                          ))
                        )}
                      </ul>
                    </div>

                    {/* الفئات ذات النسبة القياسية */}
                    {/* Standard-Rated Categories */}
                    <div>
                      <h3 className="font-medium mb-2">
                        {t('tax.standardRatedCategories') || 'الفئات ذات النسبة القياسية'}
                        <span className="text-xs block text-muted-foreground">Standard-Rated Categories</span>
                      </h3>
                      <ul className="space-y-2">
                        {settings.standardRatedCategories.length === 0 ? (
                          <li className="text-sm text-muted-foreground">
                            {t('tax.noCategories') || 'لا توجد فئات'}
                          </li>
                        ) : (
                          settings.standardRatedCategories.map((category, index) => (
                            <li key={index} className="flex justify-between items-center bg-gray-50 p-2 rounded-md">
                              <span>{category}</span>
                              <button
                                type="button"
                                onClick={() => handleRemoveCategory(category, 'standardRated')}
                                className="text-red-500 hover:text-red-700"
                              >
                                ×
                              </button>
                            </li>
                          ))
                        )}
                      </ul>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* علامة التبويب المتقدمة */}
              {/* Advanced Tab */}
              <TabsContent value="advanced" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Switch
                      id="show-on-invoices"
                      checked={settings.showOnInvoices}
                      onCheckedChange={handleShowOnInvoicesChange}
                    />
                    <Label htmlFor="show-on-invoices">
                      {t('tax.showOnInvoices') || 'إظهار الضريبة على الفواتير'}
                      <span className="text-sm text-muted-foreground mr-2">Show Tax on Invoices</span>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Switch
                      id="apply-to-all-items"
                      checked={settings.applyToAllItems}
                      onCheckedChange={handleApplyToAllItemsChange}
                    />
                    <Label htmlFor="apply-to-all-items">
                      {t('tax.applyToAllItems') || 'تطبيق الضريبة على جميع العناصر'}
                      <span className="text-sm text-muted-foreground mr-2">Apply Tax to All Items</span>
                    </Label>
                  </div>

                  <div className="bg-gray-50 border border-gray-200 rounded-md p-4 text-gray-800 text-sm mt-4">
                    <h3 className="font-medium mb-2">
                      {t('tax.ftaRequirements') || 'متطلبات الهيئة الاتحادية للضرائب'}
                      <span className="text-xs block text-muted-foreground">Federal Tax Authority Requirements</span>
                    </h3>
                    <ul className="list-disc list-inside space-y-1">
                      <li>
                        {t('tax.requirement1') || 'يجب أن تحتوي الفواتير الضريبية على رقم التسجيل الضريبي'}
                        <span className="text-xs block text-muted-foreground">Tax invoices must include the Tax Registration Number</span>
                      </li>
                      <li>
                        {t('tax.requirement2') || 'يجب أن تكون الفواتير الضريبية باللغتين العربية والإنجليزية'}
                        <span className="text-xs block text-muted-foreground">Tax invoices must be in both Arabic and English</span>
                      </li>
                      <li>
                        {t('tax.requirement3') || 'يجب أن تحتوي الفواتير الضريبية على رمز الاستجابة السريعة (QR)'}
                        <span className="text-xs block text-muted-foreground">Tax invoices must include a QR code</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
