import * as React from "react";
import Link from 'next/link';
import { ChevronLeft } from "lucide-react";
import { cn } from "@/lib/utils";

// مكون Breadcrumb الرئيسي
export interface BreadcrumbSegment {
  title: string;
  href?: string;
}

export interface BreadcrumbProps extends React.ComponentPropsWithoutRef<"nav"> {
  separator?: React.ReactNode;
  children?: React.ReactNode;
  segments?: BreadcrumbSegment[];
}

export function Breadcrumb({
  separator = <ChevronLeft className="h-4 w-4" />,
  className,
  children,
  segments,
  ...props
}: BreadcrumbProps) {
  return (
    <nav
      aria-label="Breadcrumb"
      className={cn("flex", className)}
      {...props}
    >
      {segments ? (
        <BreadcrumbList>
          {segments.map((segment, index) => (
            <React.Fragment key={index}>
              <BreadcrumbItem>
                {segment.href && index < segments.length - 1 ? (
                  <BreadcrumbLink href={segment.href}>
                    {segment.title}
                  </BreadcrumbLink>
                ) : (
                  <span className="font-medium text-foreground">
                    {segment.title}
                  </span>
                )}
              </BreadcrumbItem>
              {index < segments.length - 1 && (
                <BreadcrumbSeparator>{separator}</BreadcrumbSeparator>
              )}
            </React.Fragment>
          ))}
        </BreadcrumbList>
      ) : (
        children
      )}
    </nav>
  );
}

// مكون BreadcrumbList
export interface BreadcrumbListProps extends React.ComponentPropsWithoutRef<"ol"> {
  children: React.ReactNode;
}

export function BreadcrumbList({
  className,
  children,
  ...props
}: BreadcrumbListProps) {
  return (
    <ol
      className={cn(
        "flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",
        className
      )}
      {...props}
    >
      {children}
    </ol>
  );
}

// مكون BreadcrumbItem
export interface BreadcrumbItemProps extends React.ComponentPropsWithoutRef<"li"> {
  children: React.ReactNode;
}

export function BreadcrumbItem({
  className,
  children,
  ...props
}: BreadcrumbItemProps) {
  return (
    <li
      className={cn("inline-flex items-center gap-1.5", className)}
      {...props}
    >
      {children}
    </li>
  );
}

// مكون BreadcrumbLink
export interface BreadcrumbLinkProps extends React.ComponentPropsWithoutRef<"a"> {
  asChild?: boolean;
  children: React.ReactNode;
  href?: string;
}

export function BreadcrumbLink({
  asChild = false,
  className,
  children,
  href,
  ...props
}: BreadcrumbLinkProps) {
  if (href) {
    return (
      <Link
        href={href}
        className={cn("transition-colors hover:text-foreground", className)}
        {...props}
      >
        {children}
      </Link>
    );
  }

  return (
    <a
      className={cn("transition-colors hover:text-foreground", className)}
      {...props}
    >
      {children}
    </a>
  );
}

// مكون BreadcrumbSeparator
export interface BreadcrumbSeparatorProps extends React.ComponentPropsWithoutRef<"li"> {
  children?: React.ReactNode;
}

export function BreadcrumbSeparator({
  className,
  children = <ChevronLeft className="h-3.5 w-3.5" />,
  ...props
}: BreadcrumbSeparatorProps) {
  return (
    <li
      className={cn("mx-1 text-muted-foreground", className)}
      aria-hidden="true"
      {...props}
    >
      {children}
    </li>
  );
}

// مكون BreadcrumbEllipsis
export interface BreadcrumbEllipsisProps extends React.ComponentPropsWithoutRef<"span"> {
  children?: React.ReactNode;
}

export function BreadcrumbEllipsis({
  className,
  children = "...",
  ...props
}: BreadcrumbEllipsisProps) {
  return (
    <span
      className={cn("flex h-9 w-9 items-center justify-center", className)}
      {...props}
    >
      {children}
    </span>
  );
}
