'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Monitor, Download, Settings, Shield } from 'lucide-react';

function ElectronFeatures() {
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    // فحص إذا كان التطبيق يعمل في Electron
    setIsElectron(typeof window !== 'undefined' && window.navigator.userAgent.includes('Electron'));
  }, []);

  if (!isElectron) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            تطبيق سطح المكتب
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            هذه الصفحة مخصصة لتطبيق سطح المكتب. يرجى تحميل التطبيق للوصول إلى هذه الميزات.
          </p>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            تحميل التطبيق
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            ميزات تطبيق سطح المكتب
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  الأمان المحلي
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  بياناتك محفوظة محلياً على جهازك مع تشفير متقدم
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  العمل بدون إنترنت
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  استخدم التطبيق حتى بدون اتصال بالإنترنت
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  إعدادات متقدمة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  تحكم كامل في إعدادات التطبيق والنظام
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  أداء محسن
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  أداء أسرع وتجربة مستخدم محسنة
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>إعدادات التطبيق</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>التحديث التلقائي</span>
            <Button variant="outline" size="sm">
              تفعيل
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <span>النسخ الاحتياطي التلقائي</span>
            <Button variant="outline" size="sm">
              تكوين
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <span>إعدادات الأمان</span>
            <Button variant="outline" size="sm">
              إدارة
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export { ElectronFeatures };
export default ElectronFeatures;
