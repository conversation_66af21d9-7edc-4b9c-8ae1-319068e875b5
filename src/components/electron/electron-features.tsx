'use client';

import { useState } from 'react';
import { useElectron } from '@/components/providers/electron-provider';
import { Button } from '@/components/ui';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { showMessageBox, showOpenDialog, showSaveDialog, getSystemInfo } from '@/lib/electron';
import { Download, Upload, Info, AlertCircle } from 'lucide-react';

/**
 * مكون ميزات Electron
 * Electron features component
 */
export function ElectronFeatures() {
  const { isElectronApp } = useElectron();
  const [systemInfo, setSystemInfo] = useState<any>(null);

  // عرض مربع حوار حفظ الملف
  // Show save file dialog
  const handleSaveDialog = async () => {
    const result = await showSaveDialog({
      title: 'حفظ الملف',
      defaultPath: 'تقرير.pdf',
      filters: [
        { name: 'PDF', extensions: ['pdf'] },
        { name: 'Excel', extensions: ['xlsx'] },
        { name: 'جميع الملفات', extensions: ['*'] }
      ],
      buttonLabel: 'حفظ'
    });

    if (!result.canceled && result.filePath) {
      await showMessageBox({
        type: 'info',
        title: 'تم الحفظ',
        message: 'تم حفظ الملف بنجاح',
        detail: `المسار: ${result.filePath}`,
        buttons: ['موافق']
      });
    }
  };

  // عرض مربع حوار فتح الملف
  // Show open file dialog
  const handleOpenDialog = async () => {
    const result = await showOpenDialog({
      title: 'فتح ملف',
      filters: [
        { name: 'PDF', extensions: ['pdf'] },
        { name: 'Excel', extensions: ['xlsx'] },
        { name: 'جميع الملفات', extensions: ['*'] }
      ],
      properties: ['openFile'],
      buttonLabel: 'فتح'
    });

    if (!result.canceled && result.filePaths.length > 0) {
      await showMessageBox({
        type: 'info',
        title: 'تم الفتح',
        message: 'تم اختيار الملف بنجاح',
        detail: `المسار: ${result.filePaths[0]}`,
        buttons: ['موافق']
      });
    }
  };

  // عرض معلومات النظام
  // Show system information
  const handleShowSystemInfo = async () => {
    const info = await getSystemInfo();
    setSystemInfo(info);

    if (isElectronApp) {
      await showMessageBox({
        type: 'info',
        title: 'معلومات النظام',
        message: 'معلومات النظام',
        detail: `
          النظام: ${info.platform}
          المعمارية: ${info.arch}
          إصدار Electron: ${info.version}
          إصدار Node.js: ${info.nodeVersion ?? 'غير متاح'}
          إصدار Chrome: ${info.chromeVersion ?? 'غير متاح'}
        `,
        buttons: ['موافق']
      });
    }
  };

  if (!isElectronApp) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>ميزات Electron</CardTitle>
          <CardDescription>هذه الميزات متاحة فقط في تطبيق سطح المكتب</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-6 bg-gray-100 rounded-md">
            <AlertCircle className="w-8 h-8 text-amber-500 mr-2" />
            <p className="text-gray-700">
              أنت تستخدم الإصدار المستند إلى الويب. قم بتنزيل تطبيق سطح المكتب للوصول إلى جميع الميزات.
            </p>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">
            تنزيل تطبيق سطح المكتب
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>ميزات Electron</CardTitle>
        <CardDescription>استخدم ميزات تطبيق سطح المكتب</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button onClick={handleSaveDialog} className="flex items-center justify-center">
            <Download className="mr-2 h-4 w-4" />
            حفظ ملف
          </Button>

          <Button onClick={handleOpenDialog} className="flex items-center justify-center">
            <Upload className="mr-2 h-4 w-4" />
            فتح ملف
          </Button>

          <Button onClick={handleShowSystemInfo} className="flex items-center justify-center">
            <Info className="mr-2 h-4 w-4" />
            معلومات النظام
          </Button>
        </div>

        {systemInfo && (
          <div className="mt-4 p-4 bg-gray-100 rounded-md">
            <h3 className="font-medium mb-2">معلومات النظام:</h3>
            <ul className="space-y-1 text-sm">
              <li><strong>النظام:</strong> {systemInfo.platform}</li>
              <li><strong>المعمارية:</strong> {systemInfo.arch}</li>
              <li><strong>إصدار Electron:</strong> {systemInfo.version}</li>
              <li><strong>إصدار Node.js:</strong> {systemInfo.nodeVersion}</li>
              <li><strong>إصدار Chrome:</strong> {systemInfo.chromeVersion}</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
