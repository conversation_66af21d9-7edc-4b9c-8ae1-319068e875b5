# مكونات Electron

هذا المجلد يحتوي على مكونات React المتعلقة بـ Electron لتطبيق أمين بلس.

## المكونات الرئيسية

- **electron-features.tsx**: مكون يعرض ميزات Electron المتاحة في تطبيق سطح المكتب.

## كيفية الاستخدام

### استخدام مكون ElectronFeatures

```tsx
import { ElectronFeatures } from '@/components/electron/electron-features';

export default function DesktopFeaturesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">ميزات تطبيق سطح المكتب</h1>
      <ElectronFeatures />
    </div>
  );
}
```

## الميزات المتاحة

مكون ElectronFeatures يوفر واجهة مستخدم للتفاعل مع ميزات Electron التالية:

- **حفظ الملفات**: عرض مربع حوار حفظ الملف.
- **فتح الملفات**: عرض مربع حوار فتح الملف.
- **معلومات النظام**: عرض معلومات النظام مثل نظام التشغيل والمعمارية وإصدار Electron.

## التوافق مع الويب

مكون ElectronFeatures يتحقق مما إذا كان التطبيق يعمل في بيئة Electron أم لا، ويعرض واجهة مستخدم مناسبة لكل حالة:

- **في بيئة Electron**: يعرض أزرار للتفاعل مع ميزات Electron.
- **في بيئة الويب**: يعرض رسالة تفيد بأن هذه الميزات متاحة فقط في تطبيق سطح المكتب.

## مثال على استخدام واجهة برمجة التطبيق

```typescript
import { showSaveDialog, showOpenDialog, showMessageBox, getSystemInfo } from '@/lib/electron-utils';

// عرض مربع حوار حفظ الملف
const handleSaveDialog = async () => {
  const result = await showSaveDialog({
    title: 'حفظ الملف',
    defaultPath: 'تقرير.pdf',
    filters: [
      { name: 'PDF', extensions: ['pdf'] },
      { name: 'Excel', extensions: ['xlsx'] },
      { name: 'جميع الملفات', extensions: ['*'] }
    ],
    buttonLabel: 'حفظ'
  });

  if (!result.canceled && result.filePath) {
    await showMessageBox({
      type: 'info',
      title: 'تم الحفظ',
      message: 'تم حفظ الملف بنجاح',
      detail: `المسار: ${result.filePath}`,
      buttons: ['موافق']
    });
  }
};
```
