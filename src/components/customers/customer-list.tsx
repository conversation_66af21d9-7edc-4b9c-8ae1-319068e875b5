'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
}

export default function CustomerList() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const response = await fetch('/api/customers');
        if (!response.ok) {
          throw new Error('فشل في جلب العملاء');
        }
        const data = await response.json();
        setCustomers(data.customers || []);
        setLoading(false);
      } catch (err) {
        setError('حدث خطأ أثناء جلب العملاء');
        setLoading(false);
        console.error(err);
      }
    };

    fetchCustomers();
  }, []);

  const handleDeleteCustomer = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع الفواتير المرتبطة به أيضًا.')) return;

    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('فشل في حذف العميل');
      }

      setCustomers(customers.filter(customer => customer.id !== id));
    } catch (err) {
      console.error(err);
      alert('حدث خطأ أثناء حذف العميل');
    }
  };

  if (loading) return <div className="text-center py-10">جاري التحميل...</div>;
  if (error) return <div className="text-center text-red-500 py-10">{error}</div>;

  return (
    <div className="container mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">العملاء</h1>
        <Link
          href="/dashboard/customers/new"
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
        >
          إضافة عميل جديد
        </Link>
      </div>

      {customers.length === 0 ? (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          لا يوجد عملاء بعد. قم بإضافة عميل جديد للبدء.
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white rounded-lg overflow-hidden shadow">
            <thead className="bg-gray-100 text-gray-700">
              <tr>
                <th className="py-3 px-4 text-right">الاسم</th>
                <th className="py-3 px-4 text-right">البريد الإلكتروني</th>
                <th className="py-3 px-4 text-right">الهاتف</th>
                <th className="py-3 px-4 text-right">العنوان</th>
                <th className="py-3 px-4 text-right">إجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {customers.map((customer) => (
                <tr key={customer.id} className="hover:bg-gray-50">
                  <td className="py-3 px-4">{customer.name}</td>
                  <td className="py-3 px-4">{customer.email}</td>
                  <td className="py-3 px-4">{customer.phone || '-'}</td>
                  <td className="py-3 px-4">{customer.address || '-'}</td>
                  <td className="py-3 px-4">
                    <div className="flex space-x-2">
                      <Link
                        href={`/dashboard/customers/${customer.id}/edit`}
                        className="text-blue-500 hover:text-blue-700 ml-3"
                      >
                        تعديل
                      </Link>
                      <button
                        type="button"
                        onClick={() => handleDeleteCustomer(customer.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
