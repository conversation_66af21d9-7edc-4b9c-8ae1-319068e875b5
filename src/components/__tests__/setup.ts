// ملف إعداد الاختبارات
// Test setup file

import '@testing-library/jest-dom';
import { jest } from '@jest/globals';

// تهيئة محاكاة لـ next/navigation
// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  })),
  usePathname: jest.fn(() => '/'),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}));

// تهيئة محاكاة لـ next-auth/react
// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
        permissions: ['VIEW_DASHBOARD', 'MANAGE_USERS'],
      },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    },
    status: 'authenticated',
  })),
  signIn: jest.fn(),
  signOut: jest.fn(),
}));

// تهيئة محاكاة لـ next-intl - تم إزالة لأن المكتبة غير مثبتة
// Mock next-intl - removed as library is not installed
// jest.mock('next-intl', () => ({
//   useTranslations: jest.fn(() => (key: string) => key),
//   useLocale: jest.fn(() => 'ar'),
// }));

// إضافة اختبار وهمي لتجنب خطأ "no tests"
describe('Setup', () => {
  it('should setup test environment', () => {
    expect(true).toBe(true);
  });
});

// تهيئة محاكاة لـ hooks
// Mock custom hooks
jest.mock('@/hooks/usePermission', () => ({
  usePermission: jest.fn(() => ({
    hasPermission: jest.fn(() => true),
  })),
}));

// تهيئة محاكاة لـ localStorage
// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
  writable: true,
});

// تهيئة محاكاة لـ matchMedia
// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
