'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  RefreshCcw, 
  Calendar, 
  Clock, 
  Edit, 
  Trash2, 
  Plus,
  PauseCircle,
  PlayCircle,
  AlertCircle
} from 'lucide-react';

// نوع بيانات المصروف المتكرر
// Recurring expense data type
interface RecurringExpense {
  id: string;
  title: string;
  description?: string;
  amount: number;
  taxAmount?: number;
  totalAmount: number;
  expenseDate: string;
  dueDate?: string;
  paymentStatus: string;
  paymentMethod?: string;
  recurring: boolean;
  recurringPeriod: string;
  recurringEndDate?: string;
  isPaused: boolean;
  nextOccurrence: string;
  category?: {
    id: string;
    name: string;
    color?: string;
  };
}

/**
 * مكون إدارة المصروفات المتكررة
 * Recurring Expenses Management Component
 */
export function RecurringExpenses() {
  const [expenses, setExpenses] = useState<RecurringExpense[]>([]);
  const [loading, setLoading] = useState(true);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<RecurringExpense | null>(null);
  const [confirmAction, setConfirmAction] = useState<'pause' | 'resume' | 'delete' | null>(null);
  
  // جلب المصروفات المتكررة
  // Fetch recurring expenses
  useEffect(() => {
    const fetchRecurringExpenses = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/expenses?recurring=true');
        
        if (!response.ok) {
          throw new Error('فشل في جلب المصروفات المتكررة');
        }
        
        const data = await response.json();
        setExpenses(data.data || []);
      } catch (error) {
        console.error('Error fetching recurring expenses:', error);
        toast.error('حدث خطأ أثناء جلب المصروفات المتكررة');
      } finally {
        setLoading(false);
      }
    };
    
    fetchRecurringExpenses();
  }, []);
  
  // تعديل مصروف متكرر
  // Edit recurring expense
  const handleEditExpense = (expense: RecurringExpense) => {
    // يمكن تنفيذ التوجيه إلى صفحة التعديل
    // Can implement redirection to edit page
    window.location.href = `/dashboard/expenses/edit/${expense.id}`;
  };
  
  // إيقاف مصروف متكرر
  // Pause recurring expense
  const handlePauseExpense = (expense: RecurringExpense) => {
    setSelectedExpense(expense);
    setConfirmAction('pause');
    setShowConfirmDialog(true);
  };
  
  // استئناف مصروف متكرر
  // Resume recurring expense
  const handleResumeExpense = (expense: RecurringExpense) => {
    setSelectedExpense(expense);
    setConfirmAction('resume');
    setShowConfirmDialog(true);
  };
  
  // حذف مصروف متكرر
  // Delete recurring expense
  const handleDeleteExpense = (expense: RecurringExpense) => {
    setSelectedExpense(expense);
    setConfirmAction('delete');
    setShowConfirmDialog(true);
  };
  
  // تنفيذ الإجراء المؤكد
  // Execute confirmed action
  const handleConfirmAction = async () => {
    if (!selectedExpense || !confirmAction) return;
    
    try {
      let url = `/api/expenses/${selectedExpense.id}`;
      let method = 'PATCH';
      let body: any = {};
      
      switch (confirmAction) {
        case 'pause':
          body = { isPaused: true };
          break;
        case 'resume':
          body = { isPaused: false };
          break;
        case 'delete':
          method = 'DELETE';
          break;
      }
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: method === 'DELETE' ? undefined : JSON.stringify(body),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء تنفيذ الإجراء');
      }
      
      // تحديث قائمة المصروفات
      // Update expenses list
      if (confirmAction === 'delete') {
        setExpenses(prev => prev.filter(exp => exp.id !== selectedExpense.id));
        toast.success('تم حذف المصروف المتكرر بنجاح / Recurring expense deleted successfully');
      } else {
        setExpenses(prev => 
          prev.map(exp => 
            exp.id === selectedExpense.id 
              ? { ...exp, isPaused: confirmAction === 'pause' } 
              : exp
          )
        );
        
        toast.success(
          confirmAction === 'pause'
            ? 'تم إيقاف المصروف المتكرر بنجاح / Recurring expense paused successfully'
            : 'تم استئناف المصروف المتكرر بنجاح / Recurring expense resumed successfully'
        );
      }
      
      setShowConfirmDialog(false);
    } catch (error) {
      console.error('Error executing action:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'حدث خطأ أثناء تنفيذ الإجراء / Error executing action'
      );
    }
  };
  
  // ترجمة فترة التكرار
  // Translate recurring period
  const translateRecurringPeriod = (period: string) => {
    switch (period) {
      case 'DAILY':
        return 'يومي / Daily';
      case 'WEEKLY':
        return 'أسبوعي / Weekly';
      case 'MONTHLY':
        return 'شهري / Monthly';
      case 'QUARTERLY':
        return 'ربع سنوي / Quarterly';
      case 'YEARLY':
        return 'سنوي / Yearly';
      default:
        return period;
    }
  };
  
  // ترجمة حالة الدفع
  // Translate payment status
  const translatePaymentStatus = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'مدفوع / Paid';
      case 'PENDING':
        return 'قيد الانتظار / Pending';
      case 'PARTIAL':
        return 'مدفوع جزئياً / Partial';
      case 'CANCELLED':
        return 'ملغي / Cancelled';
      default:
        return status;
    }
  };
  
  // الحصول على لون حالة الدفع
  // Get payment status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'PARTIAL':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>
          المصروفات المتكررة
          <span className="block text-sm font-normal text-muted-foreground">Recurring Expenses</span>
        </CardTitle>
        <Button onClick={() => window.location.href = '/dashboard/expenses/new'} size="sm">
          <Plus className="mr-2 h-4 w-4" />
          إضافة مصروف متكرر / Add Recurring
        </Button>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <RefreshCcw className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : expenses.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="h-12 w-12 mx-auto text-muted-foreground/50" />
            <p className="mt-2">لا توجد مصروفات متكررة / No recurring expenses</p>
            <Button 
              onClick={() => window.location.href = '/dashboard/expenses/new'} 
              variant="outline" 
              className="mt-4"
            >
              <Plus className="mr-2 h-4 w-4" />
              إضافة مصروف متكرر جديد / Add New Recurring Expense
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {expenses.map((expense) => (
              <div
                key={expense.id}
                className={`border rounded-lg p-4 ${
                  expense.isPaused 
                    ? 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50' 
                    : 'border-primary/20'
                }`}
              >
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <div className="flex items-center">
                      <h3 className="font-medium">
                        {expense.title}
                        {expense.isPaused && (
                          <Badge variant="outline" className="ml-2 text-yellow-500 border-yellow-200 dark:border-yellow-800">
                            متوقف / Paused
                          </Badge>
                        )}
                      </h3>
                    </div>
                    
                    {expense.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {expense.description}
                      </p>
                    )}
                    
                    <div className="flex flex-wrap gap-x-4 gap-y-2 mt-2 text-sm">
                      <div className="flex items-center text-muted-foreground">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{translateRecurringPeriod(expense.recurringPeriod)}</span>
                      </div>
                      
                      <div className="flex items-center text-muted-foreground">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>
                          التالي / Next: {formatDate(expense.nextOccurrence)}
                        </span>
                      </div>
                      
                      {expense.recurringEndDate && (
                        <div className="flex items-center text-muted-foreground">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          <span>
                            ينتهي في / Ends: {formatDate(expense.recurringEndDate)}
                          </span>
                        </div>
                      )}
                      
                      {expense.category && (
                        <div className="flex items-center">
                          <div 
                            className="w-3 h-3 rounded-full mr-1" 
                            style={{ backgroundColor: expense.category.color || '#FF6384' }}
                          />
                          <span>{expense.category.name}</span>
                        </div>
                      )}
                      
                      <div className={`px-2 py-0.5 rounded text-xs ${getStatusColor(expense.paymentStatus)}`}>
                        {translatePaymentStatus(expense.paymentStatus)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col md:items-end">
                    <div className="text-xl font-bold">
                      {formatCurrency(expense.totalAmount)}
                    </div>
                    
                    <div className="flex items-center space-x-1 space-x-reverse mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditExpense(expense)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        تعديل / Edit
                      </Button>
                      
                      {expense.isPaused ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResumeExpense(expense)}
                        >
                          <PlayCircle className="h-4 w-4 mr-1" />
                          استئناف / Resume
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePauseExpense(expense)}
                        >
                          <PauseCircle className="h-4 w-4 mr-1" />
                          إيقاف / Pause
                        </Button>
                      )}
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteExpense(expense)}
                        className="text-red-500 border-red-200 hover:bg-red-50 dark:border-red-800 dark:hover:bg-red-950"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        حذف / Delete
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      
      {/* مربع حوار التأكيد */}
      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {confirmAction === 'delete'
                ? 'تأكيد الحذف / Confirm Deletion'
                : confirmAction === 'pause'
                ? 'تأكيد الإيقاف / Confirm Pause'
                : 'تأكيد الاستئناف / Confirm Resume'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            {confirmAction === 'delete' ? (
              <p>
                هل أنت متأكد من حذف المصروف المتكرر "{selectedExpense?.title}"؟ لا يمكن التراجع عن هذا الإجراء.
                <br />
                <br />
                Are you sure you want to delete the recurring expense "{selectedExpense?.title}"? This action cannot be undone.
              </p>
            ) : confirmAction === 'pause' ? (
              <p>
                هل أنت متأكد من إيقاف المصروف المتكرر "{selectedExpense?.title}"؟ لن يتم إنشاء مصروفات جديدة حتى يتم استئنافه.
                <br />
                <br />
                Are you sure you want to pause the recurring expense "{selectedExpense?.title}"? No new expenses will be created until resumed.
              </p>
            ) : (
              <p>
                هل أنت متأكد من استئناف المصروف المتكرر "{selectedExpense?.title}"؟ سيتم استئناف إنشاء المصروفات الجديدة وفقاً للجدول.
                <br />
                <br />
                Are you sure you want to resume the recurring expense "{selectedExpense?.title}"? New expenses will be created according to schedule.
              </p>
            )}
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
            >
              إلغاء / Cancel
            </Button>
            <Button
              variant={confirmAction === 'delete' ? 'destructive' : 'default'}
              onClick={handleConfirmAction}
            >
              {confirmAction === 'delete'
                ? 'حذف / Delete'
                : confirmAction === 'pause'
                ? 'إيقاف / Pause'
                : 'استئناف / Resume'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
