'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/utils';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend } from 'recharts';

interface ExpenseData {
  id: number;
  title: string;
  amount: number;
  totalAmount: number;
  expenseDate: string;
  category?: {
    id: number;
    name: string;
    color?: string;
  };
}

interface ExpensesChartProps {
  expenses: ExpenseData[];
  loading?: boolean;
  period?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  onPeriodChange?: (period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly') => void;
}

/**
 * مكون الرسم البياني للمصروفات
 * Expenses Chart Component
 */
export function ExpensesChart({
  expenses,
  loading = false,
  period = 'monthly',
  onPeriodChange,
}: Readonly<ExpensesChartProps>) {
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  
  // تحويل البيانات حسب الفترة المحددة
  // Transform data based on selected period
  const transformData = () => {
    if (!expenses || expenses.length === 0) {
      return [];
    }
    
    if (chartType === 'pie') {
      // تجميع البيانات حسب الفئة للرسم البياني الدائري
      // Group data by category for pie chart
      const categoryMap = new Map<string, number>();
      
      expenses.forEach(expense => {
        const categoryName = expense.category?.name || 'أخرى / Other';
        const currentAmount = categoryMap.get(categoryName) || 0;
        categoryMap.set(categoryName, currentAmount + expense.totalAmount);
      });
      
      return Array.from(categoryMap.entries()).map(([name, value], index) => ({
        name,
        value,
        color: getColorByIndex(index),
      }));
    } else {
      // تجميع البيانات حسب الفترة للرسم البياني الشريطي
      // Group data by period for bar chart
      const periodMap = new Map<string, number>();
      
      expenses.forEach(expense => {
        const date = new Date(expense.expenseDate);
        let periodKey = '';
        
        switch (period) {
          case 'daily':
            periodKey = date.toISOString().split('T')[0];
            break;
          case 'weekly':
            // الحصول على أول يوم في الأسبوع
            // Get first day of the week
            const firstDayOfWeek = new Date(date);
            const day = date.getDay();
            const diff = date.getDate() - day + (day === 0 ? -6 : 1);
            firstDayOfWeek.setDate(diff);
            periodKey = `${firstDayOfWeek.toISOString().split('T')[0]}`;
            break;
          case 'monthly':
            periodKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
            break;
          case 'quarterly':
            const quarter = Math.floor(date.getMonth() / 3) + 1;
            periodKey = `${date.getFullYear()} Q${quarter}`;
            break;
          case 'yearly':
            periodKey = date.getFullYear().toString();
            break;
        }
        
        const currentAmount = periodMap.get(periodKey) || 0;
        periodMap.set(periodKey, currentAmount + expense.totalAmount);
      });
      
      // ترتيب البيانات حسب الفترة
      // Sort data by period
      return Array.from(periodMap.entries())
        .sort((a, b) => a[0].localeCompare(b[0]))
        .map(([name, value]) => ({
          name: formatPeriodLabel(name, period),
          value,
        }));
    }
  };
  
  // تنسيق تسمية الفترة
  // Format period label
  const formatPeriodLabel = (periodKey: string, periodType: string) => {
    switch (periodType) {
      case 'daily':
        return new Date(periodKey).toLocaleDateString('ar-AE');
      case 'weekly':
        const weekStart = new Date(periodKey);
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        return `${weekStart.toLocaleDateString('ar-AE')} - ${weekEnd.toLocaleDateString('ar-AE')}`;
      case 'monthly':
        const [year, month] = periodKey.split('-');
        const date = new Date(parseInt(year), parseInt(month) - 1, 1);
        return date.toLocaleDateString('ar-AE', { year: 'numeric', month: 'long' });
      case 'quarterly':
        return periodKey;
      case 'yearly':
        return periodKey;
      default:
        return periodKey;
    }
  };
  
  // الحصول على لون حسب الفهرس
  // Get color by index
  const getColorByIndex = (index: number) => {
    const colors = [
      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
      '#FF9F40', '#8AC926', '#1982C4', '#6A4C93', '#F94144',
    ];
    return colors[index % colors.length];
  };
  
  // تنسيق تلميح الرسم البياني
  // Format chart tooltip
  const formatTooltip = (value: number) => {
    return formatCurrency(value);
  };
  
  // بيانات الرسم البياني
  // Chart data
  const chartData = transformData();
  
  // ألوان الرسم البياني الدائري
  // Pie chart colors
  const COLORS = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#8AC926', '#1982C4', '#6A4C93', '#F94144'];
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle>
            تحليل المصروفات
            <span className="block text-sm font-normal text-muted-foreground">Expense Analysis</span>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Tabs defaultValue={chartType} onValueChange={(value) => setChartType(value as 'bar' | 'pie')}>
              <TabsList className="h-8">
                <TabsTrigger value="bar" className="text-xs px-2">شريطي / Bar</TabsTrigger>
                <TabsTrigger value="pie" className="text-xs px-2">دائري / Pie</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <Select defaultValue={period} onValueChange={(value) => onPeriodChange?.(value as any)}>
              <SelectTrigger className="h-8 w-[130px]">
                <SelectValue placeholder="الفترة / Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">يومي / Daily</SelectItem>
                <SelectItem value="weekly">أسبوعي / Weekly</SelectItem>
                <SelectItem value="monthly">شهري / Monthly</SelectItem>
                <SelectItem value="quarterly">ربع سنوي / Quarterly</SelectItem>
                <SelectItem value="yearly">سنوي / Yearly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-2">
        {loading ? (
          <div className="w-full h-[300px] flex items-center justify-center">
            <Skeleton className="w-full h-full" />
          </div>
        ) : chartData.length === 0 ? (
          <div className="w-full h-[300px] flex items-center justify-center text-muted-foreground">
            لا توجد بيانات كافية لعرض الرسم البياني
            <br />
            Not enough data to display chart
          </div>
        ) : chartType === 'bar' ? (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45} 
                textAnchor="end" 
                height={60} 
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                tickFormatter={formatTooltip}
                width={80}
              />
              <Tooltip formatter={formatTooltip} />
              <Bar dataKey="value" fill="#FF6384" />
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={formatTooltip} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )}
      </CardContent>
    </Card>
  );
}
