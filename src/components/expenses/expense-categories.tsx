'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  RefreshCcw,
  Tag,
  Palette
} from 'lucide-react';

// نوع بيانات فئة المصروفات
// Expense category data type
interface ExpenseCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive: boolean;
}

// خصائص مكون فئات المصروفات
// Expense categories component props
interface ExpenseCategoriesProps {
  onCategorySelect?: (category: ExpenseCategory) => void;
}

/**
 * مكون إدارة فئات المصروفات
 * Expense Categories Management Component
 */
export function ExpenseCategories({ onCategorySelect }: Readonly<ExpenseCategoriesProps>) {
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDialog, setShowDialog] = useState(false);
  const [formData, setFormData] = useState<Partial<ExpenseCategory>>({
    name: '',
    description: '',
    color: '#FF6384',
    isActive: true,
  });
  const [isEditing, setIsEditing] = useState(false);
  const [savingCategory, setSavingCategory] = useState(false);
  
  // جلب فئات المصروفات
  // Fetch expense categories
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/expense-categories');
        
        if (!response.ok) {
          throw new Error('فشل في جلب فئات المصروفات');
        }
        
        const data = await response.json();
        setCategories(data);
      } catch (error) {
        console.error('Error fetching expense categories:', error);
        toast.error('حدث خطأ أثناء جلب فئات المصروفات');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);
  
  // فتح نموذج إضافة فئة جديدة
  // Open add new category form
  const handleAddCategory = () => {
    setFormData({
      name: '',
      description: '',
      color: '#FF6384',
      isActive: true,
    });
    setIsEditing(false);
    setShowDialog(true);
  };
  
  // فتح نموذج تعديل فئة
  // Open edit category form
  const handleEditCategory = (category: ExpenseCategory) => {
    setFormData(category);
    setIsEditing(true);
    setShowDialog(true);
  };
  
  // تحديث قيمة في النموذج
  // Update form value
  const handleInputChange = (field: keyof ExpenseCategory, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };
  
  // حفظ الفئة (إضافة أو تعديل)
  // Save category (add or edit)
  const handleSaveCategory = async () => {
    if (!formData.name) {
      toast.error('اسم الفئة مطلوب / Category name is required');
      return;
    }
    
    setSavingCategory(true);
    
    try {
      const url = isEditing ? `/api/expense-categories/${formData.id}` : '/api/expense-categories';
      const method = isEditing ? 'PATCH' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء حفظ الفئة');
      }
      
      const savedCategory = await response.json();
      
      if (isEditing) {
        setCategories(prev => 
          prev.map(cat => cat.id === savedCategory.id ? savedCategory : cat)
        );
        toast.success('تم تحديث الفئة بنجاح / Category updated successfully');
      } else {
        setCategories(prev => [...prev, savedCategory]);
        toast.success('تم إضافة الفئة بنجاح / Category added successfully');
      }
      
      setShowDialog(false);
    } catch (error) {
      console.error('Error saving category:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'حدث خطأ أثناء حفظ الفئة / Error saving category'
      );
    } finally {
      setSavingCategory(false);
    }
  };
  
  // حذف فئة
  // Delete category
  const handleDeleteCategory = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه الفئة؟ / Are you sure you want to delete this category?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/expense-categories/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء حذف الفئة');
      }
      
      setCategories(prev => prev.filter(cat => cat.id !== id));
      toast.success('تم حذف الفئة بنجاح / Category deleted successfully');
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'حدث خطأ أثناء حذف الفئة / Error deleting category'
      );
    }
  };
  
  // اختيار فئة
  // Select category
  const handleSelectCategory = (category: ExpenseCategory) => {
    if (onCategorySelect) {
      onCategorySelect(category);
    }
  };
  
  // ألوان الفئات المقترحة
  // Suggested category colors
  const suggestedColors = [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
    '#FF9F40', '#8AC926', '#1982C4', '#6A4C93', '#F94144',
  ];
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>
          فئات المصروفات
          <span className="block text-sm font-normal text-muted-foreground">Expense Categories</span>
        </CardTitle>
        <Button onClick={handleAddCategory} size="sm">
          <Plus className="mr-2 h-4 w-4" />
          إضافة فئة / Add Category
        </Button>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <RefreshCcw className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : categories.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Tag className="h-12 w-12 mx-auto text-muted-foreground/50" />
            <p className="mt-2">لا توجد فئات مصروفات / No expense categories</p>
            <Button onClick={handleAddCategory} variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" />
              إضافة فئة جديدة / Add New Category
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map((category) => (
              <div
                key={category.id}
                className="border rounded-lg p-4 hover:border-primary/50 transition-colors cursor-pointer"
                onClick={() => handleSelectCategory(category)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div 
                      className="w-4 h-4 rounded-full mr-2" 
                      style={{ backgroundColor: category.color || '#FF6384' }}
                    />
                    <h3 className="font-medium">{category.name}</h3>
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditCategory(category);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteCategory(category.id);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                {category.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {category.description}
                  </p>
                )}
                <div className="flex items-center mt-2 text-xs text-muted-foreground">
                  <span className={category.isActive ? 'text-green-500' : 'text-red-500'}>
                    {category.isActive ? 'نشط / Active' : 'غير نشط / Inactive'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      
      {/* نموذج إضافة/تعديل فئة */}
      {/* Add/Edit Category Form */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isEditing ? 'تعديل فئة / Edit Category' : 'إضافة فئة جديدة / Add New Category'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                اسم الفئة / Category Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name || ''}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="أدخل اسم الفئة / Enter category name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">
                الوصف / Description
              </Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="أدخل وصف الفئة / Enter category description"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label>
                اللون / Color
              </Label>
              <div className="flex flex-wrap gap-2 mt-1">
                {suggestedColors.map((color) => (
                  <div
                    key={color}
                    className={`w-8 h-8 rounded-full cursor-pointer border-2 ${
                      formData.color === color ? 'border-primary' : 'border-transparent'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleInputChange('color', color)}
                  />
                ))}
                <div className="flex items-center">
                  <Input
                    type="color"
                    value={formData.color || '#FF6384'}
                    onChange={(e) => handleInputChange('color', e.target.value)}
                    className="w-8 h-8 p-0 border-0"
                  />
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleInputChange('isActive', checked)}
              />
              <Label htmlFor="isActive">
                نشط / Active
              </Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDialog(false)}
              disabled={savingCategory}
            >
              <X className="mr-2 h-4 w-4" />
              إلغاء / Cancel
            </Button>
            <Button onClick={handleSaveCategory} disabled={savingCategory}>
              {savingCategory ? (
                <>
                  <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                  جاري الحفظ... / Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {isEditing ? 'تحديث / Update' : 'حفظ / Save'}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
