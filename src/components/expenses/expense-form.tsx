'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'sonner';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { BilingualPageTitle } from '@/components/common/bilingual-page-title';
import { formatCurrency } from '@/lib/utils';
import { 
  CalendarIcon, 
  Receipt, 
  CreditCard, 
  Tag, 
  Paperclip, 
  Save, 
  X, 
  ArrowLeft,
  RefreshCcw,
  Clock
} from 'lucide-react';

// تعريف مخطط التحقق من صحة البيانات
// Define validation schema
const expenseSchema = z.object({
  title: z.string().min(3, {
    message: 'العنوان يجب أن يكون 3 أحرف على الأقل / Title must be at least 3 characters',
  }),
  description: z.string().optional(),
  amount: z.coerce.number().positive({
    message: 'المبلغ يجب أن يكون أكبر من صفر / Amount must be greater than zero',
  }),
  taxAmount: z.coerce.number().min(0).optional(),
  expenseDate: z.date(),
  dueDate: z.date().optional().nullable(),
  paymentStatus: z.string(),
  paymentMethod: z.string().optional(),
  reference: z.string().optional(),
  recurring: z.boolean().default(false),
  recurringPeriod: z.string().optional(),
  recurringEndDate: z.date().optional().nullable(),
  categoryId: z.string().optional(),
  supplierId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  attachments: z.array(
    z.object({
      fileName: z.string(),
      fileUrl: z.string(),
      fileType: z.string(),
      fileSize: z.number(),
    })
  ).optional(),
});

// نوع بيانات المصروف
// Expense data type
type ExpenseFormData = z.infer<typeof expenseSchema>;

// خصائص مكون نموذج المصروفات
// Expense form component props
interface ExpenseFormProps {
  initialData?: ExpenseFormData;
  isEditing?: boolean;
  categories?: { id: string; name: string; color?: string }[];
  suppliers?: { id: string; name: string }[];
  onSuccess?: (data: ExpenseFormData) => void;
  onCancel?: () => void;
}

/**
 * مكون نموذج المصروفات - يستخدم لإضافة وتعديل المصروفات
 * Expense Form Component - Used for adding and editing expenses
 */
export function ExpenseForm({
  initialData,
  isEditing = false,
  categories = [],
  suppliers = [],
  onSuccess,
  onCancel,
}: Readonly<ExpenseFormProps>) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [attachmentFiles, setAttachmentFiles] = useState<File[]>([]);
  
  // إعداد النموذج مع القيم الافتراضية
  // Setup form with default values
  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
    reset,
  } = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    defaultValues: initialData || {
      title: '',
      description: '',
      amount: 0,
      taxAmount: 0,
      expenseDate: new Date(),
      dueDate: null,
      paymentStatus: 'PAID',
      paymentMethod: 'CASH',
      reference: '',
      recurring: false,
      recurringPeriod: '',
      recurringEndDate: null,
      categoryId: '',
      supplierId: '',
      tags: [],
      attachments: [],
    },
  });
  
  // مراقبة قيم النموذج
  // Watch form values
  const watchRecurring = watch('recurring');
  const watchAmount = watch('amount');
  const watchTaxAmount = watch('taxAmount') || 0;
  
  // حساب المبلغ الإجمالي
  // Calculate total amount
  const totalAmount = (watchAmount || 0) + (watchTaxAmount || 0);
  
  // معالجة تحميل المرفقات
  // Handle attachment uploads
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setAttachmentFiles(prev => [...prev, ...newFiles]);
    }
  };
  
  // حذف مرفق
  // Remove attachment
  const handleRemoveAttachment = (index: number) => {
    setAttachmentFiles(prev => prev.filter((_, i) => i !== index));
  };
  
  // تحميل المرفقات إلى الخادم
  // Upload attachments to server
  const uploadAttachments = async (): Promise<{ fileName: string; fileUrl: string; fileType: string; fileSize: number }[]> => {
    if (attachmentFiles.length === 0) return [];
    
    const uploadedAttachments = [];
    
    for (const file of attachmentFiles) {
      // هنا يمكن إضافة رمز لتحميل الملفات إلى الخادم
      // Here you can add code to upload files to the server
      // في هذا المثال، نقوم بمحاكاة التحميل
      // In this example, we simulate the upload
      
      // تحويل الملف إلى URL محلي
      // Convert file to local URL
      const fileUrl = URL.createObjectURL(file);
      
      uploadedAttachments.push({
        fileName: file.name,
        fileUrl: fileUrl,
        fileType: file.type,
        fileSize: file.size,
      });
    }
    
    return uploadedAttachments;
  };
  
  // معالجة تقديم النموذج
  // Handle form submission
  const onSubmit = async (data: ExpenseFormData) => {
    setLoading(true);
    
    try {
      // تحميل المرفقات
      // Upload attachments
      const uploadedAttachments = await uploadAttachments();
      
      // إضافة المرفقات المحملة إلى البيانات
      // Add uploaded attachments to data
      const formData = {
        ...data,
        attachments: [...(data.attachments || []), ...uploadedAttachments],
      };
      
      // تحديد عنوان الطلب والطريقة
      // Determine request URL and method
      const url = isEditing ? `/api/expenses/${initialData?.id}` : '/api/expenses';
      const method = isEditing ? 'PATCH' : 'POST';
      
      // إرسال البيانات إلى الخادم
      // Send data to server
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء حفظ المصروف');
      }
      
      const savedExpense = await response.json();
      
      // عرض رسالة نجاح
      // Show success message
      toast.success(
        isEditing 
          ? 'تم تحديث المصروف بنجاح / Expense updated successfully' 
          : 'تم إضافة المصروف بنجاح / Expense added successfully'
      );
      
      // استدعاء دالة النجاح إذا تم توفيرها
      // Call success callback if provided
      if (onSuccess) {
        onSuccess(savedExpense);
      } else {
        // إعادة التوجيه إلى صفحة المصروفات
        // Redirect to expenses page
        router.push('/dashboard/expenses');
        router.refresh();
      }
    } catch (error) {
      console.error('Error saving expense:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'حدث خطأ أثناء حفظ المصروف / Error saving expense'
      );
    } finally {
      setLoading(false);
    }
  };
  
  // معالجة الإلغاء
  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle>
            {isEditing ? 'تعديل مصروف / Edit Expense' : 'إضافة مصروف جديد / Add New Expense'}
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="basic">
                البيانات الأساسية / Basic Info
              </TabsTrigger>
              <TabsTrigger value="payment">
                بيانات الدفع / Payment
              </TabsTrigger>
              <TabsTrigger value="recurring" disabled={!watchRecurring}>
                التكرار / Recurring
              </TabsTrigger>
              <TabsTrigger value="attachments">
                المرفقات / Attachments
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">
                    العنوان / Title <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="title"
                    {...register('title')}
                    placeholder="أدخل عنوان المصروف / Enter expense title"
                    className={errors.title ? 'border-red-500' : ''}
                  />
                  {errors.title && (
                    <p className="text-red-500 text-sm">{errors.title.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="amount">
                    المبلغ / Amount <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('amount')}
                    placeholder="أدخل مبلغ المصروف / Enter expense amount"
                    className={errors.amount ? 'border-red-500' : ''}
                  />
                  {errors.amount && (
                    <p className="text-red-500 text-sm">{errors.amount.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="taxAmount">
                    مبلغ الضريبة / Tax Amount
                  </Label>
                  <Input
                    id="taxAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('taxAmount')}
                    placeholder="أدخل مبلغ الضريبة / Enter tax amount"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>
                    المبلغ الإجمالي / Total Amount
                  </Label>
                  <div className="h-10 px-3 py-2 rounded-md border bg-muted/50 flex items-center">
                    {formatCurrency(totalAmount)}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="expenseDate">
                    تاريخ المصروف / Expense Date <span className="text-red-500">*</span>
                  </Label>
                  <DatePicker
                    id="expenseDate"
                    value={watch('expenseDate')}
                    onChange={(date) => setValue('expenseDate', date || new Date())}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="categoryId">
                    الفئة / Category
                  </Label>
                  <Select
                    value={watch('categoryId') || ''}
                    onValueChange={(value) => setValue('categoryId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر فئة المصروف / Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center">
                            {category.color && (
                              <div 
                                className="w-3 h-3 rounded-full mr-2" 
                                style={{ backgroundColor: category.color }}
                              />
                            )}
                            {category.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="description">
                    الوصف / Description
                  </Label>
                  <Textarea
                    id="description"
                    {...register('description')}
                    placeholder="أدخل وصف المصروف / Enter expense description"
                    rows={3}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="supplierId">
                    المورد / Supplier
                  </Label>
                  <Select
                    value={watch('supplierId') || ''}
                    onValueChange={(value) => setValue('supplierId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر المورد / Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2 flex items-center">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Checkbox
                      id="recurring"
                      checked={watchRecurring}
                      onCheckedChange={(checked) => setValue('recurring', checked === true)}
                    />
                    <Label htmlFor="recurring" className="cursor-pointer">
                      مصروف متكرر / Recurring Expense
                    </Label>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="payment" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="paymentStatus">
                    حالة الدفع / Payment Status <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watch('paymentStatus')}
                    onValueChange={(value) => setValue('paymentStatus', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حالة الدفع / Select payment status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PAID">مدفوع / Paid</SelectItem>
                      <SelectItem value="PENDING">قيد الانتظار / Pending</SelectItem>
                      <SelectItem value="PARTIAL">مدفوع جزئياً / Partial</SelectItem>
                      <SelectItem value="CANCELLED">ملغي / Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="paymentMethod">
                    طريقة الدفع / Payment Method
                  </Label>
                  <Select
                    value={watch('paymentMethod') || ''}
                    onValueChange={(value) => setValue('paymentMethod', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر طريقة الدفع / Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">نقدي / Cash</SelectItem>
                      <SelectItem value="BANK_TRANSFER">تحويل بنكي / Bank Transfer</SelectItem>
                      <SelectItem value="CREDIT_CARD">بطاقة ائتمان / Credit Card</SelectItem>
                      <SelectItem value="CHECK">شيك / Check</SelectItem>
                      <SelectItem value="EWALLET">محفظة إلكترونية / E-Wallet</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="reference">
                    الرقم المرجعي / Reference Number
                  </Label>
                  <Input
                    id="reference"
                    {...register('reference')}
                    placeholder="أدخل الرقم المرجعي للدفع / Enter payment reference"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dueDate">
                    تاريخ الاستحقاق / Due Date
                  </Label>
                  <DatePicker
                    id="dueDate"
                    value={watch('dueDate')}
                    onChange={(date) => setValue('dueDate', date)}
                  />
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="recurring" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="recurringPeriod">
                    فترة التكرار / Recurring Period <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watch('recurringPeriod') || ''}
                    onValueChange={(value) => setValue('recurringPeriod', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر فترة التكرار / Select recurring period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DAILY">يومي / Daily</SelectItem>
                      <SelectItem value="WEEKLY">أسبوعي / Weekly</SelectItem>
                      <SelectItem value="MONTHLY">شهري / Monthly</SelectItem>
                      <SelectItem value="QUARTERLY">ربع سنوي / Quarterly</SelectItem>
                      <SelectItem value="YEARLY">سنوي / Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="recurringEndDate">
                    تاريخ انتهاء التكرار / Recurring End Date
                  </Label>
                  <DatePicker
                    id="recurringEndDate"
                    value={watch('recurringEndDate')}
                    onChange={(date) => setValue('recurringEndDate', date)}
                  />
                </div>
              </div>
              
              <div className="bg-amber-50 dark:bg-amber-950 p-4 rounded-md border border-amber-200 dark:border-amber-800">
                <div className="flex items-start">
                  <Clock className="text-amber-500 mr-2 h-5 w-5 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-amber-800 dark:text-amber-300">
                      معلومات التكرار / Recurring Information
                    </h4>
                    <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                      سيتم إنشاء مصروفات جديدة تلقائياً حسب فترة التكرار المحددة حتى تاريخ الانتهاء.
                      إذا لم يتم تحديد تاريخ انتهاء، سيستمر التكرار إلى أجل غير مسمى.
                    </p>
                    <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                      New expenses will be created automatically according to the selected recurring period until the end date.
                      If no end date is specified, the recurrence will continue indefinitely.
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="attachments" className="space-y-4">
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-6 text-center">
                  <Paperclip className="mx-auto h-8 w-8 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                    إضافة مرفقات / Add Attachments
                  </h3>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    اسحب وأفلت الملفات هنا، أو انقر لتحديد الملفات
                  </p>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Drag and drop files here, or click to select files
                  </p>
                  <input
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>
                
                {attachmentFiles.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">
                      الملفات المرفقة / Attached Files
                    </h4>
                    <ul className="space-y-2">
                      {attachmentFiles.map((file, index) => (
                        <li key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                          <div className="flex items-center">
                            <Paperclip className="h-4 w-4 mr-2 text-gray-500" />
                            <span className="text-sm">{file.name}</span>
                            <span className="text-xs text-gray-500 ml-2">
                              ({(file.size / 1024).toFixed(2)} KB)
                            </span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveAttachment(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            <X className="mr-2 h-4 w-4" />
            إلغاء / Cancel
          </Button>
          
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                جاري الحفظ... / Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {isEditing ? 'تحديث المصروف / Update Expense' : 'حفظ المصروف / Save Expense'}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
