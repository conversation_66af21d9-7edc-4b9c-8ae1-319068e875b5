'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/utils';
import { TrendingUpIcon, TrendingDownIcon, DollarSignIcon } from 'lucide-react';

interface ExpensesSummaryProps {
  totalAmount: number;
  monthlyAverage?: number;
  yearlyTotal?: number;
  previousPeriodChange?: number;
  loading?: boolean;
}

/**
 * مكون ملخص المصروفات
 * Expenses Summary Component
 */
export function ExpensesSummary({
  totalAmount,
  monthlyAverage,
  yearlyTotal,
  previousPeriodChange,
  loading = false,
}: Readonly<ExpensesSummaryProps>) {
  // حساب متوسط المصروفات الشهرية إذا لم يتم توفيره
  // Calculate monthly average if not provided
  const calculatedMonthlyAverage = monthlyAverage || totalAmount / 12;

  // حساب إجمالي المصروفات السنوية إذا لم يتم توفيره
  // Calculate yearly total if not provided
  const calculatedYearlyTotal = yearlyTotal || totalAmount;

  return (
    <>
      <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900 border-red-200 dark:border-red-800">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-red-600 dark:text-red-400">
                إجمالي المصروفات
                <span className="block text-xs text-red-500 dark:text-red-300 opacity-80">Total Expenses</span>
              </h3>
              {loading ? (
                <Skeleton className="h-8 w-32 mt-1 bg-red-200 dark:bg-red-700" />
              ) : (
                <p className="text-2xl font-bold text-red-700 dark:text-red-300 mt-1">
                  {formatCurrency(totalAmount)}
                </p>
              )}
            </div>
            <div className="w-12 h-12 rounded-full bg-red-200 dark:bg-red-800 flex items-center justify-center">
              <DollarSignIcon className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950 dark:to-amber-900 border-amber-200 dark:border-amber-800">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-amber-600 dark:text-amber-400">
                متوسط المصروفات الشهرية
                <span className="block text-xs text-amber-500 dark:text-amber-300 opacity-80">Monthly Average</span>
              </h3>
              {loading ? (
                <Skeleton className="h-8 w-32 mt-1 bg-amber-200 dark:bg-amber-700" />
              ) : (
                <p className="text-2xl font-bold text-amber-700 dark:text-amber-300 mt-1">
                  {formatCurrency(calculatedMonthlyAverage)}
                </p>
              )}
            </div>
            <div className="w-12 h-12 rounded-full bg-amber-200 dark:bg-amber-800 flex items-center justify-center">
              <TrendingDownIcon className="w-6 h-6 text-amber-600 dark:text-amber-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border-blue-200 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-blue-600 dark:text-blue-400">
                إجمالي المصروفات السنوية
                <span className="block text-xs text-blue-500 dark:text-blue-300 opacity-80">Yearly Total</span>
              </h3>
              {loading ? (
                <Skeleton className="h-8 w-32 mt-1 bg-blue-200 dark:bg-blue-700" />
              ) : (
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300 mt-1">
                  {formatCurrency(calculatedYearlyTotal)}
                </p>
              )}
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-200 dark:bg-blue-800 flex items-center justify-center">
              <TrendingUpIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
