'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { BilingualName } from '@/components/common/bilingual-name';
import { toast } from '@/components/ui/use-toast';

interface CompanyInfo {
  nameAr: string;
  nameEn: string;
  addressAr: string;
  addressEn: string;
  phone: string;
  email: string;
  website: string;
  taxNumber: string;
  logoUrl: string;
}

/**
 * نموذج معلومات الشركة ثنائي اللغة
 * Bilingual Company Information Form
 */
export function CompanyInfoForm() {
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    nameAr: '',
    nameEn: '',
    addressAr: '',
    addressEn: '',
    phone: '',
    email: '',
    website: '',
    taxNumber: '',
    logoUrl: ''
  });
  
  const [loading, setLoading] = useState(false);

  // تحديث اسم الشركة
  const handleNameChange = (nameAr: string, nameEn: string) => {
    setCompanyInfo(prev => ({
      ...prev,
      nameAr,
      nameEn
    }));
  };

  // تحديث عنوان الشركة
  const handleAddressChange = (addressAr: string, addressEn: string) => {
    setCompanyInfo(prev => ({
      ...prev,
      addressAr,
      addressEn
    }));
  };

  // تحديث معلومات الاتصال
  const handleContactInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCompanyInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // حفظ معلومات الشركة
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // هنا يمكن إضافة كود لحفظ البيانات في قاعدة البيانات
      // await saveCompanyInfo(companyInfo);
      
      // عرض رسالة نجاح
      toast({
        title: "تم الحفظ بنجاح | Saved Successfully",
        description: "تم حفظ معلومات الشركة بنجاح | Company information has been saved successfully",
      });
    } catch (error) {
      // عرض رسالة خطأ
      toast({
        title: "خطأ في الحفظ | Save Error",
        description: "حدث خطأ أثناء حفظ معلومات الشركة | An error occurred while saving company information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>معلومات الشركة | Company Information</CardTitle>
          <CardDescription>
            أدخل معلومات الشركة باللغتين العربية والإنجليزية
            <br />
            Enter company information in both Arabic and English
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* اسم الشركة - Company Name */}
          <BilingualName
            nameAr={companyInfo.nameAr}
            nameEn={companyInfo.nameEn}
            onNameChange={handleNameChange}
            label="اسم الشركة | Company Name"
            labelAr="اسم الشركة بالعربية"
            labelEn="Company Name in English"
            required={true}
            showPreview={true}
          />

          {/* عنوان الشركة - Company Address */}
          <div className="space-y-4">
            <Label>العنوان | Address</Label>
            
            <div className="space-y-2">
              <Label htmlFor="address-ar" className="text-right block text-sm">
                العنوان بالعربية
              </Label>
              <Textarea
                id="address-ar"
                dir="rtl"
                value={companyInfo.addressAr}
                onChange={(e) => setCompanyInfo(prev => ({ ...prev, addressAr: e.target.value }))}
                placeholder="أدخل عنوان الشركة بالعربية"
                className="text-right"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="address-en" className="text-left block text-sm">
                Address in English
              </Label>
              <Textarea
                id="address-en"
                dir="ltr"
                value={companyInfo.addressEn}
                onChange={(e) => setCompanyInfo(prev => ({ ...prev, addressEn: e.target.value }))}
                placeholder="Enter company address in English"
                className="text-left"
              />
            </div>
          </div>

          {/* معلومات الاتصال - Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">
                رقم الهاتف | Phone Number
              </Label>
              <Input
                id="phone"
                name="phone"
                value={companyInfo.phone}
                onChange={handleContactInfoChange}
                placeholder="+971 4 123 4567"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">
                البريد الإلكتروني | Email
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={companyInfo.email}
                onChange={handleContactInfoChange}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="website">
                الموقع الإلكتروني | Website
              </Label>
              <Input
                id="website"
                name="website"
                value={companyInfo.website}
                onChange={handleContactInfoChange}
                placeholder="www.aminplus.com"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="taxNumber">
                الرقم الضريبي | Tax Number
              </Label>
              <Input
                id="taxNumber"
                name="taxNumber"
                value={companyInfo.taxNumber}
                onChange={handleContactInfoChange}
                placeholder="123456789012345"
              />
            </div>
          </div>

          {/* شعار الشركة - Company Logo */}
          <div className="space-y-2">
            <Label htmlFor="logo">
              شعار الشركة | Company Logo
            </Label>
            <div className="flex items-center gap-4">
              {companyInfo.logoUrl && (
                <div className="w-16 h-16 border rounded overflow-hidden">
                  <img 
                    src={companyInfo.logoUrl} 
                    alt={`${companyInfo.nameAr} | ${companyInfo.nameEn}`}
                    className="w-full h-full object-contain" 
                  />
                </div>
              )}
              <Input
                id="logo"
                name="logoUrl"
                type="file"
                accept="image/*"
                onChange={(e) => {
                  // هنا يمكن إضافة كود لرفع الصورة وتحديث الرابط
                  // في هذا المثال، نستخدم URL.createObjectURL للعرض المحلي فقط
                  if (e.target.files && e.target.files[0]) {
                    const url = URL.createObjectURL(e.target.files[0]);
                    setCompanyInfo(prev => ({ ...prev, logoUrl: url }));
                  }
                }}
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button type="submit" disabled={loading}>
            {loading ? 'جاري الحفظ... | Saving...' : 'حفظ المعلومات | Save Information'}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
