'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardHeader, CardT<PERSON><PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox'; // Fixed import path
import { toast } from 'sonner';

// ترجمة الصلاحيات إلى العربية
const permissionLabels: Record<string, string> = {
    VIEW_DASHBOARD: 'عرض لوحة التحكم',
    MANAGE_CUSTOMERS: 'إدارة العملاء',
    MANAGE_INVOICES: 'إدارة الفواتير',
    MANAGE_PRODUCTS: 'إدارة المنتجات',
    VIEW_REPORTS: 'عرض التقارير',
    MANAGE_USERS: 'إدارة المستخدمين',
    MANAGE_SETTINGS: 'إدارة الإعدادات',
};

// تعريف نوع البيانات للدور
interface Role {
    id: number;
    name: string;
    description: string;
    permissions: string[];
    usersCount: number;
}

// نموذج الدور الفارغ للإنشاء
const emptyRole = {
    name: '',
    description: '',
    permissions: ['VIEW_DASHBOARD'],
};

// جميع الصلاحيات المتاحة
const availablePermissions = Object.keys(permissionLabels);

export function RoleManagement() {
    const [roles, setRoles] = useState<Role[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedRole, setSelectedRole] = useState<Role | null>(null);
    const [isEditing, setIsEditing] = useState(false);
    const [editedRole, setEditedRole] = useState<Role | null>(null);
    const [isCreating, setIsCreating] = useState(false);
    const [newRole, setNewRole] = useState({ ...emptyRole });

    // جلب الأدوار من واجهة برمجة التطبيقات
    const fetchRoles = useCallback(async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/roles');
            if (!response.ok) throw new Error(`خطأ في الاستجابة: ${response.status}`);
            const data = await response.json();
            setRoles(data);
        } catch (error) {
            console.error('فشل في جلب الأدوار:', error);
            toast.error('حدث خطأ أثناء جلب الأدوار');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchRoles();
    }, [fetchRoles]);

    // اختيار دور للعرض
    const handleRoleClick = (role: Role) => {
        setSelectedRole(role);
        setIsEditing(false);
    };

    // بدء تعديل دور
    const handleEditClick = () => {
        if (selectedRole) {
            setEditedRole({ ...selectedRole });
            setIsEditing(true);
        }
    };

    // إلغاء التعديل
    const handleCancelEdit = () => {
        setIsEditing(false);
        setEditedRole(null);
    };

    // إلغاء الإنشاء
    const handleCancelCreate = () => {
        setIsCreating(false);
        setNewRole({ ...emptyRole });
    };

    // حفظ الدور (تعديل أو إنشاء)
    const saveRole = async (url: string, method: 'PATCH' | 'POST', roleData: any, successMessage: string) => {
        try {
            const response = await fetch(url, {
                method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(roleData),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'حدث خطأ أثناء حفظ البيانات');
            }

            const result = await response.json();

            if (method === 'PATCH') {
                setRoles((prev) => prev.map((role) => (role.id === result.id ? result : role)));
                setSelectedRole(result);
            } else {
                setRoles((prev) => [...prev, result]);
                setSelectedRole(result);
            }

            setIsEditing(false);
            setIsCreating(false);
            setEditedRole(null);
            setNewRole({ ...emptyRole });

            toast.success(successMessage);
        } catch (error: any) {
            toast.error(error.message || 'حدث خطأ');
        }
    };

    // حفظ التعديلات
    const handleSaveEdit = () => {
        if (!editedRole) return;
        saveRole(`/api/roles/${editedRole.id}`, 'PATCH', editedRole, 'تم تحديث الدور بنجاح');
    };

    // إنشاء دور جديد
    const handleCreateRole = () => {
        if (!newRole.name) {
            toast.error('يجب إدخال اسم الدور');
            return;
        }
        saveRole('/api/roles', 'POST', newRole, 'تم إنشاء الدور بنجاح');
    };

    // حذف دور
    const handleDeleteRole = async () => {
        if (!selectedRole) return;

        if (selectedRole.id <= 3) {
            toast.error('لا يمكن حذف الأدوار الأساسية');
            return;
        }

        if (selectedRole.usersCount > 0) {
            toast.error('لا يمكن حذف دور مرتبط بمستخدمين');
            return;
        }

        if (!confirm('هل أنت متأكد من حذف هذا الدور؟')) {
            return;
        }

        try {
            const response = await fetch(`/api/roles/${selectedRole.id}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'حدث خطأ أثناء حذف الدور');
            }

            setRoles((prev) => prev.filter((role) => role.id !== selectedRole.id));
            setSelectedRole(null);
            toast.success('تم حذف الدور بنجاح');
        } catch (error: any) {
            toast.error(error.message || 'حدث خطأ أثناء الحذف');
        }
    };

    if (loading) {
        return <div className="flex justify-center p-8">جاري التحميل...</div>;
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-end">
                <Button type="button" onClick={() => setIsCreating(true)} disabled={isCreating}>
                    إضافة دور جديد
                </Button>
            </div>

            {isCreating && (
                <RoleForm
                    title="إضافة دور جديد"
                    role={newRole}
                    setRole={setNewRole}
                    onCancel={handleCancelCreate}
                    onSave={handleCreateRole}
                    isNew
                />
            )}

            <div className="grid gap-6 md:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle>الأدوار</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {roles.length === 0 ? (
                            <div className="text-center p-4 text-gray-500">لا توجد أدوار</div>
                        ) : (
                            <ul className="space-y-2">
                                {roles.map((role) => (
                                    <li
                                        key={role.id}
                                        className={`rounded-md border cursor-pointer hover:bg-gray-50 ${selectedRole?.id === role.id ? 'border-primary bg-gray-50' : ''
                                            }`}
                                    >
                                        <button
                                            className="w-full text-right p-3"
                                            onClick={() => handleRoleClick(role)}
                                        >
                                            <div className="font-medium">{role.name}</div>
                                            <div className="text-sm text-gray-600">{role.description}</div>
                                            <div className="text-xs text-gray-500 mt-1">المستخدمين: {role.usersCount}</div>
                                        </button>
                                    </li>
                                ))}
                            </ul>
                        )}
                    </CardContent>
                </Card>

                {selectedRole && !isEditing && (
                    <Card>
                        <CardHeader className="flex flex-row items-start justify-between">
                            <div>
                                <CardTitle>{selectedRole.name}</CardTitle>
                                {selectedRole.description && (
                                    <p className="text-sm text-gray-600 mt-1">{selectedRole.description}</p>
                                )}
                            </div>
                            <div className="flex gap-2">
                                <Button variant="outline" size="sm" onClick={handleEditClick}>
                                    تعديل
                                </Button>
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={handleDeleteRole}
                                    disabled={selectedRole.id <= 3}
                                >
                                    حذف
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <h3 className="font-medium mb-2">الصلاحيات:</h3>
                            {selectedRole.permissions.length === 0 ? (
                                <p className="text-sm text-gray-500">لا توجد صلاحيات</p>
                            ) : (
                                <ul className="space-y-1">
                                    {selectedRole.permissions.map((permission: string) => (
                                        <li key={permission} className="flex items-center text-sm">
                                            <span className="h-2 w-2 rounded-full bg-green-500 ml-2" />
                                            {permissionLabels[permission] || permission}
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </CardContent>
                    </Card>
                )}

                {isEditing && editedRole && (
                    <RoleForm
                        title={`تعديل دور: ${selectedRole?.name}`}
                        role={editedRole}
                        setRole={setEditedRole}
                        onCancel={handleCancelEdit}
                        onSave={handleSaveEdit}
                        isReadOnly={editedRole.id <= 3}
                    />
                )}
            </div>
        </div>
    );
}

interface RoleFormProps {
    readonly title: string;
    readonly role: any;
    readonly setRole: (role: any) => void;
    readonly onCancel: () => void;
    readonly onSave: () => void;
    readonly isNew?: boolean;
    readonly isReadOnly?: boolean;
}

function RoleForm({
    title,
    role,
    setRole,
    onCancel,
    onSave,
    isNew = false,
    isReadOnly = false
}: Readonly<RoleFormProps>) { // Marked props as read-only
    // تحديث قيمة في الدور
    const updateRoleField = (field: string, value: any) => {
        setRole((prev: any) => ({ ...prev, [field]: value }));
    };

    // تحديث الصلاحيات
    const handlePermissionChange = (permission: string, checked: boolean) => { // Explicitly typed 'checked'
        const updatedPermissions = checked
            ? [...role.permissions, permission]
            : role.permissions.filter((p: string) => p !== permission);

        updateRoleField('permissions', updatedPermissions);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>{title}</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div>
                        <label
                            htmlFor={`${isNew ? 'new' : 'edit'}-role-name`} // Associated label with control
                            className="block mb-1 text-sm font-medium"
                        >
                            اسم الدور
                        </label>
                        <Input
                            id={`${isNew ? 'new' : 'edit'}-role-name`}
                            value={role.name}
                            onChange={(e) => updateRoleField('name', e.target.value)}
                            disabled={isReadOnly}
                            className="max-w-xs"
                            dir="rtl"
                        />
                    </div>
                    <div>
                        <label
                            htmlFor={`${isNew ? 'new' : 'edit'}-role-description`} // Associated label with control
                            className="block mb-1 text-sm font-medium"
                        >
                            وصف الدور
                        </label>
                        <Input
                            id={`${isNew ? 'new' : 'edit'}-role-description`}
                            value={role.description || ''}
                            onChange={(e) => updateRoleField('description', e.target.value)}
                            dir="rtl"
                        />
                    </div>
                    <div>
                        <label className="block mb-1 text-sm font-medium" htmlFor="permissions">
                            الصلاحيات
                        </label>
                        <div id="permissions" className="grid gap-2 sm:grid-cols-2">
                            {availablePermissions.map((permission) => (
                                <div key={permission} className="flex items-center gap-2">
                                    <Checkbox
                                        id={`${isNew ? 'new' : 'edit'}-${permission}`}
                                        checked={role.permissions.includes(permission)}
                                        onCheckedChange={(checked: boolean) => // Explicitly typed 'checked'
                                            handlePermissionChange(permission, checked === true)
                                        }
                                    />
                                    <label
                                        htmlFor={`${isNew ? 'new' : 'edit'}-${permission}`} // Associated label with control
                                        className="text-sm cursor-pointer"
                                    >
                                        {permissionLabels[permission]}
                                    </label>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={onCancel}>
                    إلغاء
                </Button>
                <Button type="button" onClick={onSave}>
                    حفظ
                </Button>
            </CardFooter>
        </Card>
    );
}
