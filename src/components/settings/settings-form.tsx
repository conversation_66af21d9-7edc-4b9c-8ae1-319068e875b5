'use client';

import { useState, useEffect } from 'react';
import { useTauriAPI } from '@/components/providers/tauri-api-provider';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/toast';
import { useTranslation } from '@/lib/i18n';

/**
 * مكون نموذج الإعدادات
 * Settings form component
 */
export function SettingsForm() {
  const { settings, api, reload } = useTauriAPI();
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();

  // تحميل الإعدادات
  // Load settings
  useEffect(() => {
    if (settings) {
      setFormData({ ...settings });
    }
  }, [settings]);

  // تحديث قيمة الإعداد
  // Update setting value
  const handleChange = (key: string, value: string) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  // حفظ الإعدادات
  // Save settings
  const handleSave = async () => {
    if (!settings) return;

    setLoading(true);
    try {
      // تحديث كل إعداد تم تغييره
      // Update each changed setting
      for (const [key, value] of Object.entries(formData)) {
        if (settings[key] !== value) {
          await api.updateSetting(key, value);
        }
      }

      // إعادة تحميل الإعدادات
      // Reload settings
      await reload();

      // عرض رسالة نجاح
      // Show success message
      toast({
        title: t('settings.saveSuccess'),
        description: t('settings.saveSuccessDescription'),
        type: 'success',
      });
    } catch (error) {
      console.error('Error saving settings:', error);

      // عرض رسالة خطأ
      // Show error message
      toast({
        title: t('settings.saveError'),
        description: String(error),
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  if (!settings) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-40">
            <p>{t('common.loading')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Tabs defaultValue="company">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="company">{t('settings.tabs.company')}</TabsTrigger>
        <TabsTrigger value="system">{t('settings.tabs.system')}</TabsTrigger>
        <TabsTrigger value="invoice">{t('settings.tabs.invoice')}</TabsTrigger>
      </TabsList>

      {/* إعدادات الشركة */}
      {/* Company settings */}
      <TabsContent value="company">
        <Card>
          <CardHeader>
            <CardTitle>{t('settings.company.title')}</CardTitle>
            <CardDescription>{t('settings.company.description')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company_name">{t('settings.company.nameAr')}</Label>
                <Input
                  id="company_name"
                  value={formData.company_name || ''}
                  onChange={(e) => handleChange('company_name', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company_name_en">{t('settings.company.nameEn')}</Label>
                <Input
                  id="company_name_en"
                  value={formData.company_name_en || ''}
                  onChange={(e) => handleChange('company_name_en', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_address">{t('settings.company.address')}</Label>
              <Input
                id="company_address"
                value={formData.company_address || ''}
                onChange={(e) => handleChange('company_address', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company_phone">{t('settings.company.phone')}</Label>
                <Input
                  id="company_phone"
                  value={formData.company_phone || ''}
                  onChange={(e) => handleChange('company_phone', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company_email">{t('settings.company.email')}</Label>
                <Input
                  id="company_email"
                  value={formData.company_email || ''}
                  onChange={(e) => handleChange('company_email', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_tax_number">{t('settings.company.taxNumber')}</Label>
              <Input
                id="company_tax_number"
                value={formData.company_tax_number || ''}
                onChange={(e) => handleChange('company_tax_number', e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>

      {/* إعدادات النظام */}
      {/* System settings */}
      <TabsContent value="system">
        <Card>
          <CardHeader>
            <CardTitle>{t('settings.system.title')}</CardTitle>
            <CardDescription>{t('settings.system.description')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="currency">{t('settings.system.currency')}</Label>
                <Input
                  id="currency"
                  value={formData.currency || ''}
                  onChange={(e) => handleChange('currency', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="language">{t('settings.system.language')}</Label>
                <select
                  id="language"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={formData.language || 'ar'}
                  onChange={(e) => handleChange('language', e.target.value)}
                >
                  <option value="ar">{t('settings.system.languages.ar')}</option>
                  <option value="en">{t('settings.system.languages.en')}</option>
                </select>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>

      {/* إعدادات الفواتير */}
      {/* Invoice settings */}
      <TabsContent value="invoice">
        <Card>
          <CardHeader>
            <CardTitle>{t('settings.invoice.title')}</CardTitle>
            <CardDescription>{t('settings.invoice.description')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="invoice_prefix">{t('settings.invoice.prefix')}</Label>
                <Input
                  id="invoice_prefix"
                  value={formData.invoice_prefix || ''}
                  onChange={(e) => handleChange('invoice_prefix', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="purchase_prefix">{t('settings.invoice.purchasePrefix')}</Label>
                <Input
                  id="purchase_prefix"
                  value={formData.purchase_prefix || ''}
                  onChange={(e) => handleChange('purchase_prefix', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
