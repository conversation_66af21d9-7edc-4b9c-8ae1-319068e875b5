/* ===== تحسينات واجهة المستخدم المتقدمة ===== */
/* ===== Advanced UI Enhancements ===== */

/* تحسينات الحركة والتفاعل */
/* Animation and Interaction Enhancements */

/* تأثيرات التمرير المتقدمة */
/* Advanced Hover Effects */
.hover-glow-blue:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.hover-glow-purple:hover {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.4), 0 0 40px rgba(139, 92, 246, 0.2);
  transform: translateY(-2px);
}

.hover-glow-green:hover {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2);
  transform: translateY(-2px);
}

/* تأثيرات الضغط */
/* Press Effects */
.press-effect {
  transition: transform 0.1s ease;
}

.press-effect:active {
  transform: scale(0.95);
}

/* تأثيرات التحميل المتقدمة */
/* Advanced Loading Effects */
.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loading-bounce {
  animation: bounce 1s infinite;
}

/* تأثيرات النصوص المتقدمة */
/* Advanced Text Effects */
.text-shimmer {
  background: linear-gradient(
    90deg,
    #000 0%,
    #fff 50%,
    #000 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 2s linear infinite;
}

.dark .text-shimmer {
  background: linear-gradient(
    90deg,
    #fff 0%,
    #000 50%,
    #fff 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* تأثيرات الخلفية المتحركة */
/* Animated Background Effects */
.bg-particles {
  position: relative;
  overflow: hidden;
}

.bg-particles::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(34, 197, 94, 0.1) 0%, transparent 50%);
  animation: particleFloat 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(20px) rotate(240deg); }
}

/* تحسينات الأزرار المتقدمة */
/* Advanced Button Enhancements */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* تحسينات البطاقات المتقدمة */
/* Advanced Card Enhancements */
.card-tilt {
  transition: transform 0.3s ease;
  transform-style: preserve-3d;
}

.card-tilt:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
}

.card-flip {
  perspective: 1000px;
}

.card-flip-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.card-flip:hover .card-flip-inner {
  transform: rotateY(180deg);
}

.card-flip-front,
.card-flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.card-flip-back {
  transform: rotateY(180deg);
}

/* تحسينات النماذج المتقدمة */
/* Advanced Form Enhancements */
.form-group-floating {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-group-floating input {
  width: 100%;
  padding: 1rem 0.75rem 0.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  background: transparent;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group-floating input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group-floating label {
  position: absolute;
  top: 1rem;
  left: 0.75rem;
  font-size: 1rem;
  color: #6b7280;
  pointer-events: none;
  transition: all 0.3s ease;
  background: white;
  padding: 0 0.25rem;
}

.form-group-floating input:focus + label,
.form-group-floating input:not(:placeholder-shown) + label {
  top: -0.5rem;
  left: 0.5rem;
  font-size: 0.75rem;
  color: #3b82f6;
}

/* تحسينات الجداول المتقدمة */
/* Advanced Table Enhancements */
.table-hover tbody tr {
  transition: all 0.2s ease;
}

.table-hover tbody tr:hover {
  background-color: rgba(59, 130, 246, 0.05);
  transform: scale(1.01);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-striped tbody tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

.dark .table-striped tbody tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.02);
}

/* تحسينات الإشعارات المتقدمة */
/* Advanced Notification Enhancements */
.notification-slide-up {
  animation: slideUpFromBottom 0.3s ease-out;
}

.notification-slide-down {
  animation: slideDownToBottom 0.3s ease-in;
}

@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDownToBottom {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* تحسينات التنقل المتقدمة */
/* Advanced Navigation Enhancements */
.nav-item {
  position: relative;
  transition: all 0.3s ease;
}

.nav-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-item:hover::before,
.nav-item.active::before {
  width: 100%;
}

/* تحسينات الشريط الجانبي */
/* Sidebar Enhancements */
.sidebar-item {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  margin: 0.25rem 0;
}

.sidebar-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
}

.sidebar-item.active {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-left: 3px solid #3b82f6;
}

/* تحسينات الوضع المظلم */
/* Dark Mode Enhancements */
.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
}

.dark .shadow-medium {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.dark .shadow-strong {
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.5), 0 2px 8px -2px rgba(0, 0, 0, 0.3);
}

/* تحسينات الاستجابة المتقدمة */
/* Advanced Responsive Enhancements */
@media (max-width: 768px) {
  .mobile-stack {
    flex-direction: column !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
  
  .mobile-center {
    text-align: center !important;
  }
  
  .mobile-hide {
    display: none !important;
  }
}

@media (max-width: 640px) {
  .sm-text-sm {
    font-size: 0.875rem !important;
  }
  
  .sm-p-2 {
    padding: 0.5rem !important;
  }
  
  .sm-gap-2 {
    gap: 0.5rem !important;
  }
}

/* تحسينات الطباعة */
/* Print Enhancements */
@media print {
  .print-hide {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
  
  .print-no-break {
    page-break-inside: avoid;
  }
  
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
