import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  try {
    // فحص المصادقة للمسارات المحمية
    if (pathname.startsWith('/dashboard')) {
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET
      });

      if (!token) {
        // إعادة توجيه إلى صفحة تسجيل الدخول
        const loginUrl = new URL('/auth/login', request.url);
        loginUrl.searchParams.set('callbackUrl', pathname);
        return NextResponse.redirect(loginUrl);
      }
    }

    // فحص API routes
    if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth/')) {
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET
      });

      if (!token) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
    }

    return NextResponse.next();
  } catch (error) {
    console.error('[Middleware Error]:', error);

    // في حالة خطأ في المصادقة، إعادة توجيه إلى صفحة الخطأ
    if (pathname.startsWith('/dashboard')) {
      const errorUrl = new URL('/auth/error', request.url);
      errorUrl.searchParams.set('error', 'MiddlewareError');
      return NextResponse.redirect(errorUrl);
    }

    // للـ API routes، إرجاع خطأ 500
    if (pathname.startsWith('/api/')) {
      return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }

    return NextResponse.next();
  }
}







export const config = {
  matcher: [
    '/dashboard/:path*',
    '/api/:path*',
  ],
};
