{"app": {"name": "<PERSON>in <PERSON>", "description": "Sales & Inventory Management System"}, "common": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "loading": "Loading...", "noResults": "No results found", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "actions": "Actions", "status": "Status", "details": "Details", "date": "Date", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "quantity": "Quantity", "price": "Price", "description": "Description", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "notes": "Notes", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "dashboard": "Dashboard", "welcome": "Welcome to Amin Plus", "language": "Language", "theme": "Theme", "dark": "Dark", "light": "Light", "system": "System", "currency": "<PERSON><PERSON><PERSON><PERSON>", "yes": "Yes", "no": "No", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "retry": "Retry", "ascending": "Ascending", "descending": "Descending"}, "auth": {"username": "Username", "password": "Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Logged in successfully", "loginError": "<PERSON><PERSON> failed", "logoutSuccess": "Logged out successfully", "registerSuccess": "Registered successfully", "registerError": "Registration failed"}, "dashboard": {"title": "Dashboard", "overview": "Overview", "sales": "Sales", "expenses": "Expenses", "inventory": "Inventory", "customers": "Customers", "products": "Products", "invoices": "Invoices", "reports": "Reports", "settings": "Settings", "users": "Users", "roles": "Roles", "permissions": "Permissions", "activity": "Activity", "notifications": "Notifications", "recentSales": "Recent Sales", "topProducts": "Top Products", "salesOverview": "Sales Overview", "expensesOverview": "Expenses Overview", "inventoryStatus": "Inventory Status", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "revenue": "Revenue", "recentInvoices": "Recent Invoices", "tabs": {"all": "All"}}, "invoices": {"title": "Invoices", "new": "New Invoice", "edit": "Edit Invoice", "view": "View Invoice", "print": "Print Invoice", "download": "Download Invoice", "send": "Send Invoice", "delete": "Delete Invoice", "create": "Create New Invoice", "number": "Invoice Number", "date": "Invoice Date", "dueDate": "Due Date", "customer": "Customer", "items": "Items", "addItem": "Add Item", "removeItem": "Remove Item", "status": {"draft": "Draft", "sent": "<PERSON><PERSON>", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled", "unpaid": "Unpaid"}, "taxInvoice": "Tax Invoice", "supplier": "Supplier", "paymentMethod": "Payment Method", "bankTransfer": "Bank Transfer", "cash": "Cash", "creditCard": "Credit Card", "paymentStatus": "Payment Status", "amountInWords": "Amount in Words", "terms": "Terms & Conditions", "notes": "Notes", "signature": "Signature", "stamp": "Stamp", "taxNumber": "Tax Number", "vatNumber": "VAT Number", "qrCode": "Tax Invoice QR Code", "thankyou": "Thank you for your business", "generatedBy": "This invoice was generated by Amin Plus System", "searchPlaceholder": "Search for invoice...", "allStatuses": "All Statuses", "tabs": {"all": "All"}, "selectedCount": "{{count}} invoices selected", "deselectAll": "Deselect All", "deleteSelected": "Delete Selected", "table": {"invoiceNumber": "Invoice Number", "date": "Date", "customer": "Customer", "amount": "Amount", "status": "Status", "actions": "Actions"}, "noMatchingInvoices": "No invoices match your search criteria", "includingTax": "Including Tax", "confirmDelete": "Are you sure you want to delete this invoice?", "confirmBulkDelete": "Are you sure you want to delete {{count}} selected invoices?", "errors": {"fetchFailed": "Failed to fetch invoices", "fetchError": "An error occurred while fetching invoices", "deleteFailed": "Failed to delete invoice", "deleteError": "An error occurred while deleting the invoice", "bulkDeleteError": "An error occurred while deleting invoices"}}, "customers": {"title": "Customers", "new": "New Customer", "edit": "Edit Customer", "view": "View Customer", "delete": "Delete Customer", "name": "Customer Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "postalCode": "Postal Code", "taxNumber": "Tax Number", "notes": "Notes", "totalPurchases": "Total Purchases", "lastPurchase": "Last Purchase", "status": "Status", "active": "Active", "inactive": "Inactive"}, "products": {"title": "Products", "new": "New Product", "edit": "Edit Product", "view": "View Product", "delete": "Delete Product", "name": "Product Name", "description": "Description", "price": "Price", "cost": "Cost", "sku": "SKU", "barcode": "Barcode", "category": "Category", "subcategory": "Subcategory", "stock": "Stock", "lowStockAlert": "Low Stock Alert", "unit": "Unit", "weight": "Weight", "dimensions": "Dimensions", "taxRate": "Tax Rate", "status": "Status", "active": "Active", "inactive": "Inactive", "images": "Images"}, "expenses": {"title": "Expenses", "new": "New Expense", "edit": "Edit Expense", "view": "View Expense", "delete": "Delete Expense", "date": "Expense Date", "category": "Expense Category", "amount": "Amount", "description": "Description", "receipt": "Receipt", "uploadReceipt": "Upload Receipt", "paymentMethod": "Payment Method", "reference": "Reference", "vendor": "<PERSON><PERSON><PERSON>", "notes": "Notes", "status": "Status", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "categories": {"rent": "Rent", "utilities": "Utilities", "salaries": "Salaries", "supplies": "Supplies", "marketing": "Marketing", "travel": "Travel", "maintenance": "Maintenance", "insurance": "Insurance", "taxes": "Taxes", "other": "Other"}}, "inventory": {"title": "Inventory", "stock": "Stock", "stockCount": "Stock Count", "stocktake": "Stocktake", "movements": "Stock Movements", "adjustments": "Stock Adjustments", "transfer": "Stock Transfer", "receive": "Receive Stock", "return": "Return Stock", "waste": "Stock Waste", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "expiryDate": "Expiry Date", "location": "Location", "batch": "<PERSON><PERSON>", "serialNumber": "Serial Number", "status": "Status", "quantity": "Quantity", "date": "Date", "notes": "Notes", "reason": "Reason"}, "credits": {"title": "Credits", "new": "New Credit", "edit": "Edit Credit", "view": "View Credit", "delete": "Delete Credit", "customer": "Customer", "amount": "Amount", "date": "Date", "dueDate": "Due Date", "status": "Status", "notes": "Notes", "paymentSchedule": "Payment Schedule", "installments": "Installments", "remainingAmount": "Remaining Amount", "paidAmount": "<PERSON><PERSON>", "overdue": "Overdue", "paid": "Paid", "pending": "Pending", "addPayment": "Add Payment", "paymentDate": "Payment Date", "paymentAmount": "Payment Amount", "paymentMethod": "Payment Method", "reference": "Reference"}, "budget": {"title": "Budget", "new": "New Budget", "edit": "Edit Budget", "view": "View Budget", "delete": "Delete Budget", "name": "Budget Name", "period": "Period", "startDate": "Start Date", "endDate": "End Date", "category": "Category", "plannedAmount": "Planned Amount", "actualAmount": "Actual Amount", "variance": "<PERSON><PERSON><PERSON>", "notes": "Notes", "status": "Status", "active": "Active", "inactive": "Inactive", "categories": {"sales": "Sales", "expenses": "Expenses", "profit": "Profit", "investment": "Investment", "other": "Other"}}, "taxFiling": {"title": "Tax Filing", "new": "New Tax Filing", "edit": "Edit Tax Filing", "view": "View Tax Filing", "delete": "Delete Tax Filing", "period": "Period", "startDate": "Start Date", "endDate": "End Date", "dueDate": "Due Date", "filingDate": "Filing Date", "taxType": "Tax Type", "taxAmount": "Tax Amount", "status": "Status", "notes": "Notes", "documents": "Documents", "uploadDocuments": "Upload Documents", "taxTypes": {"vat": "Value Added Tax (VAT)", "incomeTax": "Income Tax", "corporateTax": "Corporate Tax", "payrollTax": "Payroll Tax", "other": "Other"}, "statuses": {"pending": "Pending", "submitted": "Submitted", "paid": "Paid", "overdue": "Overdue", "rejected": "Rejected"}}, "purchases": {"title": "Purchases", "new": "New Purchase", "edit": "<PERSON> Purchase", "view": "View Purchase", "delete": "Delete Purchase", "date": "Purchase Date", "vendor": "<PERSON><PERSON><PERSON>", "amount": "Amount", "status": "Status", "items": "Items", "addItem": "Add Item", "removeItem": "Remove Item", "notes": "Notes", "reference": "Reference", "paymentMethod": "Payment Method", "paymentStatus": "Payment Status", "receiveStatus": "Receive Status", "received": "Received", "partiallyReceived": "Partially Received", "pending": "Pending", "cancelled": "Cancelled", "receiveItems": "Receive Items", "returnItems": "Return Items"}, "waste": {"title": "Waste", "new": "New Waste", "edit": "Edit Waste", "view": "View Waste", "delete": "Delete Waste", "date": "Waste Date", "product": "Product", "quantity": "Quantity", "reason": "Reason", "notes": "Notes", "cost": "Cost", "status": "Status", "reasons": {"expired": "Expired", "damaged": "Damaged", "spoiled": "Spoiled", "lost": "Lost", "other": "Other"}}, "reports": {"title": "Reports", "sales": "Sales Report", "expenses": "Expenses Report", "inventory": "Inventory Report", "customers": "Customers Report", "products": "Products Report", "profit": "Profit Report", "tax": "Tax Report", "waste": "Waste Report", "period": "Period", "startDate": "Start Date", "endDate": "End Date", "generate": "Generate Report", "export": "Export Report", "print": "Print Report", "filters": "Filters", "groupBy": "Group By", "sortBy": "Sort By", "summary": "Summary", "details": "Details", "chart": "Chart", "table": "Table"}, "notifications": {"title": "Notifications", "markAllAsRead": "<PERSON> as <PERSON>", "viewAll": "View All Notifications", "noNotifications": "No notifications", "allMarkedAsRead": "All notifications marked as read", "featureComingSoon": "This feature will be implemented soon", "read": "Read", "unread": "Unread", "tabs": {"all": "All", "unread": "Unread", "production": "Production", "inventory": "Inventory", "quality": "Quality"}, "errors": {"updateFailed": "Failed to update notification", "updateError": "Error updating notification", "updateAllError": "Error updating notifications"}, "useMockData": "Using mock data", "usingMockData": "Using mock notifications data", "unknownTime": "Unknown time"}, "sidebar": {"dashboard": "Dashboard", "customers": "Customers", "invoices": "Invoices", "products": "Products", "recipes": "Recipes", "payments": "Payments", "desktop": "Desktop App", "settings": "Settings", "testUser": "Test User", "backToHome": "Back to Home"}, "theme": {"dark": "Dark", "light": "Light", "system": "System", "switchToLight": "Switch to Light Mode", "switchToDark": "Switch to Dark Mode", "errors": {"loadError": "Error loading theme:", "toggleError": "Error toggling theme:"}}, "settings": {"title": "Settings", "general": "General", "company": "Company", "users": "Users", "roles": "Roles", "permissions": "Permissions", "taxes": "Taxes", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "notifications": "Notifications", "backup": "Backup", "restore": "Rest<PERSON>", "update": "Update", "appearance": "Appearance", "theme": "Theme", "companyName": "Company Name", "companyLogo": "Company Logo", "companyAddress": "Company Address", "companyPhone": "Company Phone", "companyEmail": "Company Email", "companyWebsite": "Company Website", "companyTaxNumber": "Company Tax Number", "defaultCurrency": "<PERSON><PERSON><PERSON>", "defaultLanguage": "Default Language", "selectLanguage": "Select Language", "currentLanguage": "Current Language", "switchTo": "Switch to {{language}}", "languageChanged": "Language changed to: {{language}}", "pageReloading": "Page is reloading...", "languageChangeError": "Error changing language", "defaultTaxRate": "Default Tax Rate", "invoicePrefix": "Invoice Prefix", "invoiceTerms": "Invoice Terms", "invoiceNotes": "Invoice Notes", "emailSettings": "<PERSON><PERSON>s", "smtpHost": "SMTP Host", "smtpPort": "SMTP Port", "smtpUser": "SMTP User", "smtpPassword": "SMTP Password", "emailFrom": "Email From", "emailName": "Sender Name", "save": "Save Settings", "reset": "Reset Settings", "invoiceSettings": "Invoice Settings", "saveSettings": "Save Settings", "saving": "Saving...", "saveSuccess": "Setting<PERSON> saved successfully", "currencySettings": "<PERSON><PERSON><PERSON><PERSON>", "defaultTaxSettings": "Default Tax Settings", "companyInformation": "Company Information", "companyNameArabic": "Company Name (Arabic)", "companyNameEnglish": "Company Name (English)", "nextInvoiceNumber": "Next Invoice Number", "termsAndConditions": "Terms and Conditions", "invoiceFooter": "Invoice Footer", "errors": {"saveError": "Error saving settings:", "saveErrorMessage": "An error occurred while saving settings"}}}