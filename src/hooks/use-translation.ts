'use client';

import { useState, useEffect } from 'react';
import translations from '@/locales/translations';

export function useTranslation() {
  const [locale, setLocale] = useState('ar');

  useEffect(() => {
    // استرداد اللغة المحفوظة من localStorage
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
      setLocale(savedLanguage);
    }
  }, []);

  const t = (key: string): string => {
    // تقسيم المفتاح إلى أجزاء (مثال: 'invoices.title' -> ['invoices', 'title'])
    const keys = key.split('.');
    
    // الحصول على الترجمات للغة الحالية
    let translation = translations[locale];
    
    // إذا لم تكن اللغة موجودة، استخدم اللغة العربية كلغة افتراضية
    if (!translation) {
      translation = translations['ar'];
    }
    
    // البحث عن الترجمة المطلوبة
    let result = translation;
    for (const k of keys) {
      if (result && typeof result === 'object' && k in result) {
        result = result[k] as any;
      } else {
        // إذا لم يتم العثور على الترجمة، أعد المفتاح الأصلي
        return key;
      }
    }
    
    // إذا كانت النتيجة نصية، أعدها، وإلا أعد المفتاح الأصلي
    return typeof result === 'string' ? result : key;
  };

  const changeLanguage = (newLocale: string) => {
    // حفظ اللغة الجديدة في localStorage
    localStorage.setItem('language', newLocale);
    
    // تحديث اللغة الحالية
    setLocale(newLocale);
    
    // تحديث اتجاه الصفحة حسب اللغة
    const direction = newLocale === 'ar' || newLocale === 'ur' ? 'rtl' : 'ltr';
    document.documentElement.dir = direction;
    document.documentElement.lang = newLocale;
  };

  return { t, locale, changeLanguage };
}
