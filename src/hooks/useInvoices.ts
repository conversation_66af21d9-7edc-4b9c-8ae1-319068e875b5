import useSWR from 'swr';

const fetcher = (url: string) => fetch(url).then(res => res.json());

export function useInvoices(status?: string) {
  const queryParams = status ? `?status=${status}` : '';
  const { data, error, isLoading, mutate } = useSWR(
    `/api/invoices${queryParams}`,
    fetcher
  );

  const createInvoice = async (invoiceData) => {
    try {
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData)
      });

      if (!response.ok) {
        throw new Error('فشل في إنشاء الفاتورة');
      }

      const newInvoice = await response.json();
      mutate(); // تحديث البيانات المخزنة مؤقتاً
      return newInvoice;
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  };

  return {
    invoices: data || [],
    isLoading,
    error,
    createInvoice,
    mutate
  };
}
