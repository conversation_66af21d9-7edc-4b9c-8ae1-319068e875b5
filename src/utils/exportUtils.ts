import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';

// Define types for invoice and customer
interface InvoiceItem {
  name: string;
  price: number;
  quantity: number;
}

interface Invoice {
  number: string;
  date: string | Date;
  customer: {
    name: string;
    email: string;
  };
  items: InvoiceItem[];
  total: number;
}

interface Customer {
  name: string;
  email: string;
  phone?: string;
}

// تحقق من توفر المكتبات
let exportFunctionsAvailable = false;

try {
  // نتحقق أن jsPDF وXLSX متوفران
  if (typeof jsPDF === 'function' && XLSX) {
    exportFunctionsAvailable = true;
  }
} catch (error) {
  console.error("Failed to load export libraries:", error);
}

// تصدير الفاتورة كـ PDF
export function exportInvoiceToPdf(invoice: Invoice): boolean {
  if (!exportFunctionsAvailable) {
    console.error("PDF export functionality is unavailable.");
    return false;
  }

  try {
    const doc = new jsPDF();

    // إضافة دعم اللغة العربية (في بيئة حقيقية ستحتاج لتحميل ملف الخط)
    // doc.addFont('path/to/arabic-font.ttf', 'Arabic', 'normal');
    // doc.setFont('Arabic');

    // بيانات الشركة
    doc.setFontSize(20);
    doc.text('فاتورة', 105, 20, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`رقم الفاتورة: ${invoice.number}`, 190, 30, { align: 'right' });

    // معالجة التاريخ بشكل أفضل
    const invoiceDate = invoice.date instanceof Date
      ? invoice.date
      : new Date(invoice.date);
    doc.text(`التاريخ: ${invoiceDate.toLocaleDateString('ar-EG')}`, 190, 35, { align: 'right' });

    // بيانات العميل
    doc.text('العميل:', 190, 50, { align: 'right' });
    doc.text(invoice.customer.name, 190, 55, { align: 'right' });
    doc.text(invoice.customer.email, 190, 60, { align: 'right' });

    // جدول المنتجات
    const tableColumn = ['السعر', 'الكمية', 'الإجمالي', 'المنتج'];
    const tableRows: string[][] = [];

    // التحقق من السعر قبل المعالجة
    invoice.items.forEach((item: InvoiceItem) => {
      const price = typeof item.price === 'number' && !isNaN(item.price) ? item.price : 0;
      const itemData = [
        price.toFixed(2),
        item.quantity.toString(),
        (price * item.quantity).toFixed(2),
        item.name
      ];
      tableRows.push(itemData);
    });

    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 70,
      theme: 'grid',
      styles: { fontSize: 8, halign: 'right', direction: 'rtl' },
      headStyles: { fillColor: [66, 139, 202] }
    });

    // الإجمالي
    const finalY = (doc as any).lastAutoTable.finalY + 10;
    const total = typeof invoice.total === 'number' && !isNaN(invoice.total) ? invoice.total : 0;
    doc.text(`الإجمالي: ${total.toFixed(2)} ريال`, 190, finalY, { align: 'right' });

    doc.save(`invoice-${invoice.number}.pdf`);
    return true;
  } catch (error) {
    console.error("Error generating PDF:", error);
    return false;
  }
}

// تصدير العملاء كـ Excel
export function exportCustomersToExcel({
  customers,
  fileName = "customers.xlsx"
}: {
  customers: Customer[];
  fileName?: string;
}): boolean {
  if (!exportFunctionsAvailable) {
    console.error("Excel export functionality is unavailable.");
    return false;
  }

  try {
    const worksheet = XLSX.utils.json_to_sheet(customers);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "العملاء");
    XLSX.writeFile(workbook, fileName);
    return true;
  } catch (error) {
    console.error("Error generating Excel file:", error);
    return false;
  }
}
