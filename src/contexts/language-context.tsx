'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { SupportedLanguage, DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES, getCurrentLanguage, changeLanguage } from '@/config/languages';
import { bilingualToast } from '@/components/ui/bilingual-toast';

interface LanguageContextType {
  language: SupportedLanguage;
  setLanguage: (language: SupportedLanguage) => void;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
  initialLanguage?: SupportedLanguage;
}

/**
 * مزود سياق اللغة
 * Language Context Provider
 */
export function LanguageProvider({
  children,
  initialLanguage = DEFAULT_LANGUAGE
}: Readonly<LanguageProviderProps>) {
  const [language, setLanguageState] = useState<SupportedLanguage>(initialLanguage);
  const [isRTL, setIsRTL] = useState<boolean>(SUPPORTED_LANGUAGES[initialLanguage].direction === 'rtl');
  const [isInitialized, setIsInitialized] = useState(false);

  // تهيئة اللغة عند تحميل المكون
  useEffect(() => {
    if (typeof window !== 'undefined' && !isInitialized) {
      const currentLanguage = getCurrentLanguage();
      setLanguageState(currentLanguage);
      setIsRTL(SUPPORTED_LANGUAGES[currentLanguage].direction === 'rtl');
      
      // تطبيق اتجاه اللغة على عنصر HTML
      document.documentElement.lang = currentLanguage;
      document.documentElement.dir = SUPPORTED_LANGUAGES[currentLanguage].direction;
      
      setIsInitialized(true);
    }
  }, [isInitialized]);

  // تغيير اللغة
  const setLanguage = (newLanguage: SupportedLanguage) => {
    if (newLanguage === language) return;
    
    // تحديث حالة اللغة
    setLanguageState(newLanguage);
    setIsRTL(SUPPORTED_LANGUAGES[newLanguage].direction === 'rtl');
    
    // حفظ اللغة وتطبيق الاتجاه
    changeLanguage(newLanguage);
    
    // عرض إشعار بتغيير اللغة
    bilingualToast.info(
      'تم تغيير اللغة إلى العربية. جاري إعادة تحميل الصفحة...',
      'Language changed to English. Reloading page...'
    );
    
    // إعادة تحميل الصفحة لتطبيق التغييرات
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
}

/**
 * هوك استخدام سياق اللغة
 * Language Context Hook
 */
export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  
  return context;
}
