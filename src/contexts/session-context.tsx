'use client';

import { SessionProvider as NextAuthSessionProvider } from 'next-auth/react';
import { ReactNode, useEffect, useState } from 'react';
import { toast } from 'sonner';

interface SessionContextProps {
  children: ReactNode;
}

export function SessionProvider({ children }: SessionContextProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    // معالجة أخطاء NextAuth
    const handleNextAuthError = (event: any) => {
      if (event.detail?.error) {
        console.error('[NextAuth Client Error]:', event.detail.error);
        
        // عرض رسالة خطأ للمستخدم
        const errorMessages: Record<string, string> = {
          'CLIENT_FETCH_ERROR': 'خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.',
          'CLIENT_SESSION_ERROR': 'خطأ في جلسة المستخدم. يرجى تسجيل الدخول مرة أخرى.',
          'CLIENT_SIGNIN_ERROR': 'خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.',
          'default': 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.'
        };

        const errorType = event.detail.error.message || 'default';
        const message = errorMessages[errorType] || errorMessages.default;
        
        toast.error(message);
      }
    };

    // الاستماع لأخطاء NextAuth
    window.addEventListener('nextauth:error', handleNextAuthError);

    return () => {
      window.removeEventListener('nextauth:error', handleNextAuthError);
    };
  }, []);

  // عدم عرض المحتوى حتى يتم تحميل العميل
  if (!isClient) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <NextAuthSessionProvider
      session={null}
      refetchInterval={5 * 60} // إعادة جلب الجلسة كل 5 دقائق
      refetchOnWindowFocus={true}
      refetchWhenOffline={false}
    >
      {children}
    </NextAuthSessionProvider>
  );
}
